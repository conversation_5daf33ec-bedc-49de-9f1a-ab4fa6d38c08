<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
  </head>
  <body>
    <script>
      const ORIGIN_ALLOWS = [
        "https://touch.10086.cn",
        "https://grey.touch.10086.cn",
        "https://m.staff.ydsc.liuliangjia.cn"
      ];
      const url = new URL(location.href);
      const artifact = url.searchParams.get("artifact");
      let backUrl = new URL(url.searchParams.get("backUrl"));
      const index = ORIGIN_ALLOWS.indexOf(backUrl.origin);
      if (top != window) {
        if (index > -1) {
          top.postMessage(
            {
              action: "artifact-process",
              res: artifact,
            },
            ORIGIN_ALLOWS[index]
          );
        }
      } else {
        if (
          index === -1
        ) {
          backUrl = new URL(location.origin + "/yundian/nearby/index.html");
        }
        let type = url.searchParams.get("type") || "00"
        backUrl.searchParams.set("type", type);
        backUrl.searchParams.set("artifact", url.searchParams.get("artifact"));
        location.href = backUrl.toString() || "/";
      }
      // if (artifact && artifact != '-1') {
      //     const type = url.searchParams.get('type')
      //     fetch(`/netShop/login/getArtifact?artifact=${artifact}&type=${type}`).then(async res => {
      //         let result = await res.json()
      //         console.log(result)
      //         if (result.code == 0) {
      //             localStorage.setItem('yundianToken', result.data.token);
      //             redirect(result.data)
      //         } else {
      //             redirect()
      //         }
      //     });
      // let xmlHttp = new XMLHttpRequest()
      // let sendJson = {
      //     artifact : artifact,
      //     type : type
      // }
      // xmlHttp.open("get","/netShop/login/getArtifact")
      // xmlHttp.setRequestHeader("Content-Type","application/json")
      // xmlHttp.send(JSON.stringify(sendJson))
      // let myTest = ''
      // xmlHttp.onreadystatechange = function() {
      //     if(this.status==200){
      //         myTest = this.responseText
      //         console.log("responseText",this.responseText)
      //         console.log("responseText",JSON.parse(this.responseText))
      //         console.log("code",this.responseText.code)
      //         console.log("message",this.responseText.message)
      //     }
      // }
      // } else {
      //     redirect()
      // }

      // function redirect(data) {
      //   if (top != window) {
      //     top.postMessage(
      //       {
      //         action: "artifact-process",
      //         res: data,
      //       },
      //       "*"
      //     );
      //   } else {
      //     const backUrl = url.searchParams.get("backUrl");
      //     location.href = backUrl || "/";
      //   }
      // }
    </script>
  </body>
</html>
