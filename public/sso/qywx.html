<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
  </head>
  <body>
    <script>
      const ORIGIN_ALLOWS = [
        "https://touch.10086.cn",
        "https://grey.touch.10086.cn",
        "https://m.staff.ydsc.liuliangjia.cn"
      ];
      const url = new URL(location.href);
      let backUrl = new URL(url.searchParams.get("state"));
      const index = ORIGIN_ALLOWS.indexOf(backUrl.origin);
      if ( index > -1 ) {
        backUrl.searchParams.set('code',url.searchParams.get('code'))
        location.href = backUrl.toString() || "/";
      }

    </script>
  </body>
</html>
