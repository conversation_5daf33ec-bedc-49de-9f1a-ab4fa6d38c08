<!--
 * @Author: 李亚龙
 * @Description:
 * @FilePath: \yundian-m\public\index.html
-->
<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport"
    content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no">
  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
  <meta name="WT.mc_ev" content="">
  <!------解决跳转后回来页面会放大的问题------------>
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Cache-Control" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <!------解决跳转后回来页面会放大的问题------------>
  <title>云店</title>
  <script type="text/javascript" src="https://img0.shop.10086.cn/thirdpart/sdc/jt_sdc_load.js"></script>
  <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
  <script type="text/javascript" src="https://open.work.weixin.qq.com/wwopen/js/jwxwork-1.0.0.js"></script>
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= webpackConfig.name %> doesn't work properly without JavaScript enabled. Please enable it
      to continue.</strong>
  </noscript>
  <div id="app"></div>
  <div class="icpinfo">
    <address class="aligncenter">
      京ICP备05002571号 &nbsp;&nbsp;&nbsp;
      <!-- <a href="https://www.10086.cn/zzxx">证照信息</a> -->
      <!-- <a href="https://www.10086.cn/zzxx/">证照信息</a> -->
      <a href="https://www.10086.cn/zzxx/?channel=0002">证照信息</a>
      <br>
      Copyright©1999-2023 中国移动 版权所有<br>本网站支持IPv6访问</address>
  </div>
  <!-- built files will be auto injected -->
  <script type="text/JavaScript">
    if(location.search.indexOf('debugger=1') > -1 ){
      var script = document.createElement('script')
      //script.src = 'https://cdn.bootcdn.net/ajax/libs/vConsole/3.3.4/vconsole.min.js'
      script.src = 'https://cdn.jsdelivr.net/npm/vconsole@latest/dist/vconsole.min.js'
      script.onload = function(){
        window.vConsole = new VConsole();
      }
      document.body.appendChild(script)
    }
    let url = window.location.href,notkafa = location.hostname.indexOf("staff")!=-1 ||location.hostname.indexOf("grey")!=-1||location.hostname.indexOf("touch.10086.cn")!=-1
    if ((notkafa)&&url.indexOf("https") < 0) {
      url = url.replace("http:", "https:");
      window.location.replace(url);
    }
  </script>
  <style>
    .icpinfo {
      background: #f6f6f6;
      font-size: 12px;
      text-align: center;
      line-height: 1.5;
      height: 80px;
      padding: 10px 0;
      font-family: none;
    }

    .icpinfo a {
      color: #333;
    }

  </style>
</body>

</html>
