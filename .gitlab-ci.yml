# This file is a template, and might need editing before it works on your project.
# Official framework image. Look for the different tagged releases at:
# https://hub.docker.com/r/library/node/tags/

stages:
  - build
  - deploy
  - publish

job-build:
  stage: build
  image:
    name: node:lts-buster-slim
  only:
    - staff
    - grey
    - master
    - web
  tags:
    - vue

  cache:
    key: ${CI_COMMIT_REF_NAME}
    paths:
      - node_modules/
  script:
    - npm set registry https://registry.npm.taobao.org
    - npm set sass_binary_site https://npm.taobao.org/mirrors/node-sass
    - npm set proxy http://***********:3128
    - npm install --progress=false
    - npm run build
  artifacts:
    expire_in: 1 week
    paths:
      - dist
      - docker

job-deploy:
  stage: deploy
  image: docker:stable
  before_script:
    - echo ${CI_REGISTRY_IMAGE}
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} **********
  script:
    - docker build --pull -t **********/cnmall/${CI_PROJECT_NAME}:${CI_COMMIT_REF_NAME} -f docker/Dockerfile .
    - docker push **********/cnmall/${CI_PROJECT_NAME}:${CI_COMMIT_REF_NAME}
  tags:
    - docker
  only:
    - web
    - staff
    - schedules


job-update-rancher:
  image: curlimages/curl
  stage: publish
  script:
    - curl -u "${RANCHER_TOKEN}" -X POST -k "https://**********/v3/project/c-82pgx:p-nztxv/workloads/deployment:default:yundian-m?action=redeploy"
  tags:
    - docker
  only:
    - web
    - staff
    - schedules


