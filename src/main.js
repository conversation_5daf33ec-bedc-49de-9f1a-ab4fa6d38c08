/*
 * @Author: 李亚龙
 * @Date: 2021-08-24 11:34:58
 * @LastEditTime: 2022-04-25 10:36:20
 * @LastEditors: Please set LastEditors
 * @Description:
 * @FilePath: \yundian-m\src\main.js
 */
// import "amfe-flexible"
import Vue from "vue"
import "@/styles/main.scss"
import "@/utils/array"
import "@/styles/reset.css"
import "@/assets/icon/iconfont.css"
import * as filters from "@/filters"
import router from "@/router"
import store from "@/store"
import App from "./App.vue"
import VueCookies from "vue-cookies"
import { Toast } from "vant"
import wisdomScreentLoading from "@/views/wisdomScreen/components/wisdomScreentLoading/wisdomScreentLoading.js"
import VueCompositionAPI from '@vue/composition-api'
Vue.use(VueCompositionAPI)
Vue.use(wisdomScreentLoading)

import touch from "vue-directive-touch"
import { encryptRsa } from "./utils/transit-encrypt"
Vue.use(touch)

Vue.use(VueCookies).use(Toast)
// import "@/mock"
// register global utility filters.
Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key])
})

Vue.directive("myhtml", function(el, binding) {
  if (binding && binding.oldValue != binding.value) {
    el.innerHTML = binding.value
  }
})

//全局rsa加密防漏洞问题
Vue.prototype.$encryption = encryptRsa

router.beforeEach((_to, _from, next) => {
  if (_to.meta.content || _to.meta.name) {
    let head = document.getElementsByTagName("head")
    let meta = document.createElement("meta")
    meta.content = _to.meta.content
    meta.name = _to.meta.name
    head[0].appendChild(meta)
  }
  if (_to.meta.metas) {
    let head = document.getElementsByTagName("head")
    _to.meta.metas.forEach(item=>{
      let meta = document.createElement("meta")
      meta.name = item.name
      meta.content = item.content
      meta.setAttribute('itemprop',item.itemprop)
      head[0].appendChild(meta)
    })
  }
  /* 路由发生变化修改页面title */
  if (_to.meta.title) {
    document.title = _to.meta.title
  }
  let refid = null //VueCookies.get('AssistantInfo')
  let refA = "",
    refB = "",
    tmp = null
  // eslint-disable-next-line no-cond-assign
  if ((tmp = document.cookie.match(/AssistantInfo=([^\s;]+)/i))) {
    refA = tmp[1]
  }
  // eslint-disable-next-line no-cond-assign
  if ((tmp = document.cookie.match(/ChannelControl=([^\s;]+)/i))) {
    refB = tmp[1]
  }
  if (refA || refB) {
    refid = refA + "|" + refB
  }
  if (window._tag && refid) {
    window._tag.setOtherParameter({ refid: refid })
  }
  next()
})
Vue.config.productionTip = false
/* eslint-disable no-new */
new Vue({
  el: "#app",
  render: (h) => h(App),
  store,
  router,
})
