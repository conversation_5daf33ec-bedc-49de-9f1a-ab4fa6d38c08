import Vue from "vue"
import Router from "vue-router"
import views from "./routers"
import loginUtils from '@/utils/login'
import LoginDialog from '@/components/login/index'
import { Dialog } from "vant"
import { editCookie } from "@/utils/utils.js"

Vue.use(Router)
// 4G免登弹框
function createLoginDialog(callback) {
  var a = document.createElement('div')
  a.id ="loginArea"
  document.body.appendChild(a)
  let vm =new Vue({
    el: '#loginArea',
    render: function(h) {
      return (
        <div id="loginArea">
          <LoginDialog
            is-force-login={true}
            is-auto-login={true}
            isloginfn={(args) => {
              vm.$destroy()
              document.body.removeChild(document.getElementById("loginArea"))
              callback(args)
            }}
          >
          </LoginDialog>
        </div>)
    }
  })
}
// 权限检查
function roleCheck(router,role,flag){
  if(router.meta && router.meta.role && router.meta.role.includes(parseInt(role))){
    return true
  }else{
    if(router.name=="ydManage" && flag.shops && flag.shops.length>0){
      if(flag.shops && flag.shops.length==1){
        if(flag.shops[0].shopId){//已开店
          location.href='/ydmanage/index.html?shopId='+flag.shops[0].shopId
        }else{//未开店
          location.href='/ydmanage/index.html?staffId='+ flag.shops[0].staffId
        }

      }else{
        location.href="/hd/xskd/storelist.html"
      }
      return true
    }else{
      return false
    }
  }
}
// 没有权限提示
function createDialog(to,next){
  Dialog.confirm({
    title: "温馨提示",
    message: "您没有权限查看当前页面",
    confirmButtonColor: "#5099fe",
    confirmButtonText: "确认",
    showCancelButton:false
  }).then(() => {
    if(to.query && to.query.shopId){
      next({ path: '/index.html',query:{shopId:to.query.shopId} })
    }else{
      next({ path: '/nearby' })
    }
  })
}
// 登录成功回调
function loginSuccess(flag,to,next){
  editCookie("uhide","1")
  if(flag) {
    let hasRole = roleCheck(to,flag.RuleId,flag)
    if(hasRole){
      next()
    }else{
      createDialog(to,next)
    }
  }
}

// console.log(views)
const router = new Router({
  mode: "history",
  base: "/yundian",
  routes: [
    ...views,
    // { path: "/404", component: () => import("@/views/404"), hidden: true },
    { path: "*", component: () => import("@/views/404")},
    { path: "", redirect: "/index.html", hidden: true },
  ],
  scrollBehavior(to, from, savedPosition) {
    return { x: 0, y: 0 }
  }
})

router.beforeEach(async(to, from, next) => {
  const isConfigure = to.query && to.query.configure && to.query.configure==="1"
  const isManage = to.name=="ydManage" ? null : "0"
  if(to.meta && to.meta.login === true || isConfigure) {
    loginUtils.login(true, true, (flag)=> {
      loginSuccess(flag,to,next)
    }, false, true, () => {
      createLoginDialog((flag) => {
        //4G免登成功
        loginSuccess(flag,to,next)
      })
    }, isManage)
  } else {
    next()
  }
})
export default router
