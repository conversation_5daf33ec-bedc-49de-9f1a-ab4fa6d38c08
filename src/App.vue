<template>
  <div id="app">
    <keep-alive :exclude="exclude">
      <router-view v-if="$route.meta.keepAlive && isRouterAlive" :key="$route.fullPath"></router-view>
    </keep-alive>
    <router-view v-if="!$route.meta.keepAlive"></router-view>
  </div>
</template>

<script>
import Vue from 'vue'
import VueCookies from 'vue-cookies'
import { getAllSearchParamsArray } from '@/utils/utils'
import UA from "@/utils/ua"
Vue.use(VueCookies)
export default {
  name: "App",
  provide() {
    return {
      reload: this.reload
    }
  },
  data(){
    return {
      isRouterAlive:true,
      exclude:null
    }
  },
  async created(){
    let allParams = getAllSearchParamsArray(location.href)
    let wxInfo= allParams.wxInfo
    if (wxInfo) {
      this.$cookies.set('wxInfo', wxInfo.replace(/\s/g,'+') , { 'path': '/' })
    }
    const isWXMapp = await UA.isWeChatMiniApp()
    if(!isWXMapp){
      this.$cookies.remove('wxInfo')
    }
    if(allParams['WT.ac_id']){
      this.$cookies.set('ac_id', allParams['WT.ac_id'] , { 'path': '/' })
    }
    this.$cookies.config("180d",'/')
  },
  methods:{
    reload(exclude){
      this.isRouterAlive = false
      this.exclude = null
      this.$nextTick(()=>{
        this.isRouterAlive = true
        this.exclude = exclude
      })
    }
  }
}
</script>
<style lang="scss">
#app{
  min-height: calc(100vh - 80px);
}
// 全屏置灰代码
// html{
//   -webkit-filter: grayscale(100%);
//   -moz-filter: grayscale(100%);
//   -ms-filter: grayscale(100%);
//   -o-filter: grayscale(100%);
//   filter: grayscale(100%);
// }
</style>
