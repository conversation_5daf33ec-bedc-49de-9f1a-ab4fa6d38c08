export const iosInputHandle = () => {
  // 判断是否是ios
  var ua = navigator.userAgent
  if (ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
    let flag = false
    let pageBackNormFunc
    document.body.addEventListener('focusin', (event) => {
      if(event.target.getAttribute("data-name")!=="addfocus"){
        return false
      }
      // 软键盘弹起事件
      flag = true
      pageBackNormFunc && clearTimeout(pageBackNormFunc)
      if(document.documentElement.scrollTop < 300 ){
        let pageBackNormFunc1 = setTimeout(function() { 
          // 当键盘弹起的时候如果页面在原始位置，往上滑动一段距离
          window.scrollTo({ top: 300, left: 0, behavior: 'smooth' })
          clearTimeout(pageBackNormFunc1)
        }, 50)
      }
    })
    document.body.addEventListener('focusout', (event) => {
      if(event.target.getAttribute("data-name")!=="addfocus"){
        return false
      }
      // 软键盘关闭事件
      if (flag) {
        pageBackNormFunc = setTimeout(function() { 
          // 当键盘收起的时候让页面回到原始位置
          window.scrollTo({ top: 0, left: 0, behavior: 'smooth' }) 
        }, 200)
      }
      flag = false
    })
  }
}
export default {
  iosInputHandle
}