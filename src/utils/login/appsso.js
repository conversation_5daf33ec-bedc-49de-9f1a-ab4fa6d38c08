// APPSSO
import { getQueryString, dateFormat } from "@/utils/utils"
import iframeLogin from "./iframeLogin"
import { getUserInfoByArtifact } from "./getUserInfo"

const APPSSO = function(loginUrl, channelID, uid, sucCb, failCb,userType,token,reqsource) {
  let channelId = getQueryString("channelId")
  let url = `${window.location.origin}${window.location.pathname}`
  let target
  if (channelId) {
    target = encodeURIComponent(`${url}?channelId=${channelId}`)
  } else {
    target = encodeURIComponent(url)
  }

  let timeStamp = dateFormat(new Date(),"yyyyMMddhhmmss"),
    randomNumber = Math.ceil(Math.random() * Math.pow(10, 10))

  const TransactionID = channelID + timeStamp + randomNumber

  let actionUrl = `${loginUrl}/AppSSO.action`
  let jumpUrl = `${actionUrl}?UID=${uid}&targetUrl=${target}&targetChannelID=${channelID}&TransactionID=${TransactionID}`
  iframeLogin(jumpUrl, artifact => {
    console.log('iframeLogin', artifact)
    getUserInfoByArtifact(artifact, sucCb, failCb,userType,token,reqsource)
  })
}

export default APPSSO
