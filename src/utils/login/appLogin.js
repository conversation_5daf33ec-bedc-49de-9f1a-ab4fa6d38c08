// APP登录
import { getQueryString } from "../utils"
import leadeonLoader from "../leadeonloader"
import APPSSO from "./appsso"
import { getUserInfoByArtifact } from "./getUserInfo"

// 删除链接上的artifact、type、uid
const delParams = () => {
  let url = new URL(location.href),
    params = url.searchParams
  params.delete("artifact")
  params.delete("type")
  params.delete("uid")
  params.delete("cmToken")
  history.replaceState(null, null, url.toString())
}

// 删除链接上的参数同时执行初始化方法
const delParamsAndInit = (sucCb) => {
  return (opts) => {
    delParams()
    sucCb && sucCb(opts)
  }
}

const AppLogin = async(appForceLogin, loginUrl, channelID, sucCb , isLogined,userType,reqsource) => {
  console.log('APP登录')
  const leadeon = await leadeonLoader()
  console.log('APP登录-leadeon')
  leadeon.userStatus({
    success: function(res) {
      console.log('APP登录-success', res)
      var status = res.status // 本网登录状态：0未登录；1服务密码登录；2短信验证码登录
      if (status == 1 || status == 2) {
        // 会话校验
        leadeon.checkSessionIsvalid({
          success: function(res1) {
            console.log('checkSessionIsvalid', res1)
            var status1 = res1.status // 字符串类型，状态：0 校验失败；1 校验成功。
            if (status1 == 1) {
              //获取客户端用户信息
              console.log('执行到了leadeon.getYDRZToken')
              leadeon.getYDRZToken({
                debug:false,
                sourceId: '12006',
                success: function(res) {
                  console.log('getYDRZToken', res)
                  getUserInfoByArtifact(
                    null,
                    delParamsAndInit(sucCb),
                    delParams,
                    userType,
                    res.token,
                    reqsource
                  ).then(res => {
                    if(res.status){
                      console.log('登录成功', res)
                    }
                  })
                },
                error:function(error){
                  console.log(error,'error')
                  appForceLogin && leadeon.showLogin()
                }
              })

              // leadeon.getUserInfo({
              //   success: function(data) {
              //     // 端内判断已登录的条件：1 app已登录 2 页面已登录 3 app登录用户同页面登录用户
              //     if (data.token) {
              //       // 用户在app内已登录，走APPSSO，更新页面用户信息
              //       let uidJSON = data.token.split(";")[1]
              //       let uid = uidJSON.split("=")[1]

              //       // 测试环境uid直接写在链接上
              //       let uidTest = getQueryString("uidTest")
              //       if (uidTest) {
              //         uid = uidTest
              //       }
              //       APPSSO(loginUrl, channelID, uid, sucCb,null,userType,null,reqsource)
              //       if(!isLogined || data.phoneNumber != window.decode_mobile(localStorage.getItem('wtmobile'))){
              //         localStorage.removeItem('yundianToken')
              //         APPSSO(loginUrl, channelID, uid, sucCb,null,userType,null,reqsource)
              //       }
              //     } else {
              //       // 用户未登录
              //       appForceLogin && leadeon.showLogin()
              //     }
              //   },
              //   error: function(data) {
              //     appForceLogin && leadeon.showLogin()
              //   }
              // })
            } else {
              appForceLogin && leadeon.overTime()
            }
          },
          error: function(error) {
            appForceLogin && leadeon.showLogin()
          }
        })
      } else {
        // appForceLogin && leadeon.showLogin()
        //未登录就删除token
        localStorage.removeItem('yundianToken')
        if(appForceLogin){
          leadeon.showLogin()
        }else{
          sucCb()
        }
      }
    },
    error: function(error) {
      //alert('APP登录-error')
      localStorage.removeItem('yundianToken')
      if(appForceLogin){
        leadeon.showLogin()
      }else{
        sucCb()
      }
    }
  })
}

export default AppLogin
