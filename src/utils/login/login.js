// 登录逻辑(废弃文件)
import { getQueryString } from "@/utils/utils"
import UA from "../ua"
import H5Login from "./H5Login"
import AppLogin from "./appLogin"
import { getLocalStorage } from "./localStorage"
import { getUserInfoByArtifact } from "./getUserInfo"
import Cookie from "js-cookie"
import { tySSO } from "./tysso"
import APPSSO from "./appsso"

/**
 * @param {Boolean} h5ForceLogin h5是否强制登录
 * @param {Boolean} appForceLogin app是否强制登录
 * @param {Function} sucCb 登录成功后的回调
 * @param {Boolean} smsLogin h5是否使用弹窗短信验证码登录
 */
async function login(
  h5ForceLogin = false,
  appForceLogin = false,
  sucCb = () => {},
  smsLogin = false
) {
  const channelID = "12118",
    testLoginUrl = "https://actest.10086.cn", // 测试登录域名
    prodLoginUrl = "https://login.10086.cn", // 生产登录域名
    isProd = location.host.indexOf("staff.ydsc.liuliangjia.cn") == -1 // 是否为生产地址

  let loginUrl = isProd ? prodLoginUrl : testLoginUrl
  let { auth, isLogined } = getLocalStorage()
  let dq_uid = localStorage.getItem("dq_uid")
  if (UA.isApp) {
    AppLogin(appForceLogin, loginUrl, channelID, sucCb)
  } else {
    // 通过链接上artifact登录
    let artifact = getQueryString("artifact")
    let uid = getQueryString("uid")
    let token = getQueryString("token")
    let is_login = Cookie.get("is_login")
    let status = 1 // 0 成功 1 失败

    if (UA.isWechat) {
      window.wx.miniProgram.getEnv((res) => {
        if (res.miniprogram) {
          // 小程序内
          if (dq_uid && dq_uid == uid && isLogined) {
            sucCb(auth)
            delParams()
            return //失败没有回调
          }
        } else {
          // 微信内
          if (isLogined) {
            sucCb(auth)
            delParams()
            return
          }
        }
      })
    } else {
      if (isLogined) {
        sucCb(auth)
        delParams()
        return
      }
    }

    // artifact登录
    if (artifact) {
      let artifactResult = await getUserInfoByArtifact(
        artifact,
        delParamsAndInit(sucCb),
        delParams
      )
      status = artifactResult.status
      if (!status) {
        //登录成功后就不往下走了
        return
      }
    }

    // 大网token登录
    if (token) {
      let artifactResult = await getUserInfoByArtifact(
        null,
        delParamsAndInit(sucCb),
        delParams,
        "0",
        null,
        token
      )
      status = artifactResult.status

      //登录成功后就不往下走了
      if (!status) {
        return
      }
    }

    // uid登录
    if (status && uid) {
      localStorage.setItem("dq_uid", uid)
      APPSSO(loginUrl, channelID, uid, sucCb) //失败没有回调
    } else if (status && is_login) {
      // alert("cookie");
      tySSO(async(artifact) => {
        if (artifact && artifact != -1) {
          let tyResult = await getUserInfoByArtifact(
            artifact,
            delParamsAndInit(sucCb)
          )
          status = tyResult.status
        } else {
          // 未登录将不再传-1
          Cookie.remove("is_login", { path: "/", domain: ".10086.cn" })
          Cookie.remove("is_login", { path: "/" })
          if (h5ForceLogin) {
            H5Login(loginUrl, channelID, smsLogin)
          } else {
            if(!appForceLogin){
              // !sucCb()
              sucCb()
            }
          }
        }
      })
    } else if (status && h5ForceLogin) {
      H5Login(loginUrl, channelID, smsLogin)
    } else {
      if(!h5ForceLogin){
        if(!appForceLogin){
          // !sucCb(auth) //回调加上返回值
          sucCb(auth) //回调加上返回值
        }
      }
    }
  }
}

// 删除链接上的artifact、type、uid
const delParams = () => {
  let url = new URL(location.href),
    params = url.searchParams
  params.delete("artifact")
  params.delete("type")
  params.delete("uid")
  history.replaceState(null, null, url.toString())
}

// 删除链接上的参数同时执行初始化方法
const delParamsAndInit = (sucCb) => {
  return (opts) => {
    delParams()
    sucCb && sucCb(opts)
  }
}

export default login
