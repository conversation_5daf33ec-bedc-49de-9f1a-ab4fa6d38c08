// iframe形式进行登录
let cb = null

// callback的参数为artifact
const iframeLogin = function(url, callback) {
  handleCallback(callback)
  let ssoframe = document.querySelector("#ssocheckframe")
  if (!ssoframe) {
    let frame = document.createElement("iframe")
    frame.id = "ssocheckframe"
    // frame.style = "display:none";  此方法ios10会报错
    frame.setAttribute("style", "display: none")
    frame.src = "about:blank"
    document.body.appendChild(frame)
    ssoframe = document.querySelector("#ssocheckframe")
  }
  ssoframe.onload = function() {
    document.body.removeChild(ssoframe)
  }
  ssoframe.setAttribute("src", url)
}

const handleCallback = callback => {
  console.log('成功注册全局 message 事件')
  window.addEventListener("message", receiveMessage, false)
  cb = callback
}

// 接收iframe发来的信息
const receiveMessage = event => {
  console.log('全局message事件被触发', event)
  
  /*
  action: "artifact-process"
  res: "157015d04c9d41589e82f0c062bdae1e"
  */
  if (event && event.data) {
    let data = event.data
    console.log('event.data 有', event)
    if (data.action && data.action == "artifact-process") {
      console.log('data.res', data.res)
      window.removeEventListener("message", receiveMessage, false)
      cb && cb(data.res)
    }
  }else{
    console.log('event.data 没有', event.data)
  }
}

export default iframeLogin
