/*
 * @User: JOJO
 * @FilePath: \yundian-m\src\utils\login\tysso.js
 */

// 和统一认证进行sso，便于和其他接入统一认证的页面进行单点
import iframeLogin from "./iframeLogin"

const channelID = "12118", // 12086 思特奇环境 12093 it开发平台
  testLoginUrl = "https://actest.10086.cn", // 测试登录域名
  prodLoginUrl = "https://login.10086.cn", // 生产登录域名
  isProd = location.host !== "m.staff.ydsc.liuliangjia.cn" // 是否为生产地址

// 在统一认证注入登录信息
export const addUID = artifact => {
  // console.log('使用了adduid',artifact)
  let timeStamp = new Date().valueOf(),
    randomNumber = Math.ceil(Math.random() * Math.pow(10, 10))
  const TransactionID = channelID + timeStamp + randomNumber

  let loginUrl = isProd ? prodLoginUrl : testLoginUrl
  let url = "https://touch.10086.cn/yundian/favicon.ico"
  let backUrl = encodeURIComponent(url)

  let jumpUrl = `${loginUrl}/AddUID.htm?channelID=${channelID}&Artifact=${artifact}&backUrl=${backUrl}&TransactionID=${TransactionID}`

  let img = document.createElement("img")
  img.id = "adduidImg"
  img.setAttribute("style", "display:none")
  img.src = "about:blank"
  document.body.appendChild(img)
  let imgEle = document.querySelector("#adduidImg")
  img.onload = function() {
    document.body.removeChild(imgEle)
  }
  img.setAttribute("src", jumpUrl)
}

// callback的参数为artifact
export const tySSO = callback => {
  let loginUrl = isProd ? prodLoginUrl : testLoginUrl
  let url = `${window.location.origin}${window.location.pathname}`
  let backUrl = encodeURIComponent(url)

  let jumpUrl = `${loginUrl}/SSOCheck.action?channelID=${channelID}&backUrl=${backUrl}`
  iframeLogin(jumpUrl, callback)
}

export default {
  addUID,
  tySSO
}
