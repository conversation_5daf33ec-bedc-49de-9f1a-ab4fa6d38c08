import loginApi from "@/api/login"
import { setLocalStorage } from "./localStorage"
import store from '../../store'
/**
 * @description 企业微信登录-获取code参数
 * @param {String} url 登录成功后跳转的地址,请使用urlencode对链接进行处理
 * @param {String} parame 登录成功后跳转的地址带的参数,企业可以填写a-zA-Z0-9的参数值，长度不可超过128个字节
 */
function getWechatCode(url,state='state'){
  const appid = 'ww1df2e4de743def9a'
  const agentid = process.env.VUE_APP_AGENTID
  const scope = 'snsapi_privateinfo'
  let redirect_uri = encodeURIComponent(url)
  let authorUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${
    appid
  }&redirect_uri=${redirect_uri}&response_type=code&scope=${scope}&state=${state}&agentid=${agentid}#wechat_redirect`
  alert(authorUrl)
  //https://open.weixin.qq.com/connect/oauth2/authorize?appid=ww1df2e4de743def9a
  // &redirect_uri=https%3A%2F%2Ftouch.10086.cn%2Fyundian%2Findex.html%3FshopId%3D17&response_type=code&scope=snsapi_privateinfo&state=ON&agentid=1000069#wechat_redirect
  window.location.href = authorUrl
}

/**
 * @description 企业微信登录
 * @param {String} url 登录成功后跳转的地址,请使用urlencode对链接进行处理
 * @param {String} parame 登录成功后跳转的地址带的参数,企业可以填写a-zA-Z0-9的参数值，长度不可超过128个字节
 */
async function getUserInfoByWechatCode(code, sucCb, failCb,userType,reqsource){
  return new Promise(resolve => {
    loginApi
      .getWxTokenByCode(code)
      .then(async(res = {}) => {
        if (res.code == 0 && res.data && res.data.token) {
          // 成功换取用户信息
          let userInfo = await loginApi.getUserInfo(res.data.token,userType,reqsource).then((res)=>{
            console.log(res)
            if (res.code == 0) {
              return res.data
            }else{
              return null
            }
          })
          store.commit('SET_LOGINFIRST',1)
          store.commit('SET_USERINFO',userInfo)
          store.commit('SET_USERNAME',res.data.UserName)
          localStorage.setItem('userName', res.data.UserName)
          setLocalStorage(res.data)
          sucCb && sucCb(userInfo?userInfo:res.data)
          resolve({
            status: 0, // 0 成功 1 失败
            data: res.data
          })
        } else {
          /**
           * @todo 修改了failcb的入参，之前是err,但是未定义，需要验证
           */
          failCb && failCb({ status: 1 })
          resolve({ status: 1 })
        }
      })
      .catch(err => {
        failCb && failCb(err)
        resolve({ status: 1 })
      })
  })
}
export default {
  getWechatCode,
  getUserInfoByWechatCode
}

