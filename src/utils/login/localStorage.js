let authKey = "yundianToken",
  userKey = "user"

// 获取localStorage
export function getLocalStorage(item) {
  // let store =
  //     (localStorage.getItem(authKey) &&
  //     JSON.parse(localStorage.getItem(authKey))) || {}
  // let auth = store[userKey]

  // let isLogined = auth && auth.UserName && store.token  // 用户已登录

  // return { auth, isLogined }
  return localStorage.getItem(item)
}

export function getToken() {
  return localStorage.getItem(authKey)
}

// 设置locaStorage
export function setLocalStorage(user){
  const token = user.token
  delete user.token
  if(window._tag&&user&&user.wtmobile){
    window._tag.setMobile(user.wtmobile)
  }
  localStorage.setItem(authKey, token)
  localStorage.setItem('wtmobile', user.wtmobile)
}

// 清空localStorage
export function removeLocalStorage() {
  localStorage.removeItem(authKey)
}

export default {
  getLocalStorage,
  setLocalStorage,
  removeLocalStorage,
  getToken
}
