/*
 * @Author: 李亚龙
 * @Description: 公共跳转微信小程序方法
 * @FilePath: \yundian-m\src\utils\wxMiniProgram.js
 */
import UA from "@/utils/ua"
const context = window

/* 判断是否在微信小程序 */
export async function  isWxProgram(callback,fail){
  const isWXMapp = await UA.isWeChatMiniApp()
  if(isWXMapp){
    callback()
  }else{
    fail()
  }
}

/* 跳转首页 */
export const toHome = (shopId,callback)=>{
  if(UA.isWechat){
    isWxProgram(() => {
      let miniProgram = window.wx.miniProgram
      window.wx.miniProgram.switchTab({url: '/pages/home/<USER>'})
      if(shopId){
      // 如果有店铺id,需要把店铺id回传给小程序
        miniProgram.postMessage({ data: {wxtoHomeShopId:shopId} })
      }
    })
    return
  }
}

/* 跳转我的 */
export const toMy = (shopId,callback)=>{
  if(UA.isWechat){
    isWxProgram(() => {
      window.wx.miniProgram.switchTab({url: '/pages/my/my'})
      if(shopId){
        // 如果有店铺id,需要把店铺id回传给小程序
        window.wx.miniProgram.postMessage({ data: {wxtoHomeShopId:shopId} })
      }
    })
    return
  }
}

/* 每日分享 */
export const toShareTaskList = (shopId,callback)=>{
  if(UA.isWechat){
    isWxProgram(() => {
      let miniProgram = window.wx.miniProgram
      window.wx.miniProgram.navigateTo({url: '/subPackages/sharelist/sharelist'})
      if(shopId){
      // 如果有店铺id,需要把店铺id回传给小程序
        miniProgram.postMessage({ data: {wxtoHomeShopId:shopId} })
      }
    },callback)
    return
  }else{
    callback()
  }
}

export default {
  toMy,
  toHome,
  toShareTaskList
}
