/**
 * 优化 try-catch 的错误处理
 * @param {Function} asyncFun 异步函数
 * @param {Object} params
 * @returns [err, res] 返回被捕获异常和成功的结果
 * demo:
    async loginOut(){
        let [err ,res] = await capturedAsync(LoginOut,{mobileLogin:true})
        if(err) return
    }
     export function LoginOut(params){
        return request.get('/a/logout',{params:params})
      }
 */
export const capturedAsync = async(asyncFun, params) => {
  try {
    const res = await asyncFun(params)
    return [null, res]
  } catch (err) {
    return [err, null]
  }
}

/**
 * 是否是空json对象
 * @param obj
 * @returns {boolean}
 */
export function isEmptyObject(obj) {
  return !obj || Object.keys(obj).length === 0
}

/**
 * 判断是否为object对象，排除null
 * @param  {obj}  value 判断的对像
 * @return {Boolean} true/false
 *
 */
function isObject(obj) {
  let type = typeof obj
  return type === "function" || (type === "object" && !!obj)
}

/**
 * 检验url是否合法
 * @param strUrl
 * @returns {boolean}
 */
export function isUrl(strUrl) {
  // ftp的user@
  /* eslint-disable no-useless-escape */
  let strRegex =
    "^((https|http|ftp|rtsp|mms)?://)" +
    "?(([0-9a-z_!~*'().&=+$%-]+: )?[0-9a-z_!~*'().&=+$%-]+@)?" +
    // IP形式的URL- **************
    "(([0-9]{1,3}.){3}[0-9]{1,3}" +
    // 允许IP和DOMAIN（域名）
    "|" +
    // 域名- www.
    "([0-9a-z_!~*'()-]+.)*" +
    // 二级域名
    "([0-9a-z][0-9a-z-]{0,61})?[0-9a-z]." +
    // first level domain- .com or .museum
    "[a-z]{2,6})" +
    // 端口- :80
    "(:[0-9]{1,4})?" +
    // a slash isn't required if there is no file name
    "((/?)|" +
    "(/[0-9a-z_!~*'().;?:@&=+$,%#-]+)+/?)$"
  let re = new RegExp(strRegex)
  return re.test(strUrl)
}

/**
 * 获取连接上面参数
 * @param name
 * @returns {*}
 */
export function getQueryString(name) {
  const url = new URL(location.href)
  if (url.searchParams.has(name)) {
    return url.searchParams.get(name)
  } else {
    return null
  }
}
/**
 * 垂直平滑滚动
 * @param {Number} pos 必传 需要滚动到的位置
 * @param {Function} callback 滚动完成后执行的事件
 */
export function scrollTo(pos, callback) {
  let top = window.scrollY
  smooth()

  // 平滑滚动
  function smooth() {
    top = top + (pos - top) / 4
    // 临界判断，终止动画
    if (Math.abs(top - pos) <= 1) {
      window.scrollTo(0, pos)
      callback && callback()
      return
    }
    window.scrollTo(0, top)
    requestAnimationFrame(smooth)
  }
}
/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
  if (typeof target !== "object") {
    target = {}
  }
  if (Array.isArray(source)) {
    return source.slice()
  }
  Object.keys(source).forEach((property) => {
    const sourceProperty = source[property]
    if (typeof sourceProperty === "object") {
      target[property] = objectMerge(target[property], sourceProperty)
    } else {
      target[property] = sourceProperty
    }
  })
  return target
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result
  const later = function() {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp
    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function(...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }
    return result
  }
}

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string}
 * index:parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}')
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}"
  let date
  if (typeof time === "object") {
    date = time
  } else {
    if (typeof time === "string" && /^[0-9]+$/.test(time)) {
      time = parseInt(time)
    }
    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value]
    }
    if (result.length > 0 && value < 10) {
      value = "0" + value
    }
    return value || 0
  })
  return time_str
}

/**
 * 日期格式化
 * @param value
 * @param format
 * @returns {*}
 * index:parseTime(new Date(), 'yyyy-MM-dd')
 */
export function dateFormat(value, format) {
  if (typeof value === "string") {
    value = value.replace(/-/g, "/")
  }
  var t = new Date(value)
  var o = {
    "M+": t.getMonth() + 1, // month
    "d+": t.getDate(), // day
    "h+": t.getHours(), // hour
    "m+": t.getMinutes(), // minute
    "s+": t.getSeconds(), // second
    "q+": Math.floor((t.getMonth() + 3) / 3), // quarter
    S: t.getMilliseconds(), // millisecond
  }
  if (/(y+)/.test(format)) {
    format = format.replace(
      RegExp.$1,
      (t.getFullYear() + "").substr(4 - RegExp.$1.length)
    )
  }
  for (var k in o) {
    if (new RegExp("(" + k + ")").test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length)
      )
    }
  }
  return format
}

/**
 * 判断时间间隔不超过多少月
 * @param str 待验证字符串
 * @returns {*}
 */
export function checkTime(startTime,endTime,month) {
  if (typeof value === "string") {
    startTime = startTime.replace(/-/g, "/")
  }
  if (typeof value === "string") {
    endTime = endTime.replace(/-/g, "/")
  }
  if(!startTime){
    return false
  }
  if(!endTime){
    return false
  }
  let year1 = new Date(startTime).getFullYear()
  let year2 = new Date(endTime).getFullYear()
  let month1 = new Date(startTime).getMonth()+1
  let month2 = new Date(endTime).getMonth()+1
  let date1 = new Date(startTime).getDate()
  let date2 = new Date(endTime).getDate()
  let yearDifference = year2-year1
  if(yearDifference>0){
    if((yearDifference*12+month2 - month1)>month){
      return true
    }else if((yearDifference*12+month2 - month1)==month){
      return date2>date1
    }else{
      return false
    }
  }else{
    if((month2 - month1)>month){
      return true
    }else if((month2 - month1)==month){
      return date2>date1
    }else{
      return false
    }
  }
}

/**
 * 判断小写字母
 * @param str 待验证字符串
 * @returns {*}
 */
export function validateLowerCase(str) {
  const reg = /^[a-z]+$/
  return reg.test(str)
}

/**
 * 判断字母或数字
 * @param str 待验证字符串
 * @returns {*}
 */
export function validateNumerCase(str) {
  const reg = /^[A-Za-z0-9]+$/
  return reg.test(str)
}

/**
 * 判断手机号
 * @param str 待验证字符串
 * @returns {*}
 */
export function validateMobile(str) {
  const reg = /^1[0-9]{10}$/
  return reg.test(str)
}

/**
 * 判断汉字
 * @param str 待验证字符串
 * @returns {*}
 */
export function validateChinese(str) {
  const reg = /^[\u4e00-\u9fa5]+$/
  return reg.test(str)
}

/**
 * 判断姓名
 * @param str 待验证字符串
 * @returns {*}
 */
export function validateName(str) {
  const reg =
    /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5a-zA-Z·]{0,19}[\u4e00-\u9fa5a-zA-Z]+$/
  return reg.test(str)
}

export function getImgUrl(name, pathname,little) {
  if (!name) return ""
  if (name.indexOf("http") === 0) return name
  let url = ""
  pathname = pathname ? pathname : "goods"
  if (location.origin.indexOf("staff") !== -1) {
    url = `https://img1.staff.ydsc.liuliangjia.cn/`
  } else if (location.origin.indexOf('zz.ydsc')!== -1||location.origin.indexOf('grey.touch')!== -1) {
    url = "https://img1.zz.ydsc.liuliangjia.cn/"
  } else if (location.origin.indexOf("10086.cn") !== -1) {
    url = "https://img1.shop.10086.cn/"
  } else {
    url = "/fs_server/"
  }
  if (name.indexOf("fs_") == 0) {
    url += "fs/"
  }
  if(little){
    url += `${pathname}/${name}_100x100.png`
  }else{
    url += `${pathname}/${name}.png`
  }

  return url
}

// 商城图片
export function getShopImgUrl(name, pathname) {
  if (!name) return ""
  if (name.indexOf("http") === 0) return name
  let url = ""
  pathname = pathname ? pathname : "material"
  if (location.origin.indexOf("staff") !== -1) {
    url = `//img1.staff.ydsc.liuliangjia.cn/`
  } else if (location.origin.indexOf('zz.ydsc')!== -1||location.origin.indexOf('grey.touch')!== -1) {
    url = "//img1.zz.ydsc.liuliangjia.cn/"
  } else if (location.origin.indexOf("grey") !== -1) {
    url = `//img1.grey.ydsc.liuliangjia.cn/`
  } else if (location.origin.indexOf("10086.cn") !== -1) {
    url = "//img1.shop.10086.cn/"
  } else {
    url = "/fs_server/"
  }
  if (name.indexOf("fs_") == 0) {
    url += "fs/"
  }
  url += `${pathname}/${name}.png`
  return url
}

export function getUrl(urlname) {
  return location.origin + urlname
}

/**
 * 链接里边加多个参数
 * @param url 需加参数的链接
 * @param Obj 参数对象
 * @returns {*}
 * index:setSearchParamsArray(url, {'a':1,'b':2})
 */

export function setSearchParamsArray(url, Obj) {
  let seturl = new URL(url)
  Object.keys(Obj).forEach((key) => {
    seturl.searchParams.set(key, Obj[key])
  })
  return seturl.href
}

export function setAcId(url){
  let urln  =  new URL(url),currentUrl = new URL(location.href) , ac_id ,cookies
  cookies = document.cookie.match(/ac_id=([^\s;]+)/i) ?
    document.cookie.match(/ac_id=([^\s;]+)/i)[1] :
    ""
  ac_id =  currentUrl.searchParams.get('WT.ac_id') || cookies || null
  ac_id && urln.searchParams.set('WT.ac_id',ac_id)
  return urln.href
}

/**
 * 获取cookie里的值
 * @param cookieName 需获取的key
 * @returns String
 * index:getCookie("ac_id")
 */
export function getCookie(cookieName){
  let reg = new RegExp(cookieName+"=([^\s;]+)","i")
  let cookies = document.cookie.match(reg) ? document.cookie.match(reg)[1] : ""
  return cookies
}

/**
 * 修改cookie里的值
 * @param name 需修改的key
 * @returns String
 * index:editCookie("ac_id","yundian")
 */
export function editCookie(name,cval,expires,path="/"){
  document.cookie = name + "=" + cval + ";path="+path+";expires=" + expires
}

/**
 * 删除cookie里的值
 * @param name 需删除的key
 * @returns String
 * index:delCookie("ac_id")
 */
export function delCookie(name){
  editCookie(name,"")
  var exp = new Date()
  exp.setTime(exp.getTime() - 1000000)
  var cval = getCookie(name)
  if (cval != null) document.cookie = name + "="+null+";expires=" + exp.toGMTString()
}
/**
 * 获取链接里边所有参数
 * @param url 所有参数
 * @returns {*}
 * index:getSearchParamsArray(url) //从链接url里获取参数
 */
export function getSearchParams(url){
  let urln  =  new URL(url)
  return urln.search
}

/**
 * 获取链接里边多个指定参数
 * @param url 需加参数的链接
 * @param Obj 参数数组
 * @returns {*}
 * index:getSearchParamsArray(url, ['a','b']) //从链接url里获取a和b的参数
 */

export function getSearchParamsArray(url, searchKey) {
  let urlData = new URL(url),
    searchObject = {}
  if (searchKey) {
    searchKey.forEach((item) => {
      if (urlData.searchParams.has(item)) {
        searchObject[item] = urlData.searchParams.get(item)
      }
    })
  }
  return searchObject
}

/**
 * 获取链接里边全部参数
 * @param url 需加参数的链接
 * @returns {*}
 * index:getAllSearchParamsArray(url) //从链接url里获取全部参数
 */

export function getAllSearchParamsArray(url) {
  if(!url) return {}
  let urlData = new URL(url),
    searchObject = {}
  for (var obj of urlData.searchParams.entries()) {
    searchObject[obj[0]] = obj[1]
  }
  return searchObject
}
// 设置title
export function setTitle(actTitle) {
  document.title = actTitle
  var iframe = document.createElement("iframe")
  iframe.style.visibility = "hidden"
  iframe.style.width = "1px"
  iframe.style.height = "1px"
  iframe.onload = function() {
    setTimeout(function() {
      document.body.removeChild(iframe)
    }, 0)
  }
  document.body.appendChild(iframe)
}

/* 分 转 元 保留两位小数*/
export const regFenToYuan = (fen) => {
  let toDecimal2 = function(x) {
    let f = parseFloat(x)
    if (isNaN(f)) {
      return false
    }
    f = Math.round(x * 100) / 100
    let s = f.toString()
    let rs = s.indexOf(".")
    if (rs < 0) {
      rs = s.length
      s += "."
    }
    while (s.length <= rs + 2) {
      s += "0"
    }
    return s
  }
  fen = fen * 0.01
  fen += ""
  let reg =
    fen.indexOf(".") > -1
      ? /(\d{1,3})(?=(?:\d{3})+\.)/g
      : /(\d{1,3})(?=(?:\d{3})+$)/g
  fen = fen.replace(reg, "$1")
  fen = toDecimal2(fen)
  return fen
}

/* 分转元 (自动处理单位为空,则转为元,反之) */
export const regFenToYuan2 = (val, unit, no) => {
  if (unit && !no) {
    return val + unit
  } else if (no) {
    return regFenToYuan(val)
  } else {
    return regFenToYuan(val) + "元"
  }
}

/* 分转元 (自动处理单位为空,则转为元,反之) */
export const regFenToYuan3 = (val, unit) => {
  if (unit) {
    return regFenToYuan(val) + unit
  } else {
    return regFenToYuan(val) + "元"
  }
}

/* 对象是否为空 */
export function isObjectEmpty(obj) {
  if (obj === null) {
    return true
  }
  for (var key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      return false
    }
  }
  return true
}

/* 数组是否为空 */
export function isArrayEmpty(arr) {
  return Array.isArray(arr) && arr.length === 0 || !arr
}

/* 加载外部js文件 */
export function loadJs() {
  let script = document.createElement('script')
  script.type = "text/javascript"
  script.onload = ()=> {
    setTimeout(()=> { arguments[1] && arguments[1]('ok')},100)
  }
  script.onerror = () => {
    setTimeout(()=> { arguments[1] && arguments[1]('error')},100)
  }
  script.src = arguments[0]
  document.body.appendChild(script)
}
/* 加密手机号 */
export function getPassPhone(mobile){
  if (!mobile) return ""
  let reg = /^(.{3}).*(.{4})$/
  return mobile.replace(reg, "$1****$2")
}


function pre_fix_integer(num, n) {
  return (Array(n).join(0) + num).slice(-n)
}
/**
 * 解密手机号
 * @param str 需解密的手机号
 * @returns {*}
 * index:decode_mobile("49991-54608-5471-262") //从getuserinfo的wtmobile字段
 */
export function decode_mobile(str) {
  var key = "abcdef"
  str = str.split("-")
  var m3 = pre_fix_integer(Number(str[0] ^ key).toString(16), 4)
  var m4 = pre_fix_integer(Number(str[1] ^ key).toString(16), 4)
  var m1 = pre_fix_integer(Number(str[2] ^ key).toString(16), 4)
  var m2 = pre_fix_integer(Number(str[3] ^ key).toString(16), 4)
  var m5 = m1 + m2 + m3 + m4
  return (
    m5.substring(0, 2) +
    m5.substring(4, 7) +
    m5.substring(9, 12) +
    m5.substring(13, 17)
  )
}
/* 生成新的数组或对象 */
export function copy(data){
  if(Array.isArray(data)){
    return [].concat(data)
  }else{
    return Object.assign({},data)
  }

}

export function setScrollTop(){
  let scrollTop =
    document.documentElement.scrollTop ||
    window.pageYOffset ||
    document.body.scrollTop
  if (scrollTop == 0) {
    window.scrollTo(0, 1)
  }
}

export default {
  dateFormat,
  parseTime,
  checkTime,
  validateMobile,
  validateNumerCase,
  validateChinese,
  regFenToYuan,
  regFenToYuan2,
  regFenToYuan3,
  getShopImgUrl,
  loadJs,
  getPassPhone,
  copy,
  setScrollTop,
  getCookie,
  editCookie,
  delCookie
}
