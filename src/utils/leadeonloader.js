
import { loadJs } from '@/utils/utils'

let status = 0

function leadeonLoader() {
  return new Promise(resResolve => {
    if(window.leadeon) {
      resResolve(window.leadeon)
    }else{
      if(status == 0) {
        status = 1
        loadJs("//img0.shop.10086.cn/thirdpart/leadeon/leadeon.js__0.js")
      }
      var timer = setInterval(()=> {
        if(window.leadeon) {
          clearInterval(timer)
          resResolve(window.leadeon)
        }
      },300)
    }
  })
}

export default leadeonLoader
