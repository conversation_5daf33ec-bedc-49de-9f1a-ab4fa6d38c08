/**
 * 查找某项，扩展了能查找对象等
 * @param item
 * @returns {number}
 */
Array.prototype.findItem = function(item) {
  // 不存在的下标
  let index = -1
  this.forEach((_item, _index) => {
    if (JSON.stringify(item) === JSON.stringify(_item)) {
      index = _index
    }
  })
  return index
}
/**
 * 通过下标删除
 * @param item
 */
Array.prototype.remove = function(item) {
  let index = this.findItem(item)
  if (index > -1) {
    this.splice(index, 1)
  } else {
    return this
  }
}

// 在数组上扩展一个去重的方法
Array.prototype.unique = function(arg) {
  const res = new Map()
  return this.filter(m => {
    if (arg) {
      return !res.has(m[arg]) && res.set(m[arg], 1)
    } else {
      return !res.has(m) && res.set(m, 1)
    }
  })
}
