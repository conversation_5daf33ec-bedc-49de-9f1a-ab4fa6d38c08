import { loadJs } from '@/utils/utils'
import {ENV} from "@/utils/env.js"

let status = 0
let _aspsc = null
function aspscLoader() {
  return new Promise(resolve => {
    if(window['asp-smart-collection']) {
      resolve(window['asp-smart-collection'])
    }else{
      let $resolve = resolve
      if(status == 0) {
        status = 1
        loadJs(`https://${ENV.getAspscDomain()}/thirdpart/aspsc/aspsc.v1.2.1.js`)
      }
      var timer = setInterval(()=> {
        if(window['asp-smart-collection']) {
          clearInterval(timer)
          $resolve(window['asp-smart-collection'])
        }
      },300)
    }
  })
}
/**
 * @method
 * @description 添加商城插码
 * @param {Object} params
 * @param {string} params.uniChannelId -String 64 是 全网19位渠道编码
 * @param {string} params.shopId -String 256 是 店铺id
*/
async function init(params={}){
  if (_aspsc) {
    return _aspsc
  }else {
    await aspsc<PERSON>oader().then(AspSmartCollection=> {
      _aspsc = AspSmartCollection
      AspSmartCollection.Track.init({
        url: `https://${ENV.getAspscDomain()}/aspsc/log.gif`,
        isTrackOnly: true,
        sendType:'beacon',
        debug: false,
        // visitOverTimeDuration: 60 * 1000
      })
    })
    return _aspsc
  }
}
async function setOtherInfo(params={},pageshow = false){
  const AspSmartCollection = await init()
  AspSmartCollection.Track.aspSetOtherInfo(params)
  if(pageshow) {
    AspSmartCollection.Track.pageExposure()
  }
  // if(params.msisdn){
  //   AspSmartCollection.Track.changeUserInfo()
  // }
}


export default {
  aspscLoader,
  init,
  setOtherInfo
}
