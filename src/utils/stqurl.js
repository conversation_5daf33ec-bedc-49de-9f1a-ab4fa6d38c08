/*
 * @Author: 李亚龙
 * @Date: 2021-10-19 18:24:49
 * @LastEditTime: 2021-11-25 14:54:17
 * @LastEditors: your name
 * @Description:
 * @FilePath: \yundian-m\src\utils\stqurl.js
 */
import VueCookies from 'vue-cookies'
import {setSearchParamsArray} from '@/utils/utils'
let stqurl = {
  packremainqry:"https://touch.10086.cn/i/mobile/packremainqry.html",//套餐余量
  busiqrydeal:"https://touch.10086.cn/i/mobile/busiqrydeal.html",//已订业务
  ordersList:"https://touch.10086.cn/i/mobile/ordersList.html",//我的订单现网
  returnorderqry:"https://touch.10086.cn/i/gray/mobile/returnorderqry.html",//退换货现网
}

if((location.origin.indexOf('grey')!== -1&&location.origin.indexOf('3m.grey')=== -1)||location.origin.indexOf('staff')!== -1){
  //测试环境
  stqurl.ordersList = "https://touch.10086.cn/i/gray/mobile/ordersList.html"
  stqurl.returnorderqry = "https://touch.10086.cn/i/gray/mobile/returnorderqry.html"
}else{
  stqurl.ordersList = "https://touch.10086.cn/i/mobile/ordersList.html"
  stqurl.returnorderqry = "https://touch.10086.cn/i/mobile/returnorderqry.html"
}

//支持小程序支付，增加小程序标识
let url = new URL(location.href)
let wxInfo= url.searchParams.get('wxInfo') || VueCookies.get("wxInfo")
if (wxInfo) {
  Object.keys(stqurl).forEach(function(key){
    stqurl[key] = setSearchParamsArray(stqurl[key],{'wxInfo':wxInfo})
  })
}
export default stqurl
