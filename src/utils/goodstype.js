// 云店订单-商品类型数据列表
export const goodsTypeList = [
  { text: "普通号码", value: 20000 },
  { text: "套餐", value: 30000 },
  { text: "资费套餐", value: 30100 },
  { text: "4G自选套餐", value: 30200 },
  { text: "4G套餐", value: 30300 },
  { text: "5G套餐", value: 30400 },
  { text: "增值业务", value: 40000 },
  { text: "增值业务-流量套餐", value: 40100 },
  { text: "增值业务-数据业务", value: 40200 },
  { text: "增值业务-优惠语音包", value: 40300 },
  { text: "增值业务-积分兑换商品", value: 40400 },
  { text: "增值业务-家庭网业务", value: 40500 },
  { text: "增值业务-语音业务", value: 40600 },
  { text: "增值业务-权益领取业务", value: 40700 },
  { text: "话费直充", value:  40001 },
  { text: "流量直充", value: 40002 },
  { text: "配件", value: 50000 },
  { text: "号卡", value: 60000 },
  { text: "终端", value: 70000 },
  { text: "宽带新装商品", value: 90000 },
  { text: "全网宽带新装预约", value: 90010 },
  { text: "跨省宽带", value: 90020 },
  { text: "全网宽带提速", value: 90030 },
]

export const goodsTypeObj = {
  "20000": "普通号码",
  "30000": "套餐",
  "30100": "资费套餐",
  "30200": "4G自选套餐",
  "30300": "4G套餐",
  "30400": "5G套餐",
  "40000": "增值业务",
  "40001": "话费直充",
  "40002": "流量直充",
  "40100": "增值业务-流量套餐",
  "40200": "增值业务-数据业务",
  "40300": "增值业务-优惠语音包",
  "40400": "增值业务-积分兑换商品",
  "40500": "增值业务-家庭网业务",
  "40600": "增值业务-语音业务",
  "40700": "增值业务-权益领取业务",
  "50000": "配件",
  "60000": "号卡",
  "70000": "终端",
  "90000": "宽带新装商品",
  "90010": "全网宽带新装预约",
  "90020": "跨省宽带",
  "90030": "全网宽带提速"
}

export default {
  goodsTypeList,
  goodsTypeObj
}