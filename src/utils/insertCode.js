/*
 * @User: JOJO
 * @FilePath: \yundian-m\src\utils\insertCode.js
 */
// 插码
import { getQueryString } from "./utils"
import UA from "@/utils/ua"
import store from "../store"
import UnifiedAuthenticationMigration from "@/model/unifiedAuthenticationMigration"

const uaStr = UA.isApp ? "DN" : "DW"
/**
 *
 * @param {String} code
 * @param {String} url
 */
export default function insertCode(code, url = null) {
  let channelid = getQueryString("channelId"),
    pageid = getQueryString("pageId"),
    shopid = getQueryString("shopId")
  let argsa = ["DCS.dcsuri", "/nopv.gif"]
  if (pageid) {
    argsa.push("WT.pageid", pageid)
  }
  if (channelid) {
    argsa.push("WT.channelid", channelid)
  }
  if (shopid) {
    argsa.push("WT.shopid", shopid)
  }
  // console.log(store.getters)
  if (store.getters.pageInfo && store.getters.pageInfo.pageInfo) {
    argsa.push("WT.tplid", store.getters.pageInfo.pageInfo.tplId)
    argsa.push("WT.actid", store.getters.pageInfo.pageInfo.actId)
  }

  if(typeof code === 'string'){
    argsa.push("WT.event", code)
  }else if(typeof code === 'object' && code !== null){
    Object.keys(code).forEach(item => {
      if(code[item]){
        argsa.push(item, code[item])
      }
    })
  }

  // window.Webtrends.multiTrack({ argsa })

  if (url) {
    // 云店跳转别人页面的链接有token的逻辑
    UnifiedAuthenticationMigration.init(url).then((url) => {
      setTimeout(function() {
        window.open(url, "_self")
      }, 350)
    })
  }
}
/**
 *
 * @param {String[]} codes
 * @param {String?} url
 */
export function insertCodeWithUA(codes, url = null) {
  let code = ""
  if (Array.isArray(codes)) {
    try {
      if (UA.isApp) {
        code = codes[0]
      } else {
        code = codes[1]
      }
    } catch (e) {
      throw new Error("插码配置错误")
    }
  } else {
    code = codes.toString()
  }
  if (url) {
    insertCode(code, url)
  } else {
    insertCode(code)
  }
}
/**
 *
 * @param {String[]} codes
 * @param {String} url
 * @param {String|Number} province
 */
export function insertMultiCodeWithUA(codes, url = null, province = "") {
  codes.forEach((code, index) => {
    const event = code.replace("{UA}", uaStr).replace("{PROV}", province)
    if (url && index == codes.length - 1) {
      insertCode(event, url)
    } else {
      insertCode(event)
    }
  })
}
