/**
 * @var {object}
 * @desc 变量定义
 * @property {string}singlePrice 1 价格类型是一口价
 * @property {string} rangePrice 2 价格类型是区间价
 * @property {string} discountPrice 3 价格类型是折扣价
 */
export const typeList = {
  "singlePrice":1,//价格类型是一口价
  "rangePrice":2,//价格类型是区间价
  "discountPrice":3//价格类型是折扣价
}

/**
 * @var {object}
 * @desc 变量定义
 * @property {string}singlePrice 1 价格类型是一口价
 * @property {string} rangePrice 2 价格类型是区间价
 * @property {string} discountPrice 3 价格类型是折扣价
 */
export const typeAllList = {
  "singlePrice":1,//价格类型是一口价
  "rangePrice":2,//价格类型是区间价
  "discountPrice":3//价格类型是折扣价
}
export default {
  typeList,
  typeAllList
}