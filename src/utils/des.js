import CryptoJS from "crypto-js"

export function desDeclassified(data){
  const keyHex = CryptoJS.enc.Utf8.parse("")
  const decrypted = CryptoJS.DES.decrypt(
    {
      ciphertext: CryptoJS.enc.Base64.parse(
        data
      ),
    },
    keyHex,
    {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    }
  )

  let ms,ios
  try {
    ms = decrypted.toString(CryptoJS.enc.Utf8)
  } catch (error) {
    // console.log(error)
  }

  if(ms && ms.split){
    ios = ms.split('Mobile=')[1]
  }else{
    ios = '空'
  }

  return ios
}

export default {
  desDeclassified
}