import "nprogress/nprogress.css"
import NProgress from "nprogress"
import router from "@/router"
// 简单配置
NProgress.inc(0.2)
NProgress.configure({ easing: "ease", speed: 500, showSpinner: false })
router.beforeEach((to, from, next) => {
  // 动态设置meta
  let _appTitle = "demo"
  if (to.meta.title) {
    _appTitle = to.meta.title
  }

  document.title = _appTitle
  NProgress.start()
  next()
})

router.afterEach(() => {
  NProgress.done()
})
