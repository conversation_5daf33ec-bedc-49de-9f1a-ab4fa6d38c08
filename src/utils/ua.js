const userAgent = navigator.userAgent

export default {
  isWechat: !!userAgent.match(/MicroMessenger/gi), // 是否微信内
  isWechatWork: !!userAgent.match(/wxwork/gi), // 是否企业微信内
  isApp: !!userAgent.match(/leadeon/gi), // 是否app内
  isMobile: !!userAgent.match(/mobile/gi), // 是否移动端
  isApple: !!userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), // 是否ios / mac 系统
  isAndroid:
    userAgent.indexOf("Android") > -1 || userAgent.indexOf("Linux") > -1, // 是否android
  isIos : !!userAgent.toLocaleLowerCase().match(/\(i[^;]+;( u;)? cpu.+mac os x/),//是否ios
  isIosQQ :
    (!!userAgent.toLocaleLowerCase().match(/\(i[^;]+;( u;)? cpu.+mac os x/)) && / QQ/i.test(navigator.userAgent),//是否QQ内
  isAndroidQQ :
    (userAgent.indexOf("Android") > -1 || userAgent.indexOf("Linux") > -1)
    && /MQQBrowser/i.test(navigator.userAgent) && /QQ/i.test((navigator.userAgent).split('MQQBrowser')),//是否QQ内
  isBaiduMapp:
    /swan\//.test(window.navigator.userAgent) ||
    /^webswan-/.test(window.name) ,
  isYDBG:  //是否在移动办公内
    /WorkbenchType_1\//i.test(window.navigator.userAgent),
  isWeChatMiniApp() {
    const ua = window.navigator.userAgent.toLowerCase()
    return new Promise(resolve => {
      if (ua.indexOf("micromessenger") === -1 || ua.indexOf("miniprogram") === -1) {
        resolve(false)
      } else {
        let flag = false
        window.wx.miniProgram.getEnv(res => {
          flag = true
          if (res.miniprogram) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
        setTimeout(()=> {
          if(flag == false) {
            resolve(false)
          }
        },2000)
      }
    })
  }
}


