{"formList": [{"label": "商铺名称：", "classify": "basic", "type": "input", "columnName": "storeName", "defaultValue": "", "isModel": true, "class": "", "required": true, "requiredCutomizeTips": "", "rules": [{"required": true, "message": "商铺名称不能为空", "trigger": "blur"}], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "text": "", "wordLimitStyle": "transparent"}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": "", "operations": [{"status": ["info"], "attr": "label", "formatter": ""}, {"status": ["edit"], "attr": "clearData", "formatter": ""}]}, {"label": "优惠资源名称：", "classify": "basic", "type": "input", "columnName": "couponName", "defaultValue": "", "isModel": true, "class": "", "required": true, "requiredCutomizeTips": "", "rules": [{"required": true, "message": "商铺简称不能为空", "trigger": "blur"}], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent"}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "operations": [{"status": ["info"], "attr": "label", "formatter": ""}, {"status": ["edit", "create"], "attr": "clearable", "formatter": ""}]}, {"label": "优惠资源推荐语：", "classify": "basic", "type": "input", "columnName": "couponPropaganda", "defaultValue": "", "isModel": true, "class": "", "required": true, "requiredCutomizeTips": "", "rules": [{"required": true, "message": "商铺地址不能为空", "trigger": "blur"}], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent"}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "operations": [{"status": ["info"], "attr": "label", "formatter": ""}, {"status": ["create", "edit"], "attr": "clearable", "formatter": ""}]}, {"label": "优惠资源图片：", "type": "imageGroup", "columnName": "imgUrl", "defaultValue": "", "classify": "basic", "isModel": true, "icon-dev": "el-icon-picture", "isLabelWidth": false, "operation": [], "required": true, "requiredCutomizeTips": "", "rules": [{"required": true, "message": "优惠资源图片不能为空", "trigger": "blur"}], "span": 24, "width": "100%", "hiddenPlus": false, "hidden": false, "style": {"height": "auto"}, "props": {"alt": "", "fit": "fill", "preview-src-list": "", "text": "", "wordLimitStyle": "transparent"}, "imgGroupProps": {"attachUploadSwitch": false, "attachTipSwitch": false, "isStringOfValue": false, "prefix": "", "proxy": "", "attachType": "", "isEffect": false, "localDelete": false, "count": "1", "size": "9", "type": "jpg;jpeg;png", "unitType": "G", "apiName": "upload/imageThumbUpload", "apiType": "", "apiParam": "", "picPoxy": true, "showFileLoading": false, "uploadFileName": "file", "customizeResponse": false, "responseKey": "", "responseValue": "", "resonseUrlKey": "", "verifySize": false, "verifyPictureWidth": "", "verifyPictureHeight": "", "hideUploadBtn": true}, "isCompare": true, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": "", "copyOldVal": [], "operations": [{"status": ["info"], "attr": "label", "formatter": ""}, {"status": ["edit"], "attr": "clearable", "formatter": ""}]}, {"label": "分类：", "type": "select", "isLabelWidth": false, "classify": "basic", "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "required": true, "requiredCutomizeTips": "", "rules": [{"required": true, "message": "分类不能为空", "trigger": "blur"}], "columnName": "couponType", "targetName": "label_select_1657962720005", "defaultValue": "", "defaultContent": "", "isModel": true, "operation": [], "isCheckAll": false, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "hidden": false, "props": {"placeholder": "", "multiple": false, "clearable": true, "disabled": false, "readonly": false, "filterable": false, "remote": false, "wordLimitStyle": "transparent", "text": ""}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ","}, "options": [{"label": "周边精选", "value": "0"}, {"label": "云店特惠", "value": "1"}], "allItemSwitch": false, "span": 24, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "showOptions": [{"label": "周边精选", "value": "0", "disabled": false}, {"label": "云店特惠", "value": "1", "disabled": false}], "labelWidth": 160, "filterOptionStatus": [], "disabledOptionStatus": [], "copyNewVal": "", "operations": [{"status": ["info"], "attr": "label", "formatter": ""}, {"status": ["edit"], "attr": "clearable", "formatter": ""}], "isHelpTipText": ""}, {"label": "券码：", "classify": "basic", "type": "input", "columnName": "couponCode", "defaultValue": "", "isModel": true, "class": "", "required": true, "requiredCutomizeTips": "", "rules": [{"required": true, "message": "联系人不能为空", "trigger": "blur"}], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "text": "", "wordLimitStyle": "transparent"}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": "", "operations": [{"status": ["info"], "attr": "label", "formatter": ""}, {"status": ["create", "edit"], "attr": "clearable", "formatter": ""}]}, {"label": "生效日期：", "type": "datePicker", "classify": "basic", "required": true, "requiredCutomizeTips": "", "rules": [{"required": true, "message": "生效日期不能为空", "trigger": "blur"}], "columnName": "effectiveTimeStart", "defaultValue": "", "isModel": true, "icon-dev": "iconfont iconriqi", "icon": "", "isLabelWidth": false, "operation": [], "span": 24, "hidden": false, "aspProps": {"dateRange": 0}, "props": {"type": "date", "placeholder": "", "format": "yyyy-MM-dd", "value-format": "yyyy-MM-dd", "range-separator": "-", "start-placeholder": "", "end-placeholder": "", "disabled": false, "readonly": false, "picker-options": {}, "wordLimitStyle": "transparent"}, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "defaultSwitch": false, "name": "basic", "labelWidth": 160, "copyNewVal": "", "copyOldVal": "17:16:23", "operations": [{"status": ["info"], "attr": "label", "formatter": ""}, {"status": ["create", "edit"], "attr": "clearable", "formatter": ""}]}, {"label": "失效日期：", "type": "datePicker", "classify": "basic", "required": true, "requiredCutomizeTips": "", "rules": [{"required": true, "message": "失效日期不能为空", "trigger": "blur"}], "columnName": "effectiveTimeEnd", "defaultValue": "", "isModel": true, "icon-dev": "iconfont iconriqi", "icon": "", "isLabelWidth": false, "operation": [], "span": 24, "hidden": false, "aspProps": {"dateRange": 0}, "props": {"type": "date", "placeholder": "", "format": "yyyy-MM-dd", "value-format": "yyyy-MM-dd", "range-separator": "-", "start-placeholder": "", "end-placeholder": "", "disabled": false, "readonly": false, "picker-options": {}, "wordLimitStyle": "transparent"}, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "defaultSwitch": false, "name": "basic", "labelWidth": 160, "copyNewVal": "", "operations": [{"status": ["info"], "attr": "label", "formatter": ""}, {"status": ["create", "edit"], "attr": "clearable", "formatter": ""}]}, {"label": "总库存量：", "classify": "basic", "type": "input", "columnName": "stockSize", "defaultValue": "", "isModel": true, "class": "", "required": true, "requiredCutomizeTips": "", "rules": [{"required": true, "message": "联系电话不能为空", "trigger": "blur"}], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "text": "", "wordLimitStyle": "transparent"}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": "", "operations": [{"status": ["info"], "attr": "label", "formatter": ""}, {"status": ["create", "edit"], "attr": "clearable", "formatter": ""}], "numberTypeRequired": true}, {"label": "核销量：", "classify": "basic", "type": "input", "columnName": "writeOffSize", "defaultValue": "", "isModel": true, "class": "", "required": true, "requiredCutomizeTips": "", "rules": [{"required": true, "message": "核销量不能为空", "trigger": "blur"}], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent"}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "numberTypeRequired": true, "operations": [{"status": ["info"], "attr": "label", "formatter": ""}, {"status": ["create", "edit"], "attr": "clearable", "formatter": ""}]}, {"label": "领取方式：", "type": "select", "isLabelWidth": false, "classify": "basic", "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "required": true, "requiredCutomizeTips": "", "rules": [{"required": true, "message": "领取方式不能为空", "trigger": "blur"}], "columnName": "receiveType", "targetName": "label_select_1657963140945", "defaultValue": "0", "defaultContent": "常规领取", "isModel": true, "operation": [], "isCheckAll": false, "option-label": "label", "option-value": "value", "option-alias": "alias", "option-disabled": "disabled", "searchProps": {"need": "0", "apiName": "", "isShowInput": false, "labelAlias": "", "remotePropName": "condition"}, "hidden": false, "props": {"placeholder": "", "multiple": false, "clearable": true, "disabled": false, "readonly": false, "filterable": false, "remote": false, "wordLimitStyle": "transparent", "text": "0"}, "optionProps": {"optionType": "0", "sessionKey": "", "dicKey": "", "apiName": "", "apiType": "", "apiParam": "", "separator": ","}, "options": [{"label": "常规领取", "value": "0"}, {"label": "跳转链接", "value": "1"}, {"label": "填写表单", "value": "2"}], "allItemSwitch": false, "span": 24, "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "name": "basic", "copyNewVal": "0", "showOptions": [{"label": "常规领取", "value": "0", "disabled": false}, {"label": "跳转链接", "value": "1", "disabled": false}, {"label": "填写表单", "value": "2", "disabled": false}], "labelWidth": 160, "filterOptionStatus": [], "disabledOptionStatus": [], "operations": [{"status": ["info"], "attr": "label", "formatter": ""}, {"status": ["create", "edit"], "attr": "clearable", "formatter": ""}], "isHelpTipText": "", "isAutoFillData": false, "autoFillListprop": [{"autoFillProp": "outerLink", "selectProp": ""}, {"autoFillProp": "", "selectProp": ""}], "isMutexSwitch": false, "mutexShowRetion": "hidden", "copyOldVal": "", "statusList": [], "authSwitch": false, "dynamic": {}}, {"label": "", "classify": "basic", "type": "input", "columnName": "outerLink", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": false, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "请在此处填写跳转链接", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 160, "isHelpTipText": "", "authSwitch": true, "dynamic": {"single_mainform_list": [{"key": 1658129820281, "source": {"label": "outerLink", "columnName": "outerLink", "props": {"placeholder": "请在此处填写跳转链接", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "text": ""}, "name": "basic", "type": "input"}, "target": [{"label": "领取方式： : receiveType", "columnName": "receiveType", "targetName": "label_select_1657963140945", "props": {"placeholder": "", "multiple": false, "clearable": true, "disabled": false, "readonly": false, "filterable": false, "remote": false, "wordLimitStyle": "transparent", "text": "0"}, "name": "basic", "type": "select"}], "condition": [{"columnName": "outerLink", "condition": "=", "valueType": "value", "compareValueType": "string", "columnValue": "1||3", "type": ["hidden"], "result": true, "value": "", "status": ["edit", "create", "info"], "numberSign": ">=", "numberValue": 1}], "targetSwitch": true}]}}, {"label": "栅格布局", "type": "row", "columnName": "row_1658050769015", "icon-dev": "iconfont <PERSON>on-editor-grid", "operation": [], "hidden": false, "classify": "layout", "rowTypeFlex": true, "formFields": [{"span": 4, "childList": []}, {"span": 20, "childList": [{"label": "填写项1：", "classify": "basic", "type": "input", "columnName": "term1", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": true, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "text": ""}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 110, "isHelpTipText": "", "dynamic": {}}, {"label": "填写项2：", "classify": "basic", "type": "input", "columnName": "term2", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": true, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent"}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 110}, {"label": "领取注意事项：", "classify": "basic", "type": "input", "columnName": "attention", "defaultValue": "", "isModel": true, "class": "", "required": false, "requiredCutomizeTips": "", "rules": [], "label-width": "", "isLabelWidth": true, "icon-dev": "iconfont icon<PERSON><PERSON><PERSON>", "icon": "", "hidden": false, "props": {"placeholder": "", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent"}, "style": {"word-break": "break-all"}, "operation": [], "button": false, "slot": "", "button-name": "", "span": 24, "aspInputLableLayoutFlag": false, "aspInputLableLayoutBlock": false, "aspInputLableLayoutwidth": "50%", "width": "100%", "isCompare": true, "isDisabledRequired": false, "isReadonlyRequired": false, "isNumberType": false, "name": "basic", "copyNewVal": "", "labelWidth": 110}, {"label": "添加logo：", "type": "imageGroup", "columnName": "logoImg", "defaultValue": "", "classify": "basic", "isModel": true, "icon-dev": "el-icon-picture", "isLabelWidth": true, "operation": [], "required": false, "requiredCutomizeTips": "", "rules": [], "span": 24, "width": "100%", "hiddenPlus": false, "hidden": false, "style": {"height": "auto"}, "props": {"alt": "", "fit": "fill", "preview-src-list": "", "wordLimitStyle": "transparent", "text": ""}, "imgGroupProps": {"attachUploadSwitch": false, "attachTipSwitch": false, "isStringOfValue": false, "prefix": "", "proxy": "", "attachType": "", "isEffect": false, "localDelete": false, "count": "1", "size": "9", "type": "jpg;jpeg;png", "unitType": "M", "apiName": "upload/imageThumbUpload", "apiType": "", "apiParam": "", "picPoxy": false, "showFileLoading": false, "uploadFileName": "", "customizeResponse": false, "responseKey": "", "responseValue": "", "resonseUrlKey": "", "verifySize": false, "verifyPictureWidth": "", "verifyPictureHeight": "", "hideUploadBtn": true}, "isCompare": true, "name": "basic", "copyNewVal": "", "labelWidth": 110, "isHelpTipText": ""}]}], "name": "layout", "labelWidth": 160, "dynamic": {"single_mainform_list": [{"key": 1658129991961, "source": {"label": "栅格布局:row_1658050769015", "columnName": "row_1658050769015", "name": "layout", "type": "row"}, "target": [{"label": "领取方式： : receiveType", "columnName": "receiveType", "targetName": "label_select_1657963140945", "props": {"placeholder": "", "multiple": false, "clearable": true, "disabled": false, "readonly": false, "filterable": false, "remote": false, "wordLimitStyle": "transparent", "text": "0"}, "name": "basic", "type": "select"}], "condition": [{"columnName": "row_1658050769015", "condition": "=", "valueType": "value", "compareValueType": "string", "columnValue": "1||2", "type": ["hidden"], "result": true, "value": "", "status": [], "numberSign": ">=", "numberValue": 1}], "targetSwitch": true}]}}, {"label": "按钮组", "type": "buttonGroup", "isLabelWidth": false, "classify": "layout", "columnName": "buttonGroup_1657018791994", "icon-dev": "iconfont <PERSON><PERSON><PERSON>", "span": 24, "hidden": false, "bpmSwitchFlag": false, "bpmNodesButtonActiveList": [], "align": null, "position": "center", "operation": [], "toolList": [{"columnName": "submit", "type": "primary", "icon": "contactNumber", "label": "保存", "interactive": "button_custom_submit_part_validate", "validateProp": ["storeName", "couponName", "couponPropaganda", "imgUrl", "couponType", "couponCode", "effectiveTimeStart", "effectiveTimeEnd", "stockSize", "writeOffSize", "receiveType"], "apiName": "/cooperateShop/edit/resource", "class": "", "default": "show", "apiCloseDialog": false, "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "apiCloseDialogWithResposne": true, "confirmStatus": [], "activeType": "button_group_form_interface", "parentPageOpenFlag": false, "routerPageOpenFlag": false, "apiParam": {"couponType": "$couponType$", "receiveType": "$receiveType$", "attention": "$attention$", "couponCode": "$couponCode$", "couponName": "$couponName$", "couponPropaganda": "$couponPropaganda$", "effectiveTimeStart": "$effectiveTimeStart$", "id": "$id$", "imgUrl": "$imgUrl$", "imgUrlNew": "$imgUrlNew$", "logoImg": "$logoImg$", "imgUrlOld": "$imgUrlOld$", "imgUrlNewOld": "$imgUrlNewOld$", "logoImgOld": "$logoImgOld$", "outerLink": "$outerLink$", "storeId": "$storeId$", "stockSize": "$stockSize$", "term1": "$term1$", "term2": "$term2$", "writeOffSize": "$writeOffSize$"}, "apiMethod": "post+json", "apiIsRefresh": "", "apiIsReturn": "", "actionWithPrePageList": [], "info": "hidden", "create": "show", "edit": "show"}, {"is-table-column": false, "columnName": "edit", "label": "修改", "type": "primary", "class": "", "icon": "", "bpmFlowImageCloumnName": "", "default": "show", "authSwitch": false, "authId": "", "confirmationSwitch": false, "confirmationMessage": "", "interactive": "", "validateProp": [], "apiMethod": "", "apiName": "", "apiCloseDialogWithResposne": true, "parentPageOpenFlag": false, "routerPageOpenFlag": false, "info": "show", "create": "hidden", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "confirmStatus": [], "activeType": "button_group_other", "apiParam": "", "apiIsRefresh": 1, "apiIsReturn": "", "edit": "hidden", "apiCloseDialog": false}, {"is-table-column": false, "columnName": "cacel", "label": "取消", "type": "primary", "class": "", "icon": "", "bpmFlowImageCloumnName": "", "default": "show", "authSwitch": false, "authId": "", "confirmationSwitch": false, "confirmationMessage": "", "interactive": "", "validateProp": "", "apiMethod": "", "apiName": "", "apiCloseDialogWithResposne": false, "parentPageOpenFlag": false, "routerPageOpenFlag": false, "info": "show", "create": "show", "dialogConfig": {"bindName": "", "title": "", "subTitle": "", "titleClass": "dialog-title-default", "width": "", "height": "", "toolList": []}, "confirmStatus": [], "activeType": "button_group_close_dialog", "edit": "show", "apiCloseDialog": false}], "name": "layout", "labelWidth": 160, "hiddenListStatus": ["info"], "operations": [{"status": ["info"], "show": false}, {"status": ["create"], "show": true}], "copyOldVal": ""}], "formConfig": {"labelWidth": 160, "labelPosition": "right", "leftStatusList": [], "rightStatusList": [], "topStatusList": [], "statusLabelPosition": "", "starPostion": "left", "class": "", "exportName": "resourceform", "defaultClass": "webbas", "size": "small", "statusList": ["info", "create", "edit"], "serverProps": {"localProxy": "", "nigxProxy": "", "statusKey": "status", "statusValue": "200", "dataKey": "data", "requestDataKey": ""}, "bpmProps": {"bpmOpenSwitchFlag": false, "initPrefix": "/web/wf", "actionPrefix": "/web/business", "proxy": "", "bpmFlowKey": "bpm<PERSON><PERSON><PERSON>ey", "instanceId": "", "taskId": "", "list": [{"flowKey": "bpm<PERSON><PERSON><PERSON>ey", "flowKeyName": "", "flowActionList": []}]}, "compareStatusList": [], "compareDescript": "变更前：", "isOpenHelpTipSwitch": false, "helpTipProps": {"helpTipPostion": "right", "helpTipPageId": "", "helpTipSessionKey": "pageHelpConfig", "helpTipIcon": "el-icon-question", "helpTipPlacement": "right", "helpTipWidth": ""}, "titleName": "资源管理"}, "dataConfig": {"data_dy_single_list": [{"source": {"dataType": "mainForm", "columnName": "receiveType"}, "target": [{"label": "outerLink", "columnName": "outerLink", "props": {"placeholder": "请在此处填写跳转链接", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "text": ""}, "name": "basic", "type": "input"}, {"label": "栅格布局 : row_1658050769015", "columnName": "row_1658050769015", "name": "layout", "type": "row"}], "condition": []}], "single_option_picker": [{"source": {"dataType": "mainForm", "columnName": "receiveType"}, "target": [{"label": "outerLink", "columnName": "outerLink", "props": {"placeholder": "请在此处填写跳转链接", "maxlength": "", "show-word-limit": false, "clearable": true, "disabled": false, "readonly": false, "wordLimitStyle": "transparent", "text": ""}, "name": "basic", "type": "input"}, {"label": "栅格布局 : row_1658050769015", "columnName": "row_1658050769015", "name": "layout", "type": "row"}], "condition": []}]}, "virtual_model": {}, "model": {"storeName": "", "couponName": "", "couponPropaganda": "", "imgUrl": "", "couponType": "", "label_select_1657962720005": "", "couponCode": "", "effectiveTimeStart": "", "effectiveTimeEnd": "", "stockSize": "", "writeOffSize": "", "receiveType": "0", "label_select_1657963140945": "常规领取", "outerLink": "", "term1": "", "term2": "", "attention": "", "logoImg": ""}, "dynamic": {}}