@mixin ellipsis($clamp:1){
    overflow: hidden;
    text-overflow: ellipsis;
    @if($clamp==1){
        white-space: nowrap;
    }@else{
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: $clamp;
        -webkit-box-orient: vertical;
    }
}

@mixin flex($x:flex-start,$y:flex-start,$direction:row,$wrap:nowrap){
    display: -webkit-box;
    display: box;
    display: flex;
    align-items: $y;
    justify-content: $x;
    flex-direction: $direction;
    flex-wrap:$wrap;
}
