@import "./function";

.nearBy{
    .shop-list{
        // background: #f3f3f3;
        padding-top:10px;
    }
    .nearbyitem{
        box-shadow: 0px 1px 1px 0px rgba(0,0,0,0.11);
        margin:0 10px 10px;
        // height: 135px;
        background: #ffffff;
        border: 1px solid #f0f0f0;
        border-radius: 8px;
        padding-left:0!important;
        padding-right:0!important;
        position: relative;
        .itemLeft{
            margin-left:13px;
        }
        .itemRight{
            height:60px;
            width:80px;
            float: right;
            margin-right:13px;
            overflow: hidden;
            img{
                height:100%;
            }
        }
        &:only-child{
            margin:0 5px;
        }
        .title {
            font-size: 16px;
            color: #333;
            font-weight: bold;
            line-height: 18px;
        }
        .address {
            width: 240px!important;
            font-size: 12px;
            padding:0!important;
            height:30px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .businesscontent{
            width: 266px;
            height: 20px;
            opacity: 0.8;
            font-size: 10px;
            font-weight: 400;
            text-align: left;
            color: #000000;
            .arrow-down{
                transform: translate(0,2px) rotate(-90deg);
            }
            img{
                height: 15px;
                vertical-align: sub;
            }
        }
        .btnGroup{
            width:100%;
            margin-top:10px;
            border-top:1px solid #f4e9dc;
            height:35px;
            display: flex;
            .btnItem{
                height: 25px;
                font-size: 15px;
                font-weight: 400;
                text-align: left;
                color: #f7454b;
                width:174px;
                img{
                    width:24px;
                    height:24px;
                    vertical-align: bottom;
                    margin-right:5px;
                }
                &:nth-child(2){
                    border-left:1px solid #f4e9dc;
                    color:#1A9BFC;
                }
                &:only-child{
                    color:#1A9BFC;
                    width:100%;
                }
                text-align: center;
                margin-top: 10px;
            }
        }
    }
    .distance,.icon_adress{
        color:#333;
    }
    .icon_adress{
        width:19px!important;
        height:19px!important;
        display: inline-block;
        transform:translate(-2px, 5px);
    }
    .addressName{
        font-size: 11px;
        font-weight: 400;
        text-align: left;
        color: #333;
        line-height: 16px;
    }
    .btnInfo{
        color: white;
        background: linear-gradient(to right, rgb(96, 199, 255), rgb(131, 116, 254));
        border: 0px;
        border-radius: 1000px;
        font-size: 11px;
        width:82px;
    }
    .btnPlain{
        width: 82px;
        float: right;
        // border:1px solid;
        // // border-image: linear-gradient(90deg, #5fc8ff 1%, #8373fe);
        padding-left:12px;
        .van-icon{
            transform: translate(-4px,2px);
        }
    }

}
.appnearBy {
    .nearbyitem{
        .address {
            width: 196px!important;
            .icon_adress{
                transform: translate(-4px,4.875px);
            }
        }
        .itemRight{
            width:120px;
            height: 90px;
            position: absolute;
            right: 0!important;
            img{
                height:100%;
            }
        }
        .itemLeft{
            width:196px!important;
            margin-left:0!important;
        }
    }
    &.index{
        .shop-list li{
            padding-top:20px!important;
        }
        .shop-message .itemLeft{
            margin-left:5px!important;
        }
    }
    .businesscontent{
        img{
            transform: translate(-6px, 0);
        }
    }
}
