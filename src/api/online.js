import request from "@/utils/request"
/**
 *  req.reqsource -请求用户信息缓存id,从管理页过来默认是5
*/
const reqsource = 5
/**
 * 校验资质状态接口
 * @method
 * @description 校验资质状态接口
 * @param {Object} req
 * @param {string} req.shopId  店铺id
*/
export function getOnlineCheck({
  shopId
}) {
  return request.post("qualification/check", {
    shopId
  },{headers:{reqsource:reqsource}})
}
/**
 * 提交审核接口
 * @method
 * @description 提交审核接口
 * @param {Object} req
 * @param {integer} req.type  提交类型,1:提交审核,2:下架
*/
export function getOnlineAudit({
  type
}) {
  return request.post("qualification/commitAudit", {
    type
  },{headers:{reqsource:reqsource}})
}
/**
 * 店铺资质信息详情接口
 * @method
 * @description 获取店铺资质信息详情
 * @param {Object} req
 * @param {string} req.shopId  店铺id
*/
export function getOnlineDetail({
  shopId
}) {
  return request.post("qualification/detail", {
    shopId
  },{headers:{reqsource:reqsource}})
}

/**
 * 店铺修改在附近店铺里展示图片接口
 * @method
 * @description 获取店铺修改在附近店铺里展示图片
 * @param {Object} req
 * @param {string} req.shopId  店铺id
 * @param {string} req.shopPicture  图片id
 * @param {string} req.position  图片位置
*/
export function updateNearbyImg({
  shopId,shopPicture,position
}) {
  return request.post("qualification/nearbySave", {
    shopId,shopPicture,position
  },{headers:{reqsource:reqsource}})
}

/**
 * 获取在附近店铺里展示图片接口
 * @method
 * @description 获取在附近店铺里展示图片信息
 * @param {Object} req
 * @param {string} req.shopId  店铺id
*/
export function getNearbyImg({
  shopId
}) {
  return request.post("qualification/nearbyShow", {
    shopId
  },{headers:{reqsource:reqsource}})
}

/**
 * 店铺资质图片上传
 * @method
 * @description 店铺资质图片上传
 * @param {Object} req
 * @param {string} req.file  图片
 * @param {string} req.watermarkText  水印
*/
export function getQualificationImage({
  file,watermarkText
}) {
  return request.post("upload/qualificationImage", {
    file,watermarkText
  },{headers:{reqsource:reqsource}})
}
/**
 * 保存店铺资质信息
 * @method
 * @description 保存店铺资质信息
 * @param {Object} req
 * @param {string} req.shopFace  店铺外观图
 * @param {string} req.shopInner  店铺内景图
 * @param {string} req.shopLicense  营业执照图
*/
export function getOnlineSave({
  shopFace,shopInner,shopLicense
}) {
  return request.post("qualification/save", {
    shopFace,shopInner,shopLicense
  },{headers:{reqsource:reqsource}})
}

export default {
  getOnlineCheck,
  getOnlineAudit,
  getOnlineDetail,
  getQualificationImage,
  getOnlineSave,
  updateNearbyImg,
  getNearbyImg
}
