import request from "@/utils/request"
/**
 *  req.reqsource -请求用户信息缓存id,从管理页过来默认是5
*/
const reqsource = 5
/**
 * 获取店铺IOP产品列表
 * @method
 * @description 获取店铺IOP产品列表
 * @param {Object} req
 * @param {string} req.shopId -店铺ID
 * @param {string} req.productName -产品名称
 * @param {string} req.categoryId -分类编码
 * @param {string} req.onlineTime -上线时间
 * @param {string} req.offlineTime -下线时间
 * @param {string} req.phone -手机号
*/
export function getProductList({
  shopId,categoryId,productName,onlineTime,offlineTime,pageNo,pageSize,phone
}) {
  return request.post("/iopProduct/getProductList", {
    shopId,categoryId,productName,onlineTime,offlineTime,pageNo,pageSize,phone
  },
  {
    headers:{reqsource:reqsource},
    // needEncrypt:1
  }
  )
}

/**
 * 获取店铺IOP产品详情
 * @method
 * @description 获取店铺IOP产品详情
 * @param {Object} req
 * @param {string} req.id -产品id
*/
export function getProductDetail({
  id
}) {
  return request.post("/iopProduct/getProductDetail", {
    id
  },
  {
    headers:{reqsource:reqsource},
    // needEncrypt:1
  }
  )
}

/**
 * @method
 * @description 获取店铺IOP产品的用户(粉丝)数据
 * @param {Object} req
 * @param {string} req.shopId -店铺ID
 * @param {string} req.id -产品数据主键
*/
export function getFansList({
  shopId,id,pageNo,pageSize,isContact,phone
}) {
  return request.post("/iopProduct/getFansList", {
    shopId,id,pageNo,pageSize,isContact,phone
  },
  {
    headers:{reqsource:reqsource},
    needEncrypt:1
  }
  )
}

/**
 * 获取店铺拥有IOP商品的粉丝数据
 * @method
 * @description 获取店铺拥有IOP商品的粉丝数据
 * @param {Object} req
 * @param {string} req.shopId -店铺ID
 *
*/
export function getMemberList({
  pageNo,pageSize,shopId
}) {
  return request.post("/iopProduct/getMemberList", {
    pageNo,pageSize,shopId
  },
  {
    headers:{reqsource:reqsource},
    needEncrypt:1
  }
  )
}

/**
 * 反馈数据提交
 * @method
 * @description 反馈数据提交
 * @param {Object} req
 * @param {string} req.shopId -店铺ID
 * @param {string} req.phone -粉丝手机号
 * @param {string} req.provinceId -省ID
 * @param {string} req.activityId -活动ID
 * @param {string} req.productName -活动名称
 * @param {string} req.productId -产品ID
 * @param {string} req.status -店铺ID
 * @param {string} req.feedback -店铺ID
 *
 * {
  "phone": "13522088367",
  "provinceId": "100",
  "activityId": "huodongtest001",
  "productId": "chanpingtest001",
  "productName": "流量假期包-20元",
  "shopId": 10000001,
  "status": 1,
  "feedback": "反馈信息",
}
*/
export function fansFeedbackAdd({
  feedback,status,shopId,productId,productName,activityId,provinceId,phone
}) {
  return request.post("/fansFeedback/add", {
    feedback,status,shopId,productId,productName,activityId,provinceId,phone
  },
  {
    headers:{reqsource:reqsource},
    needEncrypt:1
  }
  )
}

/**
 * 获取店铺IOP产品详情--面向用户
 * @method
 * @description 校验用户是否是该店铺的适用用户
 * @param {Object} req
 * @param {string} req.shopId -店铺ID
 * @param {string} req.id -产品数据主键
 * @param {string} req.phone -需校验的用户
 *
*/
export function checkUserInFansList({
  id,phone,shopId
}) {
  return request.post("/iopProduct/checkUserInFansList", {
    id,phone,shopId
  },
  {
    headers:{reqsource:1},
    // needEncrypt:1
  }
  )
}

/**
 * @method
 * @description 推送粉丝活动开始提醒订阅消息
 * @param {Object} req
 * @param {string} req.shopId -店铺ID
 * @param {string} req.id -产品数据主键(产品主键ID)
*/
export function getFansSubscribeTo({
  shopId,id,phone
}) {
  return request.post("/subscriptionUser/activity/push", {
    shopId,id,phone
  },
  {
    headers:{reqsource:reqsource},
    needEncrypt:1
  }
  )
}

export default {
  getProductList,
  getProductDetail,
  getFansList,
  checkUserInFansList,
  getMemberList,
  fansFeedbackAdd,
  getFansSubscribeTo
}
