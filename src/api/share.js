import request from "@/utils/request"
/**
 *企微分享调用接口
 * @method
 * @description 企微分享查询企微信息
 * @param {Object} req
 * @param {string} req.code -微信code
 * @param {string} req.url -省id
*/
export function getSign({
  code,
  url
}) {
  return request({
    // location.origin+
    url:"/busi/qw/getJsSdkSign",
    method: "post",
    data: { code, url },
  })
}

export function getAgentJsSdk({corpId,code,url}) {
  return request({
    url:"/busi/qw/getAgentJsSdk",
    method: "post",
    data: {corpId,code,url}
  })
}

export function getShortUrl({url}) {
  return request({
    url:"/shortUrl/convert",
    method: "get",
    params: {url}
  })
}

export default {
  getSign,
  getAgentJsSdk,
  getShortUrl
}
