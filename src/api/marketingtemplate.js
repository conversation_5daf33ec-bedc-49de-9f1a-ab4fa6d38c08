import request from "@/utils/request"
/**
 * 获取营销模板列表信息
 * @method
 * @description 营销模板列表信息
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
*/
export function getMarketingTemplate({
  shopId
}) {
  return request.post("/marketing/template/getMarketingTemplate", {
    shopId
  })
}
/**
 * 获取模板支撑商品列表信息
 * @method
 * @description 商品列表信息
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 * @param {string} req.templateId - 模板ID
*/
export function getMarketingTemplateGoods({
  shopId,templateId
}) {
  return request.post("/marketing/template/getMarketingTemplateGoods", {
    shopId,templateId
  })
}
/**
 * 生成推广页ID
 * @method
 * @description 新增记录
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 * @param {string} req.templateId - 模板ID
 * @param {Array} req.goodsIdList - 商品ID
*/
export function getAddRecord({
  shopId,templateId,goodsIdList
}) {
  return request.post("/marketing/template/addRecord", {
    shopId,templateId,goodsIdList
  })
}
/**
 * 获取模板配置详情信息
 * @method
 * @description 模板配置详情信息
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 * @param {string} req.recordId - 生成ID（带商品模板标识）
*/
export function getMarketingPageInfo({
  shopId,recordId
}) {
  return request.post("/marketing/template/getMarketingPageInfo", {
    shopId,recordId
  },{ needEncrypt:1 })
}
export default {
  getMarketingTemplate,
  getMarketingTemplateGoods,
  getAddRecord,
  getMarketingPageInfo
}
