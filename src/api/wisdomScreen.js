/*
 * @Author: 李亚龙
 * @FilePath: \yundian-m\src\api\wisdomScreen.js
 */
import request from "@/utils/request"

/**
 * 查询智慧大屏店铺分类（菜单）
 * @method
 * @description
 * @param {string} req.shopId  店铺id
 */
export function queryMenus({ shopId }) {
  return request.post("/busi/bgs/queryMenus", {
    shopId,
  })
}

/**
 * 查询店铺广告列表接口
 * @method
 * @description
 * @param {string} req.shopId  店铺id
 */
export function busiBgsQueryShopAds({ shopId, createRole }) {
  return request.post("/busi/bgs/queryShopAds", {
    shopId,
    createRole
  })
}

/**
 * 查询大屏商品
 * @method
 * @description
 * @param {string} req.shopId  店铺id
 */
export function queryGoods({ shopId, pageNum, pageSize, catalogId, createRole }) {
  return request.post("/busi/bgs/queryGoods", {
    shopId,
    pageNum,
    pageSize,
    catalogId,
    createRole
  })
}

/**
 * 大屏商品详情查询接口
 * @method
 * @description
 * @param {string} req.screenGoodsId  店铺大屏商品id（非商品id）
 * @param {string} req.shopId  店铺id
 */
export function goodsDetail({ shopId, screenGoodsId, goodsType,  createRole }) {
  return request.post("/busi/bgs/goodsDetail", {
    shopId,
    screenGoodsId,
    goodsType,
    createRole
  })
}
/**
 * 大屏商品详情查询接口2
 * @method
 * @description
 * @param {string} req.screenId  店铺大屏商品id（非商品id）
 * @param {string} req.shopId  店铺id
 */
export function goodsDetail2({ shopId, screenGoodsId }) {
  return request.post("/bigScreen/goods/detail", {
    shopId,
    screenId: screenGoodsId,
  })
}

/**
 * @description: 查询店铺大屏状态接口
 * @param {*} shopId
 * @return {*}
 */
export function queryBigScreenStatus({ shopId }) {
  return request.post("/busi/bgs/queryBigScreenStatus", {
    shopId,
  })
}

/**
 * @description: 查询店铺大屏横屏
 * @param {*} shopId
 * @return {*}
 */
export function shoponlinegetDisplay({ shopId }) {
  return request.post("/shoponline/getDisplay", {
    shopId,
  })
}

/**
 * @description: 前端获取智慧大屏横竖屏设置
 * @param {*} shopId
 * @return {*}
 */
export function fetchDisplay({ shopId, createRole }) {
  return request.post("/busi/bgs/fetchDisplay", {
    shopId,
    createRole
  })
}

export default {
  queryMenus,
  busiBgsQueryShopAds,
  queryGoods,
  goodsDetail,
  queryBigScreenStatus,
  shoponlinegetDisplay,
  goodsDetail2,
  fetchDisplay
}
