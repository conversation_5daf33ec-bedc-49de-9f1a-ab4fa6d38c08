import request from "@/utils/request"
/**
 *  req.reqsource -请求用户信息缓存id,从管理页过来默认是5
*/
const reqsource = 5

/**
 * 云店预约单列表
 * @method
 * @description 云店预约单列表
 * @param {Object} req
 * @param {string} req.shopId -店铺ID
 * @param {string} req.status -意向单状态
 * @param {string} req.customerPhone -意向电话号码
 * @param {string} req.orderId -意向单号
 * @param {string} req.customerName -意向用户姓名
 * @param {string} req.createTimeStart -下单时间查询范围起始
 * @param {string} req.createTimeEnd -下单时间查询范围结束
 * @param {string} req.staffId -店员工号
*/
export function intentOrderList({
  shopId,status,customerPhone,customerName,orderId,createTimeStart,createTimeEnd,pageNo,pageSize,coopType
}) {
  let sendData = {
    shopId,status,customerPhone,customerName,orderId,createTimeStart,createTimeEnd,pageNo,pageSize,coopType
  }
  if(!sendData.createTimeStart){
    delete sendData.createTimeStart
  }
  if(!sendData.createTimeEnd){
    delete sendData.createTimeEnd
  }
  if(!sendData.status){
    delete sendData.status
  }
  return request.post("/intentOrder/intentOrderList", sendData ,{headers:{reqsource:reqsource}, needEncrypt: 1 })
}

/**
 * 云店预约单详情
 * @method
 * @description 云店预约单详情
 * @param {Object} req
 * @param {string} req.orderId -预约单号
 * @param {string} req.shopId -店铺ID
*/
export function intentOrderDetail({
  orderId,shopId
}) {
  return request.post("/intentOrder/intentOrderDetail", {
    orderId,shopId
  },{headers:{reqsource:reqsource}, needEncrypt: 1 })
}

/**
 * 云店订单处理接口
 * @method
 * @description 获取宽带预约模板详情
 * @param {integer} req.reqsource - 是否是从工作台来的页面是的话传5
 * @param {Object} req.data
 * @param {integer?} req.data.orderId - 订单id
 * @param {integer?} req.data.imei - 终端串号
 * @param {integer} req.data.cannelReason - 甩单取消理由
 * @param {integer} req.data.status - 订单状态
*/
export function updateOrderState({orderId,imei,cannelReason,status,shopId,staffId,staffPhon}) {
  return request.post("/intentOrder/updateOrderState",{orderId,imei,cannelReason,status,shopId,staffId,staffPhon},{headers:{reqsource:reqsource}, needEncrypt: 1 })
}

export default {
  intentOrderList,
  updateOrderState,
  intentOrderDetail
}
