/*
 * @User: JOJO
 * @FilePath: \yundian-m\src\api\ydsc.js
 */
import ua from "@/utils/ua"
const context = window

/**
 * 频接ios函数名
 * @method
 * @description 频接ios函数名，添加ydsc前缀
 * @param {string} actionName  客户端能力方法名
 * @returns {String}
*/
function getIosFuncName(actionName) {
  return 'ydsc'+actionName[0].toUpperCase()+actionName.substr(1)
}

/**
 * 客户端原生能力调用桥
 * @method
 * @description
 * @param {string} actionName  客户端能力方法名
 * @param {{timeout:Number,callback:String,...}} data  客户端能力参数 ，必传项为 timeout 超时时间，callback 回调函数名
 * @returns {void}
*/
function execAction(actionName,data) {
  try{
    if(ua.isIos) {//ios调用方法
      let actionNameIos = getIosFuncName(actionName)
      let mes = window.webkit.messageHandlers[actionNameIos]
      mes.postMessage(JSON.stringify(data))
    }else if(window.ydsc){//安卓调用方法
      window.ydsc[actionName](JSON.stringify(data))
    }else{
      window[data.callback]({
        code: 999,
        message: '获取信息失败',
        messageInternal: '桥函数不存在'
      })
    }
  }catch(e){
    window[data.callback]({
      code: 999,
      message: '获取信息失败',
      messageInternal: e
    })
  }
}
/**
 * 检查api是否存在
 * @method
 * @description 检查api是否存在
 * @param {string} [actionName]  客户端能力方法名
 * @returns {boolean}
*/
export function checkApi(actionName = 'getAppLocationInfo' ){
  return !!((window.ydsc && window.ydsc[actionName]) || (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers[getIosFuncName(actionName)]))
}
/**
 * 获取地理位置信息
 * @method
 * @description 获取地理位置信息
 * @returns {Promise<{code:Number,message:String,data:GeolocationCoordinates}>}
*/
export function getAppLocationInfo() {
  const timeout = 10000
  return new Promise(resolve => {
    let funcName = "callback_getlocation"+ new Date().valueOf()
    window[funcName] = function(data){
      resolve(JSON.parse(data))
      delete window[funcName]
    }
    //如果超时未返回，手动删除方法
    setTimeout(()=>{
      window[funcName] && window[funcName]({
        code: 998,
        message: '获取信息超时，请稍后再试'
      })
    },timeout)
    let param = { timeout, callback: funcName }
    execAction('getAppLocationInfo',param)
  })
}
/**
 * @method
 * @description 图片保存方法调用
 * @param {Object} param 对象，入参
 * @param {String} param.type  父元素（data）图片传参方式：1表示url方式，2表示base64方式
 * @param {String} param.image 父元素（data）根据type值不同，data为url链接或者base64数据
 * @param {String} param.photoName 父元素（data）手机号（只有android使用，用于图片命名）
     JSON格式为：{"type:":1,"image": 'http://xxx.a.png', "photoName":"myposter.png"，"success":successfn,"error":errorfn}
* @returns {Promise<{code:Number,message:String,data:{url:String}}>}
*/

export function savePhoto(param) {
  const timeout = param.timeout || 10000
  return new Promise(resolve => {
    let funcName = "callback_saveImg"+ new Date().valueOf()
    window[funcName] = function(data){
      let res 
      if (typeof data == 'string'){
        res = JSON.parse(data)
      }else{
        res = data
      }
      if(res.code == 0) {
        param.success && param.success(res)
      }else{
        param.error && param.error(res)
      }
      resolve(res)
      delete window[funcName]
    }
    //如果超时未返回，手动删除方法
    setTimeout(()=>{
      window[funcName] && window[funcName]({
        code: 998,
        message: '获取信息超时，请稍后再试'
      })
    },timeout)
    let paramAction = { timeout, callback: funcName , data:param}
    execAction("saveImg",paramAction)
  })
}
export default {
  checkApi,
  getAppLocationInfo,
  savePhoto
}
