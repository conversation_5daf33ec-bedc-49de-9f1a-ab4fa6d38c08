import request from "@/utils/request"
import ua from '@/utils/ua'
/**
 * //店铺状态查询(没有缓存)
 * @method
 * @description 店铺状态查询(没有缓存)
 * @param {Object} req
 * @param {string[]} req.shopId - 店铺id
 * @param {channelId}  渠道代码 小程序:1000032,H5:1000033 精准营销办套餐 ?
*/
export async function getQueryshopidandstatusNc({
  shopId,
  channelId
}) {
  channelId = channelId?channelId:'1000033'
  let res = await ua.isWeChatMiniApp()
  if(res){
    channelId = '1000032'
  }
  return request.post("/shoponline/queryshopidandstatusNc", {
    shopId
  },{headers:{reqsource:5}})
}
/**
 * @method
 * @description 获取店铺管理模块配置信息
 * @param {Object} req
 * @param {string[]} req.shopId - 店铺id
*/
export function getShopIconConfig({
  shopId
}) {
  return request.post("/iconConfig/getShopIconConfig", {
    shopId
  },{headers:{reqsource:5}})
}

/**
 * 店铺详情
 * @method
 * @description 店铺详情
 * @param {Object} req
 * @param {string[]} req.shopId - 店铺id
*/
export function getShopdetailFromO2o({
  shopId
}) {
  return request.post("/shoponline/shopdetailFromO2o", {
    shopId
  },{headers:{reqsource:5},needEncrypt:1})
}


/**
 * 粉丝数据详情列表
 * @method
 * @description 粉丝数据详情列表
 * @param {Object} req
 * @param {string[]} req.shopId - 店铺id
 * @param {string[]} req.pageNum - 默认1 ,
 * @param {string[]} req.pageSize - 默认10
*/
export function getShopFansTotal({
  shopId,pageNum,pageSize
}) {
  return request.post("/shopFansTotal/list", {
    shopId,pageNum,pageSize
  },{headers:{reqsource:5}})
}
/**
 * 检查粉丝IOP是否有数据
 * @method
 * @description 检查粉丝IOP是否有数据
 * @param {Object} req
 * @param {string[]} req.shopId - 店铺id
*/
export function gerCheckHaveProductList({
  shopId
}) {
  return request.post("/iopProduct/checkHaveProductList", {
    shopId
  },{headers:{reqsource:5}})
}
/**
 * 检查粉丝IOP是否有数据
 * @method
 * @description 检查粉丝IOP是否有数据
 * @param {Object} req
 * @param {string[]} req.shopId - 店铺id
*/
export function getQrCode({
  shopId
}) {
  return request.post("/shopFansTotal/getQrCode", {
    shopId
  },{headers:{reqsource:5}})
}
/**
 * @method
 * @description 海报弹窗详情
 * @param {Object} req
 * @param {string[]} req.shopId - 店铺id
*/
export function getShopPosterRuleInfo({
  shopId
}) {
  return request.post("/poster/rule/info", {
    shopId
  },{headers:{reqsource:5},needEncrypt:1})
}
export default {
  getQueryshopidandstatusNc,
  getShopdetailFromO2o,
  getShopFansTotal,
  gerCheckHaveProductList,
  getQrCode,
  getShopIconConfig,
  getShopPosterRuleInfo
}
