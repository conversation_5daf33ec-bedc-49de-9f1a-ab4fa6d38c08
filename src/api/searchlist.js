import request from "@/utils/request"
/**
 * 店主获取搜索商品列表数据
 * @method
 * @description 店主获取搜索商品列表数据
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 * @param {string?} req.keyWord - 搜索内容
 * @param {string?} req.minPrice - 最低价格
 * @param {string?} req.maxPrice - 最高价格
 * @param {string?} req.sort- 排序，1综合排序，2价格正序。3价格倒叙
*/
export function getSearchList({
  shopId,
  keyWord,
  minPrice,
  maxPrice,
  sort
}) {
  return request.post("/search/searchGoods", {
    shopId,
    keyWord,
    minPrice,
    maxPrice,
    sort
  })
}

export default {
  getSearchList
}