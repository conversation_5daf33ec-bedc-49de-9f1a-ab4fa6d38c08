import request from "@/utils/request"
/**
 * 获取优惠购机合约信息接口
 * @method
 * @description 获取优惠购机合约信息接口
 * @param {Object} req
 * @param {string[]} req.provinceGoodsId - 省商品编码
*/
export function queryByProGoodsCode({
  provinceGoodsId,type,shopId
}) {
  return request.post('transmit/contract/queryByProGoodsCode',
    {provinceGoodsId,type,shopId})
}

/**
 * 老用户合约机购买资格校验接口
 * @method
 * @description 老用户合约机购买资格校验接口
 * @param {Object} req
 * @param {string[]} req.contractId - 合约档位id
 * @param {string[]} req.serialNo - 流水号
*/
export function purchaseQuliCheck({
  contractId,serialNo,shopId,checkType
}) {
  return request.post('transmit/contract/purchaseQuliCheck',
    {contractId,serialNo,shopId,checkType})
}

/**
 * 5G金币购机-立即购买
 * @method
 * @description 5G金币购机-立即购买
 * @param {Object} req
 * @param {string[]} req.contractId - 合约档位id
 * @param {string[]} req.serialNo - 流水号
*/
export function purchase({
  contractId,serialNo
}) {
  return request.post('transmit/contract/purchase',
    {contractId,serialNo})
}

export default {
  queryByProGoodsCode,
  purchaseQuliCheck,
  purchase
}
