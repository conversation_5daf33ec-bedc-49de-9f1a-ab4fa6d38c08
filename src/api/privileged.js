import request from "@/utils/request"

/**
 *  req.reqsource -请求用户信息缓存id,从管理页过来默认是5
*/
const reqsource = 5
/**
 * 获取特权活动列表数据
 * @method
 * @description 获取特权活动列表数据
 * @param {Object} req
 * @param {string?} req.pageNum - 页数
 * @param {string?} req.pageSize - 每页多少条
*/
export function getSpecialActivityList({
  pageNum,
  pageSize
}) {
  return request.post("/specialAct/getSpecialActivityList", {
    pageNum,
    pageSize
  },{headers:{reqsource:reqsource}})
}

/**
 * 获取特权活动详情数据
 * @method
 * @description 获取特权活动详情数据
 * @param {Object} req
 * @param {string?} req.activityId - 活动id
*/
export function getSpecialActivityDetail({
  activityId
}) {
  return request.post("/specialAct/getSpecialActivityDetail", {
    activityId
  },{headers:{reqsource:reqsource}})
}

/**
 * 特权活动表单提交
 * @method
 * @description 特权活动表单提交
 * @param {Object} req
 * @param {string?} req.activityId - 特权活动id
 * @param {string?} req.name - 姓名
 * @param {string?} req.receiveNumber - 手机号
 *  @param {string?} req.serialNumber - 终端串号
*/
export function getSpecialActivityInsert({
  activityId,
  name,
  receiveNumber,
  serialNumber
}) {
  return request.post("/specialActRec/insert", {
    activityId,
    name,
    receiveNumber,
    serialNumber
  },{headers:{reqsource:reqsource}})
}

/**
 * 获取商品带有推荐人（当前登录人）的分享链接
 * @method
 * @description 获取商品带有推荐人的分享链接
 * @param {Object} req
 * @param {string?} req.goodsId - 商品id
 * @param {string?} req.goodsType - 商品类型
 * @param {string?} req.skuId - 
*/
export function getSharingGoodsLink({
  goodsId,
  goodsType,
  skuId
}) {
  return request.post("/specialAct/getSharingGoodsLink", {
    goodsId,
    goodsType,
    skuId
  },{headers:{reqsource:reqsource}})
}

export default {
  getSpecialActivityList,
  getSpecialActivityInsert,
  getSharingGoodsLink,
  getSpecialActivityDetail
}