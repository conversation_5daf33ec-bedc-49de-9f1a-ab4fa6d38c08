import request from "@/utils/request"
/**
 *  req.reqsource -请求用户信息缓存id,从管理页过来默认是5
*/
const reqsource = 5
/**
 * 直播活动列表
 * @method
 * @description 直播活动列表
 * @param {Object} req
 * @param {integer?} req.pageNum - 分页
 * @param {integer?} req.pageSize - 分页
 * @param {integer} req.type - 类型
*/
export function getBroadList(params) {
  return request.post("/shoplive/list",params,{headers:{reqsource:reqsource}})
}

/**
 * 删除直播
 * @method
 * @description 直播活动删除
 * @param {Object} req
 * @param {integer} req.liveId - 直播id
*/
export function deleteBroad(params) {
  return request.post("/shoplive/delete",params,{headers:{reqsource:reqsource}})
}
/**
 * 获取直播详情
 * @method
 * @description 直播活动详情
 * @param {Object} req
 * @param {integer} req.liveId - 直播id
*/
export function getBroadDetail(params) {
  return request.post("/shoplive/detail",params,{headers:{reqsource:reqsource}})
}

/**
 * 新建直播
 * @method
 * @description 创建直播活动
 * @param {Object} req
 * @param {string[]?} req.goodsId - 商品
 * @param {string} req.startTime - 开始时间
 * @param {string} req.endTime - 结束时间
 * @param {string?} req.icon - 图片
 * @param {string?} req.intro - 介绍
 * @param {integer} req.position - 位置
 * @param {string} req.title - 名称
 * @param {integer} req.type - 类型
 * @param {string} req.url - 地址
*/
export function createBroad(params) {
  return request.post("/shoplive/create",params,{headers:{reqsource:reqsource}})
}
/**
 * 更新直播
 * @method
 * @description 更新直播
 * @param {Object} req
 * @param {string[]?} req.goodsId - 商品
 * @param {string?} req.startTime - 开始时间
 * @param {string?} req.endTime - 结束时间
 * @param {string?} req.icon - 图片
 * @param {string?} req.intro - 介绍
 * @param {integer?} req.position - 位置
 * @param {string?} req.title - 名称
 * @param {integer?} req.type - 类型
 * @param {string?} req.url - 地址
 * @param {integer} req.liveId - 直播id
*/
export function updateBroad(params) {
  return request.post("/shoplive/update",params,{headers:{reqsource:reqsource}})
}
/**
 * 已绑定的商品选择器
 * @method
 * @description 已绑定的商品选择器
 * @param {Object} req
 * @param {integer?} req.pageNum - 分页
 * @param {integer?} req.pageSize - 分页
*/
export function goodsSekector(params) {
  return request.post("/selector/shopgoods",params)
}
