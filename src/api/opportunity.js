import request from "@/utils/request"
/**
 * 商机管理列表
 * @method
 * @description 商机管理列表
 * @param {Object} req
 * @param {string} req.shopId -店铺ID
 * @param {string} req.status -订单状态
 * @param {string} req.createTime -创建时间
 * @param {string} req.pushReason -推送原因
 * @param {string} req.goodsType -商品类型
 * @param {string} req.pageNo -页号
 * @param {string} req.pageSize -每页显示条数
*/
export function getOpportunityList({
  shopId,status,createTime,pushReason,goodsType,pageNo,pageSize=10
}) {
  return request.post("/businessManage/list", {
    shopId,status,createTime,pushReason,goodsType,pageNo,pageSize
  },
  {
    headers:{reqsource:5}, 
    needEncrypt:1 
  })
}

export default {
  getOpportunityList
}