import request from "@/utils/request"

/**
 * @description 根据省id获取城市列表
 * @param {String} provinceId
 * @returns
 */
export function getCityByProvinceId(provinceId) {
  return request({
    url: "/area/query",
    method: "post",
    data: {
      deep:1,
      provinceId
    }
  })
}

/**
 * @description 根据省id获取城市列表
 * @param {String} cityId
 * @returns
 */
export function getRegionByCityId(cityId) {
  return request({
    url: "/area/query",
    method: "post",
    data: {
      deep:2,
      cityId
    }
  })
}
/**
 * @description 省列表
 */
export const PROVINCE_LIST = [
  { provinceId: "100", provinceName: "北京" },
  { provinceId: "220", provinceName: "天津" },
  { provinceId: "311", provinceName: "河北" },
  { provinceId: "351", provinceName: "山西" },
  { provinceId: "471", provinceName: "内蒙古" },
  { provinceId: "240", provinceName: "辽宁" },
  { provinceId: "431", provinceName: "吉林" },
  { provinceId: "451", provinceName: "黑龙江" },
  { provinceId: "210", provinceName: "上海" },
  { provinceId: "250", provinceName: "江苏" },
  { provinceId: "571", provinceName: "浙江" },
  { provinceId: "551", provinceName: "安徽" },
  { provinceId: "591", provinceName: "福建" },
  { provinceId: "791", provinceName: "江西" },
  { provinceId: "531", provinceName: "山东" },
  { provinceId: "371", provinceName: "河南" },
  { provinceId: "270", provinceName: "湖北" },
  { provinceId: "731", provinceName: "湖南" },
  { provinceId: "200", provinceName: "广东" },
  { provinceId: "771", provinceName: "广西" },
  { provinceId: "898", provinceName: "海南" },
  { provinceId: "230", provinceName: "重庆" },
  { provinceId: "280", provinceName: "四川" },
  { provinceId: "851", provinceName: "贵州" },
  { provinceId: "871", provinceName: "云南" },
  { provinceId: "891", provinceName: "西藏" },
  { provinceId: "290", provinceName: "陕西" },
  { provinceId: "931", provinceName: "甘肃" },
  { provinceId: "971", provinceName: "青海" },
  { provinceId: "951", provinceName: "宁夏" },
  { provinceId: "991", provinceName: "新疆" }
]
export const PROVINCE_MAP = (()=> {
  let map = {}
  PROVINCE_LIST.forEach(item => {
    map[item.provinceId] = item.provinceName
  })
  return map
})()

export default {
  getCityByProvinceId,
  getRegionByCityId,
  PROVINCE_LIST,
  PROVINCE_MAP,
}
