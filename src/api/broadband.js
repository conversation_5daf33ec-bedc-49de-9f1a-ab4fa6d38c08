import request from "@/utils/request"
/**
 *  req.reqsource -请求用户信息缓存id,从管理页过来默认是5
*/
const reqsource = 5
/**
 * 用户宽带预约订单列表
 * @method
 * @description 用户宽带预约订单列表
 * @param {Object} req
 * @param {string} req.shopId -店铺ID
 * @param {string} req.state -订单状态
 * @param {string} req.orderId -预约单号
*/
export function getUserBAOrderList({
  shopId,state,orderId,pageNo,pageSize
}) {
  return request.post("/broadband/getUserBAOrderList", {
    shopId,state,orderId,pageNo,pageSize
  },{ needEncrypt:1 })
}

/**
 * 用户宽带预约订单详情
 * @method
 * @description 云店宽带预约订单列表
 * @param {Object} req
 * @param {string} req.orderId -预约单号
*/
export function getUserBAOrderInfo({
  orderId
}) {
  return request.post("/broadband/getUserBAOrderInfo", {
    orderId
  },{ needEncrypt:1 })
}

/**
 * 云店宽带预约订单列表
 * @method
 * @description 云店宽带预约订单列表
 * @param {Object} req
 * @param {string} req.shopId -店铺ID
 * @param {string} req.state -订单状态
 * @param {string} req.appointmentMobile -预约手机号
 * @param {string} req.orderId -预约单号
 * @param {string} req.staffNumber -推荐人工号
 * @param {string} req.staffMobile -推荐人手机号
 * @param {string} req.createTimeStart -下单时间查询范围起始
 * @param {string} req.createTimeEnd -下单时间查询范围结束
 * @param {string} req.staffId -店员工号
*/
export function getShopBAOrderList({
  shopId,state,appointmentMobile,orderId,staffNumber,staffMobile,createTimeStart,createTimeEnd,staffId,stafferId,pageNo,pageSize
}) {
  return request.post("/broadband/getShopBAOrderList", {
    shopId,state,appointmentMobile,orderId,staffNumber,staffMobile,createTimeStart,createTimeEnd,staffId,stafferId,pageNo,pageSize
  },{headers:{reqsource:reqsource}, needEncrypt:1 })
}

/**
 * 云店宽带预约订单详情
 * @method
 * @description 云店宽带预约订单列表
 * @param {Object} req
 * @param {string} req.orderId -预约单号
*/
export function getShopBAOrderInfo({
  orderId
}) {
  return request.post("/broadband/getShopBAOrderInfo", {
    orderId
  },{headers:{reqsource:reqsource}, needEncrypt:1})
}

/**
 * 获取宽带预约模板详情
 * @method
 * @description 获取宽带预约模板详情
 * @param {Object} req
 * @param {integer?} req.shopId - 店铺id
 * @param {integer?} req.status - 模板状态
 * @param {integer} req.type - 类型 1：宽带商品模板，2：终端商品模板';
*/
export function queryShopTmpl(params,reqsource=1) {
  return request.post("/shop/gtmpl/queryShopTmpl",params,{headers:{reqsource:reqsource}})
}

/**
 * 获取约模板商品
 * @method
 * @description 获取约模板商品
 * @param {Object} req
 * @param {integer?} req.templateId - 模板id
*/
export function queryShopTmplGoods(params,reqsource=1) {
  return request.post("/shop/gtmpl/queryShopTmplGoods",params,{headers:{reqsource:reqsource}})
}

/**
 * 获取宽带预约开关状态
 * @method
 * @description 获取宽带预约开关状态
 * @param {Object} req
 * @param {integer?} req.shopId - 店铺id
*/
export function getBroadbandStatus(params,reqsource=1) {
  return request.post("/shoponline/getBroadbandStatus",params,{headers:{reqsource:reqsource}})
}

/**
 * 更新宽带预约开关状态
 * @method
 * @description 更新宽带预约开关状态
 * @param {Object} req
 * @param {integer?} req.shopId - 店铺id
*/
export function updateBroadbandStatus(params,reqsource=1) {
  return request.post("/shoponline/updateBroadbandStatus",params,{headers:{reqsource:reqsource}})
}

/**
 * 云店订单处理接口
 * @method
 * @description 获取宽带预约模板详情
 * @param {integer} req.reqsource - 是否是从工作台来的页面是的话传5
 * @param {Object} req.data
 * @param {integer?} req.data.orderId - 订单id
 * @param {integer?} req.data.productCode - productCode
 * @param {integer} req.data.reason - 预约失败理由
 * @param {integer} req.data.state - 订单状态
*/
export function updateBAOrderState({orderId,productCode,reason,state},reqsource=1) {
  return request.post("/broadband/updateBAOrderState",{orderId,productCode,reason,state},{headers:{reqsource:reqsource}})
}

/**
 * 取消预约订单接口
 * @method
 * @description 获取宽带预约模板详情
 * @param {Object} req
 * @param {integer?} req.orderId - 订单id
 * @param {integer} req.reason - 预约失败理由
*/
export function userCannelBAOrder({orderId,reason}) {
  return request.post("/broadband/userCannelBAOrder",{orderId,reason})
}

/**
 * 删除预约订单接口
 * @method
 * @description 获取宽带预约模板详情
 * @param {Object} req
 * @param {integer?} req.orderId - 订单id
*/
export function userDeleteBAOrder({orderId,reason}) {
  return request.post("/broadband/userDeleteBAOrder",{orderId,reason})
}

/**
 * 宽带预约提交接口
 * @method
 * @description 获取宽带预约模板详情
 * @param {Object} req
 * @param {integer?} req.shopId - 店铺id
 * @param {integer?} req.appointmentAddress - 详细地址
 * @param {integer} req.appointmentMobile - 预约手机号
 * @param {integer} req.appointmentName - 预约名称
 * @param {integer} req.district - 地址区
 * @param {integer} req.templateId - 模板id
 * @param {integer} req.referrerSecretStr - 推荐人加密串
 * @param {integer} req.goodsName - 商品名称
 * @param {integer} req.skuName - 商品sku名称
 * @param {integer} req.credentials - 证件类型（01：身份证）
 * @param {integer} req.credentialsNo - 证件号码
 * @param {integer} req.memo - 备注
 *
 *
*/
export function broadbandSubmit({
  shopId,appointmentAddress,appointmentMobile,appointmentName,district,templateId,referrerSecretStr,goodsId,skuId,credentials,credentialsNo,memo
}) {
  return request.post("/broadband/createBroadbandOrder",{
    shopId,appointmentAddress,appointmentMobile,appointmentName,district,templateId,referrerSecretStr,goodsId,skuId,credentials,credentialsNo,memo
  },{needEncrypt:1})
}

/**
 * 修改宽带预约订单信息
 * @method
 * @description 修改宽带预约订单信息
 * @param {Object} req
 * @param {integer?} req.appointmentAddress - 预约地址
 * @param {integer?} req.appointmentMobile - 预约人手机号
 * @param {integer} req.appointmentName - 预约人
 * @param {integer} req.orderId - 预约单号
 * @param {integer} req.district - 地址区
 *
*/
export function updateBAOrder({
  appointmentAddress,
  appointmentMobile,
  appointmentName,
  orderId,
  district
}) {
  return request.post("/broadband/updateBAOrder",{
    appointmentAddress,appointmentMobile,district,appointmentName,orderId
  },{needEncrypt:1,headers:{reqsource:reqsource}})
}

/**
 * 撤销修改宽带预约单信息
 * @method
 * @description 撤销修改宽带预约单信息
 * @param {Object} req
 * @param {integer?} req.appointmentAddress - 预约地址
 * @param {integer?} req.appointmentMobile - 预约人手机号
 * @param {integer} req.appointmentName - 预约人
 * @param {integer} req.orderId - 预约单号
 *
*/
export function undoModifyBAOrder({
  appointmentAddress,
  appointmentMobile,
  appointmentName,
  orderId
}) {
  return request.post("/broadband/undoModifyBAOrder",{
    appointmentAddress,appointmentMobile,appointmentName,orderId
  },{needEncrypt:1,headers:{reqsource:reqsource}})
}

/**
 * 前端显示预约专区
 * @method
 * @description 前端显示预约专区
 * @param {Object} req
 * @param {integer?} req.shopId - 店铺id
 *
*/
export function getOnlineSection({
  shopId,
}) {
  return request.post("/reservation/getOnlineSection",{
    shopId
  })
}

export default {
  getUserBAOrderList,
  getShopBAOrderList,
  queryShopTmpl,
  broadbandSubmit,
  updateBAOrderState,
  userCannelBAOrder,
  userDeleteBAOrder,
  getShopBAOrderInfo,
  getUserBAOrderInfo,
  getBroadbandStatus,
  updateBroadbandStatus,
  updateBAOrder,
  undoModifyBAOrder,
  getOnlineSection,
  queryShopTmplGoods
}
