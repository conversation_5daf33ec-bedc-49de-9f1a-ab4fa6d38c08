import request from "@/utils/request"
/**
 * 获取小程序码
 * @method
 * @description 获取小程序码
 * @param {Object} req
 * @param {string[]} req.width - 图片宽度
*/
export function getappletcode({
  width,hyaline,page,reqsource=5
}) {
  return request.post('wx/getStaffWxAcodeUnlimit', {
    width,hyaline,page
  } ,
  {
    responseType: 'arraybuffer',
    headers:{reqsource:reqsource}
  }
  )
}

/**
 * 获取小程序码
 * @method
 * @description 获取小程序码
 * @param {Object} req
 * @param {string[]} req.width - 图片宽度
*/
export function getGoodsWxAcodeUnlimit({
  width,hyaline,page,reqsource=5,url
}) {
  return request.post('wx/getGoodsWxAcodeUnlimit', {
    width,hyaline,page,url
  } ,
  {
    responseType: 'arraybuffer',
    headers:{reqsource:reqsource}
  }
  )
}

export default {
  getappletcode,
  getGoodsWxAcodeUnlimit
}
