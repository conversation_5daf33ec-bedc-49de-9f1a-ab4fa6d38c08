import request from "@/utils/request"
/**
 *  req.reqsource -请求用户信息缓存id,从管理页过来默认是5
*/
const reqsource = 5

/**
 * 表单反馈列表
 * @method
 * @description 表单反馈列表
 * @param {Object} req
 * @param {string} req.shopId -店铺ID
 * @param {string} req.status -意向单状态
 * @param {string} req.customerPhone -意向电话号码
 * @param {string} req.orderId -意向单号
 * @param {string} req.customerName -意向用户姓名
 * @param {string} req.createTimeStart -下单时间查询范围起始
 * @param {string} req.createTimeEnd -下单时间查询范围结束
 * @param {string} req.staffId -店员工号
*/
export function getFromStatisticInfo({
  shopId,titleName,page,pageSize,titleType
}) {
  let sendData = {
    shopId,titleName,page,pageSize,titleType
  }
  if(!sendData.createTimeStart){
    delete sendData.createTimeStart
  }
  if(!sendData.createTimeEnd){
    delete sendData.createTimeEnd
  }
  if(!sendData.status){
    delete sendData.status
  }
  return request.post("/form/getFromStatisticInfo", sendData ,{headers:{reqsource:reqsource}})
}

/**
 * 表单反馈详情
 * @method
 * @description 表单反馈详情
 * @param {Object} req
 * @param {string} req.orderId -预约单号
 * @param {string} req.shopId -店铺ID
*/
export function formFeedbackDetails({
  actId,adpId,endTime,makersType,page,pageSize,shopId,startTime
}) {
  return request.post("/form/formFeedbackDetails", {
    actId,adpId,endTime,makersType,page,pageSize,shopId,startTime
  },{headers:{reqsource:reqsource} })
}

/**
 * 查看状态接口
 * @method
 * @description 
 * @param {integer} req.reqsource - 是否是从工作台来的页面是的话传5
 * @param {Object} req.data
 * @param {integer?} req.data.formId - 表单id（adp_id）
 * @param {integer?} req.data.shopId - 店铺id
*/
export function setViewStatus({formId,shopId}) {
  return request.post("/form/setViewStatus",{formId,shopId}, {headers:{reqsource:reqsource} })
}

/**
 * 标记/未标记状态接口
 * @method
 * @description 获取宽带预约模板详情
 * @param {integer} req.reqsource - 是否是从工作台来的页面是的话传5
 * @param {Object} req.data
 * @param {Number?} req.data.formId - 表单id
 * @param {String?} req.data.makersType - 标记状态
 * @param {Number} req.data.shopId - 店铺id
*/
export function markerState({formId,shopId,makersType}) {
  return request.post("/form/markerState",{formId,shopId,makersType}, {headers:{reqsource:reqsource} })
}

/**
 * 更新表单备注接口
 * @method
 * @description 更新表单备注接口
 * @param {integer} req.reqsource - 是否是从工作台来的页面是的话传5
 * @param {Object} req.data
 * @param {Number?} req.data.formId - 表单id
 * @param {String?} req.data.markContent - 备注内容
 * @param {Number} req.data.shopId - 店铺id
*/
export function setRemark({formId,shopId,markContent}) {
  return request.post("/form/setRemark",{formId,shopId,markContent}, {headers:{reqsource:reqsource} })
}

export default {
  getFromStatisticInfo,
  setViewStatus,
  formFeedbackDetails,
  markerState,
  setRemark
}
