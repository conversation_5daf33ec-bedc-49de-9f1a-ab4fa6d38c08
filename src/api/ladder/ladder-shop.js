import request from "@/utils/request"
/**
 * 关注商铺
 * @method
 * @description 关注商铺
 * @param {Object} req
 * @param {string[]} req.operateType - integer($int32) 操作类型（0：取关 1：关注）
 * @param {string[]} req.storeId - integer($int32) 商铺id
*/
export function followShop({ storeId,operateType }) {
  return request.post("/ladder/followShop", {
    storeId,operateType
  })
}

/**
 * @method
 * @description 领取优惠券
 * @param {Object} req
 * @param {string[]} req.receiveType - 领券方式（1：直接领取 2：跳转外部链接 3：填写表单）
 * @param {string[]} req.couponId - integer($int32) 优惠券id
 * @param {string[]} req.formName - 姓名
 * @param {string[]} req.formName - 联系方式
*/
export function receiveCoupon({ couponId,receiveType,formName,formTel }) {
  return request.post("/ladder/user/receiveCoupon", {
    couponId,receiveType,formName,formTel
  })
}
/**
 * @method
 * @description  * 云店优惠资源详情
 * @param {Object} req
 * @param {string} req.id - 卡券ID
 *
 */
export function resourceDetail({ id }) {
  return request.post("/cooperateShop/detail/resource", {
    id,
  })
}

/**
 * @method
 * @description  * 查询店铺是否添加商铺
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 *
 */
export function includeStore({ shopId }) {
  return request.post("/ladder/shop/includeStore", {
    shopId
  })
}

export default {
  followShop,
  receiveCoupon,
  resourceDetail,
  includeStore
}