import request from "@/utils/request"
/**
 * @method
 * @description  * 云店优惠资源详情
 * @param {Object} req
 * @param {string} req.id - 卡券ID
 * @param {string} req.mobile - 卡券核销后，已核销的加密手机号
 */
export function writeOffDetail({ id,mobile }) {
  return request.post("/ladder/writeOff/detail", {
    id,mobile
  })
}

/**
 * @method
 * @description  * 优惠券资源核销
 * @param {Object} req
 * @param {string} req.secretKey - 卡券加密id
 *
 */
export function writeOffResource({ secretKey }) {
  return request.post("/cooperateShop/writeOff/resource", {
    secretKey
  })
}

/**
 * @method
 * @description * 查看我的券包
 * @param {Object} req
 * @param {string} req.pageNum - 第几页
 * @param {string} req.pageSize - 每页条数
 * @param {string} req.couponStatus - 优惠券状态
 * @param {string} req.shopId - 店铺Id
 *
 */
export function couponBagSearch({ couponStatus,pageNum,pageSize,shopId }) {
  return request.post("/ladder/couponBagSearch", {
    couponStatus,pageNum,pageSize,shopId
  })
}


export default {
  writeOffDetail,
  couponBagSearch,
  writeOffResource
}
