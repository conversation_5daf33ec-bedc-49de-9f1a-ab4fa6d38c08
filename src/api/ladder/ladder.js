import request from "@/utils/request"
/**
 * @method
 * @description  * 云店特惠卡券列表
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 * @param {string} req.pageNum - 第几页
 * @param {string} req.pageSize - 每页条数
 *
 */
export function getCouponPreferentialList({ shopId,pageNum,pageSize }) {
  return request.post("/ladder/netShopPreferential", {
    shopId,pageNum,pageSize
  })
}

/**
 * @method
 * @description * 周边精选卡券列表
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 *
 */
export function getCouponSelectedList({ shopId }) {
  return request.post("/ladder/peripheralSelect", {
    shopId,
  })
}

/**
 * @method
 * @description * 关注商铺资源更新
 * @param {Object} req
 * @param {string} req.pageNum - 第几页
 * @param {string} req.pageSize - 每页条数
 * @param {string} req.shopId - 店铺ID
 *
 */
export function followedResourceUpdate({ shopId,pageNum,pageSize }) {
  return request.post("/ladder/followedResourceUpdate", {
    shopId,pageNum,pageSize
  })
}

/**
 * @method
 * @description * 首页搜索
 * @param {Object} req
 * @param {string} req.pageNum - 第几页
 * @param {string} req.pageSize - 每页条数
 * @param {string} req.shopId - 店铺ID
 * @param {string} req.keyWord - 关键词
 *
 */
export function homePageSearch({ shopId,pageNum,pageSize,keyWord }) {
  return request.post("/ladder/homePageSearch", {
    shopId,pageNum,pageSize,keyWord
  })
}




export default {
  getCouponPreferentialList,
  getCouponSelectedList,
  followedResourceUpdate,
  homePageSearch
}
