import request from "@/utils/request"
/**
 * 每日分享任务列表
 * @method
 * @description 每日分享任务列表
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 * @param {string?} req.pageType - 任务列表类型（1当前任务，2历史任务）
 * @param {string?} req.pageNum - 页号（默认1）
 * @param {string?} req.pageSize - 每页显示数（默认20）
*/
export function getSharingList({
  shopId,
  pageType,
  pageNum,
  pageSize
}) {
  return request.post("/dailySharing/list", {
    shopId,
    pageType,
    pageNum,
    pageSize
  },{ headers: { reqsource: 5 } })
}
/**
 * 每日分享任务状态记录
 * @method
 * @description 每日分享任务状态记录
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 * @param {string?} req.taskId - 任务记录
 * @param {string?} req.status - 分享状态（1成功，2失败）
 * @param {string?} req.phone - 手机号
*/
export function getSharingRecord({
  shopId,
  taskId,
  status
}) {
  return request.post("/dailySharing/record", {
    shopId,
    taskId,
    status
  },{ headers: { reqsource: 5 } })
}
export default {
  getSharingList,
  getSharingRecord
}
