import request from "@/utils/request"

/**
 * @method
 * @description 消息订阅数据
 * @param {Object} req
  * @param {string} req.templates - List<SubscriptionTemplateReq>
 * {
    "templates": [
        {
          "templateId": "IUYIUYTYRTRRTY",
          "type": 1,
          "orderId": "13555151",
          "status": 1
        }
      ]
    }
*/
export function checkSubscription({
  templates
}) {
  return request.post("subscriptionUser/checkSubscription",{
    templates
  },{
    headers:{reqsource:1},
    needEncrypt:1
  })
}

export default {
  checkSubscription
}
