import request from "@/utils/request"
/**
 * 查看资料
 * @method
 * @description 查询我的资料数据
 * @param {Object} req
 * @param {string} req.tonken -用户登录信息
*/
export function getShopUserInfo({
  tonken
}) {
  return request.post("/shopuser/info", {
    tonken
  })
}
/**
 * 保存资料
 * @method
 * @description 保存我的资料数据
 * @param {Object} req
 * @param {string} req.birthday - 时间(默认传参1990-01-01)
 * @param {Integer} req.gender - 性别(1男2女)
 * @param {string?} req.nickname - 昵称
 * @param {string?} req.str - 时间、性别、昵称一起的加密串 {"birthday":"1990-01-01 00:00:00","gender":0,"nickname":"test"}
*/
export function getShopUserCreate({
  birthday,
  gender,
  nickname,
  str
}) {
  return request.post("/shopuser/create", {
    birthday,
    gender,
    nickname,
    str
  })
}

export default {
  getShopUserCreate,
  getShopUserInfo
}
