import request from "@/utils/request"
/**
 *  req.reqsource -请求用户信息缓存id,从管理页过来默认是5
*/
const reqsource = 5
/**
 * 移动业务券查询接口
 * @method
 * @description 查询移动业务券列表
 * @param {Object} req
 * @param {string} req.shopId -店铺ID
 * @param {string} req.obtainBusi -领取业务编码(固定传yd)
 * @param {string} req.pageSize -每页显示条数
 * @param {string} req.pageNum -页码
*/
export function getQuerySitechConpons({
  obtainBusi,pageSize,pageNum
}) {
  return request.post("/coupon/querySitechConpons", {
    obtainBusi,pageSize,pageNum
  })
}

/**
 *  移动业务券领取接口
 * @method
 * @description 移动业务券领取
 * @param {Object} req
 * @param {string} req.actId -活动标识
 * @param {string} req.batchID -券批次的唯一标示
*/
export function getReceiveconpon({
  batchID
}) {
  return request.post("/coupon/receiveconpon", {
    batchID
  })
}
/**
 * 店铺商品券查询接口
 * @method
 * @description 查询店铺商品券列表
 * @param {Object} req
 * @param {string} req.shopId -店铺ID
 * @param {string} req.pageSize -每页显示条数
 * @param {string} req.pageNum -页码
*/
export function getQueryGoodsCoupons({
  shopId,pageSize,pageNum
}) {
  return request.post("/coupon/queryGoodsCoupons", {
    shopId,pageSize,pageNum
  })
}

/**
 *  店铺商品券领取接口
 * @method
 * @description 店铺商品券领取
 * @param {Object} req
 * @param {string} req.couDistId -优惠券发放id
 * @param {string} req.couponId -优惠券id
 * @param {string} req.prdId -活动id
*/
export function getGoodsCoupons({
  couDistId,prdId,shopId
}) {
  return request.post("/coupon/getGoodsCoupons", {
    couDistId,prdId,shopId
  })
}
export default {
  getQuerySitechConpons,
  getReceiveconpon,
  getQueryGoodsCoupons,
  getGoodsCoupons
}
