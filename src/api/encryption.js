import request from "@/utils/request"
/**
 * 跳转领券中心加密接口
 * @method
 * @description 跳转领券中心加密接口
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 * @param {string} req.type - 加密类型
 * 加密串为storeId-渠道编码，otherStoreId-shopId
*/
export function getEncryption({
  shopId,type
}) {
  return request.post("/Encryption/share", {
    shopId,type
  })
}
/**
 * @description 获取rsakey
 * @returns
 */
export function getPubKey(){
  return request.post("/Encryption/auth", {})
}

export default {
  getEncryption,
  getPubKey
}
