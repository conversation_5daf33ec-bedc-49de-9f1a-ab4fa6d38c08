/*
 * @Author: 李亚龙
 * @Date: 2021-08-24 11:34:58
 * @LastEditTime: 2021-11-06 14:12:12
 * @LastEditors: your name
 * @Description:
 * @FilePath: \yundian-m\src\api\vip.js
 */
import request from "@/utils/request"
/**
 * 判断用户是否为会员
 * @method
 * @description
 * @param {string} req.shopId  店铺id
*/
export function IsMember({
  shopId
}) {
  return request.post("shopMember/isMember",{
    shopId
  })
}

/**
 * 会员信息
 * @method
 * @description
 * @param {string} req.shopId  店铺id
 * @param {string} req.memberId  店铺会员id
*/
export function MemberInfo({
  shopId,
  memberId
}) {
  return request.post("shopMember/info",{
    shopId,
    memberId
  })
}

/**
 * 非会员的用户信息
 * @method
 * @description
 * @param {string} req.token  token
*/
export function UserInfo() {
  return request.post("shopuser/info")
}


/**
 * 添加会员信息
 * @method
 * @description
 * @param {string} req.shopId  店铺id
 * @param {string} req.staffId  店员/店长ID
 * @param {string} req.birthday   生日;格式为：yyyy-MM-dd
*/
export function AddMemberInfo({
  shopId,
  staffId,
  birthday
}) {
  return request.post("shopMember/add",{
    shopId,
    staffId,
    birthday
  })
}

/**
 * 退出会员
 * @method
 * @description
 * @param {string} req.shopId  店铺id
*/
export function withdeaw({
  shopId
}) {
  return request.post("shopMember/withdeaw",{
    shopId
  })
}

/**
 * 查询店铺卡券活动
 * @method
 * @description
 * @param {string} req.token  token
 * @param {string} req.shopId  店铺id
*/
export function CouponCheck({
  shopId
}) {
  return request.post("coupon/check",{
    shopId
  })
}

/**
 * 入会领券
 * @method
 * @description
 * @param {string} req.couponCode  卡券编码
*/
export function CouponReceive({
  couponCode,
  activityId
}) {
  return request.post("coupon/receive",{
    couponCode,
    activityId
  })
}

export default {
  IsMember,
  MemberInfo,
  UserInfo,
  AddMemberInfo,
  withdeaw,
  CouponCheck,
  CouponReceive
}
