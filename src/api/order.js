import request from "@/utils/request"
/**
 * 订单状态汇总数量接口
 * @method
 * @description 订单状态汇总数量接口
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 *
 */
export function getOrderStatuscount({ shopId }) {
  return request.post("/busi/order/getOrderStatus", {
    shopId,
  })
}
const reqsource = 5

/**
 * 通信订单列表
 * @method
 * @description 通信订单列表
 * @param {Object} req
 * @param {string} req.orderTab	 1全部，2本地
 * @param {string} req.goodsName	string($date-time) 商品名称
 * @param {string} req.goodsType	integer($int32)商品类型
 * @param {string} req.mobile	integer 客户手机号
 * @param {string} req.shopAssistantName		integer($int64)店员姓名
 * @param {string} req.result	string办理结果 1办理成功 2办理失败
 * @param {string} req.pageNo	integer($int32)页号
 * @param {string} req.pageSize	integer($int32)每页显示行数
 * @param {string} req.createTimeStart	integer($int64)店员编号
 * @param {string} req.createTimeEnd	integer($int64)店员编号
 *
 *
}
*/
export function getShopOrderList({
  orderTab,
  createTimeStart,
  goodsType,
  result,
  mobile,
  goodsName,
  pageNo,
  pageSize,
  shopAssistantName,
  createTimeEnd,
}) {
  return request.post("/order/getShopOrderList",
    {
      orderTab,
      createTimeStart,
      goodsType,
      result,
      mobile,
      goodsName,
      pageNo,
      pageSize,
      shopAssistantName,
      createTimeEnd,
    },
    { headers: { reqsource: reqsource },needEncrypt:1 }
  )
}

/**
 * 店铺订单统计
 * @method
 * @description 店铺订单统计
 * @param {Object} req
 * @param {string} req.orderTab	 1全部，2本地
 * @param {string} req.staffId	string($date-time) 店员名称
 * @param {string} req.goodsType	integer($int32)商品类型
 * @param {string} req.pageNo	integer($int32)页号
 * @param {string} req.pageSize	integer($int32)每页显示行数
 * @param {string} req.createTime	integer($int64)起始日期
 * @param {string} req.endTime	integer($int64)结束日期
 *
 *
}
*/
export function statisticsOrder({
  orderTab,
  createTime,
  goodsType,
  staffId,
  pageNo,
  pageSize,
  endTime,
}) {
  return request.post("/order/statisticsOrder",
    {
      orderTab,
      createTime,
      goodsType,
      staffId,
      pageNo,
      pageSize,
      endTime,
    },
    { headers: { reqsource: reqsource } }
  )
}

/**
 * 订单列表
 * @method
 * @description 订单列表
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 * @param {string} req.createTimeStart	string($date-time) 下单时间起始
 * @param {string} req.createTimeEnd	string($date-time) 下单时间截至
 * @param {string} req.goodsType	integer($int32)商品类型
 * @param {string} req.numApplyWay	string 办理方式 example: 1：包邮到家、2：京东到家、3：厅内自提、4：极速办卡
 * @param {string} req.numactiveStatus	string 号码激活状态 example: WA：待激活、AD：已激活 、OA：已取消
 * @param {string} req.orderId	string订单号
 * @param {string} req.pageNo	integer($int32)页号
 * @param {string} req.pageSize	integer($int32)每页显示行数
 * @param {string} req.shopAssistantId	integer($int64)店员编号
 *
 *
}
*/
export function getOrderList({
  shopId,
  createTimeStart,
  goodsType,
  numApplyWay,
  numactiveStatus,
  orderId,
  pageNo,
  pageSize,
  shopAssistantId,
  createTimeEnd,
}) {
  return request.post("/number/getNumOrderList",
    {
      shopId,
      createTimeStart,
      goodsType,
      numApplyWay,
      numactiveStatus,
      orderId,
      pageNo,
      pageSize,
      shopAssistantId,
      createTimeEnd,
    },
    { headers: { reqsource: reqsource } ,needEncrypt:1 }
  )
}

/**
 * 订单详情-号卡订单
 * @method
 * @description 订单详情
 * @param {Object} req
 * @param {string} req.orderId --订单id
 *
 *
}
*/
export function getOrderDetail({ orderId, operator,shopId }) {
  return request.post("/order/getShopOrderDetail",
    {
      orderId,
      operator,
      shopId
    },
    { headers: { reqsource: reqsource } ,needEncrypt:1 }
  )
}

/**
 * 订单详情-通信订单
 * @method
 * @description 订单详情
 * @param {Object} req
 * @param {string} req.orderId --订单id
 *
 *
}
*/
export function getOrderDetails({ orderId, operator,shopId }) {
  return request.post("/order/getShopOrderDetails",
    {
      orderId,
      operator,
      shopId
    },
    { headers: { reqsource: reqsource },needEncrypt:1 }
  )
}

/**
 * 订单转交
 * @method
 * @description 订单转交
 * @param {Object} req
 * @param {string} req.orderId --订单id
 * @param {string} req.shopId --店铺id
 * @param {string} req.staffId --转交店铺id
 *
 *
 */
export function forwardOrder({ orderId, shopId, staffId }) {
  return request.post("/number/forwardOrder",
    {
      orderId,
      staffId,
      shopId,
    },
    { headers: { reqsource: reqsource } }
  )
}

/**
 * 取消订单
 * @method
 * @description 取消订单
 * @param {Object} req
 * @param {string} req.orderId --订单id
 * @param {string} req.shopId --店铺id
 * @param {string} req.staffId --转交店铺id
 *
 *
 */
export function canelOrder({ orderId, shopId, staffId }) {
  return request.post("/number/canelOrder",
    {
      orderId,
      staffId,
      shopId,
    },
    { headers: { reqsource: reqsource } }
  )
}

/**
 * 选择店员
 * @method
 * @description 选择店员
 * @param {Object} req
 * @param {string} req.shopId --店铺id
 *
 *
 */
export function getStaffs({ shopId }) {
  return request.post(`/number/getStaffs?shopId=${shopId}`,
    {},
    {
      headers: { reqsource: reqsource },
    }
  )
}

/**
 * 小程序提醒跳转页面查询订单详情接口
 * @method
 * @description 小程序提醒跳转页面查询订单详情接口
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 * @param {string} req.orderType - 订单类型
 * @param {string} req.orderId - 订单id
 */
export function getWxMiniOrderDetail({ shopId, orderType, orderId }) {
  return request.post("/order/wxMiniOrderDetail", {
    shopId,orderType,orderId
  })
}

/**
 * 小程序提醒跳转页面 查询banner和icon
 * @method
 * @description 小程序提醒跳转页面 查询banner和icon
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 */
export function getAdDaseInfo({ shopId }) {
  return request.post("/order/ad/base/info", {
    shopId
  })
}

/**
 * 小程序提醒跳转页面 查询分区和商品信息
 * @method
 * @description 小程序提醒跳转页面 查询运营位楼层商品信息
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 */
export function getAdQueryFloorGood({ shopId }) {
  return request.post("/order/ad/query/floorGoods", {
    shopId
  })
}

export default {
  getOrderStatuscount,
  getOrderList,
  getOrderDetail,
  getOrderDetails,
  forwardOrder,
  canelOrder,
  getStaffs,
  statisticsOrder,
  getShopOrderList,
  getWxMiniOrderDetail,
  getAdDaseInfo,
  getAdQueryFloorGood
}
