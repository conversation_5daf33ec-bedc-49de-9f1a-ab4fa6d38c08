import request from "@/utils/request"
import ua from '@/utils/ua'

/**
 * 店主获取活动页面数据
 * @method
 * @description 店主获取活动页面数据
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 * @param {string?} req.cityId - 市ID
 * @param {string?} req.provinceId - 省ID
 * @param {string?} req.preview - 是否预览模式
 * @param {string?} req.configure- 是否编辑模式
*/
export function getActData({
  shopId,
  actId,
  cityId,
  provinceId,
  preview,
  configure,
  sort,
  pageSize,
  pageNum
}) {

  return request.post("/actpage/getData", {
    shopId,
    actId,
    cityId,
    provinceId,
    preview,
    configure,
    sort,
    pageSize,
    pageNum
  },{ needEncrypt:1 })
}

/**
 * 店主获取活动页面数据
 * @method
 * @description 首页楼层分页接口
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 * @param {string?} req.cityId - 市ID
 * @param {string?} req.provinceId - 省ID
 * @param {string?} req.preview - 是否预览模式
 * @param {string?} req.configure- 是否编辑模式
*/
export function getDataBySort({
  shopId,
  actId,
  cityId,
  provinceId,
  preview,
  configure,
  sort,
  pageSize,
  pageNum
}) {
  return request.post("/actpage/getDataBySort", {
    shopId,
    actId,
    cityId,
    provinceId,
    preview,
    configure,
    sort,
    pageSize,
    pageNum
  })
}

/**
 * 店主获取店铺状态
 * @method
 * @description 获取异网商品信息
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 * @param {string} req.queryHomeSwitch 是否需要查询是不是新模板
*/
export function getShopStatus({
  shopId,
  queryHomeSwitch
}) {
  return request.post("/busi/shop/queryStatus", {
    shopId,
    queryHomeSwitch
  })
}


/**
 * 店主获取店铺信息
 * @method
 * @description
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
*/
export function queryShopInfo({
  shopId
}) {
  return request.post("/busi/shop/queryShopInfo", {
    shopId
  },{ needEncrypt:1 })
}
/**
 * 获取阿波罗运营位商品信息
 * @method
 * @description 店主获取店铺状态 0未开通店铺 1开通未注册 2审核中 3审核通过 4是关闭 5审核未通过
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
*/
export function getFashionCmData({
  shopId, pageSize , pageNum
}) {
  return request.post("/actpage/getFashionCmData", {
    shopId,pageSize,pageNum
  })
}
/**
 * 获取异网商品信息
 * @method
 * @description 店主获取店铺状态 0未开通店铺 1开通未注册 2审核中 3审核通过 4是关闭 5审核未通过
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
*/
export function getFashionApoloData({
  shopId, pageSize , pageNum
}) {
  return request.post("/actpage/getFashionApoloData", {
    shopId,pageSize,pageNum
  })
}
/**
 * 获取手机专区查询信息
 * @method
 * @description 店主获取店铺状态 0未开通店铺 1开通未注册 2审核中 3审核通过 4是关闭 5审核未通过
 * @param {Object} req
 * @param {string} req.brands - 品牌id
 * @param {string} req.maxPrice - 最高价格单位分（价格区间）
 * @param {string} req.minPrice - 最低价格单位分（价格区间）
 * @param {string} req.order - 价格排序（0：升序,1：降序）
 * @param {string} req.shopId - 店铺ID
 * @param {string?} req.pageNum - 第几页
 * @param {string?} req.pageSize - 每页数量
 * @param {channelId}  渠道代码 小程序:1000032,H5:1000033 精准营销办套餐
*/
export async function getMobileZone({
  shopId,brands,maxPrice,minPrice,order,pageNum,pageSize,channelId
}) {
  channelId = channelId?channelId:'1000033'
  let res = await ua.isWeChatMiniApp()
  if(res){
    channelId = '1000032'
  }
  return request.post("/commonGoods/getGoods", {
    shopId,brands,maxPrice,minPrice,order,pageNum,pageSize
  })
}

/**
 * 店主获取企微客服链接
 * @method
 * @description
 * @param {Object} req
 * @param {string} req.sharingCode - 店铺ID
*/
export function getCustomerCode({
  sharingCode
}) {
  return request.post("/busi/qw/queryWorkWeChatUserInfo", {
    sharingCode
  })
}

/**
 * 获取店铺组件展示配置
 * @method
 * @description
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
*/
export function getComponentQuery({
  shopId
}) {
  return request.post("/component/query", {
    shopId
  })
}

/**
 * 店铺运营位数据
 * @method
 * @description
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 * @param {string} req.cmCodeList -运营位标识
*/
export function getCmData({
  shopId,cmCodeList
}) {
  return request.post("/busi/cm/getCmDataByCode", {
    shopId,cmCodeList
  })
}

/**
 * 商品选择器
 * @method
 * @description 店主配置商品，选择的商品列表
 * @param {Object} req
 * @param {string}  req.goodsName - 商品名称
 * @param {string?} req.goodsId - 商品ID
 * @param {string?} req.goodsType - 商品类型
 * @param {string?} req.pageNum - 第几页
 * @param {string?} req.pageSize - 每页数量
 * @param {string?} req.source - 商品来源
 * @param {string?} req.goodsIdInUse - 已选择的商品id数组
*/
export function goodsSekector({goodsName,goodsId,goodsIdInUse,pageNum,pageSize,source,goodsType},reqsource=1) {//商品选择器
  let sendData = {
    goodsName,
    goodsId,
    pageNum,
    pageSize,
    source,
    goodsType,
    goodsIdInUse
  }
  if (!sendData.goodsId) delete sendData.goodsId
  if (!sendData.goodsName ) delete sendData.goodsName
  if (!sendData.goodsType ) delete sendData.goodsType
  if (!sendData.source ) delete sendData.source
  return request.post("/selector/goods", sendData,{
    headers:{
      reqsource:reqsource
    }
  })
}


/**
 * 标准模板按楼层筛选的商品选择器
 * @method
 * @description 店主配置商品，选择的商品列表
 * @param {Object} req
 * @param {string}  req.goodsName - 商品名称
 * @param {string?} req.goodsId - 商品ID
 * @param {string?} req.goodsType - 商品类型
 * @param {string?} req.pageNum - 第几页
 * @param {string?} req.pageSize - 每页数量
 * @param {string?} req.source - 商品来源
 * @param {string?} req.goodsIdInUse - 已选择的商品id数组
*/
export function goodsSortSekector({goodsName,goodsId,goodsIdInUse,pageNum,pageSize,source,goodsType},reqsource=1) {//商品选择器
  let sendData = {
    goodsName,
    goodsId,
    pageNum,
    pageSize,
    source,
    goodsType,
    goodsIdInUse
  }
  if (!sendData.goodsId) delete sendData.goodsId
  if (!sendData.goodsName ) delete sendData.goodsName
  if (!sendData.goodsType ) delete sendData.goodsType
  if (!sendData.source ) delete sendData.source
  return request.post("/selector/commonGoods", sendData,{
    headers:{
      reqsource:reqsource
    }
  })
}

/**
 * 添加商品
 * @method
 * @description 店主配置商品，选择的商品
 * @param {Object} req
 * @param {string}  req.sort
 * @param {string}  req.storeId
 * @param {string}  req.componentCode
 * @param {string}  req.floorId
 * @param {string}  req.moduleId
 * @param {string?} req.goodsList-多选商品列表
 * @param {string}  req.price
 * @param {string}  req.goodsSource
 * @param {string}  req.image
 * @param {string}  req.goodsName - 商品名称
 * @param {string?} req.goodsId - 商品ID
*/
export function addGoods({actId,goodsName,goodsId,image,goodsSource,price,moduleId,floorId,componentCode,storeId,sort,goodsList}) {//商品选择器
  let sendData = {
    actId,
    goodsName,
    goodsId,
    image,
    goodsSource,
    goodsList,
    price,
    moduleId,
    floorId,
    componentCode,
    storeId,
    sort
  }
  return request.post("/actpage/insertAdData", sendData)
}

/**
 * 删除商品
 * @method
 * @description 店主配置商品，删除商品
 * @param {string?} req.adId - 商品ID
*/
export function delGoods(adId) {//商品选择器
  return request.post("/actpage/delAdData", {adId:adId})
}

/**
 * 配置充值模块是否显示
 * @method
 * @description 店主配置商品，删除商品
 * @param {string?} req.floorId - 楼层id
 * @param {string?} req.design - 1是显示 0是取消显示
*/

export function changeAddCreditShow({floorId,design}){
  return request.post("/actpage/updateFloorData", {floorId,design})
}

/**
 * 首页获取直播配置信息
 * @method
 * @description 首页获取直播配置信息
 * @param {Object} req
 * @param {string} req.shopId - 店铺ID
 */
export function getLiveInfo({shopId}){
  return request.post("/busi/shop/live/getinfo", {shopId})
}


/**
 * @Description: 精准营销办套餐
 * @User: JOJO <<EMAIL>>
 * @Date: 2021-12-31 17:10:48
 * @param {channelId}  渠道代码 小程序:1000032,H5:1000033
 * @param {key} 预览标识
 * @param {shopId}  店铺ID
 * @return {*}
 */
export async function getDoMeal({
  channelId,
  key,
  shopId
}) {
  channelId = channelId?channelId:'1000033'
  let res = await ua.isWeChatMiniApp()
  if(res){
    channelId = '1000032'
  }

  return request.post("/iop/getIopData", {
    shopId,
    channelId,
    key
  })
}

/**
 * 获取店铺列表接口
 * @method
 * @description 获取店铺列表接口
 * @param {Object} req
 * @param {string[]} req.merchantId - 商户id
*/
export function getStoreList() {
  return request({
    url: '/shoponline/getUserShops',
    method: 'post',
    needEncrypt:1
  })
}

/**
 * 获取新版本店铺商品接口
 * @method
 * @description 获取店铺商品接口
 * @param {Object} req
 * @param {string[]} req.shopId - 店铺id
 * @param {channelId}  渠道代码 小程序:1000032,H5:1000033 精准营销办套餐
*/
export async function getGoods({shopId,channelId}) {
  channelId = channelId?channelId:'1000033'
  let res = await ua.isWeChatMiniApp()
  if(res){
    channelId = '1000032'
  }
  return request.post("/homepage/v2/floor/query", {
    shopId,channelId
  })
}

/**
 * 获取新版本店铺页面信息接口
 * @method
 * @description 获取店铺页面信息接口
 * @param {Object} req
 * @param {string[]} req.shopId - 店铺id
 * @param {channelId}  渠道代码 小程序:1000032,H5:1000033 精准营销办套餐
*/
export async function getBaseInfo({shopId,channelId}) {
  channelId = channelId?channelId:'1000033'
  let res = await ua.isWeChatMiniApp()
  if(res){
    channelId = '1000032'
  }
  return request.post("/homepage/v2/base/info", {
    shopId,channelId
  },{ needEncrypt:1 })
}

/**
 * 获取新版本二级页面店铺商品接口
 * @method
 * @description 获取店铺商品接口
 * @param {Object} req
 * @param {string[]} req.shopId - 店铺id
 * @param {string[]} req.floorId 商品楼层id
 * @param {channelId}  渠道代码 小程序:1000032,H5:1000033 精准营销办套餐
*/
export async function getSecondaryGoods({shopId,floorId,pageNum,pageSize=10,channelId}) {
  channelId = channelId?channelId:'1000033'
  let res = await ua.isWeChatMiniApp()
  if(res){
    channelId = '1000032'
  }
  return request.post("/homepage/v2/floor/goods/query", {
    shopId,floorId,pageSize,pageNum,channelId
  })
}

/**
 * 获取新版本二级页面热销商品接口
 * @method
 * @description 获取店铺商品接口
 * @param {Object} req
 * @param {string[]} req.shopId - 店铺id
 * @param {string[]} req.floorId 商品楼层id
*/
export function getSecondaryRecommendGoods({shopId,floorId}) {
  return request.post("/homepage/v2/floor/hotGoods/query", {
    shopId,floorId
  })
}

export default {
  getActData,
  getDataBySort,
  goodsSekector,
  goodsSortSekector,
  addGoods,
  delGoods,
  getShopStatus,
  getFashionCmData,
  getFashionApoloData,
  getMobileZone,
  changeAddCreditShow,
  getCustomerCode,
  getLiveInfo,
  queryShopInfo,
  getDoMeal,
  getStoreList,
  getCmData,
  getGoods,
  getBaseInfo,
  getSecondaryGoods,
  getSecondaryRecommendGoods,
  getComponentQuery
}
