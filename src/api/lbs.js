import request from "@/utils/request"
import loginApi from "@/api/login"

/**
 * 附近店铺
 * @method
 * @description 附近店铺
 * @param {Object} req
 * @param {number} req.count - 分页
 * @param {number} req.distance - 距离
 * @param {string} req.keywords - 关键词
 * @param {string} req.latitude - 纬度
 * @param {string} req.longitude - 经度
 * @param {string} req.pn - 关键词
 * @param {string} req.pageNum - 页码
 * @param {string} req.pageSize - 每页条数
 * @param {string} req.province - 省
 * @param {string} req.city - 市
 * @param {string} req.county - 县
 *
*/
export function getNearShop({
  count,
  distance,
  keywords,
  latitude,
  longitude,
  pn,
  pageNum,
  pageSize,
  boutique,
  province,
  city,
  county
}) {
  return request.post("lbs/getNearShop", {
    count,
    distance,
    keywords,
    latitude,
    longitude,
    pn,
    pageNum,
    pageSize,
    boutique,
    province:province?String(province):null,
    city:city?String(city):null,
    county:county?String(county):null
  },{needEncrypt:1})
}
/**
 * 附近店铺会员店
 * @method
 * @description 附近店铺会员店
 * @param {Object} req
 * @param {number} req.count - 分页
 * @param {number} req.distance - 距离
 * @param {string} req.keywords - 关键词
 * @param {string} req.latitude - 纬度
 * @param {string} req.longitude - 经度
 * @param {string} req.pn - 关键词
 * @param {string} req.pageNum - 页码
 * @param {string} req.pageSize - 每页条数
 * @param {string} req.province - 省
 * @param {string} req.city - 市
 * @param {string} req.county - 县
 *
*/
export function getVipShop({
  count,
  distance,
  keywords,
  latitude,
  longitude,
  pn,
  pageNum,
  pageSize,
  province,
  city,
  county
}) {
  return request.post("lbs/getVipShop", {
    count,
    distance,
    keywords,
    latitude,
    longitude,
    pn,
    pageNum,
    pageSize,
    province:province?String(province):null,
    city:city?String(city):null,
    county:county?String(county):null
  },{needEncrypt:1})
}
/**
 * 当前云店信息
 * @method
 * @description 查询当前云店信息
 * @param {long} req.shopId- 店铺ID
 * @param {string} req.longitude- 经度
 * @param {string} req.latitude- 纬度
 */
export function getShopInfo({
  shopId,
  longitude,
  latitude
}) {
  return request.post("lbs/getShopInfo", {
    shopId,
    longitude,
    latitude
  },{needEncrypt:1})
}
/**
 * 查询热点商品
 * @method
 * @description  查询热点商品
 * @param {long} req.shopId- 店铺ID
 * @param {string} req.adpCode- 热销广告位
 * @param {string} req.status- 状态
 */
export function shopGoodsQuer({
  shopId,
  adpCode,
  status
}) {
  return request.post("shopgoods/query", {
    shopId,
    adpCode,
    status
  })
}
/**
 * 查询热点商品
 * @method
 * @description  查询热点商品
 * @param {long} req.shopId- 店铺ID
 * @param {string} req.adpCode- 热销广告位
 * @param {string} req.reqsource -请求用户信息缓存id
 */
export function shopQueryOnLine({
  shopId,
  adpCode,
  reqsource=5
}) {
  return request.post("shopgoods/queryOnLine", {
    shopId,
    adpCode
  },
  {
    headers:{
      reqsource:reqsource
    }
  }
  )
}
/**
 * 新增热点商品
 * @method
 * @description  新增热点商品
 * @param {long} req.shopId- 店铺ID
 * @param {string} req.adpCode- 热销广告位
 * @param {string} req.cmCode- 位置标识
 * @param {string} req.goods- 商品ID
 * @param {string} req.status- 状态
 * @param {string} req.goodsSource- 商品来源（1：商城，2：异业）
 * @param {string} req.reqsource -请求用户信息缓存id
 */
export function shopInsert({
  shopId,
  adpCode,
  cmCode,
  goods,
  goodsSource,
  status,
  reqsource=5
}) {
  return request.post("shopgoods/insert", {
    shopId,
    adpCode,
    cmCode,
    goods,
    goodsSource,
    status
  },
  {
    headers:{
      reqsource:reqsource
    }
  }
  )
}
/**
 * 更新热点商品
 * @method
 * @description  更新热点商品
 * @param {long} req.shopId- 店铺ID
 * @param {string} req.adpCode- 热销广告位
 * @param {string} req.cmCode- 位置标识
 * @param {string} req.goods- 商品ID
 * @param {string} req.status- 状态
 * @param {string} req.goodsSource- 商品来源（1：商城，2：异业）
 * @param {string} req.reqsource -请求用户信息缓存id
 */
export function shopUpdate({
  shopId,
  adpCode,
  cmCode,
  goods,
  goodsSource,
  status,
  reqsource=5
}) {
  return request.post("shopgoods/update", {
    shopId,
    adpCode,
    cmCode,
    goods,
    goodsSource,
    status
  },
  {
    headers:{
      reqsource:reqsource
    }
  })
}
/**
 * 删除热点商品
 * @method
 * @description  删除热点商品
 * @param {long} req.shopId- 店铺ID
 * @param {string} req.cmId- 主键
 * @param {string} req.reqsource -请求用户信息缓存id
 */
export function shopDelete({
  shopId,
  cmId,
  reqsource=5
}) {
  return request.post("shopgoods/delete", {
    shopId,
    cmId
  },
  {
    headers:{
      reqsource:reqsource
    }
  })
}
/**
 * 排队预约取号
 * @method
 * @description
 * @param {string} req.lat  纬度
 * @param {string} req.lon  经度
 * @param {string} req.pageType 打开页面类型 0在线取号 1在线预约 默认为0
 * @param {string} req.shopId  店铺id
 * @param {string} req.channelCode  渠道 小程序=5
*/
export async function getUrlReq({
  lat,
  lon,
  pageType,
  shopId,
  channelCode = 5,
}) {
  const token = await loginApi.getCmAuthTokenWU({
    targetSourceId:'019018'
  }).then(res => {
    if (res.data && res.data.token) {
      return res.data.token
    } else {
      return null
    }
  })

  if(token){
    return request.post("appointment/geturl",{
      lat,
      lon,
      pageType,
      shopId,
      channelCode,
      token
    })
  }else{
    return new Promise(resolve => {
      resolve({
        message:'未登录'
      })
    })
  }
}

/**
 * 根据经纬度获取省
 * @method
 * @description
 * @param {string} req.latitude  纬度
 * @param {string} req.longitude  经度
*/
export async function lbsProvinceQuery({
  latitude = null,
  longitude = null,
}) {
  return request.post("lbs/province/query",{
    latitude,
    longitude
  })
  // return await {
  //   data:{
  //     provinceName:'北京',
  //     provinceId:'100'
  //   }
  // }
}

/**
 * 公域渠道链接校验接口
 * @method
 * @description
 * @param {string} req.latitude  纬度
 * @param {string} req.longitude  经度
 * 返回码code，
    0：成功
    80002：链接解析失败
    80003：链接已失效
*/
export async function incomeflowverify({
  checkId
}) {
  return request.post("/publicChannel/incomeflow/verify",{
    checkId
  })
}

/**
 * 获取商品对应最近的店铺里的商品链接的接口
 * @method 根据用户定位，自动匹配该省份（省份与配置链接的商户一致）距离该用户最近、且上架了该商品的云店A，并将云店A的信息拼接在商品链接中
 * @description
 * @param {string} req.latitude  纬度
 * @param {string} req.longitude  经度
 * @param {string} req.userProvId 用户归属省
 * @param {string} req.userCity 用户归属地市
 * @param {string} req.pubIncomeStatus 商品引流状态
*/
export async function getNearShopByGoodsId({
  latitude = null,
  longitude = null,
  goodsId,
  merchantId,
  provId,
  userProvId,
  userCity,
  pubIncomeStatus
}) {
  return request.post("lbs/getNearShopByGoodsId",{
    latitude,
    longitude,
    goodsId,
    merchantId,
    provId,
    userProvId,
    userCity,
    pubIncomeStatus
  })
}


/**
 * @description 解密字符串
 * @param {string} req.pubIncomeStatus  待解密的字符串
*/
export async function decrypt({
  pubIncomeStatus
}) {
  return request.post("lbs/EnDecrypt/decrypt",{
    pubIncomeStatus
  })
}

export default {
  getNearShop,
  getVipShop,
  getShopInfo,
  getUrlReq,
  shopGoodsQuer,
  shopQueryOnLine,
  shopInsert,
  shopUpdate,
  shopDelete,
  lbsProvinceQuery,
  getNearShopByGoodsId,
  incomeflowverify,
  decrypt
}
