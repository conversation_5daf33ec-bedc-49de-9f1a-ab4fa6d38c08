import request from "@/utils/request"
/**
 * 获取运营位数据
 * @method
 * @description 获取运营位数据
 * @param {Object} req
 * @param {string[]} req.cm_code - 运营位编码列表
 * @param {string} req.province_id - 省
 * @param {string} req.city_id - 市
*/
export function getCmData({
  cm_code,
  province_id,
  city_id,
}) {
  return request.post("cm/getCmData", {
    cm_code,
    province_id,
    city_id,
  })
}

export default {
  getCmData
}