import request from "@/utils/request"
/****reqsource管理页跳过来的默认为5 */
/**
 * 获取当前登录人的店员信息
 * @method
 * @description
*/
// export function staffInfo() {
//   return request.post("currentUser/staff")
// }
export function staffInfo(reqsource=5){
  return request({
    url:'currentUser/staff',
    method:'post',
    headers: {
      reqsource:reqsource
    }
    // params:{phone,shopId}
  })
}

/**
 * 获取会员业务汇总数据
 * @method
 * @description
 * @param {string} req.statTime -开始时间
 * @param {string} req.reqsource -请求用户信息缓存id
*/
export function staffData({
  statTime,reqsource=5
}) {
  return request.post("/bi/getMemberStat",{
    statTime
  },{headers:{reqsource:reqsource}})
}

/**
 * 获取店铺业务汇总数据
 * @method
 * @description
 * @param {string} req.statDate -开始时间
 * @param {string} req.endDate -结束时间
 * @param {string} req.productId -商品类型
 * @param {string} req.staffNumber -商品类型
 * @param {string} req.reqsource -请求用户信息缓存id
*/
export function shopData(data) {
  return request.post("bi/getOrderStat",data,{headers:{reqsource:data.reqsource}})
}

/**
 * 内蒙订单统计(全员营销)
 * @method
 * @description
 * @param {integer} req.staffId -店员id
 * @param {string} req.endDate -统计日期-end,不传默认是昨天,yyyy-MM-dd HH:mm:ss,时分秒给值00:00:00
 * @param {string} req.startDate -统计日期-start,不传默认是昨天,yyyy-MM-dd HH:mm:ss,时分秒给值00:00:00
*/
export function statisticNeimeng({
  staffId,startDate,endDate,reqsource=5
}) {
  return request.post("/statistic/neimengOrder",{
    staffId,startDate,endDate
  },
  {headers:{reqsource:reqsource}}
  )
}

export default {
  staffInfo,
  staffData,
  shopData,
  statisticNeimeng
}
