import request from "@/utils/request"
/**
 * @method
 * @description 扫码购机推荐人链接数据
 * @param {Object} req
 * @param {string} req.entryURL - 要添加分享信息的链接
 * @param {string} req.reqsource - 请求来源
*/
export function getEntryurl({
  entryURL,reqsource=5
}) {
  return request.post("/scodea/entryurl", {
    entryURL
  },{headers:{reqsource:reqsource}})
}

/**
 * 获取扫码购机商品列表
 * @method
 * @description 店主获取商品列表数据
 * @param {Object} req
 * @param {string} req.isPaging - 是否分页查询， 0：是；1：否。默认不分页
 * @param {string} req.pageNo   - 页号
 * @param {string} req.pageSize - 每页显示行数
 * @param {string} req.section  - 第几块的tab页.0 表示查询全部；1 表示第一个块；2 表示第二块；3 表示第三块
 * @param {string} req.shopId   - 商铺id
 * @param {string} req.sortedFlag - 排序， 1：综合排序；2：价格升序； 3：价格降序 。默认综合排序。
*/
export function getGoodsList({
  isPaging,pageNo,pageSize,section,shopId,sortedFlag
}) {
  return request.post("/scodea/merproductlist", {
    isPaging,pageNo,pageSize,section,shopId,sortedFlag
  })
}

/**
 * @method
 * @description 店主获取扫码购机轮播图列表
 * @param {Object} req
 * @param {string} req.shopId   - 商铺id
*/
export function getRotationList({
  shopId
}) {
  return request.post("/scodea/rotationlist", {
    shopId
  })
}

export default {
  getEntryurl,
  getGoodsList,
  getRotationList
}