import request from "@/utils/request"
import ua from '@/utils/ua'


/**
 * @method
 * @description 获取签到状态的接口
 * @param {Object} req
 * @param {string[]} req.shopId - 店铺id
 * @param {string[]} req.channel //渠道 1-微信小程序 2-H5
 * @returns {Promise<{code:Number,message:String,data:{
  * enableSignIn:String是否开启签到功能,
  * continueSignInDays:Number当前连续签到天数,
  * todaySignInStatus:boolean今天是否签到,
  * signInShareSwitch:Boolean签到分享开关,
  * requireFansUser:Boolean是否需要关注店铺，如果需要关注，签到接口需要传加密的staffId,
  * shareData:Object分享数据
 * }}>}
 * @
*/
export function querySignStatus({shopId}) {
  return request.post("/signIn/gift/queryStatus", {
    shopId,channel:2
  })
}

/**
 * @method
 * @description 查询签到记录的接口
 * @param {Object} req
 * @param {string[]} req.shopId - 店铺id
 * @param {string[]} req.channel //渠道 1-微信小程序 2-H5
 * @param {string[]} req.month //不传month默认查本周，传month指定月份
 * @returns {Promise<{code:Number,message:String,data:{
* signInDateList:Array签到日期,
* giftDateMap:Array有礼品的日期
* }}>}
* @
*/
export function querySignInGiftRecord({shopId,month}) {
  let data = {shopId,channel:2}
  if(month){
    data.month = month
  }
  return request.post("/signIn/gift/querySignInGiftRecord", data)
}

/**
 * @method
 * @description 签到接口
 * @param {Object} req
 * @param {string[]} req.shopId - 店铺id
 * @param {string[]} req.channel //渠道 1-微信小程序 2-H5
 * @returns {Promise<{code:Number,message:String,data:{
* status:Number签到状态0未签到1已签到,
* hasGift:Number签到是否获得礼品0无礼品1有礼品,
* giftContent:String礼品内容,
* newFans:Boolean是否成为新粉丝0无行为1新关注成为粉丝
* }}>}
* @
*/
export function signIn({shopId}) {
  return request.post("/signIn/gift/signIn", {shopId,channel:2})
}

/**
 * @method
 * @description 签到接口
 * @param {Object} req
 * @param {string[]} req.shopId - 店铺id
 * @param {string[]} req.channel //渠道 1-微信小程序 2-H5
 * @returns {Promise<{code:Number,message:String,data:String}>}
* @
*/
export function querySignInRule({shopId}) {
  return request.post("/signIn/gift/querySignInRule", {shopId,channel:2})
}

export default {
  querySignStatus,
  querySignInGiftRecord,
  signIn,
  querySignInRule
}
