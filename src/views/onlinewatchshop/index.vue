<template>
  <div class="storemanagement hot_product">
    <Shopname
      v-if="onlineData.showShopName"
      bg-color="#efefef"
      :content="onlineData.title"
      :styleindex="2000"
      :show-back="true"
    />
    <h3
      v-if="onlineData.shopimgnum"
      :class="{
        'padding-top-34': onlineData.showShopName,
      }"
      class="shopimgtitle"
    >
      店铺图片 （{{ onlineData.shopimgnum }}）
    </h3>
    <div v-if="onlineData.shopSave" class="hot_product">
      <template v-for="imgContainer in onlineImgListContainer">
        <div
          v-if="onlineData.shopSave[imgContainer] && onlineData.shopSave[imgContainer].title"
          :key="imgContainer"
          class="imgbox"
        >
          <h3>{{ onlineData.shopSave[imgContainer].title }}</h3>
          <template v-for="(item, key) in onlineData.shopSave[imgContainer].imgList">
            <div :key="'shopFace' + key" class="hot_product_con">
              <div class="hot_product_card">
                <div class="goodsItem">
                  <img
                    :src="item"
                    class="image"
                    @click="openimgpreview(key, imgContainer)"
                  />
                </div>
                <span class="item-close">{{ onlineData.shopSave[imgContainer].title }}</span>
              </div>
            </div>
          </template>
        </div>
      </template>
    </div>
    <van-image-preview
      v-model="onlineData.imgpreview"
      :images="onlineData.shopSaveimg"
      :closeable="true"
      :start-position="onlineData.startposition"
    >
    </van-image-preview>
    <!-- 4G免登 -->
    <LoginDialog
      :islogin="autoLoginObj.is4gLogined"
      :is-force-login="true"
      :isloginfn="logined"
      :is-auto-login="autoLoginObj.autoLogin"
      :show-cancle="false"
    />
    <Footers :user-info="onlineData.users" isactive="onlineshop" />
  </div>
</template>
<script setup>
import Vue, { reactive, onMounted, defineProps,getCurrentInstance } from 'vue'
import { Toast, ImagePreview } from 'vant'
import Online from '@/api/online'
import UA from '@/utils/ua'
import { getToken, removeLocalStorage } from "@/utils/login/localStorage.js"
import loginApi from "@/api/login"
import { getImgUrl } from '@/utils/utils'
//组件
import Shopname from '@/components/index/headercon'
import LoginDialog from '@/components/login/index'
import Footers from "@/views/my/components/myfooter.vue"
Vue.use(ImagePreview)
const onlineImgListContainer = [
  "shopFace","shopInner","shopLicense"
]
const onlineImgListTitle = [
  "店铺外观","店铺内景","营业执照"
]
const onlineData = reactive({
  title: '在线看店',
  zindex: {
    zIndex: '**********',
  },
  imgpreview: false,
  shopSaveimg: [],
  startposition: '',
  shopSave: {},
  shopimgnum: '',
  users: null,
  isApp: false,
  pageData: null,
  configure: null,
  preview: null,
  proxy:null,
  showShopName:
    !UA.isWechat &&
    !UA.isWechatWork &&
    !UA.isApp &&
    !UA.isIosQQ &&
    !UA.isAndroidQQ,
})

function logined(res) {
  onlineData.users = res
  onlineDetail()
}
const autoLoginObj = reactive({
  autoLogin: false,
  is4gLogined: null,
})
function autoLoginCb(res) {
  autoLoginObj.is4gLogined = false
  autoLoginObj.autoLogin = true
}
//查询
function onlineDetail() {
  Online.getOnlineDetail({
    shopId: onlineData.pageInfo.shopId,
  }).then((res) => {
    onlineData.shopimgnum = 0
    if (res.code == 0 && res.data) {
      onlineImgListContainer.forEach((item,index)=>{
        if(res.data[item] && res.data[item].length>0){
          let shopImgList = []
          res.data[item].forEach((itemMin) => {
            if (itemMin != '') {
              onlineData.shopimgnum++
              shopImgList.push(getImgUrl(itemMin))
            }
          })
          onlineData.shopSave[item] = {}
          onlineData.shopSave[item].imgList = shopImgList
          if(shopImgList.length>0){
            onlineData.shopSave[item].title = onlineImgListTitle[index]
          }
        }
      })
    } else {
      Toast(res.message)
    }
  })
}
function openimgpreview(index, type) {
  onlineData.shopSaveimg = onlineData.shopSave[type].imgList
  onlineData.imgpreview = true
  onlineData.startposition = index
}
onMounted(() => {
  const url = new URL(location.href)
  const shopId =
    url.searchParams.get('shopId') || url.searchParams.get('shop_id')
  const actId = url.searchParams.get('actId')
  const preview = url.searchParams.get('preview')
  onlineData.pageInfo = {
    shopId,
    actId,
    preview,
  }
  onlineData.preview = preview
  if (UA.isApp || UA.isWechat) {
    onlineData.isApp = false
  } else {
    onlineData.isApp = true
  }
  loginApi.getUserInfo(getToken(),"0",1).then((res)=>{
    if (res.code == 0) {
      if (onlineData.preview && (res.data && parseInt(res.data.RuleId) ===0)) {//店长预览
        Toast("您没有权限预览")
      } else {
        logined(res.data) //用户也可查到信息
      }
    } else if (res.code == 1) {
      removeLocalStorage()
      return null
    }
  })
})
</script>
<style>
.icpinfo {
  margin-bottom: 67.875px;
}
</style>
<style lang="scss" scoped>
.storemanagement {
  background: #f9fafc;
  font-size: 13.5px;
  color: #333;
  // min-height: 100vh;
  min-height: calc(100vh - 68px);
  h3 {
    height: 45px;
    line-height: 45px;
    color: #000000;
    padding-left: 7.5px;
  }
  h3.shopimgtitle {
    height: 45px;
    line-height: 45px;
    padding-left: 15px;
    background: #f7f8f9;
    box-sizing: content-box;
    font-weight: bold;
  }
  .hot_product {
    padding: 0 7.5px;
    .imgbox {
      clear: both;
      height: 157.5px;
    }
    .hot_product_con {
      display: block;
      width: 31%;
      margin: 0 3.75px;
      padding-bottom: 15px;
      text-align: center;
      float: left;
      height: 112.5px;
      .hot_product_card {
        background: #fff;
        border-radius: 7.5px;
        overflow: hidden;
        position: relative;
        .goodsItem {
          .image {
            display: block;
            height: 112.5px;
            margin-left: -50%;
            // width: 100%;
            // min-height: 112.5px;
            // height: auto;
          }
        }
        .item-close {
          position: absolute;
          right: 0;
          top: 0;
          font-size: 12px;
          color: #fff;
          background: rgba(0, 0, 0, 0.4);
          padding: 4.5px 7.5px;
        }
      }
    }
    .hot_product_scsh {
      display: block;
      width: 96%;
      margin: 0 auto;
      padding-bottom: 22.5px;
      text-align: center;
      font-size: 13.5px;
      p {
        background: #fff;
        border-radius: 7.5px;
        padding: 37.5px 0;
      }
    }
  }
}
.padding-top-34 {
  padding-top: 34px !important;
}
</style>
