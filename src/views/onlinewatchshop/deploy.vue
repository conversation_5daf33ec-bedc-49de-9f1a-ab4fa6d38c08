<template>
  <div class="storemanagement hot_product">
    <Shopname
      v-if="showShopTitle"
      bg-color="#efefef"
      :content="onlineData.title"
      :show-back="true"
    />
    <div
      :class="{
        'padding-top-34':showShopTitle}"
    >
      <h3>在线开店配置</h3>
      <p class="online_p">
        <span>在线看店</span>
        <a
          v-if="onlineData.detailStatus == 0"
          href="javascript:void(0);"
          class="kdpz_right"
          style="color: #ccc"
        >待审核</a>
        <a
          v-else-if="onlineData.detailStatus == 1"
          href="javascript:void(0);"
          class="kdpz_right bul"
          @click="onlineAudit(2)"
        >店铺照片下线</a>
        <a
          v-else-if="onlineData.detailStatus == 2"
          href="javascript:void(0);"
          class="kdpz_right red"
        >审核不通过</a>
        <a
          v-else
          href="javascript:void(0);"
          class="kdpz_right"
          style="color: #ccc"
        >已下线</a>
      </p>
      <p class="online_p">
        <span>附近店铺: {{ onlineData.nearbyImgPosition }} </span>
        <a
          v-if="onlineData.isEdit"
          href="javascript:void(0);"
          class="kdpz_right bul"
          style="margin-left: 10px"
          @click="updateNearbyImg"
        >保存</a>
        <a
          v-if="!onlineData.isEdit"
          href="javascript:void(0);"
          class="kdpz_right bul"
          @click="changeEdit()"
        >修改</a>
        <a
          v-if="onlineData.isEdit"
          href="javascript:void(0);"
          class="kdpz_right bul"
          @click="cancle"
        >取消</a>
      </p>
    </div>
    <h3 class="red redtip">
      建议图片尺寸：4:3
    </h3>
    <h3>店铺照片</h3>
    <h3 class="red redtitle">
      店铺照片需要上传店铺外观、内景、营业执照，最多上传9张。其中，店铺营业执照为必传且最少上传一张。
    </h3>
    <div class="hot_product">
      <template v-for="(imageArray,jkshopSaveKey) in jkshopSave">
        <div :key="jkshopSaveKey">
          <div
            v-for="(item, key) in jkshopSave[jkshopSaveKey]"
            :key="item+key"
            class="hot_product_con"
          >
            <div v-if="item && item != ''" class="hot_product_card">
              <div
                class="goodsItem"
                @click="
                  changeNearImg(jkshopSave[jkshopSaveKey][key], key, onlineImgListContainer[jkshopSaveKey])
                "
              >
                <img :src="getImgUrl(item)" class="image" />
              </div>
              <a
                v-if="!onlineData.isEdit"
                href="javascript:void(0);"
                class="item-close ac_remove_goods"
                @click="imgDelete(key, jkshopSaveKey)"
              >×</a>
            </div>
            <div v-else class="hot_product_xzsp" @click="changeNearImg(null)">
              <input
                v-if="!onlineData.isEdit"
                type="file"
                class="file untinput"
                accept="image/png,image/jpg,image/jpeg"
                @change="uploading($event, key, jkshopSaveKey)"
              />
              <img src="~@/assets/onlinewatchshop/camera.png" alt="" />
              <p>上传{{ onlineImgListTitle[jkshopSaveKey] }}图</p>
            </div>
          </div>
        </div>
      </template>
    </div>
    <div v-if="!onlineData.isEdit" class="online_button">
      <Button round type="info" @click="onlineSave()">
        保存
      </Button>
    </div>
    <div v-else class="online_button online_button_disabled">
      <Button round type="info">
        保存
      </Button>
    </div>
    <LoginDialog
      :islogin="autoLoginObj.is4gLogined"
      :is-force-login="true"
      :isloginfn="logined"
      :is-auto-login="autoLoginObj.autoLogin"
      :show-cancle="false"
    />
  </div>
</template>
<script setup>
import {reactive,getCurrentInstance, onMounted} from 'vue'
import Cropper from 'cropperjs'
import { Toast, Icon, Switch, Button } from 'vant'
import Online from '@/api/online'
import UA from '@/utils/ua'
import { getImgUrl,copy } from '@/utils/utils'
import loginUtils from '@/utils/login'
import EventBus from '@/api/eventbus'
//组件
import Shopname from '@/components/index/headercon'
import LoginDialog from '@/components/login/index'
const onlineImgListContainer = {
  shopFace:1,
  shopInner:2,
  shopLicense:3,
}
const onlineImgListTitle = {
  shopFace:"店铺外观",
  shopInner:"店铺内景",
  shopLicense:"营业执照"
}
let rate = {
  width: 750,
  height: 375,
}
let cropperImg = ""
const showShopTitle = !UA.isWechat &&!UA.isWechatWork &&!UA.isApp &&!UA.isIosQQ &&!UA.isAndroidQQ
const onlineData = reactive({
  title: '在线看店',
  detailStatus: null,
  isLogined: false,
  users: null,
  index: null,
  onty: null,
  isApp: false,
  isEdit: false,
  nearbyImgPosition: '',
  currentImg: null,
  oldCurrentImg: null,
  nearbyImgList: [],
  nearbyImgObj: {},
})

const jkshopSave = reactive({
  shopFace: [],
  shopInner: [],
  shopLicense: [],
})

function logined(res) {
  onlineData.users = res
  onlineData.isLogined = res && res.UserName > ''
}
//选图
function uploading(e, index, shopImg) {
  if (onlineData.isEdit) {
    return false
  }
  const img = e.target.files[0]
  if (
    img.type == 'image/png' ||
    img.type == 'image/jpg' ||
    img.type == 'image/jpeg'
  ) {
    cropperImg = URL.createObjectURL(img)
    onlineData.index = index
    onlineData.onty = shopImg
    //onlineData.imgName = img.name
    change(e)
  } else {
    Toast('上传图片支持jpg、png格式')
  }
}
//删除
function imgDelete(index, shopImg) {
  let myshop = copy(jkshopSave[shopImg])
  myshop[index]=''
  jkshopSave[shopImg] = copy(myshop)
}
//提交审核
function onlineAudit(ind) {
  Online.getOnlineAudit({
    type: ind,
  }).then((res) => {
    if (res.code == 0) {
      //成功
      if (ind == 1) {
        Toast('提交审核成功，等待审核')
      } else if (ind == 2) {
        Toast('店铺照片下线成功')
        onlineDetail()
      }
    } else {
      Toast(res.message)
    }
  })
}
//查询
function onlineDetail() {
  Online.getOnlineDetail({
    shopId: onlineData.pageInfo.shopId,
  }).then(async(res) => {
    if (res.code == 0) {
      onlineData.nearbyImgList = []
      onlineData.nearbyImgobj = {}
      onlineData.detailStatus = res.data.status
      Object.keys(onlineImgListContainer).forEach((item,index)=>{
        if(res.data[item] && res.data[item]!= ''){
          const jkshopFace = []
          res.data[item].forEach((innerItem, key) => {
            jkshopFace.push(innerItem)
            if (innerItem) {
              onlineData.nearbyImgList.push(innerItem)
              onlineData.nearbyImgobj[innerItem] = {
                position: 'Pic' + (index+1) + '-' + (key + 1),
              }
            }
          })
          jkshopSave[item] = jkshopFace
        }
      })
      let getnearbyImg = await getNearbyImg()
      onlineData.oldCurrentImg = getnearbyImg
      onlineData.nearbyImgPosition = getnearbyImg.position
      if (getnearbyImg.picture && onlineData.nearbyImgobj[getnearbyImg.picture]) {
        // 图片还在时的回显
        changeCurrentImg(getnearbyImg.picture, getnearbyImg.position)
      } else {
        //审核通过之后在做判断
        if (Number(onlineData.detailStatus) === 1) {
          //图片已经被删除了
          let defaultImg = null
          if (onlineData.nearbyImgList.length === 0) {
            changeCurrentImg('', '')
            updateNearbyImg()
            return false
          }
          for (let i = 0; i < onlineData.nearbyImgList.length; i++) {
            if (onlineData.nearbyImgList[i]) {
              defaultImg = onlineData.nearbyImgList[i]
              break
            }
          }
          changeCurrentImg(
            defaultImg,
            onlineData.nearbyImgobj[defaultImg].position
          )
          updateNearbyImg()
        }
      }
    } else {
      Toast(res.message)
    }
  })
}
function changeCurrentImg(shopPicture, position) {
  onlineData.currentImg = {
    shopPicture: shopPicture,
    position: position,
  }
  onlineData.nearbyImgPosition = position
}
//保存店铺资质信息
function onlineSave() {
  if (jkshopSave.shopLicense == null) {
    Toast('请上传营业执照~')
    return
  }
  const { shopFace, shopInner, shopLicense } = jkshopSave || {}
  Online.getOnlineSave({
    shopFace,
    shopInner,
    shopLicense,
  }).then((res) => {
    Toast(res.message)
    if (res.code == 0) {
      //onlineData.shopInfo = res.data
      Toast('保存成功，等待审核')
      onlineDetail()
    } else {
      Toast(res.message)
    }
  })
}
function change(event) {
  let image = cropperImg //预览对象
  clip(event, {
    resultObj: image,
    aspectWithRatio: Number(rate.width),
    aspectHeightRatio: Number(rate.height),
  })
}
//外部接口，用于input['file']对象change时的调用
function clip(e, opt) {
  onlineData.fileObj = e.srcElement
  let files = e.target.files || e.dataTransfer.files
  if (!files.length) return false //不是图片直接返回
  //调用初始化方法
  initilize(opt)
  //获取图片文件资源
  onlineData.picValue = files[0]
  //去获取拍照时的信息，解决拍出来的照片旋转问题
  // Exif.getData( files[0] , function(){
  //   self.Orientation = Exif.getTag( files[0], 'Orientation');
  //   console.log(self.Orientation)
  // });
  //调用方法转成url格式
  onlineData.originUrl = getObjectURL(onlineData.picValue)
  //每次替换图片要重新得到新的url
  if (onlineData.cropper) {
    onlineData.cropper.replace(onlineData.originUrl)
  }
}
//初始化方法
function initilize(opt) {
  onlineData.options = opt
  //创建dom
  createElement()
  onlineData.resultObj = opt.resultObj
  //初始化裁剪对象
  onlineData.cropper = new Cropper(onlineData.preview, {
    aspectRatio: opt.aspectWithRatio / opt.aspectHeightRatio,
    //aspectRatio: 1/1,//裁剪框比例 1：1
    autoCropArea: opt.autoCropArea || 0.8,
    viewMode: 0,
    guides: true,
    cropBoxResizable: false, //是否通过拖动来调整剪裁框的大小
    cropBoxMovable: false, //是否通过拖拽来移动剪裁框。
    dragCrop: false,
    dragMode: 'move', //‘crop’: 可以产生一个新的裁剪框3 ‘move’: 只可以移动3 ‘none’: 什么也不处理
    center: true,
    movable: true, //是否允许移动图片
    rotatable: true, //是否允许旋转图片
    zoomable: true, //是否允许放大图像。
    zoomOnTouch: true, //是否可以通过拖动触摸来放大图像。
    scalable: true, //是否允许扩展图片
    minCropBoxHeight: 375,
    minCropBoxWidth: 750,
    background: false,
    checkOrientation: true,
    checkCrossOrigin: true,
    zoomOnWheel: true,
    toggleDragModeOnDblclick: false,
    ready: function() {
      // console.log(onlineData.cropper.rotate(90))
      if (opt.aspectRatio == 'Free') {
        let cropBox = onlineData.cropper.cropBox
        cropBox.querySelector('span.cropper-view-box').style.outline =
          'none'
        onlineData.cropper.disable()
      }
    },
  })
}
//创建一些必要的DOM，用于图片裁剪
function createElement() {
  //初始化图片为空对象
  onlineData.preview = null
  // <img src="../../assets/app/loading.gif">
  let str =
    '<div><img id="clip_image" src=""></div><button type="button" id="fcRotate" class="replay"></button><button type="button" id="cancel_clip">取消</button><button type="button" id="clip_button">确定</button>'
  str +=
    '<div class="crop_loading"><div class="crop_content"><div class="crop_text">图片修剪中...</div></div></div>'
  str +=
    '<div class="crop_success"><div class="crop_success_text">上传成功</div></div></div>'
  let body = document.getElementsByTagName('body')[0]
  onlineData.reagion = document.createElement('div')
  onlineData.reagion.id = 'clip_container'
  onlineData.reagion.className = 'container'
  onlineData.reagion.innerHTML = str
  //添加创建好的DOM元素
  body.appendChild(onlineData.reagion)
  onlineData.preview = document.getElementById('clip_image')
  //绑定一些方法
  initFunction()
}
//初始化一些函数绑定
function initFunction() {
  onlineData.clickBtn = document.getElementById('clip_button')
  onlineData.cancelBtn = document.getElementById('cancel_clip')
  onlineData.fcRotate = document.getElementById('fcRotate')
  //确定事件
  addEvent(onlineData.clickBtn, 'click', function() {
    crop()
  })
  //取消事件
  addEvent(onlineData.cancelBtn, 'click', function() {
    destoried()
  })
  //旋转事件
  addEvent(onlineData.fcRotate, 'click', function() {
    onlineData.cropper.rotate(90)
  })
  //清空input的值
  addEvent(onlineData.fileObj, 'click', function() {
    this.value = ''
  })
}
//图片转码方法
function getObjectURL(file) {
  let url = null
  if (window.createObjectURL != undefined) {
    // basic
    url = window.createObjectURL(file)
  } else if (window.URL != undefined) {
    // mozilla(firefox)
    url = window.URL.createObjectURL(file)
  } else if (window.webkitURL != undefined) {
    // webkit or chrome
    url = window.webkitURL.createObjectURL(file)
  }
  return url
}
//点击确定进行裁剪
function crop() {
  let image = new Image()
  let croppedCanvas
  let roundedCanvas
  // Crop
  document.querySelector('.crop_loading').style.display = 'block'
  setTimeout(function() {
    croppedCanvas = onlineData.cropper.getCroppedCanvas()
    // Round
    roundedCanvas = getRoundedCanvas(croppedCanvas)
    let imgData = roundedCanvas.toDataURL('image/png', 0.6)
    image.src = imgData
    postImg(imgData)
  }, 20)
}
//获取裁剪图片资源
function getRoundedCanvas(sourceCanvas) {
  let canvas = document.createElement('canvas')
  let context = canvas.getContext('2d')
  //let width = sourceCanvas.width
  //let height = sourceCanvas.height
  let width = 750
  let height = 375
  canvas.width = width
  canvas.height = height
  context.imageSmoothingEnabled = true
  context.drawImage(sourceCanvas, 0, 0, width, height)
  context.globalCompositeOperation = 'destination-in'
  context.beginPath()
  context.rect(0, 0, width, height)
  context.fill()
  return canvas
}
//销毁原来的对象
function destoried() {
  //移除事件
  removeEvent(onlineData.clickBtn, 'click', null)
  removeEvent(onlineData.cancelBtn, 'click', null)
  removeEvent(onlineData.fileObj, 'click', null)
  //移除裁剪框
  onlineData.reagion.parentNode.removeChild(onlineData.reagion)
  //销毁裁剪对象
  onlineData.cropper.destroy()
  onlineData.cropper = null
}
//图片上传
function postImg(imageData) {
  let data = { file: imageData } //watermarkText
  if (onlineData.onty == 'shopLicense') {
    data.watermarkText = '云店专用 yundian'
  }
  Online.getQualificationImage(data).then((res) => {
    if (res.code == 0) {
      EventBus.$emit('callback', imageData)
      let jkshopSaveItem = copy(jkshopSave[onlineData.onty])
      jkshopSaveItem[onlineData.index] = res.data.fileId
      jkshopSave[onlineData.onty] = copy(jkshopSaveItem)
      //这边写图片的上传
      destoried()
    } else {
      Toast(res.message + '请稍后重新上传')
      destoried()
    }
  })
}
//图片旋转
function rotateImg(img, direction, canvas) {
  //最小与最大旋转方向，图片旋转4次后回到原方向
  const min_step = 0
  const max_step = 3
  if (img == null) return
  //img的高度和宽度不能在img元素隐藏后获取，否则会出错
  let height = img.height
  let width = img.width
  let step = 2
  if (direction == 'right') {
    step++
    //旋转到原位置，即超过最大值
    step > max_step && (step = min_step)
  } else {
    step--
    step < min_step && (step = max_step)
  }
  //旋转角度以弧度值为参数
  let degree = (step * 90 * Math.PI) / 180
  let ctx = canvas.getContext('2d')
  switch (step) {
  case 0:
    canvas.width = width
    canvas.height = height
    ctx.drawImage(img, 0, 0)
    break
  case 1:
    canvas.width = height
    canvas.height = width
    ctx.rotate(degree)
    ctx.drawImage(img, 0, -height)
    break
  case 2:
    canvas.width = width
    canvas.height = height
    ctx.rotate(degree)
    ctx.drawImage(img, -width, -height)
    break
  case 3:
    canvas.width = height
    canvas.height = width
    ctx.rotate(degree)
    ctx.drawImage(img, -width, 0)
    break
  }
}
//图片压缩
function compress(img, Orientation) {
  let canvas = document.createElement('canvas')
  let ctx = canvas.getContext('2d')
  //瓦片canvas
  let tCanvas = document.createElement('canvas')
  let tctx = tCanvas.getContext('2d')
  // let initSize = img.src.length
  let width = img.width
  let height = img.height

  //如果图片大于四百万像素，计算压缩比并将大小压至400万以下
  let ratio
  if ((ratio = (width * height) / 4000000) > 1) {
    // console.log("大于400万像素")
    ratio = Math.sqrt(ratio)
    width /= ratio
    height /= ratio
  } else {
    ratio = 1
  }
  canvas.width = width
  canvas.height = height
  //        铺底色
  ctx.fillStyle = '#fff'
  ctx.fillRect(0, 0, canvas.width, canvas.height)
  //如果图片像素大于100万则使用瓦片绘制
  let count
  if ((count = (width * height) / 1000000) > 1) {
    count = ~~(Math.sqrt(count) + 1) //计算要分成多少块瓦片
    //            计算每块瓦片的宽和高
    let nw = ~~(width / count)
    let nh = ~~(height / count)
    tCanvas.width = nw
    tCanvas.height = nh
    for (let i = 0; i < count; i++) {
      for (let j = 0; j < count; j++) {
        tctx.drawImage(
          img,
          i * nw * ratio,
          j * nh * ratio,
          nw * ratio,
          nh * ratio,
          0,
          0,
          nw,
          nh
        )
        ctx.drawImage(tCanvas, i * nw, j * nh, nw, nh)
      }
    }
  } else {
    ctx.drawImage(img, 0, 0, width, height)
  }
  //修复ios上传图片的时候 被旋转的问题
  if (Orientation != '' && Orientation != 1) {
    switch (Orientation) {
    case 6: //需要顺时针（向左）90度旋转
      onlineData.rotateImg(img, 'left', canvas)
      break
    case 8: //需要逆时针（向右）90度旋转
      onlineData.rotateImg(img, 'right', canvas)
      break
    case 3: //需要180度旋转
      onlineData.rotateImg(img, 'right', canvas) //转两次
      onlineData.rotateImg(img, 'right', canvas)
      break
    }
  }
  //进行最小压缩
  let ndata = canvas.toDataURL('image/png', 0.1)
  tCanvas.width = tCanvas.height = canvas.width = canvas.height = 0

  return ndata
}
//添加事件
function addEvent(obj, type, fn) {
  if (obj.addEventListener) {
    obj.addEventListener(type, fn, false)
  } else {
    obj.attachEvent('on' + type, fn)
  }
}
//移除事件
function removeEvent(obj, type, fn) {
  if (obj.removeEventListener) {
    obj.removeEventListener(type, fn, false)
  } else {
    obj.detachEvent('on' + type, fn)
  }
}
//点击修改附近店铺展示图片
function changeEdit() {
  if (onlineData.detailStatus == 0) {
    //待审核
    Toast('审核中，无法变更图片')
  } else if (onlineData.detailStatus == 1) {
    //店铺照片下线
    onlineData.isEdit = true
  } else if (onlineData.detailStatus == 2) {
    //审核不通过
    Toast('审核未通过，无法变更图片')
  } else {
    //已下线
    Toast('图片已下线，无法变更')
  }
  // onlineData.isEdit = true
}
function cancle() {
  onlineData.isEdit = false
  onlineData.currentImg = onlineData.oldCurrentImg
  onlineData.nearbyImgPosition = onlineData.oldCurrentImg.position
}
function changeNearImg(src, key, row) {
  if (!onlineData.isEdit) {
    return false
  }
  if (!src) {
    Toast('请选择已上传图片')
    return false
  }
  onlineData.nearbyImgPosition = 'Pic' + row + '-' + (key + 1)
  onlineData.currentImg = {
    shopPicture: src,
    position: 'Pic' + row + '-' + (key + 1),
  }
}
async function getNearbyImg(){
  return await Online.getNearbyImg({ shopId: onlineData.pageInfo.shopId }).then(
    (res) => {
      if (res.code == 0) {
        return res.data
      } else {
        Toast(res.message)
        return null
      }
    }
  )
}
function updateNearbyImg() {
  if (!onlineData.currentImg) {
    return false
  }
  onlineData.currentImg.shopId = onlineData.pageInfo.shopId
  Online.updateNearbyImg(onlineData.currentImg).then((res) => {
    if (res.code == 0) {
      onlineData.oldCurrentImg = onlineData.currentImg
      onlineData.isEdit = false
    } else {
      Toast(res.message)
    }
  })
}
const autoLoginObj = reactive({
  autoLogin: false,
  is4gLogined: null,
})

const autoLoginCb = (res) => {
  autoLoginObj.is4gLogined = false
  autoLoginObj.autoLogin = true
}
onMounted(()=>{
  const url = new URL(location.href)
  const shopId =
    url.searchParams.get('shopId') || url.searchParams.get('shop_id')
  const actId = url.searchParams.get('actId')
  const preview = url.searchParams.get('preview')
  // onlineData.$store.commit('SET_SHOPID', shopId)
  onlineData.pageInfo = {
    shopId,
    actId,
    preview,
  }
  //强登
  loginUtils.login(true, true, logined, false, false, autoLoginCb, null, 5)
  onlineDetail()
})
</script>
<style>
#app {
  margin-top: 0 !important;
}
</style>
<style lang="scss" scoped>
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
}
.storemanagement {
  background: #f9fafc;
  font-size: 13.5px;
  color: #333;
  text-align: left;
  min-height: 101vh;
  h3 {
    height: 45px;
    line-height: 45px;
    color: #999999;
    padding-left: 26.25px;
    .red {
      color: #fd5f5f;
    }
    &.redtitle {
      color: #fd5f5f;
      width: 347px;
      height: 63px;
      background: #fffbfb;
      border: 1px solid #ffe2e2;
      border-radius: 6px;
      margin: 0 14px 16px;
      padding: 5px 13px;
      color: #ff2f2f;
      line-height: 26px;
      font-size: 12px;
    }
    &.redtip {
      padding: 5px 13px;
      color: #ff2f2f;
      line-height: 26px;
      font-size: 12px;
      margin: 0 14px 16px;
      height: 10px;
    }
  }
  .online_p {
    width: 93%;
    margin: 0 auto 7.5px;
    background: #fff;
    border-radius: 11.25px;
    line-height: 45px;
    font-size: 12.750000000000002px;
    padding: 0 22.5px;
    .kdpz_right {
      float: right;
      .van-switch {
        margin-top: 7.5px;
        background: #f3f3f3;
        border: 1px solid #f7f7f7;
      }
    }
    .red {
      color: #fd5f5f;
    }
    .bul {
      color: #38a9ff;
    }
  }
  .online_button {
    clear: both;
    width: 90%;
    margin: 0 auto;
    padding-bottom: 46.5px;
    padding-top: 15px;
    .van-button {
      width: 100%;
      height: 41.25px;
      line-height: 41.25px;
      font-size: 13.5px;
      background-image: linear-gradient(to right, #60c6ff, #8274fe);
    }
  }
  .online_button_disabled .van-button {
    background: #ccc;
    border-color: #ccc;
  }
  .hot_product {
    padding: 0 7.5px 0 11.25px;
    .hot_product_con {
      display: block;
      width: 31%;
      margin: 0 6.375000000000001px 15px 1.875px;
      text-align: center;
      height: 112.5px;
      float: left;
      .hot_product_xzsp {
        align-items: center; /*定义body的元素垂直居中*/
        border: 1px solid #ededed;
        height: 100%;
        background: #fff;
        border-radius: 11.25px;
        position: relative;
        img {
          display: block;
          width: 37.5px;
          margin: 26.25px auto 0;
        }
        p {
          color: #999999;
          line-height: 30px;
        }
        .untinput {
          position: absolute;
          z-index: 1;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          opacity: 0;
          -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        }
      }
      .hot_product_card {
        width: 100%;
        height: 100%;
        background: #fff;
        border-radius: 11.25px;
        overflow: hidden;
        position: relative;
        .goodsItem {
          .image {
            display: block;
            // width: 100%;
            height: 112.5px;
            margin-left: -50%;
          }
        }
        .item-close {
          position: absolute;
          width: 18.75px;
          height: 15px;
          line-height: 14.25px;
          font-size: 13.5px;
          right: 0;
          top: 0;
          color: #fff;
          background: rgba(0, 0, 0, 0.4);
          border-radius: 0 0 0 15px;
        }
      }
    }
  }
}
.padding-top-34 {
  padding-top: 34px;
}

.vue-box {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 15px;
  .file {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    top: 0;
    left: 0;
    z-index: 99;
    cursor: pointer;
  }
  img {
    display: none;
    width: 100%;
    height: 100%;
  }

  h3 {
    text-align: center;
  }
  h1,
  h2 {
    font-weight: normal;
  }
  ul {
    list-style-type: none;
    padding: 0;
  }
  li {
    display: inline-block;
    margin: 0 10px;
  }
  a {
    color: #42b983;
  }
}
</style>
<style>

* {
  margin: 0;
  padding: 0;
}

#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  box-sizing: border-box;
}

/*img {
  display: block;
  margin: 0 auto;
}*/

input[type='file'] {
  outline: none;
  /*margin-top: 20px;*/
}

* {
  margin: 0;
  padding: 0;
}
#fcRotate {
  position: absolute;
  left: 50%;
  bottom: 123.75px;
  width: 37.5px;
  height: 37.5px;
  margin-left: -15px;
  border: none;
  border-radius: 18.75px;
  font-size: 12px;
  background: #fff;
}
#fcRotate::before {
  content: '旋转';
}
#clip_button {
  position: absolute;
  right: 10%;
  bottom: 105px;
  width: 112.5px;
  height: 37.5px;
  line-height: 37.5px;
  border: none;
  border-radius: 18.75px;
  font-size: 15.75px;
  background: #1aad19;
  color: #fff;
}
#cancel_clip {
  position: absolute;
  left: 10%;
  bottom: 105px;
  width: 112.5px;
  height: 37.5px;
  line-height: 37.5px;
  border: none;
  border-radius: 18.75px;
  font-size: 15.75px;
  color: #fff;
  background: #e64340;
}

#clip_container.container {
  z-index: 99999;
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 1);
  width: 100%;
  height: 100vh;
}

#clip_container.container > div {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

#clip_image {
  max-width: 100%;
}

.cropper-container {
  font-size: 0;
  line-height: 0;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  direction: ltr;
  -ms-touch-action: none;
  touch-action: none;
}

.crop_loading,
.crop_success {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9;
}

.crop_loading .crop_content {
  position: absolute;
  top: 50%;
  left: 50%;
  text-align: center;
  background: #000;
  opacity: 0.9;
  height: 66px;
  width: 140px;
  vertical-align: middle;
  color: #fff;
  padding-top: 20px;
  font-size: 16px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.crop_loading .crop_content img {
  margin-top: 15px;
  margin-bottom: 10px;
}

.crop_success .crop_success_text {
  position: absolute;
  top: 50%;
  left: 50%;
  text-align: center;
  background: #000;
  opacity: 0.9;
  width: 120px;
  height: 30px;
  color: #fff;
  line-height: 30px;
  font-size: 16px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.cropper-container img {
  /* Avoid margin top issue (Occur only when margin-top <= -height) */
  display: block;
  min-width: 0 !important;
  max-width: none !important;
  min-height: 0 !important;
  max-height: none !important;
  width: 100%;
  height: 100%;
  image-orientation: 0deg;
}

.cropper-wrap-box,
.cropper-canvas,
.cropper-drag-box,
.cropper-crop-box,
.cropper-modal {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.cropper-wrap-box {
  overflow: hidden;
}

.cropper-drag-box {
  opacity: 0;
  background-color: #fff;
}

.cropper-modal {
  opacity: 0.5;
  background-color: #000;
}

.cropper-view-box {
  display: block;
  overflow: hidden;

  width: 100%;
  height: 100%;

  outline: 1px solid #39f;
  outline-color: rgba(51, 153, 255, 0.75);
}

.cropper-dashed {
  position: absolute;

  display: block;

  opacity: 0.5;
  border: 0 dashed #eee;
}

.cropper-dashed.dashed-h {
  top: 33.33333%;
  left: 0;
  width: 100%;
  height: 33.33333%;
  border-top-width: 1px;
  border-bottom-width: 1px;
}

.cropper-dashed.dashed-v {
  top: 0;
  left: 33.33333%;
  width: 33.33333%;
  height: 100%;
  border-right-width: 1px;
  border-left-width: 1px;
}

.cropper-center {
  position: absolute;
  top: 50%;
  left: 50%;

  display: block;

  width: 0;
  height: 0;

  opacity: 0.75;
}

.cropper-center:before,
.cropper-center:after {
  position: absolute;
  display: block;
  content: ' ';
  background-color: #eee;
}

.cropper-center:before {
  top: 0;
  left: -3px;
  width: 7px;
  height: 1px;
}

.cropper-center:after {
  top: -3px;
  left: 0;
  width: 1px;
  height: 7px;
}

.cropper-face,
.cropper-line,
.cropper-point {
  position: absolute;

  display: block;

  width: 100%;
  height: 100%;

  opacity: 0.1;
}

.cropper-face {
  top: 0;
  left: 0;

  background-color: #fff;
}

.cropper-line {
  background-color: #39f;
}

.cropper-line.line-e {
  top: 0;
  right: -3px;
  width: 5px;
  cursor: e-resize;
}

.cropper-line.line-n {
  top: -3px;
  left: 0;
  height: 5px;
  cursor: n-resize;
}

.cropper-line.line-w {
  top: 0;
  left: -3px;
  width: 5px;
  cursor: w-resize;
}

.cropper-line.line-s {
  bottom: -3px;
  left: 0;
  height: 5px;
  cursor: s-resize;
}

.cropper-point {
  width: 5px;
  height: 5px;

  opacity: 0.75;
  background-color: #39f;
}

.cropper-point.point-e {
  top: 50%;
  right: -3px;
  margin-top: -3px;
  cursor: e-resize;
}

.cropper-point.point-n {
  top: -3px;
  left: 50%;
  margin-left: -3px;
  cursor: n-resize;
}

.cropper-point.point-w {
  top: 50%;
  left: -3px;
  margin-top: -3px;
  cursor: w-resize;
}

.cropper-point.point-s {
  bottom: -3px;
  left: 50%;
  margin-left: -3px;
  cursor: s-resize;
}

.cropper-point.point-ne {
  top: -3px;
  right: -3px;
  cursor: ne-resize;
}

.cropper-point.point-nw {
  top: -3px;
  left: -3px;
  cursor: nw-resize;
}

.cropper-point.point-sw {
  bottom: -3px;
  left: -3px;
  cursor: sw-resize;
}

.cropper-point.point-se {
  right: -3px;
  bottom: -3px;
  width: 20px;
  height: 20px;
  cursor: se-resize;
  opacity: 1;
}

@media (min-width: 768px) {
  .cropper-point.point-se {
    width: 15px;
    height: 15px;
  }
}

@media (min-width: 992px) {
  .cropper-point.point-se {
    width: 10px;
    height: 10px;
  }
}

@media (min-width: 1200px) {
  .cropper-point.point-se {
    width: 5px;
    height: 5px;
    opacity: 0.75;
  }
}

.cropper-point.point-se:before {
  position: absolute;
  right: -50%;
  bottom: -50%;
  display: block;
  width: 200%;
  height: 200%;
  content: ' ';
  opacity: 0;
  background-color: #39f;
}

.cropper-invisible {
  opacity: 0;
}

.cropper-bg {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMzTjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC');
}

.cropper-hide {
  position: absolute;

  display: block;

  width: 0;
  height: 0;
}

.cropper-hidden {
  display: none !important;
}

.cropper-move {
  cursor: move;
}

.cropper-crop {
  cursor: crosshair;
}

.cropper-disabled .cropper-drag-box,
.cropper-disabled .cropper-face,
.cropper-disabled .cropper-line,
.cropper-disabled .cropper-point {
  cursor: not-allowed;
}
</style>
