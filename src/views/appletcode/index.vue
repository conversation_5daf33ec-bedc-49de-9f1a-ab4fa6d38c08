<template>
  <div class="appletcode">
    <Shopname
      v-if="data.showShopName"
      content="店铺推广"
      bg-color="#efefef"
      :show-back="true"
    />
    <div :class="{'padding-top-34':data.showShopName}">
      <div class="title">
        <van-row>
          <van-col span="8" class="textCenter">
            边长(cm)
          </van-col>
          <van-col span="8" class="textCenter">
            建议扫描距离(m)
          </van-col>
          <van-col span="6" class="textCenter">
            小程序码下载
          </van-col>
        </van-row>
      </div>
      <div v-for="(item,key) in data.appletcodeOption" :key="key" class="listTtem">
        <van-row>
          <van-col span="8" class="textCenter">
            {{ item.imgwWidth }}
          </van-col>
          <van-col span="8" class="textCenter">
            {{ item.distance }}
          </van-col>
          <van-col span="7" class="textCenter">
            <span class="icon icon_download" @click="getUrl(item)"></span>
          </van-col>
          <van-col span="20" offset="3" class="font12">
            {{ item.describe }}
          </van-col>
        </van-row>
      </div>
      <div class="listTtem">
        <van-row>
          <van-col span="9" offset="1" class="textCenter line-height200">
            小程序码样式参考图
          </van-col>
          <van-col span="12" class="textCenter">
            <img src="@/assets/index_normal/xcxqrcode.png" alt="" class="xcxqrcode">
          </van-col>
        </van-row>
      </div>
      <van-dialog
        v-model="data.show"
        :show-confirm-button="false"
        :close-on-click-overlay="true"
      >
        <div ref="qrCodeContainer" class="qrCodeContainer">
          <img :src="data.appletCodeUrl " alt="" width="280px">
        </div>
        <!-- <div class="btn_container">
          <a v-if="!ua.isWechat" href="javascript:void(0);" class="bt_save bt_only" @click="share('wx')">分享</a>
        </div> -->
      </van-dialog>
    </div>
  </div>
</template>

<script setup>
import Vue,{onMounted, reactive,getCurrentInstance} from 'vue'
import { Col, Row ,Dialog ,Toast} from 'vant'
import appletcodeApi from '@/api/appletcode'
import loginUtils from "@/utils/login"
import {savePhoto} from "@/utils/share"
import Shopname from "@/components/index/headercon.vue"
import UA from "@/utils/ua"
import {getImgUrl} from '@/utils/utils'
Vue.use(Col).use(Row).use(Dialog).use(Toast)
let data = reactive({
  appletcodeOption:[
    {
      imgwWidth:'8cm',
      distance:'0.5m',
      describe:'适用于公众号文章，海报，名片，标签等推广场景',
      width:258
    },
    {
      imgwWidth:'12cm',
      distance:'0.8m',
      describe:'适用于海报，商品/礼品包装等推广场景',
      width:344
    },
    {
      imgwWidth:'15cm',
      distance:'1m',
      describe:'适用于微信好友，朋友圈分享等推广场景',
      width:430
    },
    {
      imgwWidth:'30cm',
      distance:'1.5m',
      describe:'适用于商品/礼品包装，易拉宝，灯箱等推广场景',
      width:860
    },
    {
      imgwWidth:'50cm',
      distance:'2.5m',
      describe:'适用于易拉宝，灯箱等推广场景',
      width:1280
    }
  ],
  isLogined:null,
  user:null,
  show:false,
  appletCodeUrl:"",
  shopId:null,
  showShopName:!UA.isWechat &&!UA.isWechatWork &&!UA.isApp &&!UA.isIosQQ &&!UA.isAndroidQQ
})
const logined = (res) => {
  data.user = res
  data.isLogined = res && res.UserName > ""
}
const getUrl = (item) => {
  appletcodeApi.getappletcode({
    width:item.width,
    hyaline:false,
    page:"pages/home/<USER>"
  }).then(res => {
    if(res.code) {
      Toast(res.message)
    }else{
      data.appletCodeUrl = getImgUrl(res.data.headers['fileid'],"material")
      data.show = true
    }
  })
}
const downLoad = (item) => {
  appletcodeApi.getappletcode({
    width:item.width,
    hyaline:true,
    page:"pages/home/<USER>"
  }).then(res => {
    if(res.code) {
      Toast(res.message)
    }else{
      const fileName = decodeURIComponent( // 获取文件名
        res.data.headers['content-disposition'].split('=')[1]
      )
      const blob = new Blob([res.data.body])
      if (data.ua.isApp) {
        savePhoto(URL.createObjectURL(blob))
      }else {
        let eleLink = document.createElement('a')
        eleLink.download = fileName
        eleLink.style.display = 'none'
        eleLink.href = URL.createObjectURL(blob)
        eleLink.click()
      }
    }
  })
}
const  downLoadImg = (res) => {
  const fileName = decodeURIComponent( // 获取文件名
    res.data.headers['content-disposition'].split('=')[1]
  )
  const blob = new Blob([res.data.body])
  let eleLink = document.createElement('a')
  eleLink.download = fileName
  eleLink.style.display = 'none'
  eleLink.href = URL.createObjectURL(blob)
  eleLink.click()
}
onMounted(()=>{
  data.ua = UA
  const url = new URL(location.href)
  data.shopId = url.searchParams.get('shopId')
  const getVueInstance = getCurrentInstance()
  const proxy = getVueInstance ? getVueInstance.proxy : null
  if (proxy) {
    proxy.$store.commit("SET_SHOPID",data.shopId)
  }
  loginUtils.login(true,true,logined,false,false,"",null,5)
})
</script>

<style lang="scss" scoped>
  .appletcode{
    font-size: 14px;
    color: #2c2c2c;
    font-weight: 400;
    background: rgb(243,243,243);
    .title{
      line-height:34px;
      height:25px;
    }
  }
  .padding-top-34{
    padding-top:34px;
  }
  .font12{
    font-size: 12px;
  }
  .textCenter{
    text-align: center;
  }
  .icon_download{
    background: url(~@/assets/index_normal/icon_download.png) 0 0 no-repeat;
    width:24px;
    height: 24px;
    display:inline-block;
    background-size:contain;
    transform: translate(0,6px);
  }
  .listTtem{
    background: #fff;
    width:344px;
    border-radius: 10px;
    margin:10px auto 0;
    line-height: 36px;
    padding:9px 0;
  }
  .xcxqrcode{
    width:180px;
    height:180px;
  }
  .line-height200{
    line-height:192px;
  }
  .qrCodeContainer{
    text-align: center;
    padding:20px 0;
  }
</style>
