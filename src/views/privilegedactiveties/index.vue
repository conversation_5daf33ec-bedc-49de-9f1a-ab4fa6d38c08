<template>
  <div>
    <Shopname 
      v-if="data.showShopname" 
      content="特权申请" 
      bg-color="#efefef"
      :show-back="true"
    />
    <div
      v-if="data.myuser"
      class="privilegedactiveties"
      :class="{'padding-top-34':data.showShopname}"
    >
      <List
        v-model="data.loading"
        :finished="data.finished"
        class="vant-clearfix"
        :finished-text="data.privilegedlist&&data.privilegedlist.length>0?'没有更多啦……':''"
        @load="getList"
      >
        <template v-if="data.privilegedlist && data.privilegedlist.length>0">
          <div v-for="(item,key) in data.privilegedlist" :key="key" class="listTtem">
            <div>
              <img :src="item.cover" />
            </div>
            <div class="listItemContent">
              <div class="listItemName">
                <div>{{ item.activityName }}</div>
              </div>
              <div class="listItemBtn">
                <p @click="goToForm(item)">
                  立即申请
                </p>
              </div>
            </div>
          </div>
        </template>
        <div v-else-if="data.privilegedlist && data.privilegedlist.length==0" class="privilegedactiveties noActivity">
          <img src="~@/assets/index_img/ice_tab_no.png" />
          <p>暂无特权活动，请耐心等待</p>
        </div>
        <div v-else></div>
      </List>
    </div>
    <PrivilegedFooter :login-callback="loginCallback" />
  </div>
</template>

<script setup>
import Vue, { reactive,getCurrentInstance,onMounted } from 'vue'
import { List , Toast } from 'vant'
import PrivilegedApi from '@/api/privileged'
import {getImgUrl} from '@/utils/utils'
import UA from "@/utils/ua"
import Shopname from "@/components/index/headercon"
import PrivilegedFooter from "@/views/privilegedactiveties/privilegedFooter.vue"

const data = reactive({
  privilegedlist:null,
  sendData:{
    pageNum: 1,
    pageSize: 15
  },
  loading:false,
  finished: false,
  ua:null,
  myuser:null,
  showShopname:!UA.isWechat&&!UA.isWechatWork&&!UA.isApp&&!UA.isIosQQ&&!UA.isAndroidQQ,
  proxy:null
})

const loginCallback = (res) => {
  data.myuser = res
}

const getList = () => {
  PrivilegedApi.getSpecialActivityList(data.sendData).then(res=>{
    if(data.sendData.pageNum==1){
      data.privilegedlist = []
    }
    if(res.code) {
      Toast(res.message)
      data.loading = false
      data.finished = true
    }else {
      if(!(res.data&&res.data.list&&res.data.list.length>0)){
        data.loading = false
        data.finished = true
        return false
      }
      res.data.list.forEach((value)=>{
        value.cover = value.cover ? getImgUrl(value.cover,"material"):require('@/assets/privileged/swiper_img.png')
        data.privilegedlist.push(value)
      })
      data.loading = false
      if(res.data.pages > data.sendData.pageNum){
        data.sendData.pageNum ++
      }else{
        data.finished = true
      }
    }
  })
}

const goToForm = (item) => {
  if(!data.proxy){
    return false
  }
  if(item.precondition===1){
    data.proxy.$router.push({
      path:'/privilegedactivities/form.html',
      query:{
        activityId:item.activityId,
        shopId:data.myuser.shopId
      }
    })
  }else{
    data.proxy.$router.push({
      path:'/privilegedactivities/qrcode.html',
      query:{
        activityId:item.activityId,
        shopId:data.myuser.shopId
      }
    })
  }
  
}

onMounted(()=>{
  const getVueInstance = getCurrentInstance()
  data.proxy = getVueInstance ? getVueInstance.proxy : null
})
</script>

<style lang="scss" scoped>
  .privilegedactiveties{
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,'Source Sans Pro', 'Trebuchet MS', Arial;
    font-size: 14px;
    color: #2c2c2c;
    font-weight: 400;
    background: rgb(247, 248, 249);
    padding:15px;
    min-height: calc(100vh - 148px);
    .title{
      line-height:34px;
      height:25px;
    }
    &.noActivity{
      padding-top:242px;
      text-align: center;
      line-height:30px;
      font-size: 16px;
      color: #757575;
      font-weight: 400;
    }
  }
  .font12{
    font-size: 12px;
  }
  .textCenter{
    text-align: center;
  }
  .icon_download{
    background: url(~@/assets/index_normal/icon_download.png) 0 0 no-repeat;
    width:24px;
    height: 24px;
    display:inline-block;
    background-size:contain;
    transform: translate(0,6px);
  }
  .listTtem{
    background: #fff;
    border-radius: 10px;
    margin-bottom:15px;
    // line-height: 36px;
    overflow: hidden;
    &:last-child{
      margin-bottom:0;
    }
    img{
      width:100%;
    }
    .listItemContent{
      padding:15px;
      text-shadow: 0px 2px 4px 0px rgba(0,0,0,0.10); 
      display: flex;
      align-items: center;
    }
    .listItemName{
      width:227px;
      padding-right:5px;
      font-size: 16px;
      font-family: PingFangSC, PingFangSC-Medium;
      font-weight: 500;
      text-align: left;
      color: #333333;
      line-height: 32px;
      text-shadow: 0px 2px 4px 0px rgba(0,0,0,0.10); 
    }
    .listItemBtn{
      p{
        width: 88px;
        height: 32px;
        line-height: 32px;
        opacity: 1;
        background: linear-gradient(90deg,#5fc8ff, #8372fe);
        border-radius: 16px;
        box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.10);
        font-size: 14px;
        font-weight: 500;
        color: #ffffff;
        text-align: center;
        text-shadow: 0px 2px 4px 0px rgba(0,0,0,0.10);
      }
    }
  }
  .padding-top-34{
    padding-top:49px; //34+15
  }
  .min-height-160{
    min-height: calc(100vh - 80px);
  }
</style>