<template>
  <div>
    <Shopname
      v-if="data.showShopname"
      content="特权领取表单填写"
    />
    <div
      class="privilegedform"
      :class="{'padding-top-34':data.showShopname}"
    >
      <div class="title">
        填写领取信息
      </div>
      <Form
        label-align="right"
        :show-error-message="true"
        :show-error="false"
      >
        <Field
          v-model="data.formData.name"
          label="姓名"
          name="name"
          placeholder="请输入用户姓名"
        />
        <Field
          v-model="data.formData.receiveNumber"
          type="receiveNumber"
          name="receiveNumber"
          label="手机号"
          placeholder="请输入用户手机号"
        />
        <Field
          v-model="data.formData.serialNumber"
          type="serialNumber"
          name="serialNumber"
          label="终端串号"
          placeholder="请输入终端串号"
        />
        <div class="formContainer">
          <Button round block class="formBtn" @click="onSubmit">
            提交并生成特权领取二维码
          </Button>
          <Button round block class="formBtn cancleBtn" @click="goBack">
            返回
          </Button>
        </div>
      </Form>
    </div>
    <PrivilegedFooter :login-callback="loginCallback" />
  </div>
</template>

<script setup>
import Vue, { reactive,ref,onMounted,getCurrentInstance } from 'vue'
import PrivilegedApi from '@/api/privileged'
import { Form , Field , Button ,Toast } from 'vant'
import UA from "@/utils/ua"
import { validateMobile , validateNumerCase } from "@/utils/utils"
import Shopname from "@/components/index/headercon"
import PrivilegedFooter from "@/views/privilegedactiveties/privilegedFooter.vue"
Vue.use(Form ).use(Field).use(Button)

const data = reactive({
  formData:{
    name:'',
    receiveNumber:'',
    serialNumber:'',
    activityId:null
  },
  qrCodeData:{},
  showShopname:!UA.isWechat&&!UA.isWechatWork&&!UA.isApp&&!UA.isIosQQ&&!UA.isAndroidQQ,
  proxy:null,
  myuser:null
})

const getActiveDetail = () =>{
  PrivilegedApi.getSpecialActivityDetail({activityId:data.formData.activityId}).then(res=>{
    if(res.code || !(res.data&&res.data.precondition==1)) {
      if(!data.proxy){
        return false
      }
      data.proxy.$router.push({
        path:'/privilegedactivities/index.html'
      })
    }
  })
}

const loginCallback = (res) =>{
  data.myuser = res
  getActiveDetail()
}

const onSubmit = (values) => {
  //做完验证
  if(!data.formData.name || data.formData.name.length>20){
    Toast('请正确填写姓名，限20位汉字')
    return false
  }
  if(!data.formData.receiveNumber || !validateMobile(data.formData.receiveNumber)){
    Toast('请正确输入用户手机号，手机号必须是11位的移动号码')
    return false
  }
  if(!data.formData.serialNumber || data.formData.serialNumber.length!==15 || !validateNumerCase(data.formData.serialNumber)){
    Toast('请正确填写15位终端串号')
    return false
  }
  PrivilegedApi.getSpecialActivityInsert(data.formData).then(res=>{
    if(res.code) {
      Toast(res.message)
    }else {
      if(!data.proxy){
        return false
      }
      data.proxy.$router.replace({
        path:'/privilegedactivities/qrcode.html',
        query:{
          activityId:data.formData.activityId,
          shopId:data.myuser.shopId
        }
      })
    }
  })

}

const goBack = () =>{
  if(!data.proxy){
    return false
  }
  data.proxy.$router.go(-1)
}
const getPartParams = (searchKey) =>{
  if(searchKey){
    let urlData = new URL(location.href)
    searchKey.forEach(item=>{
      data.qrCodeData[item]  = urlData.searchParams.get(item)
    })
  }
}

onMounted(() =>{
  let urlData = new URL(location.href)
  data.formData.activityId = urlData.searchParams.get('activityId')
  const getVueInstance = getCurrentInstance()
  data.proxy = getVueInstance ? getVueInstance.proxy : null
  if(!data.formData.activityId && data.proxy){
    data.proxy.$router.push({
      path:'/privilegedactivities/index.html'
    })
  }
})
</script>

<style lang="scss" scoped>
  $grey: #f7f8f9;
  .privilegedform{
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,'Source Sans Pro', 'Trebuchet MS', Arial;
    font-size: 14px;
    color: #666;
    font-weight: 400;
    background: $grey;
    min-height:calc(100vh - 148px);
    .title{
      line-height:47px;
      height:47px;
      padding-left:17px;
    }
  }

  .formContainer{
    margin:26px 26px 0;
  }
  .formBtn{
    width: 324px;
    height: 46px;
    line-height: 46px;
    opacity: 1;
    background: linear-gradient(90deg,#5fc8ff, #8372fe);
    border-radius: 25px;
    font-size: 16px;
    font-weight: 400;
    color: #ffffff;
    text-align: center;
    margin-bottom:15px;
  }
  .cancleBtn{
    border: 1px solid #7ab3f9;
    background: #fff;
    color: #4fa1f8;
  }
  .van-form{
    background: #fff;
    padding-bottom:11px;
    :deep(.van-field__label::before){
      content: "*";
      display: inline;
      color:red;
    }
  }
  .padding-top-34{
    padding-top:34px;
  }
</style>
