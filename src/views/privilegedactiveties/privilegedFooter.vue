<template>
  <div>
    <!-- 带有登录功能和iconlist的底部导航 -->
    <Footers v-if="myuser && myuser.shopId && currentIconList" :list="currentIconList" :user="myuser" :tpl-id="4" />
  </div>
</template>
<script setup>
import Footers from '@/components/myfooter'
import loginUtils from "@/utils/login"
import {reactive,ref,onMounted,defineProps,getCurrentInstance,computed,watch} from "vue"

const iconList = reactive({
  shopindex:{
    inactive: require("@/assets/my/other.png"),
    active: require("@/assets/my/others-active.png"),
    title: "店铺首页",
    key:'shopindex',
    links: '',
    isactive:true,
    showLink:true
  },
  onlineshop:{
    inactive: require("@/assets/my/onlineShop.png"),
    active: require("@/assets/my/onlineShop-active.png"),
    title: "在线看店",
    key:'onlineshop',
    links: '',
    isactive:false,
    showLink:false
  },
  service:{
    inactive: require("@/assets/my/customerService.png"),
    active: require("@/assets/my/customerService-active.png"),
    title: "客服",
    key:'service',
    links: '',
    isactive:false,
    showLink:false
  },
  manage:{
    inactive: require("@/assets/my/management.png"),
    active: require("@/assets/my/management-active.png"),
    title: "店铺管理",
    key:'manage',
    links: '',
    isactive:false,
    showLink:true
  },
  live:{
    inactive: require("@/assets/my/live.png"),
    active: require("@/assets/my/live-active.png"),
    title: "直播",
    key:'live',
    links: '',
    isactive:false,
    showLink:true
  },
  my:{
    inactive: require("@/assets/my/my.png"),
    active: require("@/assets/my/my-active.png"),
    title: "我的",
    key:'my',
    links: '',
    isactive:false,
    showLink:true
  }
})

const props = defineProps({
  loginCallback:{
    type:Function,
    default:()=>{
      return null
    }
  },
  propIconList:{
    type:Object,
    default:(data)=>{
      return data
    }
  },
  userInfo:{
    type:Object,
    default:()=>{
      return null
    }
  },
  needLogin:{
    type:Boolean,
    default:true
  }
})
const currentIconList = computed(()=>{
  return props.propIconList ? props.propIconList : iconList
})

watch(()=>props.userInfo,(val)=>{
  if(!props.needLogin){
    myuser.value = props.userInfo
  }
})

const myuser = ref(null)
const shopId = ref(null)
const logined = (res) => {
  myuser.value = res
  props.loginCallback && props.loginCallback(res)
  if(!myuser.value.shopId){
    document.getElementsByClassName("icpinfo")[0].style.marginBottom = "0"
  }
}
onMounted(() => {
  const url = new URL(location.href)
  shopId.value = url.searchParams.get('shopId')
  const getVueInstance = getCurrentInstance()
  if(getVueInstance && getVueInstance.proxy){
    getVueInstance.proxy.$store.commit("SET_SHOPID",shopId.value)
  }
  if(props.needLogin){
    loginUtils.login(true,true,logined,false,false,logined,"0",5)
  }
  document.body.scrollTop = 0
})
</script>

<style>
.icpinfo{
  margin-bottom: 68px ;
}
</style>
