<template>
  <div>
    <Shopname 
      v-if="showShopname" 
      content="特权领取表单填写" 
    />
    <div 
      class="privilegedqrcode"
      :class="{'padding-top-34':showShopname}"
    >
      <div class="qrcodeContainer">
        <div ref="qrCodeUrl" class="qrcode"></div>
        <div class="formBtn" @click="goToList">
          关闭并返回列表
        </div>
      </div>
    </div>
    <PrivilegedFooter :login-callback="loginCallback" />
  </div>
</template>

<script setup>
import {reactive,ref,onMounted, getCurrentInstance} from 'vue'
import {Toast} from "vant"
import QRCode from 'qrcodejs2'
import PrivilegedApi from '@/api/privileged'
import UA from "@/utils/ua"
import Shopname from "@/components/index/headercon"
import PrivilegedFooter from "@/views/privilegedactiveties/privilegedFooter.vue"

const data = reactive({
  formData:{},
  qrcodeurl:'',
  activityId:null
})
const showShopname = ref(!UA.isWechat&&!UA.isWechatWork&&!UA.isApp&&!UA.isIosQQ&&!UA.isAndroidQQ)
let formData={},
  qrcodeurl='',
  activityId=null,
  proxy=null

const loginCallback = () =>{
  getActiveDetail()
}
const getActiveDetail = () => {
  PrivilegedApi.getSpecialActivityDetail({activityId:activityId}).then(res=>{
    if(!res.code && res.data) {
      formData = {goodsId:res.data.goodsId, skuId:res.data.skuId , goodsType:res.data.goodsType }
      getSharingGoodsLink()
    }else{
      goToList()
    }
  })
}
const goToList = () =>{
  if(proxy){
    proxy.$router.go(-1)
  }
}

const qrCodeUrl = ref(null)
const creatQrCode = () => {
  //防止dialog首次加载不出二维码 在dialog上加@opened 回调
  new QRCode(qrCodeUrl.value, {
    text: qrcodeurl,
    width: 200,
    height: 200,
    colorDark: '#000000',
    colorLight: '#ffffff',
    correctLevel: QRCode.CorrectLevel.H
  })
}
const getPartParams = (searchKey) =>{
  if(searchKey){
    let urlData = new URL(location.href)
    searchKey.forEach(item=>{
      formData[item]  = urlData.searchParams.get(item)
    })
  }
}
const getSharingGoodsLink = () => {
  PrivilegedApi.getSharingGoodsLink(formData).then(res=>{
    if(res.code) {
      Toast(res.message)
    }else {
      qrcodeurl = res.data.sharingGoodsLink
      creatQrCode()
    }
  })
}
onMounted(()=>{
  let urlData = new URL(location.href)
  activityId = urlData.searchParams.get('activityId')
  const getVueInstance = getCurrentInstance()
  proxy = getVueInstance ? getVueInstance.proxy : null
  if(!activityId && proxy){
    proxy.$router.push({
      path:'/privilegedactivities/index.html'
    })
  }
})
</script>

<style lang="scss" scoped>
  .privilegedqrcode{
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,'Source Sans Pro', 'Trebuchet MS', Arial;
    font-size: 14px;
    color: #666;
    font-weight: 400;
    background: #f7f8f9;
    padding-top:20px;
    min-height:calc(100vh - 148px);
    .qrcodeContainer{
      width:327px;
      height:514px;
      margin:0 auto;
      background:url(~@/assets/privileged/qrcodebg.png);
      background-size: contain;
      padding-top:186px;
      .qrcode{
        width:200px;
        height: 200px;
        margin:0 auto;
      }
      :deep(img), :deep(canvas) {
        margin:0 auto !important;
      }
    }
  }
  .formBtn{
    width: 185px;
    height: 39px;
    line-height: 39px;
    opacity: 1;
    background: linear-gradient(90deg,#5fc8ff, #8372fe);
    border-radius: 31px;
    font-size: 16px;
    font-weight: 400;
    color: #ffffff;
    text-align: center;
    margin:50px auto;
  }
  .padding-top-34{
    padding-top:34px;
  }
</style>