export default [
  {
    path: '/privilegedactivities/index.html',
    name: 'privilegedactivities',
    component: resolve => require(['./index.vue'], resolve),
    meta: {
      title: '特权申请',
      login:true,
      role:[1,2]
    }
  },
  {
    path: '/privilegedactivities/form.html',
    name: 'privilegedform',
    component: resolve => require(['./form.vue'], resolve),
    meta: {
      title: '特权领取表单填写',
      login:true,
      role:[1,2]
    }
  },
  {
    path: '/privilegedactivities/qrcode.html',
    name: 'privilegedqrcode',
    component: resolve => require(['./qrcode.vue'], resolve),
    meta: {
      title: '特权领取表单填写',
      login:true,
      role:[1,2]
    }
  }
]
