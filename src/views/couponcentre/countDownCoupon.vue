<template>
  <span :endTime="props.endTime" :endText="props.endText">
    <slot>{{ data.content }}</slot>
  </span>
</template>

<script setup>
import Vue, {
  getCurrentInstance,
  reactive,
  onMounted,
  defineProps,
  watch,
} from 'vue'
const data = reactive({
  content: '',
  item:{}
})
const props = defineProps({
  endTime: { type: String, default: '' },
  endText: { type: String, default: '活动已结束' },
  item: {
    type: Object,
    default() {
      return {}
    },
  },
})
watch(
  () => props.endTime,
  (val) => {
    countdowm(val)
  }
)
watch(
  () => props.item,
  (val) => {
    data.item = val
  },
  {deep:true}
)
onMounted(() => {
  countdowm(props.endTime)
  countdownSetInterval(props.endTime)
})
const countdowm = (timestamp, timer) => {
  let nowTime = new Date()
  let endTime = new Date(timestamp * 1000)
  let t = endTime.getTime() - nowTime.getTime()
  if (t > 0) {
    let day = Math.floor(t / 86400000)
    let hour = Math.floor((t / 3600000) % 24)
    let min = Math.floor((t / 60000) % 60)
    let sec = Math.floor((t / 1000) % 60)
    hour = hour < 10 ? '0' + hour : hour
    min = min < 10 ? '0' + min : min
    sec = sec < 10 ? '0' + sec : sec
    let format = ''
    if (day > 0) {
      let ms = day * 24
      let ms2 = parseInt(hour) + parseInt(ms)
      format = `${ms2}:${min}:${sec}`
    }
    if (day <= 0 && hour > 0) {
      format = `${hour}:${min}:${sec}`
    }
    if (day <= 0 && hour <= 0) {
      format = `00:${min}:${sec}`
    }
    data.content = format
  } else {
    if (timer) clearInterval(timer)
    data.content = props.endText
    if(data.item.pcardStatus===3){
      data.item.pcardStatus=1
    }
  }
}
const countdownSetInterval = (timestamp) => {
  let timer = setInterval(function() {
    countdowm(timestamp, timer)
  }, 1000)
}
</script>
