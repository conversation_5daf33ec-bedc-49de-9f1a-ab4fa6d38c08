<template>
  <div class="body">
    <div v-if="data.isApp" class="header">
      <van-icon name="arrow-left" class="back" @click="back" />
      {{ data.title }}
    </div>
    <div class="title-banner">
      <div class="titban-left">
        <h3>优惠券上新</h3>
        <p>领取您的专属优惠福利券</p>
      </div>
      <img class="titban-rig" src="~@/assets/couponcentre/conp_kq.png" />
    </div>
    <div class="coupon-com">
      <div class="navtap">
        <p
          class="tap tap1"
          :class="[data.current == 1 ? 'current' : '']"
          @click="querySitechConpons(1)"
        >
          移动业务券
        </p>
        <p
          class="tap tap2"
          :class="[data.current == 2 ? 'current' : '']"
          @click="getQueryGoodsCoupons(1)"
        >
          店铺商品券
        </p>
      </div>
      <div class="conent1" :class="[data.current == 2 ? 'conent2' : '']">
        <ul v-if="data.couponQb != null && data.couponQb != 1">
          <li
            v-for="(item, key) in data.couponQb"
            :key="key"
            :class="[
              item.pcardStatus == 2 || item.pcardStatus == 4 ? 'coupzh' : '',
            ]"
          >
            <div v-if="data.current == 1" class="con-left">
              <template v-if="item.couponValue">
                <span
                  v-if="
                    item.pCardType == 'hf0002' ||
                      item.pCardType == 'll0002' ||
                      item.pCardType == 'tc0054'
                  "
                >{{ item.couponValue * 10 }}</span>
                <span v-else>{{ item.couponValue }}</span>
                <span
                  v-if="
                    item.pCardType == 'tc0004' || item.pCardType == 'll0001'
                  "
                >MB</span>
                <span v-else-if="item.pCardType == 'hf0003'">%</span>
                <span
                  v-else-if="
                    item.pCardType == 'hf0002' ||
                      item.pCardType == 'll0002' ||
                      item.pCardType == 'tc0054'
                  "
                >折</span>
                <span
                  v-else-if="
                    item.pCardType == 'hf0000' ||
                      item.pCardType == 'll0000' ||
                      item.pCardType == 'sw0000' ||
                      item.pCardType == 'tc0000' ||
                      item.pCardType == 'qt0000'
                  "
                ></span>
                <span v-else-if="item.pCardType">元</span>
                <span v-else></span>
              </template>
              <template v-else>
                <span class="kop">{{ categoryNameMethods(item) }}</span>
              </template>
            </div>
            <div v-else class="con-left active2">
              <template v-if="item.nominalValue">
                <i class="a1">{{ item.nominalValue }}</i>
                <i class="a2">元</i>
              </template>
              <div class="a3">
                {{ item.limitDesc }}
              </div>
            </div>
            <div v-if="data.current == 1" class="con-rig">
              <h6>{{ item.cardName }}</h6>
              <p class="conent-pfbt">
                {{ item.cardInfo }}
              </p>
              <p>
                {{ item.obtainEffTime.slice(0, 4) }}.{{
                  item.obtainEffTime.slice(4, 6)
                }}.{{ item.obtainEffTime.slice(6, 8) }}
                {{ item.obtainEffTime.slice(8, 10) }}:{{
                  item.obtainEffTime.slice(10, 12)
                }}
                - {{ item.obtainExpTime.slice(0, 4) }}.{{
                  item.obtainExpTime.slice(4, 6)
                }}.{{ item.obtainExpTime.slice(6, 8) }}
                {{ item.obtainExpTime.slice(8, 10) }}:{{
                  item.obtainExpTime.slice(10, 12)
                }}
              </p>
              <a
                v-if="item.pcardStatus == 1"
                href="javascript:void(0)"
                class="buy buy-ljlq"
                @click="receiveconpon(item)"
              >立即领取</a>
              <a
                v-else-if="item.pcardStatus == 3"
                href="javascript:void(0)"
                class="buy buy-dlq"
              >
                <!-- 未开始 -->
                <countDownCouponVue
                  :end-time="filterStartTime(item)"
                  :end-text="' '"
                  :item="item"
                />
              </a>
              <span
                v-else
                class="buy-ice"
                :class="[item.pcardStatus == 2 ? 'buy-ice-ylq' : ' buy-ice-lw']"
              ></span>
            </div>
            <div v-else class="con-rig active2">
              <h6>{{ item.couName }}</h6>
              <p v-if="item.couStartTime || item.couEndTime" class="a4">
                {{ item.couStartTime }} - {{ item.couEndTime }}
              </p>
              <a
                v-if="isCurrent2Status(item) === 3 && !item.pcardStatusLyl"
                href="javascript:void(0)"
                class="buy buy-ljlq"
                style="
                  background: linear-gradient(
                    90deg,
                    #ffa067,
                    #f2922a
                  ) !important;
                "
                @click="getGoodsCoupons(item)"
              >立即领取</a>
              <a
                v-else-if="isCurrent2Status(item) === 1 && !item.pcardStatusLyl"
                href="javascript:void(0)"
                class="buy buy-dlq"
                style="
                  color: rgb(255, 160, 103) !important;
                  border-color: rgb(255, 160, 103) !important;
                "
              >
                <countDownCouponVue
                  :end-time="filterStartTime(item, 2)"
                  :end-text="' '"
                />
              </a>
              <span
                v-if="item.pcardStatusLyl"
                class="buy-ice"
                :class="[item.pcardStatusLyl ? 'buy-ice-ylq' : ' buy-ice-lw']"
              ></span>
            </div>
          </li>
        </ul>
        <div v-else-if="data.couponQb == 1" class="conent-log">
          <div>
            <span class="conlog1"></span>
            <span class="conlog2"></span>
            <span class="conlog3"></span>
            <span class="conlog4"></span>
          </div>
          <p>正在查询优惠券，请稍候…</p>
        </div>
        <div v-else class="conent-logw">
          <img src="~@/assets/couponcentre/conp_wu.png" />
          <p>暂时没有可领取的优惠券</p>
        </div>
      </div>
    </div>
    <VipTk
      v-if="data.member != 1 && data.current == 2"
      :key="1"
      ref="Contact"
      :logined="data.isLogined"
      :preview="data.preview"
      :cardnewvip="data.cardvip"
      :show-share="true"
      :data-list="data.contactData.dataList"
      :checked="false"
      @AddMemberInfoSuccess="AddMemberInfoSuccess"
      @closevip="closevip"
    />

    <Footers :user-info="data.logUser" isactive="shopindex" />
    <LoginDialog
      :islogin="autoLoginObj.is4gLogined"
      :is-force-login="true"
      :isloginfn="logined"
      :is-auto-login="autoLoginObj.autoLogin"
      :show-cancle="false"
    />
  </div>
</template>
<script setup>
import VipTk from '@/components/index/vipTk'
import { Icon, Toast, Field, Cell, Dialog } from 'vant'
import { mapGetters } from 'vuex'
import Vue, { getCurrentInstance, reactive, onMounted } from 'vue'
import loginUtils from '@/utils/login'
import UA from '@/utils/ua'
import couponcentreApi from '@/api/couponcentre'
import Footers from '@/views/my/components/myfooter'
import VipApi from '@/api/vip'
import shopApi from '@/api/shop'
import lodash from 'lodash'
import countDownCouponVue from './countDownCoupon.vue'
import { computed } from '@vue/composition-api'
import LoginDialog from '@/components/login/index'
Vue.use(Icon).use(Toast).use(Field).use(Cell).use(Dialog)
const data = reactive({
  title: '领券中心',
  isApp: false,
  couponQb: 1,
  ywcoupon: 1,
  spcoupon: 1,
  current: 1,
  username: '',
  isLogined: null,
  pageData: null,
  preview: null,
  cardvip: 'cardvip',
  member: '',
  province: null,
  city: null,
  shopId: null,
  tplInfo: null,
  disabled: true,
  hasShop: true,
  tplContent: null,
  configure: 1,
  flag: false,

  logUser: null,
  contactData: {},
})
const url = new URL(location.href)
const actId = url.searchParams.get('actId')
const preview = url.searchParams.get('preview')
data.shopId = url.searchParams.get('shopId')
data.preview = preview

const getVueInstance = getCurrentInstance()
const proxy = getVueInstance ? getVueInstance.proxy : null
const isCurrent2Status = (item) => {
  let start = new Date(item.couStartTime).getTime()
  let end = new Date(`${item.couEndTime} 23:59:59`).getTime()
  let current = new Date().getTime()

  if (current < start) {
    // 未开始
    return 1
  } else if (current > end) {
    // 已结束
    return 2
  } else if (current >= start && current <= end) {
    // 可领取
    return 3
  }
}

const filterStartTime = (item, type) => {
  if (type == 2) {
    return new Date(item.couStartTime).getTime() / 1000 + ' '
  } else {
    const obtainEffTime = item.obtainEffTime
    const time = `${obtainEffTime.slice(0, 4)}/${obtainEffTime.slice(
      4,
      6
    )}/${obtainEffTime.slice(6, 8)} ${obtainEffTime.slice(
      8,
      10
    )}:${obtainEffTime.slice(10, 12)}:${obtainEffTime.slice(12, 14)}`
    const date = new Date(time).getTime() / 1000 + ' '

    return date
  }
}

const categoryNameMethods = (item) => {
  const link = {
    hf01: '话费券',
    ll01: '流量券',
    sw01: '商品券',
    tc01: '业务券',
    hz01: '合作券',
    qt01: '其他',
    qy01: '权益券',
  }

  return link[item.busiType]
}

const closevip = () => {
  querySitechConpons(1)
}

const logined = (res) => {
  data.logUser = res
  data.isLogined = res && res.UserName > ''
  if (data.isLogined) {
    getIndexData()
    querySitechConpons(1)
    ismember()
  }
}
const getIndexData = () => {
  shopApi
    .getActData({
      shopId: data.shopId,
    })
    .then((res) => {
      data.contactData = lodash
        .get(res, 'data.actData', [])
        .find((item) => item.moduleCode == 'contact')
      let {
        actTitle,
        actDec,
        actId,
        tplId,
        shopId,
        provinceId,
        keFuPhone,
        telNumber,
        unifiedChannelId,
        broadbandStatus,
      } = lodash.get(res, 'data', {})
      if (proxy) {
        proxy.$store.commit('SET_PAGEINFO', {
          actTitle,
          actDec,
          actId,
          tplId,
          shopId,
          provinceId,
          unifiedChannelId,
          broadbandStatus,
          keFuPhone,
          telNumber,
        })
        proxy.$store.commit('SET_SHOPID', shopId)
      }
    })
}
const querySitechConpons = (val) => {
  if (data.flag) {
    Toast('您点的太快啦~')
    return false
  }
  data.flag = true
  data.couponQb = 1
  data.current = 1
  couponcentreApi
    .getQuerySitechConpons({ obtainBusi: 'yd', pageSize: '20', pageNum: val })
    .then((res) => {
      data.flag = false
      // console.log(res)
      if (res.code) {
        Toast(res.message)
        data.ywcoupon = null
      } else {
        //Toast('提交成功')
        //pcardStatus 1可领取 2已领取 3未开始 4已抢完
        if (res.data.busiData[0]) {
          //调试定时器专用假数据
          // res.data.busiData[0].obtainEffTime = "20230421185400"
          // res.data.busiData[0].obtainExpTime = "20220422235959"
          // res.data.busiData[0].pcardStatus = 3

          data.ywcoupon = res.data.busiData
        } else {
          data.ywcoupon = null
        }
      }

      if(data.current == 1){
        data.couponQb = data.ywcoupon
      }
    })
}
const receiveconpon = (item) => {
  if (data.flag) {
    Toast('您点的太快啦~')
    return false
  }
  data.flag = true
  couponcentreApi.getReceiveconpon({ batchID: item.batchID }).then((res) => {
    data.flag = false
    // console.log(res)
    if (res.code) {
      //Toast(res.message)
      Dialog({
        message: res.message,
        confirmButtonColor: '#0085D0',
        confirmButtonText: '我知道了',
      })
    } else {
      Dialog({
        message: '领取成功 \n 可以在“我的”—“优惠券”里查看',
        confirmButtonColor: '#0085D0',
        confirmButtonText: '我知道了',
      }).then(() => {
        item.pcardStatus = 2
      })
    }
  })
}
const ismember = () => {
  data.couponQb = null
  VipApi.IsMember({
    shopId: data.shopId,
  }).then((res) => {
    if (res.code == 0) {
      data.member = res.data.member
    }
  })
}
const AddMemberInfoSuccess = () => {
  data.member = 1
  getQueryGoodsCoupons(1)
}
const getQueryGoodsCoupons = (val) => {
  data.couponQb = 1
  data.current = 2

  if (data.member != 1) {
    // console.log('领取会员')
  } else {
    if (data.flag) {
      Toast('您点的太快啦~')
      return false
    }
    data.flag = true

    couponcentreApi
      .getQueryGoodsCoupons({
        shopId: data.shopId,
        pageSize: '20',
        pageNum: val,
      })
      .then((res) => {
        data.flag = false
        if (res.code) {
          Toast(res.message)
          data.spcoupon = null
        } else {
          //Toast('提交成功')
          if (res.data.resultInfo[0]) {
            // res.data.resultInfo[0].couStartTime = '2022/09/10'

            // 去除过期的优惠券
            res.data.resultInfo = res.data.resultInfo.filter((item) => {
              if (isCurrent2Status(item) === 2) {
                return false
              } else {
                return true
              }
            })

            data.spcoupon = res.data.resultInfo
          } else {
            data.spcoupon = null
          }
        }
        data.couponQb = data.spcoupon
      })
  }
}
const etGoodsCoupons = (item) => {
  if (data.flag) {
    Toast('您点的太快啦~')
    return false
  }
  data.flag = true
  const toast = Toast.loading({
    duration: 0, // 持续展示 toast
    forbidClick: true,
    message: '领取中...',
  })
  couponcentreApi
    .getGoodsCoupons({
      couDistId: item.couDistId,
      prdId: item.prdId,
      shopId: data.shopId,
    })
    .then((res) => {
      data.flag = false
      toast.clear()
      if (res.code) {
        //Toast(res.message)
        Dialog({
          message: res.message,
          confirmButtonColor: '#0085D0',
          confirmButtonText: '我知道了',
        })
      } else {
        Dialog({
          message: '领取成功 \n 可以在“我的”—“优惠券”里查看',
          confirmButtonColor: '#0085D0',
          confirmButtonText: '我知道了',
        }).then(() => {
          getQueryGoodsCoupons(1)
        })
      }
    })
}
const back = async() => {
  const isWXMapp = await UA.isWeChatMiniApp()
  if (isWXMapp) {
    window.wx.miniProgram.switchTab({ url: '/pages/home/<USER>' })
  } else {
    history.go(-1)
  }
}
// 4G免登
const autoLoginObj = reactive({
  is4gLogined: false,
  autoLogin: false,
})

const autoLoginCb = (res) => {
  autoLoginObj.is4gLogined = false
  autoLoginObj.autoLogin = true
}
onMounted(() => {
  //强登
  const url = new URL(location.href)
  let tmp = document.cookie.match(/nalogin=([^\s;]+)/i)
  let nalogin =
    url.searchParams.get('nalogin') == 1 || (tmp && tmp[1] == 1) ? false : true
  loginUtils.login(true, true, logined, false, nalogin, autoLoginCb, '0')
  //页头隐藏
  data.isApp = UA.isApp || UA.isWechat ? false : true
})
</script>
<style>
body {
  background: url(~@/assets/couponcentre/conp_bg.png) left top no-repeat #f7f8f9;
  background-size: 100% auto;
  font-size: 12px;
  min-height: 101vh;
  margin-bottom: 68px;
}
.van-icon-arrow-left::before {
  color: #fff;
}
.van-dialog__message {
  font-size: 16px;
}
</style>
<style lang="scss" scoped>
.hide {
  display: none !important;
}
.body {
  min-height: calc(100vh - 73px);
  padding-bottom: 20px;
  .header {
    width: 100%;
    height: 44px;
    line-height: 44px;
    text-align: center;
    font-size: 16px;
    color: #fff;
    position: relative;
    .back {
      position: absolute;
      left: 10px;
      bottom: 13px;
    }
  }
  .title-banner {
    padding: 30px 26px;
    display: flex;
    font-size: 14px;
    align-items: center;
    color: #fff;
    .titban-left {
      flex: 1;
      line-height: 30px;
      font-size: 14px;
      h3 {
        font-size: 18px;
        font-weight: bold;
      }
    }
    .titban-rig {
      width: 67px;
      height: auto;
      display: block;
      margin-right: 14px;
      margin-bottom: 20px;
    }
  }
  .coupon-com {
    width: 93%;
    background: #fff;
    margin: 0 auto;
    border-radius: 12px;
    color: #555555;
    font-size: 16px;
    box-shadow: 0 2px 3px 0 rgba(0, 14, 105, 0.06);
    .navtap {
      display: flex;
      text-align: center;
      position: relative;
      color: rgba(0, 0, 0, 0.5);
      font-size: 14px;
      font-weight: bold;
      border-radius: 12px 12px 0 0;
      background: rgba(0, 14, 105, 0.08);
      height: 52px;
      .tap {
        flex: 1;
        line-height: 40px;
        position: relative;
        &.current {
          background: #fff;
          margin-top: -10px;
          line-height: 58px;
          color: rgba(0, 0, 0, 1);
        }
        &.current:after {
          content: '';
          position: absolute;
          top: 3px;
          width: 20px;
          height: 100%;
          background: #fff;
        }
      }
      .tap1 {
        &.current {
          border-radius: 12px 4px 0 0;
        }
        &.current:after {
          right: -10px;
          transform: rotate(-18deg);
        }
      }
      .tap2 {
        &.current {
          border-radius: 4px 12px 0 0;
        }
        &.current:after {
          left: -9px;
          transform: rotate(18deg);
        }
      }
    }
    .conent1 {
      font-size: 14px;
      margin-top: -10px;
      background: #fff;
      position: relative;
      border-radius: 12px;
      ul {
        padding: 14px;
        li {
          height: 82px;
          margin: 0 auto 10px;
          font-size: 10px;
          background: url(~@/assets/couponcentre/conp_spq.png) left top
            no-repeat;
          background-size: 100% 100%;
          display: flex;
          &.coupzh {
            opacity: 0.7;
          }
          div {
            align-self: center;
            justify-self: center;
          }
          .con-left {
            width: 82px;
            color: #fff;
            padding: 8px 5px;
            letter-spacing: -1px;
            text-align: center;
            word-break: break-all;
            span:nth-child(1) {
              font-size: 24px;
            }
          }
          .con-rig {
            flex: 1;
            position: relative;
            padding-left: 8px;
            &.active2 {
              h6,
              p {
                padding-left: 17px;
              }
              h6 {
                color: #9e5836;
                width: 150px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              p {
                color: #9e5836;
                font-size: 10px;
              }
            }
            h6 {
              color: #2892ff;
              font-size: 15px;
              width: 222px;
              font-weight: bold;
              line-height: 25px;
              margin-bottom: 3px;
              overflow: hidden;
              white-space: nowrap;
            }
            p {
              color: #a8a8a8;
              margin-bottom: 6px;
              &.conent-pfbt {
                width: 150px;
                line-height: 12px;
                margin-bottom: 8px;
                overflow: hidden;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                word-break: break-all;
              }
            }
            .buy {
              position: absolute;
              right: 10px;
              top: 50%;
              border-radius: 13px;
              padding: 0 10px;
              margin-top: -11px;
              font-size: 11px;
              width: 70px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              &.buy-ljlq {
                height: 24px;
                line-height: 23.6px;
                background: linear-gradient(to right, #2892ff, #007eff);
                color: #fff;
              }
              &.buy-dlq {
                height: 23px;
                // height:40px;
                // max-width:50px;
                line-height: 22px;
                color: #2892ff;
                border: 1px solid #2892ff;
              }
            }
            .buy-ice {
              position: absolute;
              top: 50%;
              right: 10px;
              height: 62px;
              width: 62px;
              margin-top: -31px;
              &.buy-ice-ylq {
                background: url(~@/assets/couponcentre/conp_ylq.png) left top
                  no-repeat;
                background-size: 100% 100%;
              }
              &.buy-ice-lw {
                background: url(~@/assets/couponcentre/conp_yqw.png) left top
                  no-repeat;
                background-size: 100% 100%;
              }
            }
          }
        }
      }
      .conent-log,
      .conent-logw {
        text-align: center;
        padding: 10px 0;
        padding-top: 130px;
        min-height: 410px;
        color: #555555;
        line-height: 40px;
      }
      .conent-log {
        div {
          position: relative;
          width: 38px;
          height: 38px;
          margin: 0 auto;
          span {
            position: absolute;
            width: 16px;
            height: 16px;
            border-radius: 8px;
            &.conlog1 {
              left: 0;
              top: 0;
              background: #ebf2fc;
            }
            &.conlog2 {
              right: 0;
              top: 0;
              background: #b5d2f3;
            }
            &.conlog3 {
              left: 0;
              bottom: 0;
              background: #4a90e2;
            }
            &.conlog4 {
              right: 0;
              bottom: 0;
              background: #7fb0ea;
            }
          }
        }
      }
      .conent-logw {
        img {
          display: block;
          margin: 0 auto;
          width: 100px;
        }
      }
      &.conent2 {
        ul {
          li {
            background: url(~@/assets/couponcentre/conp_ywq.png) left top
              no-repeat;
            background-size: 100% 100%;
          }
        }
      }
    }
  }
  .Booked {
    padding: 22px 0;
    background: #fff;
    h1 {
      height: 53px;
      line-height: 53px;
      background: url(~@/assets/index_img/icon_booked.png) 99px 0 no-repeat #fff;
      background-size: 53px 53px;
      text-indent: 170px;
      font-size: 26px;
      color: #0fc81b;
    }
  }

  .kop {
    font-size: 18px !important;
  }
}

.a1 {
  font-size: 18px;
  font-weight: bold;
}
.a2 {
  font-size: 14px;
}
.a3 {
  font-size: 11px;
  margin-top: 5px;
}
.a4 {
  font-size: 10px;
}
</style>
