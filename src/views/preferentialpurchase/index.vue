<template>
  <div style="min-height:100%;">
    <!-- 优惠购机、信用购机、5G金币购机通用 -->
    <Shopname
      v-if="goldData.showShop"
      :content="`一级云店-${iphoneTypeFilter}`"
      :show-back="true"
    />
    <div
      :class="{
        'padding-top-34':goldData.showShop}"
    >
      <!-- 没有数据或者未登录 -->
      <div v-if="!goldData.showContract">
        <div v-if="goldData.notContract" class="contractContainer">
          <div class="notContractMsgImg">
            <img
              v-if="goldData.showErrImgType === 2"
              class="Group3295Img"
              src="~@/assets/preferentialpurchase/noLogin.png"
            />
            <img
              v-if="goldData.showErrImgType === 1"
              class="Group3295Img"
              src="~@/assets/preferentialpurchase/Group3295.png"
            />
          </div>
          <div class="notContractMsg">
            {{ goldData.notContractMsg }}
          </div>
          <div v-if="goldData.shareParam" class="btn goBackBtn" @click="goDetail">
            返回
          </div>
        </div>
      </div>

      <!-- 内容 -->
      <div v-else class="contract-new-warpper">
        <section class="paper">
          <!-- 合约办理手机号 -->
          <div class="blockStyle">
            <div class="title">
              合约办理手机号
            </div>
            <div class="list">
              <ul>
                <li class="active">
                  <span>{{ getPassPhone(goldData.userName) }}</span>
                  <span>{{ goldData.provinceName }}</span>
                </li>
              </ul>
            </div>
          </div>

          <!-- 购买方式 -->
          <div class="blockStyle">
            <div class="title">
              购买方式
            </div>
            <div class="list">
              <ul>
                <li class="active">
                  <span> {{ iphoneTypeFilter }}</span>
                </li>
              </ul>
            </div>
          </div>
        </section>

        <section class="paper">
          <!-- 优惠方案 -->
          <div class="blockStyle">
            <div class="title">
              优惠方案
            </div>
            <div v-if="goldData.iphoneType === iphoneTypeFun.WUGJINBIGOUJI" class="desc">
              合约有效期：
            </div>
            <div class="list">
              <template v-if="goldData.iphoneType === iphoneTypeFun.WUGJINBIGOUJI">
                <div class="switchTab">
                  <ul class="tabStyle">
                    <li
                      v-for="(item, index) in goldData.contractList"
                      :key="index"
                      :class="{ active: goldData.preferentialTabCurIndex === index }"
                      @click="changeMonth(index)"
                    >
                      <span>{{ item.countMonth }}个月</span>
                    </li>
                  </ul>
                </div>
                <div class="tabCon">
                  <ul
                    v-for="(item, index) in goldData.contractList"
                    v-show="goldData.preferentialTabCurIndex === index"
                    :key="index"
                  >
                    <li
                      v-for="(item2, index2) in item.list"
                      :key="index2"
                      :class="{ active: goldData.preferentialCurIndex === index2 }"
                      @click="changeContract(index2)"
                    >
                      <span>{{ returnstr(item2) }}</span>
                    </li>
                  </ul>
                </div>
              </template>
              <template v-else>
                <ul>
                  <li
                    v-for="(item, index) in goldData.contractList"
                    :key="index"
                    :class="{ active: goldData.preferentialCurIndex === index }"
                    @click="changeContract(index,item)"
                  >
                    <span>{{ item.name }} 在网{{ item.countMonth }}个月</span>
                  </li>
                </ul>
              </template>
            </div>
          </div>

          <!-- 优惠详情 -->
          <div class="blockStyle offerDetails">
            <div class="title">
              优惠详情
            </div>
            <div class="textList">
              <!-- 优惠购机, 信用购机 -->
              <template
                v-if="
                  goldData.iphoneType === iphoneTypeFun.YOUHUIGOUJI ||
                    goldData.iphoneType === iphoneTypeFun.XINYONGGOUJI
                "
              >
                <dl v-if="goldData.currentContract">
                  <dt>
                    <span>优惠档位名称</span>：
                    <span>{{ goldData.currentContract.name }}</span>
                  </dt>
                  <dt>
                    <span>承诺在网时间</span>：
                    <span>{{ goldData.currentContract.countMonth }}个月</span>
                  </dt>
                </dl>
              </template>

              <!-- 5G金币购机 -->
              <template v-else-if="goldData.iphoneType === iphoneTypeFun.WUGJINBIGOUJI">
                <dl v-if="goldData.currentContract">
                  <dt class="itemlabel">
                    <span>方案名称</span>：
                    <span>
                      {{ goldData.currentContract.name }}
                    </span>
                  </dt>
                  <dt class="itemlabel">
                    <span>
                      <template v-if="goldData.currentContract.type === goldData.straightDown">直降金额</template>
                      <template v-else>返还话费总额</template>
                    </span>：
                    <span> {{ goldData.currentContract.prePrice / 100 }}元 </span>
                  </dt>
                  <dt class="itemlabel">
                    <span> 合约生效时间 </span>：
                    <span>
                      {{
                        hyTypeFun.getDescFromValue(goldData.currentContract.effectTime)
                      }}
                    </span>
                  </dt>
                  <dt class="itemlabel">
                    <span>最低消费</span>：
                    <span> {{ goldData.currentContract.minCharge / 100 }}元/月 </span>
                  </dt>
                  <dt class="itemlabel">
                    <span> 有效期 </span>：
                    <span> {{ goldData.currentContract.countMonth }}个月 </span>
                  </dt>
                  <dt class="itemlabel">
                    <span> 备注 </span>：
                    <span>
                      {{ goldData.currentContract.memo }}
                    </span>
                  </dt>
                </dl>
              </template>
            </div>
          </div>
        </section>

        <div class="btn buyBtn">
          <div class="bn" @click="purchaseQuliCheck">
            立即购买
          </div>
        </div>
      </div>
    </div>
    <van-dialog
      v-model="goldData.showdeleteOrder"
      confirm-button-color="#5fade8"
      confirm-button-text="返回"
      show-cancel-button
      @confirm="goDetail"
    >
      <div class="dialog_title">
        抱歉~
      </div>
      <div class="dialog_content">
        {{ goldData.errMeg }}
      </div>
    </van-dialog>
  </div>
</template>
<script setup>
import Shopname from "@/components/index/headercon.vue"
import loginUtils from "@/utils/login/index.js"
import UA from "@/utils/ua.js"
import { getPassPhone } from "@/utils/utils.js"
import PreferentialpurchaseApi from "@/api/preferentialpurchase.js"
import provinceJson from "@/utils/province.js"
import vue,{reactive,ref,onMounted,getCurrentInstance,computed} from "vue"
import { Dialog } from "vant"
import createEnum from "@/utils/createEnum.js"
import {ENV} from "@/utils/env.js"
vue.use(Dialog)

const iphoneTypeFun = createEnum({
  YOUHUIGOUJI: [1, "优惠购机"],
  XINYONGGOUJI: [2, "信用购机"],
  WUGJINBIGOUJI: [3, "5G金币购机"]
})

/* 合约生效时间枚举 */
const hyTypeFun =  createEnum({
  DANGYUE: [1, "当月"],
  CIYUE: [2, "次月"],
})

const goldData = reactive({
  showShop:
    !UA.isWechat &&
    !UA.isWechatWork &&
    !UA.isApp &&
    !UA.isIosQQ &&
    !UA.isAndroidQQ,
  contractList: [],
  currentContract: null,
  userName: null,
  provinceName: null,
  activetyIndex: 0,
  provinceGoodsId: null,
  shareParam: null,
  showContract: false,
  notContract: false,
  showdeleteOrder: false,
  notContractMsg: '',
  showErrImgType: 2,
  /* 购机类型 */
  iphoneType: null,
  proxy: null,
  errMeg: "您不具备办理该合约资格",
  preferentialTabCurIndex: 0,
  preferentialCurIndex: 0,
  
  // 返话费
  refunds : 1,
  // 直降
  straightDown : 6
})

const iphoneTypeFilter = computed(()=>{
  return iphoneTypeFun.getDescFromValue(goldData.iphoneType)
})

function returnstr(item){
  const prePrice = item.prePrice/100
  const minCharge = item.minCharge/100

  if(item.type === goldData.refunds){
    return `返${prePrice}元话费，最低消费${minCharge}元/月`
  }else if(item.type === goldData.straightDown){
    return `直降${prePrice}元，最低消费${minCharge}元/月`
  }
}
// 登录成功处理
function logined(res) {
  goldData.userName = res ? res.UserName : null
  if (!goldData.userName) {
    showError('哎呦喂~ 走神了~')
    goldData.showErrImgType = 2
  } else {
    let province = provinceJson.data[res.userProvince + '_' + res.userCity]
    goldData.provinceName = province ? province.split('_')[0] : null
    getContract()
  }
}
// 错误页面
function showError(message) {
  goldData.notContractMsg = message
  goldData.contractList = null
  goldData.notContract = true
}
// 返回上一页
function goDetail() {
  if (location.origin.indexOf("grey") !== -1) {
    location.href = `https://apollo.asiainfo.com/cnr-web/goodsDetail?upgradeType=${goldData.iphoneType}&shareParam=${goldData.shareParam}`
  } else {
    location.href =  `${ENV.getB2bDomain()}/cnr-web/goodsDetail?shareParam=${goldData.shareParam}&upgradeType=${goldData.iphoneType}`
  }
}
// 获取合约数据
function getContract() {
  PreferentialpurchaseApi.queryByProGoodsCode({
    provinceGoodsId: goldData.provinceGoodsId,
    type: goldData.iphoneType, //优惠购机类型
    shopId: goldData.shopId,
  }).then((res) => {
    goldData.showContract = false
    if (res.code === 0 && res.data && res.data.length > 0) {
      if (goldData.iphoneType === iphoneTypeFun.WUGJINBIGOUJI) {
        /* 筛选数据 */
        goldData.contractList = res.data.reduce((acc, current) => {
          const existing = acc.find(
            (item) => item.countMonth === current.countMonth
          )
          if (existing) {
            existing.list.push(current)
          } else {
            acc.push({ countMonth: current.countMonth, list: [current] })
          }
          return acc
        }, [])

        goldData.currentContract = goldData.contractList[goldData.preferentialTabCurIndex].list[
          goldData.preferentialCurIndex
        ]
      } else {
        goldData.contractList = res.data
        goldData.currentContract = res.data[0]
      }
      
      goldData.activetyIndex = 0
      goldData.showContract = true

      goldData.proxy.$nextTick(() => {
        document.querySelector('.icpinfo').style.marginBottom = `${document.querySelector('.buyBtn').offsetHeight}px`
      })
    } else {
      showError(res.message)
      goldData.showErrImgType = 1
    }
  })
}
// 选定月份
function changeMonth(index){
  goldData.preferentialTabCurIndex = index
  goldData.preferentialCurIndex = 0
  goldData.currentContract = goldData.contractList[index].list[0]
}
// 选定合约
function changeContract(index,item){
  goldData.preferentialCurIndex = index
  goldData.currentContract = item ? item : goldData.contractList[goldData.preferentialTabCurIndex].list[index]
}
// 办理资格校验
function purchaseQuliCheck() {
  PreferentialpurchaseApi.purchaseQuliCheck({
    contractId: goldData.currentContract.contractId,
    serialNo: goldData.serialNo,
    shopId: goldData.shopId,
    checkType: goldData.iphoneType,
  }).then((res) => {
    if (res.code) {
      goldData.showdeleteOrder = true
      goldData.errMeg = res.message
    } else {
      if (location.origin.indexOf('grey') !== -1) {
        location.href = `https://apollo.asiainfo.com/cnr-web/channelFillOrder?requestParam=${res.data}&upgradeType=${goldData.iphoneType}`
      } else {
        location.href = `${ENV.getB2bDomain()}/cnr-web/channelFillOrder?requestParam=${res.data}&upgradeType=${goldData.iphoneType}`
      }
    }
  })
}
onMounted(() => {
  let myUrl = new URL(location.href)
  goldData.provinceGoodsId = myUrl.searchParams.get('requestParam')
  goldData.shareParam = myUrl.searchParams.get('shareParam')
  goldData.serialNo = myUrl.searchParams.get('serialNo')
  goldData.shopId = myUrl.searchParams.get("shopId")
  goldData.iphoneType = parseInt(myUrl.searchParams.get("upgradeType")) || 1
  // 设置标题
  document.title = `一级云店-${iphoneTypeFun.getDescFromValue(
    goldData.iphoneType
  )}`
  if (!goldData.provinceGoodsId || !goldData.shareParam || !goldData.serialNo) {
    showError('哎呦喂~ 走神了~')
    goldData.showErrImgType = 2
    return false
  }
  let getVueInstance = getCurrentInstance()
  goldData.proxy = getVueInstance ? getVueInstance.proxy : null
  goldData.provinceGoodsId = goldData.provinceGoodsId.replace(/\s/g, '+')
  loginUtils.login(false, false, logined, false, false, logined, '0')
  document.body.style.minHeight = `${document.documentElement.clientHeight}px`
})
</script>
<style lang="scss" scoped>
.zj,
.zd {
  font-size: 13px;
  text-align: center;
}
.zd {
  margin-top: 8px;
  color: #838383;
}
.wugStyle {
  padding: 10px 17px;
  background: #f1f1f1;
  border: 1px solid transparent;
  &.activity {
    border: 1px solid #4187f9;
    background: transparent;
  }
}

.padding-top-34 {
  padding-top: 34px;
  // background: #f9f9f9;
}
.contractContainer {
  min-height: calc(100vh - 160px);
  padding: 10px 0;
  padding-right: 10px;
}
.item {
  padding: 15px 0 10px 0;
  background: #fff;
  margin-bottom: 10px;
  &:not(:first-child) {
    padding-top: 0;
    dl:first-child {
      margin-left: 0;
      height: 43px;
      padding-left: 20px;
      line-height: 43px;
      border-bottom: 1px solid#f7f7f7;
    }
  }
}
dl {
  height: 20px;
  font-size: 14px;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: #999999;
  line-height: 20px;
  margin: 0 0 0px 20px;
  &:not(:first-child) {
    margin-top: 15px;
  }
}
dd {
  border: 1px solid #999999;
  border-radius: 13px;
  padding: 6px 17px;
  display: inline-block;
  margin: 10px 0 0px 20px;
  p {
    font-size: 13px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #999999;
    line-height: 18px;
    text-align: center;
  }
  &.activity {
    border: 1px solid #4187f9;
    p {
      color: #4389fa;
    }
  }
}
.itemlabel {
  font-size: 13px;
}
.btn {
  width: 375px;
  height: 48px;
  background: linear-gradient(270deg, #7368ff, #3fbeff);
  color: #fff;
  text-align: center;
  line-height: 48px;
  font-size: 14px;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
}
.goBackBtn {
  width: 200px;
  border-radius: 20px;
  margin: 0 auto;
  height: 40px;
  line-height: 40px;
  font-size: 16px;
}
.buyBtn {
  position: fixed;
  bottom: 0;
}
.notContractMsg {
  margin: 20px auto 60px;
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #353535;
  font-family: PingFangSC, PingFangSC-Regular;
  line-height: 1.8;
  padding:0 50px;
}
.notContractMsgImg {
  text-align: center;
  margin-top: 170px;
  img {
    width: 150px;
    &.Group3295Img{
      width:300px;
    }
  }
}
@mixin dialog_font {
  text-align: center;
  font-weight: 600;
  font-family: PingFangSC, PingFangSC-Regular;
}
.dialog_title {
  font-size: 16px;
  color: #353535;
  padding: 37px 0 10px;
  @include dialog_font;
}
.dialog_content {
  font-size: 15px;
  color: #353535;
  padding: 5px 0 25px;
  color: #434343;
  @include dialog_font;
}
</style>
<style lang="scss">
body {
  background: #f7f8f9;
  min-height:100%;
  display: flex;
  flex-direction: column;
  #app{
    flex:1;
    background: #fff;
    min-height:100%;
  }
  .icpinfo {
    margin-top: auto;
    padding:20px 0;
    height:auto;
  }
}
</style>
<style lang="scss" scoped>
.contract-new-warpper {
  .paper {
    padding-top: 20px;
    margin-bottom: 10px;
    background: #fff;
  }

  .blockStyle {
    background: #fff;
    padding-bottom: 20px;
    &.offerDetails {
      padding-bottom: 50px;
    }
    .title,
    .list,
    .textList {
      padding-left: 20px;
      padding-right: 20px;
    }
    .desc {
      font-size: 14px;
      color: #666;
      padding-left: 20px;
      margin-bottom: 10px;
    }
    .title {
      font-size: 16px;
      color: #333333;
      position: relative;
      margin-bottom: 10px;
      &::before {
        position: absolute;
        left: 10px;
        top: 0;
        bottom: 0;
        margin: auto;
        width: 4px;
        content: "";
        height: 12px;
        border-radius: 3px;
        background: linear-gradient(180deg, #18c1ff, #0a8dfe);
      }
    }
    .list {
      ul {
        display: flex;
        flex-flow: wrap;
        &.tabStyle {
          li {
            padding-left: 15px;
            padding-right: 15px;
          }
        }
        li {
          &.active {
            border-color: #0a8dfe;
            color: #0a8dfe;
            background: #fff;
          }
          padding: 6px 25px;
          border: 1px solid #f3f4fb;
          border-radius: 16.5px;
          background: #f3f4fb;
          color: #666666;
          font-size: 14px;
          margin-right: 10px;
          margin-bottom: 10px;
          span{
            line-height: 1.5;
          }
        }
      }
    }

    .tabCon {
      display: flex;
      flex-flow: wrap;
    }

    .textList {
      dl {
        line-height: 1.8;
        height: auto;
        margin: 0;
        dt {
          display: flex;

          span {
            &:nth-of-type(1) {
              width: 90px;
              font-size: 13px;
              color: 666666;
              text-align: justify;
              text-align-last: justify;
            }
            &:nth-of-type(2) {
              flex: 1;
              color: #333333;
              font-size: 13px;
              min-width: 0;
              padding-left: 15px;
            }
          }
        }
      }
    }
  }
}

.buyBtn {
  background: #fff;
  padding: 20px;
  height: auto;
  width: 100%;
  box-shadow: 0px -1px 0px 0px rgba(0, 0, 0, 0.05);

  .bn {
    width: 100%;
    height: 44px;
    background: linear-gradient(90deg, #2892ff, #007eff);
    border-radius: 25px;
  }
}

.contractContainer{
  background: #fff;
}
</style>
