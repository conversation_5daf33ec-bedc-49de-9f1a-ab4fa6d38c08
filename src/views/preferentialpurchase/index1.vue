<template>
  <div>
    <Shopname
      v-if="
        !ua.isWechat &&
          !ua.isWechatWork &&
          !ua.isApp &&
          !ua.isIosQQ &&
          !ua.isAndroidQQ
      "
      :content="`【一级云店】${iphoneTypeFilter}`"
      :show-back="true"
    />
    <div
      :class="{
        'padding-top-34':
          !ua.isWechat &&
          !ua.isWechatWork &&
          !ua.isApp &&
          !ua.isIosQQ &&
          !ua.isAndroidQQ,
      }"
    >
      <div v-if="!showContract">
        <div v-if="notContract" class="contractContainer">
          <div class="notContractMsgImg">
            <img
              v-if="showErrImgType === 2"
              src="~@/assets/preferentialpurchase/404.png"
            />
            <img
              v-if="showErrImgType === 1"
              src="~@/assets/preferentialpurchase/noContract.png"
            />
          </div>
          <div class="notContractMsg">
            {{ notContractMsg }}
          </div>
          <div v-if="shareParam" class="btn goBackBtn" @click="goDetail">
            返回
          </div>
        </div>
      </div>
      <div v-else class="contractContainer">
        <div class="item">
          <dl class="label">
            办理合约的手机号码
          </dl>
          <dd v-if="userName" class="activity">
            <p>
              {{ userName.slice(0, 3) + "****" + userName.slice(7) }}
            </p>
            <p>
              {{ provinceName }}
            </p>
          </dd>
          <dl class="label">
            购买方式
          </dl>
          <dd class="activity">
            <p v-if="iphoneType === iphoneTypeFun.YOUHUIGOUJI">
              优惠购机
            </p>
            <p v-else-if="iphoneType === iphoneTypeFun.XINYONGGOUJI">
              信用购机
            </p>
            <p v-else-if="iphoneType === iphoneTypeFun.WUGJINBIGOUJI">
              5G金币购机
            </p>
          </dd>
          <dl class="label">
            活动方案
          </dl>
          <dd
            v-for="(item, index) in contractList"
            :key="index"
            :class="{ activity: index === activetyIndex, wugStyle:iphoneType === iphoneTypeFun.WUGJINBIGOUJI}"
            @click="getCurrent(item, index)"
          >

            <template v-if="iphoneType === iphoneTypeFun.YOUHUIGOUJI || iphoneType === iphoneTypeFun.XINYONGGOUJI">
              <p>{{ item.name }}</p>
              <p>在网{{ item.countMonth }}个月</p>
            </template>
            <template v-else-if="iphoneType === iphoneTypeFun.WUGJINBIGOUJI">
              <div class="zj">
                直降{{ (item.prePrice)/100 }}元
              </div>
              <div class="zd">
                最低消费<span style="color:red;">{{ (item.minCharge)/100 }}</span>元/月
              </div>
            </template>
          </dd>
        </div>
        <div class="item">
          <dl>活动详情</dl>

          <!-- 优惠购机, 信用购机 -->
          <template v-if="iphoneType === iphoneTypeFun.YOUHUIGOUJI || iphoneType === iphoneTypeFun.XINYONGGOUJI">
            <dl v-if="currentContract" class="itemlabel">
              优惠档位名称：<span style="color: #333333">
                {{ currentContract.name }}
              </span>
            </dl>
            <dl v-if="currentContract" class="itemlabel">
              承诺在网时间：<span style="color: #333333">
                {{ currentContract.countMonth }}个月
              </span>
            </dl>
          </template>

          <!-- 5G金币购机 -->
          <template v-else-if="iphoneType === iphoneTypeFun.WUGJINBIGOUJI">
            <dl v-if="currentContract" class="itemlabel">
              <span>方案名称：</span>
              <span style="color: #333333">
                {{ currentContract.name }}
              </span>
            </dl>
            <dl v-if="currentContract" class="itemlabel">
              <span>直降金额：</span>
              <span style="color: #333333">
                {{ (currentContract.prePrice)/100 }}元
              </span>
            </dl>
            <dl v-if="currentContract" class="itemlabel">
              <span>
                合约生效时间：
              </span>
              <span style="color: #333333">
                {{ hyTypeFun.getDescFromValue(currentContract.effectTime) }}
              </span>
            </dl>
            <dl v-if="currentContract" class="itemlabel">
              <span>最低消费：</span>
              <span style="color: #333333">
                {{ (currentContract.minCharge)/100 }}元/月
              </span>
            </dl>
            <dl v-if="currentContract" class="itemlabel">
              <span>
                有效期(月)：
              </span>
              <span style="color: #333333">
                {{ currentContract.countMonth }}个月
              </span>
            </dl>
            <dl v-if="currentContract" class="itemlabel">
              <span>
                备注：
              </span>
              <span style="color: #333333">
                {{ currentContract.memo }}
              </span>
            </dl>
          </template>
          
        </div>
        <div class="btn buyBtn" @click="purchaseQuliCheck">
          立即购买
        </div>
      </div>
    </div>
    <van-dialog
      v-model="showdeleteOrder"
      confirm-button-color="#5fade8"
      confirm-button-text="返回"
      show-cancel-button
      @confirm="goDetail"
    >
      <div class="dialog_title">
        抱歉~
      </div>
      <div class="dialog_content">
        {{ errMeg }}
      </div>
    </van-dialog>
  </div>
</template>

<script>
import Shopname from "@/components/index/headercon"
import loginUtils from "@/utils/login"
import UA from "@/utils/ua"
import PreferentialpurchaseApi from "@/api/preferentialpurchase"
import provinceJson from "@/utils/province"
import Vue from "vue"
import { Dialog } from "vant"
import createEnum from '@/utils/createEnum'
Vue.use(Dialog)
export default {
  components: {
    Shopname,
  },
  data() {
    return {
      errMeg:'您不具备办理该合约资格',

      ua: null,
      contractList: [],
      currentContract: null,
      userName: null,
      provinceName: null,
      activetyIndex: 0,
      provinceGoodsId: null,
      shareParam: null,
      showContract: false,
      notContract: false,
      showdeleteOrder: false,
      notContractMsg: "",
      showErrImgType: 2,

      /* 购机类型 */
      iphoneType:null,

      /* 购机枚举 */
      iphoneTypeFun:createEnum({
        YOUHUIGOUJI:[1,'优惠购机'],
        XINYONGGOUJI:[2, '信用购机'],
        WUGJINBIGOUJI:[3, '5G金币购机']
      }),

      /* 合约生效时间枚举 */
      hyTypeFun:createEnum({
        DANGYUE:[1, '当月'],
        CIYUE:[2, '次月'],
      })
    }
  },
  computed:{
    /* 购机类型-文本 */
    iphoneTypeFilter(){
      return this.iphoneTypeFun.getDescFromValue(this.iphoneType)
    }
  },
  created() {
    this.ua = UA
    let myUrl = new URL(location.href)
    this.provinceGoodsId = myUrl.searchParams.get("requestParam")
    this.shopId = myUrl.searchParams.get("shopId")
    this.shareParam = myUrl.searchParams.get("shareParam")
    this.serialNo = myUrl.searchParams.get("serialNo")
    this.iphoneType = parseInt(myUrl.searchParams.get("upgradeType")) || 1

    // 设置标题
    document.title = `一级云店-${this.iphoneTypeFun.getDescFromValue(this.iphoneType)}`

    if (!this.provinceGoodsId || !this.shareParam || !this.serialNo) {
      this.showError("哎呦喂~ 走神了~")
      this.showErrImgType = 2
      return false
    }
    this.provinceGoodsId = this.provinceGoodsId.replace(/\s/g, "+")
    loginUtils.login(false, false, this.logined, false, false, this.logined, "0")

    // console.log(this.iphoneType)
  },
  methods: {
    logined(res) {
      this.userName = res ? res.UserName : null
      if (!this.userName) {
        this.showError("哎呦喂~ 走神了~")
        this.showErrImgType = 2
      } else {
        let province = provinceJson.data[res.userProvince + "_" + res.userCity]
        this.provinceName = province ? province.split("_")[0] : null
        this.getContract()
      }
    },
    showError(message) {
      this.notContractMsg = message
      this.contractList = null
      this.notContract = true
    },
    goDetail() {
      if (location.origin.indexOf("grey") !== -1) {
        location.href = `https://apollo.asiainfo.com/cnr-web/goodsDetail?upgradeType=${this.iphoneType}&shareParam=${this.shareParam}`
      } else {
        location.href = `https://b2c.market.chinamobile.com/cnr-web/goodsDetail?shareParam=${this.shareParam}&upgradeType=${this.iphoneType}`
      }
    },
    getContract() {
      PreferentialpurchaseApi.queryByProGoodsCode({
        provinceGoodsId: this.provinceGoodsId,
        type: this.iphoneType, //优惠购机类型
        shopId:this.shopId
      }).then((res) => {
        this.showContract = false
        if (res.code === 0 && res.data && res.data.length > 0) {
          this.contractList = res.data
          this.currentContract = res.data[0]
          this.activetyIndex = 0
          this.showContract = true
          document.getElementsByClassName("icpinfo")[0].style.marginBottom =
            "48px"
        } else {
          this.showError(res.message)
          this.showErrImgType = 1
        }
      })
    },
    getCurrent(item, index) {
      this.currentContract = item
      this.activetyIndex = index
    },
    purchaseQuliCheck() {
      /* 5G金币购机 */
      PreferentialpurchaseApi.purchaseQuliCheck({
        contractId: this.currentContract.contractId,
        serialNo: this.serialNo,
        shopId:this.shopId,
        checkType:this.iphoneType
      }).then((res) => {
        if (res.code) {
          this.showdeleteOrder = true
          this.errMeg = res.message
        } else {
          if (location.origin.indexOf("grey") !== -1) {
            location.href = `https://apollo.asiainfo.com/cnr-web/channelFillOrder?requestParam=${res.data}&upgradeType=${this.iphoneType}`
          } else {
            location.href = `https://b2c.market.chinamobile.com/cnr-web/channelFillOrder?requestParam=${res.data}&upgradeType=${this.iphoneType}`
          }
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.zj,.zd{
  font-size: 13px;
  text-align: center;
}
.zd{
  margin-top: 8px;
  color:#838383;
}
.wugStyle{
  padding:10px 17px;
  background: #f1f1f1;
  border:1px solid transparent;
  &.activity{
    border: 1px solid #4187f9;
    background:transparent;
  }
}

.padding-top-34 {
  padding-top: 34px;
  background: #f9f9f9;
}
.contractContainer {
  min-height: calc(100vh - 160px);
  padding: 10px 0;
  padding-right: 10px;
}
.item {
  padding: 15px 0 10px 0;
  background: #fff;
  margin-bottom: 10px;
  &:not(:first-child) {
    padding-top: 0;
    dl:first-child {
      margin-left: 0;
      height: 43px;
      padding-left: 20px;
      line-height: 43px;
      border-bottom: 1px solid#f7f7f7;
    }
  }
}
dl {
  height: 20px;
  font-size: 14px;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: #999999;
  line-height: 20px;
  margin: 0 0 0px 20px;
  &:not(:first-child) {
    margin-top: 15px;
  }
}
dd {
  border: 1px solid #999999;
  border-radius: 13px;
  padding: 6px 17px;
  display: inline-block;
  margin: 10px 0 0px 20px;
  p {
    font-size: 13px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #999999;
    line-height: 18px;
    text-align: center;
  }
  &.activity {
    border: 1px solid #4187f9;
    p {
      color: #4389fa;
    }
  }
}
.itemlabel {
  font-size: 13px;
}
.btn {
  width: 375px;
  height: 48px;
  background: linear-gradient(270deg, #7368ff, #3fbeff);
  color: #fff;
  text-align: center;
  line-height: 48px;
  font-size: 14px;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
}
.goBackBtn {
  width: 200px;
  border-radius: 20px;
  margin: 0 auto;
  height: 40px;
  line-height: 40px;
  font-size: 16px;
}
.buyBtn {
  position: fixed;
  bottom: 0;
}
.notContractMsg {
  margin: 20px auto 60px;
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #353535;
  font-family: PingFangSC, PingFangSC-Regular;
}
.notContractMsgImg {
  text-align: center;
  margin-top: 170px;
  img {
    width: 150px;
  }
}
@mixin dialog_font {
  text-align: center;
  font-weight: 600;
  font-family: PingFangSC, PingFangSC-Regular;
}
.dialog_title {
  font-size: 16px;
  color: #353535;
  padding: 37px 0 10px;
  @include dialog_font;
}
.dialog_content {
  font-size: 15px;
  color: #353535;
  padding: 5px 0 25px;
  color: #434343;
  @include dialog_font;
}
</style>
