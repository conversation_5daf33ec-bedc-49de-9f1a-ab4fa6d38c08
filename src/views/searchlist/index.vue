<template>
  <div>
    <Shopname
      v-if="showShopName"
      content="商品搜索"
    />
    <div
      class="searchlist"
      :class="{'padding-top-50':showShopName}"
    >
      <div class="search-bar">
        <div v-if="!ua.isWechat" class="iconContainer" @click="goPrevious">
          <van-icon name="arrow-left" />
        </div>
        <div class="middle">
          <van-search
            v-model="searchvalue"
            shape="round"
            clearable
            :left-icon="searchImg"
            placeholder="请输入要搜索的关键词"
            @search="search"
          />
          <van-button :disabled="!searchvalue" class="searchBtn" @click="search">
            搜索
          </van-button>
        </div>
        <div class="iconContainer" @click="goHome">
          <span class="iconfont icon-home"></span>
        </div>
      </div>
      <div class="goodslist-container">
        <div v-if="showGoodsTab" class="goods-tab">
          <van-dropdown-menu active-color="#333">
            <van-dropdown-item
              ref="itemSort"
              :title="sortCheckedItem&&sortCheckedItem.text ? sortCheckedItem.text :'综合排序'"
              class="focus"
              :title-class="sortTitleClass"
            >
              <div class="sort-list">
                <div
                  v-for="(item,index) in option1"
                  :key="index"
                  class="sort-item"
                  :class="{'active':item.value==sortCheckedItem.value}"
                  @click="changeSortValue(item)"
                >
                  {{ item.text }}
                  <span
                    v-if="item.value==sortCheckedItem.value"
                    class="iconfont icon-success"
                  ></span>
                </div>
              </div>
            </van-dropdown-item>
            <van-dropdown-item
              ref="itemPrice"
              :title="priceCheckedItem&&priceCheckedItem.text ? priceCheckedItem.text :'价格区间'"
              :title-class="priceTitleClass"
            >
              <div class="price-list">
                <div
                  v-for="(item,index) in option2"
                  :key="index"
                  class="price-item"
                  :class="{'active':item.value==priceCheckedItem.value}"
                  @click="changePriceValue(item)"
                >
                  {{ item.text }}
                  <span
                    v-if="item.value==priceCheckedItem.value"
                    class="iconfont icon-success"
                  ></span>
                </div>
              </div>
            </van-dropdown-item>
          </van-dropdown-menu>
        </div>
        <ul
          v-if="showLoading"
          class="goods-tab-no"
          :class="{'isApp':!showShopName}"
        >
          <li class="tab-no-con">
            <van-loading type="spinner" />
            <P>LOADING...</P>
          </li>
        </ul>
        <div v-else-if="goodslist && goodslist.length" class="goodsList">
          <ul
            class="goods-list"
          >
            <li v-for="(item, key) in goodslist" :key="item.goodsId">
              <a href="javascript:void(0)" @click="insertCodeGoods(key+1,item.goodsId,item.goodsSource,item.goodsLink)">
                <div class="img-container">
                  <img :src="item.imgUrl" />
                </div>
                <p class="title">
                  {{ item.goodsName }}
                </p>
                <p class="subtitle">
                  {{ item.subTitle }}
                </p>
                <p class="price">
                  {{ (item.price/100).toFixed(2) }}元
                </p>
              </a>
            </li>
          </ul>
        </div>
        <ul
          v-else-if="notForst"
          class="goods-tab-no"
          :class="{'isApp':!showShopName}"
        >
          <li class="tab-no-con">
            <div>
              <img src="~@/assets/index_img/ice_tab_no.png" />
              <p>抱歉，没有找到您要搜索的商品</p>
            </div>
          </li>
        </ul>
        <ul
          v-else
          class="goods-tab-no"
          :class="{'isApp':!showShopName}"
        >
          <li class="tab-no-con">
          </li>
        </ul>
        <div
          v-if="goodslist && goodslist.length"
          class="searchBottom"
        >
          <div class="hrStyle">
            <hr style="background-color:#DADADA;border:none;height:1px">
          </div>
          <span class="hrContent">
            我是有底线的
          </span>
          <div class="hrStyle">
            <hr style="background-color:#DADADA;border:none;height:1px">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import Vue,{ reactive, onMounted,ref } from "vue"
import router from '@/router/index'
import Shopname from "@/components/index/headercon"
import searchlistApi from "@/api/searchlist"
import insertCode from "@/utils/insertCode"
import UA from "@/utils/ua"
import {toHome} from '@/utils/wxMiniProgram'
import { Search , Icon ,DropdownMenu, DropdownItem ,Button ,Loading,Toast} from 'vant'
onMounted(()=>{
  const url = new URL(location.href)
  searchData.shopId = url.searchParams.get('shopId')
  if(!searchData.shopId){
    router.push({
      name: "nearby"
    })
  }
})
Vue.use(Search).use(Icon).use(DropdownMenu).use(DropdownItem).use(Button).use(Loading)
const ua = ref(UA)
let showShopName = ref(false)
showShopName.value = !UA.isWechat&&!UA.isWechatWork&&!UA.isApp&&!UA.isIosQQ&&!UA.isAndroidQQ
const option1 = ref([
  { text: '综合排序', value: 1 },
  { text: '价格从低到高排序', value: 2 },
  { text: '价格从高到低排序', value: 3 },
])
const option2 = ref([
  { value: 0, minPrice:null,   maxPrice:null,    text: '不限'},
  { value: 1, minPrice:0,      maxPrice:5000,    text: '0-50'},
  { value: 2, minPrice:5001,   maxPrice:10000,   text: '50-100'},
  { value: 3, minPrice:10001,  maxPrice:30000,   text: '100-300'},
  { value: 4, minPrice:30001,  maxPrice:100000,  text: '300-1000'},
  { value: 5, minPrice:100001, maxPrice:null,    text: '1000以上'},
])
const searchImg = ref(require('@/assets/searchlist/search1.png'))
let searchData = {
    shopId:null,
    keyWord:'',
    minPrice:null,
    maxPrice:null,
    sort:1
  },value=0,switch1=false,switch2=false
let searchvalue = ref(""),
  priceCheckedItem = ref({}),
  sortCheckedItem= ref({}),
  goodslist= ref([]),
  priceTitleClass = ref('focus'),
  sortTitleClass= ref('focus'),
  notForst= ref(false),
  showLoading= ref(false),
  showGoodsTab= ref(false)

let itemPrice = ref(null), itemSort = ref(null)
function getNearShop(){
  notForst.value = true
  showLoading.value = true
  searchlistApi.getSearchList(searchData).then(res=>{
    showLoading.value = false
    if(res.code){
      Toast(res.message)
    }else{
      goodslist.value = []
      if( res.data && res.data.goods ){
        res.data.goods.forEach(element => {
          if(element.goodsSource==2){//异业都显示
            goodslist.value.push(element)
            return
          }
          //1、o2oGoodsStatus 2 下线 3上线 运营位下线，店铺首页不展示商品
          //2、goodsStatus 51是上架 52商品下架，这两种状态店铺首页应该展示商品，可以进入详情页
          if((element.o2oGoodsStatus==2||(element.goodsStatus&&element.goodsStatus!=51&&element.goodsStatus!=52))) {
            return
          }
          goodslist.value.push(element)
        })
      }
      if(searchData.minPrice==null&&searchData.maxPrice==null&&searchData.sort==1&&goodslist.value.length==0){
        showGoodsTab.value = false
      }else{
        showGoodsTab.value = true
      }
    }
  })
}
function changePriceValue(item){
  priceCheckedItem.value = item
  priceTitleClass.value = 'checked'
  searchData.minPrice = item.minPrice
  searchData.maxPrice = item.maxPrice
  getNearShop()
  itemPrice.value.toggle()
}
function changeSortValue(item){
  sortCheckedItem.value = item
  sortTitleClass.value = 'checked'
  searchData.sort = item.value
  getNearShop()
  itemSort.value.toggle()
}
function search(){
  searchData.keyWord = searchvalue.value
  searchData.minPrice = null
  searchData.maxPrice = null
  searchData.sort = 1
  priceCheckedItem.value = {}
  sortCheckedItem.value = {}
  sortTitleClass.value = ''
  priceTitleClass.value = ''
  getNearShop()
}
function goPrevious(){
  history.go(-1)
}
function goHome(){
  toHome()
  router.push({
    path:'/index.html?shopId='+ searchData.shopId
  })
}
function insertCodeGoods(key,goodsId,source,url){
  let dcs_id = key+'_'+goodsId+'_'+source
  insertCode("goods_"+dcs_id,url)
}
</script>
<style lang="scss" scoped>
  $bgcolor:#f7f8f9;
  .searchlist{
    background: $bgcolor;
    padding-top:15px;
  }
  .search-bar{
    display: flex;
    padding:0 15px;
    box-sizing: border-box;
    .iconContainer{
      height:36px;
      line-height:29px;
      text-align: center;
      .iconfont,.van-icon{
        font-size:22px;
        display: inline-block;
      }
      &:first-child{
        width:33px;
        line-height: 36px;
      }
    }
    .middle{
      // width:299px;
      flex:1;
      height:36px;
      margin-right: 20px;
      position: relative;
      .van-search{
        padding:0;
        background:$bgcolor;
        width:235px;
        .van-search__content{
          background:#fff;
          padding-left:10px;
          padding-right:26px;
        }
        :deep(.van-icon img){
          width: 20px;
          height: 20px;
          margin-top: 2px;
        }
      }
    }
    .searchBtn{
      width: 63px;
      height: 34px;
      line-height: 34px;
      opacity: 1;
      background: linear-gradient(90deg,#5fc8ff, #8372fe);
      border-radius: 34px;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color:#fff;
      text-align: center;
      position: absolute;
      right:0;
      top:0;
    }
  }
  .goodsList{
    background: #fff;
    padding-top:12px;
  }
  .goods-list{
    display: flex;
    // flex-direction: row;
    flex-flow:row wrap;
    border-top:1px solid #f0f0f0;
    li{
      list-style: none;
      position: relative;
      font-size: 13px;
      line-height: 20px;
      background:#fff;
      // margin-bottom:1px;
      width:375px;
      padding:14px 0;
      width:345px;
      margin:0 auto;
      border-bottom: 1px dashed #dedede;
      &:last-child{
        border:none;
      }
      .img-container{
        width:98px;
        height:98px;
        float:left;
        margin-right:8px;
        display: flex;
        align-items: center;
        img{
          width:100%;
          height: auto;
        }
      }
      p{
        overflow: hidden;
        text-overflow:ellipsis;
        white-space: nowrap;
      }
      p.title{
        height: 22px;
        line-height: 22px;
        font-size: 16px;
        font-family: PingFangSC, PingFangSC-Medium;
        font-weight: 500;
        text-align: left;
        color: #666666;
        margin-bottom:11px;
        margin-top:8px;
      }
      p.subtitle{
        height: 18px;
        font-size: 13px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        text-align: left;
        color: #999999;
        line-height: 18px;
        margin-bottom:11px;
      }
      p.price{
        font-size: 16px;
        color: #ed2668;
        height: 22px;
        line-height: 22px;
        font-weight: 500;
        em{
          font-size:11px;
          margin-right:-5px;
        }
      }
    }
  }
  .goods-tab{
    :deep(.van-dropdown-menu__bar){
      box-shadow:none;
      padding-left:18px;
      height:55px;
      display: block;
      margin-top:20px;
    }
    :deep( .van-dropdown-menu__item){
      -webkit-box-flex:none;
      float: left;
      .van-dropdown-menu__title{
        height: 34px;
        line-height:34px;
        opacity: 1;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        background: #f2f3f4;
        border-radius: 17px;
        padding: 0 31px 0 20px;
        margin-top:15px;
        margin-right:10px;
        &::after{
          right:18px;
          top:16px;
          width:4px;
          height:4px;
          border-width: 2px;
          border-left-color: #333333;
          border-bottom-color: #333333;
        }
        &.van-dropdown-menu__title--active{
          border-radius: 17px 17px 0 0;
          height:50px;
          &::after{
            color:#ccc;
          }
        }
        &.checked{
          &:not(.van-dropdown-menu__title--active){
            background: #eff6ff;
            border: 1px solid #4a90e2;
            color: #4a90e2;
            background: #eff6ff;
            border-radius: 18px;
            line-height: 32px;
            &::after{
              top:15px;
              border-left-color:#4a90e2;
              border-bottom-color: #4a90e2;
            }
          }

        }
      }

    }
  }
  .price-list,.sort-list{
    display: flex;
    width:375px;
    flex-wrap: wrap;
    padding:10px 0;
    background: #f2f3f4;
    border-radius:14px 14px 0 0;
    .price-item,.sort-item{
      width:123.75px;
      height: 40px;
      opacity: 1;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      text-align: center;
      color: #444444;
      line-height: 40px;
      &.active{
        color: #4a90e2;
        .iconfont{
          position:absolute;
          margin-left:10px;
        }
      }
    }
    .sort-item{
      width: 337.5px;
      text-align: left;
      padding-left:32px;
    }
  }
  .goods-tab-no{
    width: 100%;
    padding: 100px 0 0;
    height:calc(100vh - 166px);
    text-align: center;
    &.isApp{
      height:calc(100vh - 131px);
    }
    .tab-no-con {
      img{
        display: block;
        height: 67.5px;
        width: auto;
        margin: 0 auto 7.5px;
      }
      p{
        display: inline-block;
        line-height: 22.5px;
        font-size: 13.5px;
        font-weight: 400;
        color:#757575;
      }
    }
  }
  .hrStyle{
    width:110px;
    hr{
      margin-top:12px;
    }
  }
  .searchBottom{
    display:flex;
    padding:10px 15px;
    box-sizing: border-box;
  }
  .padding-top-50{
    padding-top:50px;
  }
  .hrContent{
    flex:1;
    text-align: center;
    color:#DADADA;
    font-size: 12px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    line-height:25px;
    display: inline-block;
    height:25px;
  }
</style>
