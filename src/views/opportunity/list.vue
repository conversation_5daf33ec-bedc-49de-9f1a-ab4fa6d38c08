<template>
  <div class="opportunity">
    <header-nav title="商机管理">
    </header-nav>
    <div class="couponMain">
      <Icon
        v-if="data.hasrole"
        name="filter-o"
        class="searchbt"
        size="18"
        @click="data.showSearch = true"
      />
      <Tabs :before-change="beforeChange">
        <Tab v-for="(item,index) in tabTitle" :key="index" :title="item">
        </Tab>
        <div class="opportunitycon">
          <List
            v-if="data.tabMain && data.tabMain.length"
            v-model="data.loading"
            :finished="data.finished"
            :finished-text="data.nodataMessage"
            class="opportunity-list ul"
            @load="getBroadbandList"
          >
            <Cell v-for="(it) in data.tabMain" :key="it.orderId" class="li">
              <div class="bd-list-content clearfix">
                <h2 class="flex-title">
                  <span class="icon-label icon-num">
                    No.
                  </span>
                  <span>
                    {{ it.orderId }}
                  </span>
                  <span class="kongbai"></span>
                  <span
                    v-if="it.userMobile"
                    class="iconfont icon-user icon-label"
                  ></span>
                  <span>
                    {{ getPassPhone(it.userMobile) }}
                  </span>
                </h2>
                <div class="mid-con">
                  <p class="mid-con-top"> 推送原因：
                    <span>
                      {{ pushReason[it.pushReason] }}
                    </span>
                  </p>
                  <p class="mid-con-bottom">
                    <span class="title">状态：</span>
                    <span class="other">
                      <span :class="colorConfig[it.status]">
                        {{ stateText[it.status] }}
                      </span>
                      <span v-if="it.status=='OF'">（失败原因：{{ it.reason ? it.reason : "--" }}）</span>
                    </span>

                  </p>
                </div>
                <div class="bd-con">
                  <p class="bd-xd-yyxt hs">
                    创建时间：{{ parseTime(it.createTime,"{y}/{m}/{d} {h}:{i}") }}
                  </p>
                  <p class="bd-xd-yyxt hs">
                    变更时间：{{ parseTime(it.createTime,"{y}/{m}/{d} {h}:{i}") }}
                  </p>
                </div>
              </div>
            </Cell>
          </List>
          <ul
            v-else
            class="ul"
          >
            <li class="tab-no-con li">
              <div>
                <img src="~@/assets/index_img/ice_tab_no.png" />
                <p>
                  {{ data.nodataMessage }}
                </p>
              </div>
            </li>
          </ul>
        </div>
      </Tabs>
    </div>
    <van-action-sheet
      v-model="data.showSearch"
      :closeable="false"
      :round="false"
      title="商品筛选"
    >
      <div class="nav filter-tab">
        <van-form
          ref="searchForm"
          :label-width="100"
          @submit="search('search')"
        >
          <div v-for="item in FILTERDATA" :key="item.id">
            <p class="filter-title">
              {{ item.title }}
            </p>
            <ul class="filter-content">
              <li
                v-for="itemLittle in item.list"
                :key="itemLittle.code"
                class="filter-item"
                :class="{active:data.sendData[item.id]==itemLittle.code}"
                @click="changeFilter(item.id,itemLittle.code)"
              >
                <template v-if="itemLittle.em">
                  <em>
                    {{ itemLittle.value }}
                  </em>
                </template>
                <template v-else>
                  {{ itemLittle.value }}
                </template>
              </li>
            </ul>
          </div>

          <div style="display: flex">
            <van-button
              plain
              block
              native-type="button"
              color="linear-gradient(to right, #60C7FF, #8374FE)"
              class="filter-btn-reset"
              @click="reset"
            >
              重置
            </van-button>
            <van-button
              block
              type="primary"
              native-type="submit"
              class="filter-btn-submit"
            >
              确定
            </van-button>
          </div>
        </van-form>
      </div>
    </van-action-sheet>
    <Footer />
  </div>
</template>

<script setup>
import headerNav from "@/views/my/components/headerNav"
import loginUtils from "@/utils/login"
import {getOpportunityList} from "@/api/opportunity.js"
import {
  Toast,
  Tab,
  Tabs,
  List,
  Cell,
  Field,
  Form,
  Button,
  ActionSheet,
  Icon,
} from "vant"
import "vant/lib/index.css"
import Vue,{reactive,getCurrentInstance} from "vue"
import {getPassPhone,parseTime} from "@/utils/utils"
Vue
  .use(Form)
  .use(Button)
  .use(ActionSheet)
//筛选的状态
const STATETEXT = [
  {code:null,text:"全部"},
  {code:"SS",text:"已完成"},
  {code:"OF",text:"已失败"}
]
function getTabTitle(arraylist,key){
  let array = []
  arraylist.forEach(element => {
    array.push(element[key])
  })
  return array
}
//tab栏展示title
const tabTitle= getTabTitle(STATETEXT,"text")
// 列表展示“状态”字段
const STATUSARRAY = [
  {code:"PO",text:"待接单",color:"blue"},
  {code:"SS",text:"已完成",color:"grey"},
  {code:"OF",text:"已失败",color:"orange"},
  {code:"OT",text:"已过期",color:"orange"}
]
function arrayToObj(arraylist,key,value){
  let Obj = {}
  arraylist.forEach(element => {
    Obj[element[key]] = element[value]
  })
  return Obj
}
const stateText= arrayToObj(STATUSARRAY,"code","text")
const colorConfig=arrayToObj(STATUSARRAY,"code","color")
let data = reactive({
  showSearch: false,
  activeNames: ["1"],
  user: null,
  tabMain: [],
  sendData: {
    shopId:null,
    status:null,
    createTime:null,
    pushReason:null,
    goodsType:null,
    pageNo:1
  },
  nodataMessage: "",
  hasrole: true,
  loading: false,
  finished: false,
  shopId: null,
})
// 筛选框数据词典
const FILTERDATA = [
  {
    title:"创建时间",
    list:[
      {code:null,value:"不限"},
      {code:1,value:"最近一天内"},
      {code:2,value:"最近三天内"},
      {code:3,value:"最近一周内"},
      {code:4,value:"最近一个月"},
      {code:5,value:"一个月以上"}
    ],
    id:"createTime"
  },
  {
    title:"推送原因",
    list:[
      {code:null,value:"不限"},
      {code:1,value:"频繁浏览意向商品"},
      {code:2,value:"长时间驻留意向商品"},
      {code:3,value:"订单支付失败"},
      {code:4,value:"订单返回办理失败"},
      {code:5,value:"业务冲突或其他原因校验失败",em:true},
      {code:"6",value:"一证五号校验失败"}
    ],
    id:"pushReason"
  },
  {
    title:"商品类型",
    list:[
      {code:null,value:"不限"},
      {code:1,value:"增值业务商品"},
      {code:2,value:"套餐商品"},
      {code:3,value:"号卡商品"},
      // {code:4,value:"宽带业务"},
      {code:5,value:"终端商品"},

    ],
    id:"goodsType"
  }
]
const filterObj = arrayToObj(FILTERDATA,"id","list")
const pushReason = arrayToObj(filterObj.pushReason,"code","value")
function changeFilter(filterData,code){
  data.sendData[filterData] = code
}
const url = new URL(location.href)
data.shopId = url.searchParams.get("shopId")
const getVueInstance = getCurrentInstance()
const proxy = getVueInstance ? getVueInstance.proxy : null
if (proxy) {
  proxy.$store.commit("SET_SHOPID",data.shopId)
}
loginUtils.login(true, true, logined, false, false, "", null, 5)
function logined(res){
  data.user = res
  // console.log(res)
  if (data.user && res.shopId) {
    data.sendData.shopId = res.shopId
    if (res.RuleId == 1) {
      data.sendData.staffId = res.staffId
      data.sendData.stafferId = res.stafferId
    }
    getBroadbandList()
    data.hasrole = true
  } else {
    data.hasrole = false //游客看不了
    data.tabMain = []
    data.nodataMessage = "无权查看，请去自己的店铺"
  }
}
function beforeChange(index){
  data.tabMain = []
  //TOC 待确认 MAS 预约成功 MAF 预约失败 HBC 已取消
  data.sendData.status = null
  // 返回 false 表示阻止此次切换
  if (index === STATETEXT.length) {
    return false
  }
  data.sendData.status = STATETEXT[index].code
  data.sendData.pageNo = 1
  data.finished = false
  getBroadbandList()
  // 返回 Promise 来执行异步逻辑
  return new Promise((resolve) => {
    // 在 resolve 函数中返回 true 或 false
    resolve(index + 1)
  })
}
function search(value){
  data.showSearch = false
  data.sendData.pageNo = 1
  data.finished = false
  getBroadbandList(value)
}
function reset(){
  Object.keys(filterObj).forEach(item=>{
    data.sendData[item] = null
  })
}
function getBroadbandList(value){
  if (!data.hasrole) {
    return false
  }
  data.loading = true
  getOpportunityList(data.sendData).then((res) => {
    if (res.code == 0) {
      if (data.sendData.pageNo == 1) {
        data.tabMain = []
      }
      data.tabMain = data.tabMain.concat(res.data.orderList)
      data.loading = false
      if (data.tabMain && data.tabMain.length == 0) {
        if (data.sendData.status == null && value != "search") {
          data.nodataMessage = "暂无商机数据"
        } else {
          data.nodataMessage = "没有搜索到您要的信息"
        }
        data.finished = true
        return false
      }
      if (res.data.total > res.data.pageNo*res.data.pageSize) {
        data.sendData.pageNo++
      } else {
        data.nodataMessage = "没有更多了"
        data.finished = true
      }
    } else {
      Toast(res.message)
    }
  })
}
function setScrollTop(){
  let scrollTop =
    document.documentElement.scrollTop ||
    window.pageYOffset ||
    document.body.scrollTop
  if (scrollTop == 0) {
    window.scrollTo(0, 1)
  }
}

</script>
<style>

</style>
<style lang="scss" scoped>
.opportunity :deep(.header-nav) {
  height: 52.5px !important;
}
.opportunity :deep(.header-nav__title) {
  font-size: 17px !important;
}
.opportunity .van-tabs__wrap {
  background: #fff;
}
.opportunity :deep(.van-tabs__nav--line) {
  width: 320px;
}
.opportunity :deep(.van-tab--active){
  color: #4387F7 !important;
  font-weight: bold;
}
.opportunity :deep(.van-tabs__line){
  background-color: #5fade8 !important;
}
// 筛选框
.opportunity :deep(.van-action-sheet){
  max-height: 100vh!important;
  height: 100vh;
  width:325px;
  right:0!important;
  left:auto;
  border-top-left-radius:15px ;
  border-bottom-left-radius: 15px;
}
.opportunity :deep(.van-action-sheet__header){
  font-weight: 600;
  color: #171717;
}
.van-cell::after{
  border-bottom:none!important;
}
ul {
  li {
    margin: 0;
    padding: 0;
    list-style: none;
  }
}
.opportunity {
  min-height: calc(101vh - 90px);
  .searchbt {
    position: absolute;
    right: 10px;
    top: 12px;
    z-index: 1;
    border-left: 2px solid #979797;
    padding-left: 10px;
  }
  .couponMain {
    position: relative;
    .opportunitycon{
      background: #f7f7f7;
      padding:10px;
      min-height: calc(100vh - 176px);
    }
    .opportunity-list{
      width: 355px;
      margin:0 auto;
      .li{
        background: #ffffff;
        border-radius: 8px;
        margin-bottom: 10px;

      }
    }
    .flex-title{
      display: flex;
      align-items: center;
      span{
        display: block;
        font-size: 14px;
        color: #4d4d4d;
      }
      .kongbai{
        flex:1
      }
      .icon-num{
        background: #96b0cb;
        width:20px;
        height: 20px;
        border-radius: 50%;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
      }
      .icon-user{
        color:#96b0cb;
        font-size: 20px;
      }
      .icon-label{
        margin-right: 6px;
      }
    }
    .mid-con{
      background: #f7f7f7;
      padding:10px;
      margin:5px 0;
      border-radius: 6px;
      .mid-con-top{
        font-size: 14px;
        color: #454a50;
        font-weight: 500;
      }
      .mid-con-bottom{
        font-size: 13px;
        color: #4d4d4d;
        font-weight: 400;
        display: flex;
        .title{
          width:42px;
          display: block;
        }
        .other{
          flex:1;
          span:last-child{
            color:#666666;
          }
        }
      }
    }
    .bd-con{
      display: flex;
      font-size: 11px;
      font-weight: 400;
      color: #999999;
      justify-content: space-between;
    }
  }
  .tab-no-con{
    background: none;
    text-align: center;
    padding: 30px;
    p{
      padding-top: 15px;
    }
  }
  // 筛选框
  .filter-tab{
    padding:0 20px;
    .filter-title{
      font-size: 14px;
      font-weight: 600;
      text-align: left;
      color: #4d4d4d;
      line-height: 20px;
      margin-bottom: 10px;
    }
    .filter-content{
      display: flex;
      flex-wrap: wrap;
      justify-content:space-between;
    }
    .filter-item{
      width: 137px;
      height: 36px;
      line-height: 36px;
      background: #f7f7f7;
      border-radius: 8px;
      font-size: 12px;
      font-weight: 400;
      text-align: center;
      box-sizing: border-box;
      color: #333333;
      margin-bottom:10px;
      border: 1px solid transparent;
      em{
        font-size: 10px;
      }
      &.active{
        background: rgba(67,135,247,0.10);
        border: 1px solid #4387f7;

      }
    }
    .filter-btn-submit{
      width:376px;
      border-radius: 21px;
      background: #4387f7;
      font-size: 16px;
    }
    .filter-btn-reset{
      color:#171717;
      font-weight: 600;
      font-size: 16px;
    }
  }
}
.grey{
  color: #9F9F9F;
}
.orange{
  color: #FE5E08;
}
.blue{
  color: #4387F7;
}
</style>
