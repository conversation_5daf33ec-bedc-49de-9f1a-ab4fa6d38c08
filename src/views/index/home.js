import UserBanner from "@/components/home/<USER>"
import ImgBanner from "@/components/home/<USER>"
import ShopInformation from "@/components/home/<USER>"
import IconList from "@/components/home/<USER>"
import IndexHomeRecommend from "@/components/home/<USER>"
import CenterBanner from "@/components/home/<USER>"
export function getComponents({indexData,shareShop,hasBanner,openQuan}){
  return [
    {
      template:UserBanner,
      props:{
        openQuan:openQuan,
        shareShop:shareShop,
        hasBanner:hasBanner.value
      },
      key:"UserBanner"
    },
    {
      template:ImgBanner,
      props:{
        bannerObj:indexData.bannerObj
      },
      key:"ImgBanner"
    },
    {
      template:ShopInformation,
      props:{
        shopInformation:indexData.shopInformation,
        shareShop:shareShop
      },
      key:"ShopInformation"
    },
    {
      template:IconList,
      props:{
        items:indexData.iconlist,
      },
      key:"IconList"
    },
    {
      template:CenterBanner,
      props:{
        bannerArr:indexData.centerBannerArr
      },
      key:"CenterBanner"
    },
    {
      template:IndexHomeRecommend,
      props:{
        recommendData:indexData.homeRecommend,
        shareShop:shareShop
      },
      key:"IndexHomeRecommend"
    }
  ]
}
export default {
  getComponents
}
