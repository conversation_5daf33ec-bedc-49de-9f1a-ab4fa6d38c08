<template>
  <div class="index">
    <template v-for="(item,key) in normalData.pageData">
      <template v-if="item.componentCode == 'member'&&props.configure!=1">
        <Vip :key="key" :logined="props.logined" :preview="props.preview" :floor-id="item.floorId" :data-list="item.dataList" />
      </template>
      <template v-else-if="item.componentCode == 'notice'&&props.configure!=1">
        <Notice :key="key" :content="item.dataList.floor.title" />
      </template>
      <template v-else-if="item.componentCode == 'single'&&props.configure!=1">
        <template v-for="(single ,index) in item.dataList.ad">
          <Single :key="`${key}_single_${index}`" :bannersrc="single.imgSrc" :adlink="single.adLink?single.adLink:'javascript:void(0)'" :preview="props.preview" :floor-id="item.floorId" :configure="props.configure" />
        </template>
      </template>
      <template v-else-if="item.componentCode == 'bigsize'&&props.configure!=1">
        <template v-for="(bigsize ,index) in item.dataList.ad">
          <Bigsize :key="`${key}_bigsize_${index}`" :bannersrc="bigsize.imgSrc" :adlink="bigsize.adLink?bigsize.adLink:'javascript:void(0)'" :preview="props.preview" :floor-id="item.floorId" :configure="props.configure" />
        </template>
      </template>
      <template v-else-if="item.componentCode == 'broadband'&&props.configure!=1">
        <Broadband :key="key" :preview="props.preview" :configure="props.configure" :broadbandlink="item.dataList.floor.broadbandLink" :floor-id="item.floorId" :broadband-status="item.dataList.floor.broadbandStatus" />
      </template>
      <template v-else-if="item.componentCode == 'carouselA'&&props.configure!=1">
        <CarouselA :key="key" :preview="props.preview" :items="item.dataList.ad" :configure="props.configure" :floor-id="item.floorId" />
      </template>
      <template v-else-if="item.componentCode == 'carouselB'&&props.configure!=1">
        <CarouselB :key="key" :preview="props.preview" :items="item.dataList.ad" :configure="props.configure" :floor-id="item.floorId" />
      </template>
      <template v-else-if="item.componentCode == 'guideA'&&props.configure!=1">
        <GuideA :key="key" :preview="props.preview" :items="item.dataList.ad" :configure="props.configure" :floor-id="item.floorId" />
      </template>
      <template v-else-if="item.componentCode == 'guideB'&&props.configure!=1">
        <GuideB :key="key" :preview="props.preview" :items="item.dataList.ad" :configure="props.configure" :floor-id="item.floorId" />
      </template>
      <template v-else-if="item.componentCode == 'addcredit'">
        <Addcredit :key="key" :preview="props.preview" :configure="props.configure" :creditlink="item.dataList.floor.creditLink" :flowlink="item.dataList.floor.flowLink" :floor-id="item.floorId" :design="item.dataList.floor.design" />
      </template>
      <template v-else-if="item.componentCode == 'sharing'&&props.configure!=1">
        <Serviceshare :key="key" :preview="props.preview" :floor-id="item.floorId" :floor-data="item.dataList.floor" />
      </template>
      <template v-else-if="item.moduleCode == 'goods'">
        <Goods 
          :key="key" 
          :preview="props.preview" 
          :items="item.dataList&&item.dataList.ad?item.dataList.ad:[]" 
          :floor="item.dataList.floor" 
          :component-code="item.componentCode" 
          :module-id="item.moduleId"
          :act-id="props.actId"
          :floor-id="item.floorId"
          :floor-sort="item.floorSort"
          :selector-key="item.selectorKey"
          :configure="props.configure"
          tpl-id="2"
        />
      </template>
      <!-- {{ item }} -->
    </template>
  </div>
</template>

<script setup>
import "vant/lib/index.css"
import Serviceshare from "@/components/index/serviceShare"
import Vip from "@/components/index/vip"
import Notice from "@/components/index/notice"
import Single from "@/components/index/single"
import Bigsize from "@/components/index/bigsize"
import CarouselA from "@/components/index/carouselA"
import CarouselB from "@/components/index/carouselB"
import GuideA from "@/components/index/guideA"
import GuideB from "@/components/index/guideB"
import Goods from "@/components/index/goods"
import Addcredit from "@/components/index/addcredit"
import Broadband from "@/components/index/broadband"
import insertCode from "@/utils/insertCode"
import {getImgUrl} from '@/utils/utils'
import {reactive,ref,defineProps,watch} from 'vue'
const props = defineProps(
  {
    pagedata:{
      type:Array,
      default:(data)=>{
        return data
      }
    },
    preview:{
      type:String,
      default:(data)=>{
        return data
      }
    },
    configure:{
      type:String,
      default:(data)=>{
        return data
      }
    },
    actId:{
      type:String,
      default:(data)=>{
        return data
      }
    },
    logined:{
      type: [Boolean,Function],
      default: null
    }
  }
)
const normalData = reactive({
  pageData: null,
})
function initPageData(val){
  normalData.pageData = val
  val.forEach((element,index) => {
    if(element.adSource=='cmData'&&element.dataList.ad){
      element.dataList.ad.forEach((item,key)=>{
        item.imgSrc = getImgUrl(item.imgsrc)
        item.adLink = item.link_skuid
      })
    }
  })
}
watch(()=>props.pagedata,(val)=>{
  insertCode("yundian")
  if(val){
    initPageData(val)
  }
},{immediate:true})
</script>
<style>
*{box-sizing:border-box;outline:none;}
body {background: #F1F1F1 !important;font-size: 12px;}
.pt12{padding-top:12px ;}
.fs12{font-size:12px;}
.fs14{font-size:14px;}
.bgwhite{width:100%;background: #fff;}
.mt7{margin-top:7px!important;}
a {
  color: #333333;
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
  user-select: none;
  -moz-user-focus: none;
  -moz-user-select: none;
  }
</style>
<style lang="scss" scoped>
.index {
  color: #333333;
  a {
    color: #333333;
  }
}
</style>
