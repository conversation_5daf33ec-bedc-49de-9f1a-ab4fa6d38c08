<template>
  <!--内蒙模板-->
  <div class="index n1" :class="[props.skin ? props.skin : '']">
    <div
      v-if="props.configure != 1"
      class="title"
      :class="[props.skin ? props.skin + '_title' : 'def_title']"
    >
      <div class="flex">
        <span class="icon icon_user"></span>
        <div
          v-if="gettersData.user && gettersData.user.userInfo && gettersData.user.userInfo.UserName"
          class="userName"
        >
          {{
            gettersData.user.userInfo.UserName.slice(0, 3) +
              "****" +
              gettersData.user.userInfo.UserName.slice(7)
          }}
          <span v-if="gettersData.user.isMember" class="icon icon_vip"></span>
        </div>
        <div v-else class="userName" @click="openBtnlogin">
          立即登录
        </div>
        <Icon v-if="showSubMsgIcon" class="iconfont icon-subscribe" name="subscribe" color="#fff" @click="Submsg()" />
        <span class="icon icon_search" @click="goToSearch"></span>
        <span class="icon icon_quan" @click="openQuan"></span>
      </div>
    </div>
    <div class="normal_1">
      <template v-for="(item, key) in indexData.pageData">
        <!-- 首页推荐运营位1 -->
        <template
          v-if="item.componentCode == 'homeRecommend' && props.configure != 1"
        >
          <IndexHomeRecommend :key="key" :data-list="item.dataList" />
        </template>

        <!-- 聚合页组件 -->
        <indexHomePageAggregation v-if="item.componentCode == 'hdpage'" :key="key" :floor-id="item.floorId" :data-list="item.dataList" />

        <template v-if="item.componentCode == 'contact' && props.configure != 1">
          <Contact
            :key="key"
            ref="contactRef"
            :logined="props.logined"
            :preview="props.preview"
            :floor-id="item.floorId"
            :data-list="item.dataList"
            :cardnewvip="indexData.cardvip"
            :floor-data="item.dataList.floor"
            :show-share="true"
            tpl-id="3"
            @handleOnlineWatchShop="handleOnlineWatchShop"
          />
        </template>
        <template v-else-if="item.componentCode == 'notice' && props.configure != 1">
          <Notice :key="key" :content="item.dataList.floor.title" />
        </template>
        <template v-else-if="item.componentCode == 'bigsize' && props.configure != 1">
          <template v-for="(bigsize, index) in item.dataList.ad">
            <Bigsize
              :key="`${key}_bigsize_${index}`"
              :bannersrc="bigsize.imgSrc"
              :adlink="bigsize.adLink ? bigsize.adLink : 'javascript:void(0)'"
              :preview="props.preview"
              :floor-id="item.floorId"
              :configure="props.configure"
            />
          </template>
        </template>
        <template
          v-else-if="item.componentCode == 'carouselA' && props.configure != 1"
        >
          <CarouselA
            :key="key"
            :preview="props.preview"
            :items="item.dataList.ad"
            :configure="props.configure"
            carousel-class="carouselA_4"
            :floor-id="item.floorId"
          />
        </template>
        <template v-if="item.componentCode == 'addcredit'">
          <GuideNew
            :key="key + '_1'"
            ref="guideRef"
            :items="indexData.guideNewList"
            :floor-id="item.floorId"
            :preview="props.preview"
            :hasbanner="indexData.hasbanner"
          />
        </template>
      </template>
      <Tabs v-model="indexData.activeName" sticky>
        <Tab title="移动业务" name="q1">
          <Tabs
            v-model="indexData.moveName"
            :before-change="beforeChangeMoveName"
            sticky
            type="card"
            class="vant-tabs-tpl4"
          >
            <template v-for="(item, key) in indexData.pageData">
              <template
                v-if="item.moduleCode == 'goods' &&
                  (item.selectorKey == 4 ||
                    item.selectorKey == 3 ||
                    item.selectorKey == 2 ||
                    item.selectorKey == 1 ||
                    item.selectorKey == 8)
                "
              >
                <GoodsTab
                  :key="key"
                  :preview="props.preview"
                  :pagedata="indexData.pageData"
                  :items="
                    item.dataList && item.dataList.ad ? item.dataList.ad : []
                  "
                  :floor="item.dataList.floor"
                  :component-code="item.componentCode"
                  :module-id="item.moduleId"
                  :act-id="props.actId"
                  :floor-id="item.floorId"
                  :floor-sort="item.floorSort"
                  :selector-key="item.selectorKey"
                  :configure="props.configure"
                  tpl-id="2"
                  :show-page-datasingle="true"
                  :page-datasingle4="indexData.pageDatasingle4"
                  :show-share="true"
                  :move-name="indexData.moveName"
                  :active-name="indexData.activeName"
                  :get-goods-data="getGoodsData"
                  @emitPrecisionMarketing="emitPrecisionMarketing"
                />
              </template>
              <!-- {{ item }} -->
            </template>
          </Tabs>
        </Tab>
        <template v-for="(item, key) in indexData.pageData">
          <template
            v-if="
              item.moduleCode == 'goods' &&
                item.selectorKey != 4 &&
                item.selectorKey != 3 &&
                item.selectorKey != 2 &&
                item.selectorKey != 1 &&
                item.selectorKey != 8
            "
          >
            <GoodsTab
              :key="key"
              :preview="props.preview"
              :items="item.dataList && item.dataList.ad ? item.dataList.ad : []"
              :floor="item.dataList.floor"
              :component-code="item.componentCode"
              :module-id="item.moduleId"
              :act-id="props.actId"
              :floor-id="item.floorId"
              :floor-sort="item.floorSort"
              :selector-key="item.selectorKey"
              :configure="props.configure"
              :move-name="indexData.moveName"
              :active-name="indexData.activeName"
              tpl-id="2"
              :show-share="true"
              :get-goods-data="getGoodsData"
            />
          </template>
        </template>
      </Tabs>
    </div>

  </div>
</template>

<script setup>
import "vant/lib/index.css"
import Contact from "@/components/index/contact"
import IndexHomeRecommend from "@/components/index/indexHomeRecommend"
import indexHomePageAggregation from "@/components/index/indexHomePageAggregation"
import Notice from "@/components/index/notice"
import CarouselA from "@/components/index/carouselA"
import GuideC from "@/components/index/guideC"
import GuideNew from "@/components/index/guideNew.vue"
import {
  getImgUrl,
  getAllSearchParamsArray,
  setSearchParamsArray,
  getCookie,
  copy
} from "@/utils/utils"
import stqurl from "@/utils/stqurl"
import {reactive,ref,getCurrentInstance,computed, inject,watch} from "vue"
import { Toast, Tab, Tabs, Icon } from "vant"
import GoodsTab from "@/components/index/goodsTab.vue"
import shopAPI from "@/api/shop"
import insertCode from "@/utils/insertCode"
import encryption from "@/api/encryption"
import PrecisionMarketing from "@/model/precisionMarketing"
import EventBus from "@/api/eventbus"
import UA from "@/utils/ua"
import {ENV} from "@/utils/env.js"
import lodash from 'lodash'
const getSon = inject("getSon")
const props = defineProps({
  skin: {//皮肤
    type: String,
    default: null,
  },
  pagedata: {//页面数据
    type: Array,
    default: (data) => {
      return data
    },
  },
  preview: {//预览页面
    type: String,
    default: (data) => {
      return data
    },
  },
  configure: {//配置页面
    type: String,
    default: (data) => {
      return data
    },
  },
  actId: {//预览页面标识
    type: String,
    default: (data) => {
      return data
    },
  },
  logined: {//是否登录
    type: [Boolean, Function],
    default: null,
  }
})
const indexData = reactive({
  pageData: null,
  actTitle: null,
  actDesc: null,
  isLogined: null,
  noticeContent: "公告内容",
  guideList: [],
  guideNewList:[],
  ad9: null,
  ad10: null,
  appointmentStatus: null,
  queueStatus: null,
  activeName: "q1",
  moveName: 13, //getData的layOut里边的排序顺序，floorSort
  cardvip: "cardvip",
  broadbandStatus: false,
  quanlink: null,
  hasbanner: false,
  pageDatasingle4: null,
  getApoloQueryData: {
    apoloPageSize: 18,
    apoloPageNumber: 1,
    isGetApolo: false,
  }
})

const gettersData = reactive({
  pageInfo:null,
  user:null
})
const getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null
if(proxy){
  gettersData.pageInfo = proxy.$store.getters.pageInfo
  gettersData.user = proxy.$store.getters.user
}
const pageInfoSendData = computed(()=>{
  if(getSon){
    return getSon.pageInfofn()
  }
  return null
})
const showSubMsgIcon = computed(()=>{
  if(myShowData.value && myShowData.value.dingYueXiaoXiIcon){
    return false
  }
  if(gettersData.user && gettersData.user.userInfo && gettersData.user.userInfo.UserName && UA.isApp){
    return true
  }
  return false
})
// 打开订阅弹框
function Submsg(){
  return getSon.Submsg()
}
watch(()=>props.pagedata,(val)=>{
  if (val && proxy) {
    initPageData(val)
  }
},{immediate: true})
// 精准营销商品分享回调
const contactRef = ref(null)
function emitPrecisionMarketing(item) {
  PrecisionMarketing.emitPrecisionMarketing(contactRef.value, item)
}

function beforeChangeMoveName(index) {
  return PrecisionMarketing.beforeChangeMoveName(
    index,
    (callback,showCancle)=>{
      openloginSelf(callback,showCancle)
    },
    props.pagedata
  )
}

//跳转优惠券加密参数
async function getEncryption() {
  await encryption
    .getEncryption({
      shopId: gettersData.pageInfo.pageInfo.shopId,
      type: "type1",
    })
    .then((res) => {
      if (res.code == 0 && res.data) {
        indexData.quanlink = res.data.shareParam
        EventBus.$emit("shareparam", res.data.shareParam)
      } else {
        Toast(res.message)
      }
    })
}
/* 点击移动logo的在线看店 */
function handleOnlineWatchShop() {
  if (gettersData.pageInfo.pageInfo.shopOnlineStatus != 1) {
    insertCode("yd_index_" + gettersData.pageInfo.pageInfo.shopId + "_icon_zxkd")
    Toast("该功能正在开放中，敬请期待")
    return false
  }
  insertCode(
    "yd_index_" + gettersData.pageInfo.pageInfo.shopId + "_icon_zxkd",
    "/yundian/onlinewatchshop/index.html?shopId=" +
      gettersData.pageInfo.pageInfo.shopId
  )
}
async function initPageData(val) {
  indexData.pageData = val
  let arr1 = [],arr2 = []
  val.forEach((element, index) => {
    if (element.adSource == "cmData" && element.dataList.ad) {
      element.dataList.ad.forEach((item, key) => {
        item.imgSrc = getImgUrl(item.imgsrc)
        item.adLink = item.link_skuid
      })
    }
    if (
      element.componentCode == "carouselA" &&
      element.dataList &&
      element.dataList.ad &&
      element.dataList.ad.length > 0
    ) {
      indexData.hasbanner = true //用于没有banner图的时候使得导航部分padding-top要大一些
    }
    if (
      element.componentCode == "single" &&
      element.adPosition == "goods"
    ) {
      indexData.pageDatasingle4 = element
    }
    if (element.componentCode == "addcredit") {
      if (UA.isApp) {
        let flowUrl = new URL(element.dataList.floor.flowLink)
        element.dataList.floor.flowLink =
          "https://flow.clientaccess.10086.cn/leadeon-flow/pages/flow/payFlow.html" +
          flowUrl.search
      }
      let allParams = getAllSearchParamsArray(location.href)
      let ac_id = allParams["WT.ac_id"] || getCookie("ac_id")
      arr1 = [
        {
          adLink: ac_id
            ? setSearchParamsArray(element.dataList.floor.flowLink, {
              "WT.ac_id": ac_id,
            })
            : element.dataList.floor.flowLink, //流量充值
          imgSrc: require("@/assets/index_normal/guide_icon3.png"),
          title: "流量直充",
          dcsId: "llzc",
          class: "llzc",
        },
        {
          adLink: ac_id
            ? setSearchParamsArray(element.dataList.floor.creditLink, {
              "WT.ac_id": ac_id,
            })
            : element.dataList.floor.creditLink, //话费充值
          imgSrc: require("@/assets/index_normal/guide_icon4.png"),
          title: "话费直充",
          dcsId: "hfzc",
          class: "hfzc",
        },
      ]
    }
    if (element.componentCode == "broadband") {
      arr2 = [
        {
          adLink:"/yundian/coupon/index.html?shopId=" +gettersData.pageInfo.pageInfo.shopId,//领券中心
          imgSrc: require("@/assets/index_normal/guide_icon5.gif"),
          title: "领券中心",
          dcsId: "lqzx",
          class: "lqzx",
        },
        {
          adLink: stqurl.busiqrydeal,
          imgSrc: require("@/assets/index_normal/guide_icon6.png"),
          title: "已订业务",
          dcsId: "ydyw",
          class: "ydyw",
        },
        {
          adLink:`/yundian/broadband/usersidelist.html?shopId=${gettersData.pageInfo.pageInfo.shopId}`, //宽带预约
          sectionId:lodash.get(element,'dataList.floor.sectionId'),
          imgSrc: require("@/assets/index_normal/guide_icon7.png"),
          title: "预约专区",
          linkId: "broadband",
          dcsId: "kdyy",
          class: "kdyy",
        },
        {
          adLink: "https://touch.10086.cn/hd/skin/500071/100_100.html", //优惠专区
          imgSrc: require("@/assets/guide/guide_icon8.png"),
          title: "优惠专区",
          dcsId: "yjzq",
          class: "yjzq",
        },
      ]
    }
  })
  await getEncryption()
  let guideList = [
    {
      //adLink:url,//在线取号
      adLink: "https://paidui.coc.10086.cn/zhyytQueueTakeNum/#/",
      imgSrc: require("@/assets/index_normal/guide_icon1.gif"),
      title: "排队取号",
      linkId: "quhao",
      queueStatus: gettersData.pageInfo.pageInfo.queueStatus,
      appointmentStatus: gettersData.pageInfo.pageInfo.appointmentStatus,
      dcsId: "zxqh",
      class: "zxqh",
    },
    {
      // adLink:'https://touch.10086.cn/i/mobile/packremainqry.html',//套餐余量链接
      adLink: stqurl.packremainqry,
      imgSrc: require("@/assets/index_normal/guide_icon2.png"),
      title: "套餐余量",
      dcsId: "tcyl",
      class: "tcyl",
    }
  ]
  indexData.guideList = [].concat(guideList,arr1, arr2)
  //爆款潮品sort是90，如果是运营位配置的数据单独请求接口覆盖
  let apoloData = await getApoloData()

  let floorSort90Index = val.findIndex(
    (item) => item.floorSort == 90
  )
  let element = JSON.parse(JSON.stringify(val[floorSort90Index]))
  element.selectorKey = 6
  element.sortKey = 6
  element.dataList.ad = apoloData
  val[floorSort90Index] = element

  // 获取到数据后强制渲染

  // 如果sessionStorage有的话, 并且已经登录了, 并且是点击套餐进来的首页
  let single = document.querySelector(".single")
  PrecisionMarketing.istclogin(single, () => {
    indexData.moveName = 70
  })
}
function openBtnlogin(callback,showCancle) {
  insertCode(
    "yd_index_" + gettersData.pageInfo.pageInfo.shopId + "_loginbar_login"
  )
  openloginSelf(callback,showCancle)
}
function openloginSelf(callback,showCancle) {
  return getSon.openlogin(callback,showCancle)
}

function openQuan() {
  if (!props.logined) {
    //强登
    openloginSelf(gotoQuanlogined,true)
  } else {
    insertCode(
      "yd_index_" + gettersData.pageInfo.pageInfo.shopId + "_icon_wdkq",
      `https://touch.10086.cn/i/mobile/mycoupons.html?shareParam=` +
        indexData.quanlink
    )
  }
}
function gotoQuanlogined(res) {
  if (res && res.UserName > "") {
    // indexData.$parent.logined(res)
    insertCode(
      "yd_index_" + gettersData.pageInfo.pageInfo.shopId + "_loginbar_coupon",
      `https://touch.10086.cn/i/mobile/mycoupons.html?shareParam=` +
        indexData.quanlink
    )
  }
}
function goToSearch() {
  if(proxy){
    proxy.$router.push({
      path: "/searchlist/index.html?shopId=" + gettersData.pageInfo.pageInfo.shopId,
    })
  }

}


async function getApoloData() {
  let AllData = []
  if (!indexData.getApoloQueryData.isGetApolo) {
    //爆款潮品运营位全量数据
    AllData = await shopAPI
      .getFashionCmData({
        shopId: gettersData.pageInfo.pageInfo.shopId
      })
      .then((r) => {
        if (r.code === 0 && r.data && r.data.ad) {
          return r.data.ad
        } else {
          return []
        }
      })
    AllData.forEach((value) => {
      value.goodsStatus = 51
    })
    indexData.getApoloQueryData.isGetApolo = true
  }
  if (indexData.getApoloQueryData.isGetApolo) {
    let apoloData = await shopAPI
      .getFashionApoloData({
        shopId: gettersData.pageInfo.pageInfo.shopId,
        pageSize: indexData.getApoloQueryData.apoloPageSize,
        pageNum: indexData.getApoloQueryData.apoloPageNumber,
      })
      .then((r) => {
        if (r.code === 0 && r.data && r.data.ad) {
          return r.data.ad
        } else {
          return []
        }
      })
    if (apoloData.length >= indexData.getApoloQueryData.apoloPageSize) {
      indexData.getApoloQueryData.apoloPageNumber++
    }else{
      indexData.getApoloQueryData.finished = true
      indexData.getApoloQueryData.isGetApolo = false
    }
    AllData = AllData.concat(apoloData)
  }
  return AllData
}
async function getGoodsData(sort, pageSize, pageNum) {
  // 爆款潮品sort是90，如果是运营位配置的数据单独请求接口覆盖
  if (sort=== 90) {
    if(indexData.getApoloQueryData.finished){
      return []
    }
    return await getApoloData()
  }else{
    return await getYdGoodsData({sort,pageSize,pageNum})
  }
}
async function getYdGoodsData({ sort, pageSize, pageNum }) {
  let { shopId, actId, preview, configure } = pageInfoSendData.value
  let ydscData = await shopAPI
    .getDataBySort({
      shopId,
      actId,
      preview,
      configure,
      sort,
      pageSize,
      pageNum,
    })
    .then((r) => {
      if (r.code === 0 && r.data && r.data.ad) {
        return r.data.ad
      } else {
        return []
      }
    })
  return ydscData
}
const myShowData = computed(()=>{
  if(getSon && getSon.myShowDatafn){
    return getSon.myShowDatafn()
  }else{
    return {}
  }
})
watch(myShowData,(val)=>{
  if(val){
    indexData.guideNewList = val.iconList
  }
},{immediate:true})
</script>
<style>
* {
  box-sizing: border-box;
  outline: none;
}
body {
  background: #f1f1f1 !important;
  font-size: 12px;
}
.pt12 {
  padding-top: 12px;
}
.fs12 {
  font-size: 12px;
}
.fs14 {
  font-size: 14px;
}
.bgwhite {
  width: 100%;
  background: #fff;
}
.mt7 {
  margin-top: 7px !important;
}
a {
  color: #333333;
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
  user-select: none;
  -moz-user-focus: none;
  -moz-user-select: none;
}
.head {
  z-index: 10;
}
.index {
  background: #fff !important;
}
.van-tabs__content .van-sticky {
  top: 43.875px !important;
}
</style>
<style lang="scss" scoped>
.index {
  color: #333333;
  a {
    color: #333333;
  }
}
.icon {
  display: block;
  width: 39px;
  height: 39px;
  margin: 0 12px;
}
.title {
  background-size: cover;
  height: 180px;
  position: absolute;
  width: 100%;
}
.def_title {
  background: #fff url(~@/assets/index_normal/bg.png) 0 0 no-repeat;
  background-size: cover;
}
.normal_1 {
  padding-top: 35px;
}
.flex {
  display: flex;
  height: 47px;
  align-items: center;
}
.userName {
  flex: 1;
  line-height: 47px;
  font-size: 16px;
  font-weight: 400;
  text-align: left;
  color: #fff;
}
.icon_user {
  background: url(~@/assets/index_normal/user.png) 0 0 no-repeat;
  background-size: contain;
}
.icon_quan {
  width: 52px;
  height: 20px;
  background: url(~@/assets/index_normal/quan.png) 0 0 no-repeat;
  background-size: contain;
}
.icon_search {
  width: 18px;
  height: 18px;
  background: url(~@/assets/searchlist/search2.png) 0 0 no-repeat;
  background-size: contain;
  margin-right: 2px;
}
.icon-subscribe{
  font-size: 18px;
  color:#fff;
}
.icon_vip {
  background: url(~@/assets/index_normal/vip_user.png) 0 0 no-repeat;
  width: 14px;
  height: 12.5px;
  display: inline-block;
  background-size: contain;
  margin-left: 6px;
}
.vant-tabs-tpl4 {
  :deep(.van-tabs__nav--card) {
    border: none !important;
    margin-top: 13px;
    margin-bottom: 13px;
    .van-tab {
      border: none;
      font-size: 13px;
      font-weight: 500;
      text-align: center;
      color: #999999;
      &.van-tab--active {
        width: 64px;
        height: 32px;
        background: rgba(#f55050, 0.1);
        border-radius: 16px;
        color: #f55050;
      }
    }
  }
  :deep(.van-sticky--fixed) {
    background: #fff;
    .van-tabs__nav--card {
      margin-top: 0;
      margin-bottom: 5px;
    }
  }
}
</style>
