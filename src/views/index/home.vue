<template>
  <div>
    <HeaderNav
      v-if="showShopName"
      :font-size="18"
      :height="44"
      :title="myShopData.shopShortName"
      :show-go-back="false"
    ></HeaderNav>
    <div class="home" :class="{'padding-top-44':showShopName}">
      <component :is="item.template" v-for="item in components" :key="item.key" v-bind="item.props"></component>
      <Goods
        v-for="item in floorList"
        :key="item.floorId"
        :floor="floor"
        :items="item"
        :shopshare="share"
      />
      <Share :shopshare="share" />
    </div>
  </div>
</template>

<script setup>
import { ref ,inject,provide, reactive,getCurrentInstance, watch,computed, onBeforeMount} from "vue"
import HeaderNav from "@/views/my/components/headerNav.vue"

import Goods from "@/components/home/<USER>"
import Share from "@/components/index/share.vue"
import { Toast} from 'vant'
import {ENV} from "@/utils/env.js"
import insertCode from "@/utils/insertCode.js"
import {getCookie,getAllSearchParamsArray,setSearchParamsArray} from "@/utils/utils.js"
import shopAPI from "@/api/shop.js"
import encryption from "@/api/encryption.js"
import EventBus from "@/api/eventbus.js"
import UA from "@/utils/ua.js"
import { STAFFRULE } from "@/utils/homedata.js"
import {getComponents} from "./home.js"
const props = defineProps({
  shopData:{
    type:Object,
    default:(obj)=>{
      return obj || {}
    }
  }
})
let showShopName = ref(false)
showShopName.value = !UA.isWechat&&!UA.isWechatWork&&!UA.isApp&&!UA.isIosQQ&&!UA.isAndroidQQ
let getSon = inject("getSon")
const myShopData = props.shopData
function insertCodeGoods(key) {
  let dcs_id = key
  insertCode("yd_index_" + myShopData.shopId + "_" + dcs_id)
}
const share = ref({
  url: location.href,
  imgUrl: 'https://img0.shop.10086.cn/favicon.png__175.png',
  dcsId: 'yd_sharestore_' + myShopData.shopId + '_share',
  desc: myShopData.propaganda,
  title: myShopData.shopShortName,
  unifiedChannelId:myShopData.uniChannelId,
  address:myShopData.address,
  shortName:myShopData.shopShortName,
  provinceId:myShopData.province
})
//分享店铺
function shareShop(obj, item = {}) {
  let allParams = getAllSearchParamsArray(location.href)
  let ac_id = allParams["WT.ac_id"] || getCookie("ac_id")
  let isExecuteNow = UA.isWechatWork
  let sharingShopUrl = indexData.shopInformation.sharingShopUrl || share.value.url
  // obj, 有机会删掉, 代码review
  EventBus.$emit(
    "openShareDialog",
    "shop",
    {
      url: ac_id
        ? setSearchParamsArray(sharingShopUrl, { "WT.ac_id": ac_id })
        : sharingShopUrl
    },
    isExecuteNow,
    share.value,
  )
  insertCodeGoods("sharestore")
}
const indexData = reactive({
  quanlink:null
})
indexData.shopInformation = {
  ...myShopData,
  shopName:myShopData.shopShortName
}
let getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null
const user = ref(null)
if(proxy){
  user.value = proxy.$store.getters.user
}
//是否展示分享标识
const showShare = computed(()=>{
  if (user.value && user.value.userInfo) {
    return (
      user.value.userInfo.shopId == myShopData.shopId &&
      (user.value.userInfo.RuleId == STAFFRULE.CLERK || user.value.userInfo.RuleId == STAFFRULE.SHOPKEEPER)
    )
  } else {
    return false
  }
})
//跳转登录
function openloginSelf(callback,showCancle) {
  return getSon.openlogin(callback,showCancle)
}
//跳转优惠券加密参数
async function getEncryption() {
  await encryption
    .getEncryption({
      shopId: myShopData.shopId,
      type: "type1",
    })
    .then((res) => {
      if (res.code == 0 && res.data) {
        indexData.quanlink = res.data.shareParam
      } else {
        Toast(res.message)
      }
    })
}
//跳转优惠券页面
function openQuan() {
  if (!props.logined) {
    //强登
    openloginSelf(gotoQuanlogined,true)
  } else {
    insertCode(
      "yd_index_" + myShopData.shopId + "_icon_wdkq",
      `https://touch.10086.cn/i/mobile/mycoupons.html?shareParam=` +
        indexData.quanlink
    )
  }
}
function gotoQuanlogined(res) {
  if (res && res.UserName > "") {
    insertCode(
      "yd_index_" + myShopData.shopId + "_loginbar_coupon",
      `https://touch.10086.cn/i/mobile/mycoupons.html?shareParam=` +
        indexData.quanlink
    )
  }
}
const hasBanner = ref(false)
const components = ref(null)
/**更新组件数据 */
components.value = getComponents({indexData,shareShop,hasBanner,openQuan})
/**获取页面基本数据 */
function getIndexData(){
  shopAPI.getBaseInfo({shopId:myShopData.shopId}).then(res=>{
    if(res.code){
      Toast(res.message)
    }else{
      let indexObj = res.data
      indexData.bannerObj = indexObj.header
      indexData.shopInformation = {
        ...myShopData,
        ...indexObj.baseInfo,
      }
      indexData.iconlist = indexObj.iconList
      indexData.homeRecommend = {
        dataList:[
          indexObj.recommendLeft,indexObj.recommendRight
        ],
        type:indexObj.recommendStyle
      }
      indexData.recommend = indexObj.recommendStyle
      hasBanner.value = (indexObj.header && indexObj.header.imgUrl) ? true :false
      indexData.centerBannerArr = indexObj.waistList
      components.value = getComponents({indexData,shareShop,hasBanner,openQuan})
    }
  })
}
const floor = ref({
  shopId:myShopData.shopId
})
const floorList = ref([])
/**获取页面商品数据 */
function getGoods(){
  shopAPI.getGoods({shopId:myShopData.shopId}).then(res=>{
    if(res.code){
      Toast(res.message)
    }else{
      if( res.data && res.data.homepageShopFloorList ){
        floorList.value = res.data.homepageShopFloorList
      }
    }
  })
}
watch(user.value,(val,oldVal)=>{
  if(!val.userInfo){
    getGoods()
  }
})
onBeforeMount(()=>{
  getIndexData()
  getGoods()
  getEncryption()
})

provide("showShare",showShare)
</script>
<style lang="scss" scoped>
.home{
  min-height: 101vh;
  background-image: linear-gradient(177deg, #FFFFFF 88%, #F5F5F5 100%);
  &::after{
    content: "";
    display: table;
  }
}
.padding-top-44{
  padding-top: 44px;
}

.header-nav{
  position: fixed !important;
  z-index: 1111111111;
}
</style>
