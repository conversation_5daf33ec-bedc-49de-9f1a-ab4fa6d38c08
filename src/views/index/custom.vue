<template>
  <div>
    <template v-for="(item,key) in props.pagedata">
      <template v-if="item.componentCode == 'member'">
        <Vip :key="key" :logined="props.logined" :floor-id="item.floorId" :preview="props.preview" :data-list="item.dataList" />
      </template>
      <template v-else-if="item.componentCode == 'notice'">
        <Notice :key="key" :content="item.dataList.floor.title" />
      </template>
      <template v-else-if="item.componentCode == 'single'">
        <template v-for="(single ,index) in item.dataList.ad">
          <Single :key="`${key}_single_${index}`" :bannersrc="single.imgSrc" :adlink="single.adLink?single.adLink:'javascript:void(0)'" :preview="props.preview" />
        </template>
      </template>
      <template v-else-if="item.componentCode == 'bigsize'">
        <template v-for="(bigsize ,index) in item.dataList.ad">
          <Bigsize :key="`${key}_bigsize_${index}`" :bannersrc="bigsize.imgSrc" :adlink="bigsize.adLink?bigsize.adLink:'javascript:void(0)'" :preview="props.preview" />
        </template>
      </template>
      <template v-else-if="item.componentCode == 'carouselA'">
        <CarouselA :key="key" :preview="props.preview" :items="item.dataList.ad" />
      </template>
      <template v-else-if="item.componentCode == 'carouselB'">
        <CarouselB :key="key" :preview="props.preview" :items="item.dataList.ad" />
      </template>
      <template v-else-if="item.componentCode == 'guideA'">
        <GuideA :key="key" :preview="props.preview" :items="item.dataList.ad" />
      </template>
      <template v-else-if="item.componentCode == 'guideB'">
        <GuideB :key="key" :preview="props.preview" :items="item.dataList.ad" />
      </template>
      <template v-else-if="item.componentCode == 'addcredit'">
        <Addcredit 
          :key="key" 
          :preview="props.preview" 
          :creditlink="item.dataList.floor.creditLink" 
          :flowlink="item.dataList.floor.flowLink"
          :floor-id="item.floorId" 
          :design="item.dataList.floor.design"
        />
      </template>
      <template v-else-if="item.componentCode == 'sharing'">
        <Serviceshare :key="key" :preview="props.preview" :floor-id="item.floorId" :floor-data="item.dataList.floor" />
      </template>
      <template v-else-if="item.moduleCode == 'goods'">
        <Goods 
          :key="key" 
          :preview="props.preview" 
          :items="item.dataList&&item.dataList.ad?item.dataList.ad:[]" 
          :floor="item.dataList&&item.dataList.floor?item.dataList.floor:{}" 
          :component-code="item.componentCode" 
        />
      </template>
      <!-- {{ item }} -->
    </template>
  </div>
</template>

<script setup>
import "vant/lib/index.css"
import Notice from "@/components/index/notice"
import Vip from "@/components/index/vip"
import Single from "@/components/index/single"
import Bigsize from "@/components/index/bigsize"
import CarouselA from "@/components/index/carouselA"
import CarouselB from "@/components/index/carouselB"
import GuideA from "@/components/index/guideA"
import GuideB from "@/components/index/guideB"
import Goods from "@/components/index/goods"
import Addcredit from "@/components/index/addcredit"
import Serviceshare from "@/components/index/serviceShare"
import {defineProps,reactive} from "vue"
const props = defineProps({
  pagedata:{
    type:Array,
    default:(data)=>{
      return data
    }
  },
  preview:{
    type:String,
    default:(data)=>{
      return data
    }
  },
  shopname:{
    type:String,
    default:(data)=>{
      return data
    }
  },
  logined:{
    type: [Boolean,Function],
    default: null
  }
})
</script>

