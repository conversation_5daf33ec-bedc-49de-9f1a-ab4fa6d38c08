<template>
  <div class="index n4" :class="[props.skin ? props.skin : '']">
    <div
      v-if="props.configure != 1"
      class="title"
      :class="[props.skin ? props.skin + '_title' : 'def_title']"
    >
      <div class="flex">
        <span class="icon icon_user"></span>
        <div
          v-if="gettersData.user && gettersData.user.userInfo && gettersData.user.userInfo.UserName"
          class="userName"
        >
          {{
            gettersData.user.userInfo.UserName.slice(0, 3) +
              "****" +
              gettersData.user.userInfo.UserName.slice(7)
          }}
          <span v-if="gettersData.user.isMember" class="icon icon_vip"></span>
        </div>
        <div v-else class="userName" @click="openBtnlogin">
          立即登录
        </div>
        <Icon v-if="showSubMsgIcon" class="iconfont icon-subscribe" name="subscribe" color="#fff" @click="Submsg()" />
        <span class="icon icon_search" @click="goToSearch"></span>
        <span class="icon icon_quan" @click="openQuan"></span>
      </div>
    </div>
    <div class="normal_4">
      <template v-for="(item, key) in commonData.pageData">
        <!-- 首页推荐运营位1 -->
        <template
          v-if="item.componentCode == 'homeRecommend' && props.configure != 1"
        >
          <IndexHomeRecommend :key="key" :data-list="item.dataList" />
        </template>

        <!-- 聚合页组件 -->
        <indexHomePageAggregation
          v-if="item.componentCode == 'hdpage'"
          :key="key"
          :floor-id="item.floorId"
          :data-list="item.dataList"
        />

        <template v-if="item.componentCode == 'contact' && props.configure != 1">
          <Contact
            :key="key"
            ref="contactref"
            :logined="props.logined"
            :preview="props.preview"
            :floor-id="item.floorId"
            :data-list="item.dataList"
            :cardnewvip="commonData.cardvip"
            :floor-data="item.dataList.floor"
            :show-share="showShare"
            @handleOnlineWatchShop="handleOnlineWatchShop"
          />
        </template>
        <template v-else-if="item.componentCode == 'bigsize' && props.configure != 1">
          <template v-for="(bigsize, index) in item.dataList.ad">
            <Bigsize
              :key="`${key}_bigsize_${index}`"
              :bannersrc="bigsize.imgSrc"
              :adlink="bigsize.adLink ? bigsize.adLink : 'javascript:void(0)'"
              :preview="props.preview"
              :floor-id="item.floorId"
              :configure="props.configure"
            />
          </template>
        </template>
        <template
          v-else-if="item.componentCode == 'carouselA' && props.configure != 1"
        >
          <CarouselA
            :key="key"
            :preview="props.preview"
            :items="item.dataList.ad"
            :configure="props.configure"
            carousel-class="carouselA_4"
            :floor-id="item.floorId"
          />
        </template>
        <GuideB
          v-else-if="item.componentCode == 'iconlink' && props.configure != 1"
          ref="guideRef"
          :key="key + '_1'"
          :items="commonData.guideNewList"
          :preview="props.preview"
          :floor-id="item.floorId"
          guide-class="guide_4"
          :hasbanner="commonData.hasbanner"
        />
        <template
          v-else-if="
            item.componentCode == 'single' &&
              props.configure != 1 &&
              item.adPosition != 'goods'
          "
        >
          <template v-for="(single, index) in item.dataList.ad">
            <Single
              :key="`${key}_single_${index}`"
              :bannersrc="single.imgSrc"
              :adlink="single.adLink ? single.adLink : 'javascript:void(0)'"
              :preview="props.preview"
              :floor-id="item.floorId"
              :shop-id="gettersData.pageInfo.pageInfo.shopId"
              single-class="single_4"
              dcs-id="waistbanner_pic"
            />
          </template>
        </template>
        <template v-else-if="item.componentCode == 'sharing' && props.configure != 1">
          <Serviceshare
            :key="key"
            :preview="props.preview"
            :floor-id="item.floorId"
            :floor-data="item.dataList.floor"
          />
        </template>
        <template v-else-if="item.moduleCode == 'goods' && props.configure == 1">
          <Goods
            :key="key"
            :preview="props.preview"
            :items="item.dataList && item.dataList.ad ? item.dataList.ad : []"
            :floor="item.dataList.floor"
            :component-code="item.componentCode"
            :module-id="item.moduleId"
            :act-id="props.actId"
            :floor-id="item.floorId"
            :floor-sort="item.floorSort"
            :selector-sort-key="item.selectorSortKey"
            :configure="props.configure"
            tpl-id="2"
          />
        </template>
      </template>
      <Tabs
        v-if="props.configure != 1 && commonData.showGoodsTab"
        v-model="commonData.activeName"
        :class="commonData.showOneTab ? 'showOneTab' : ''"
        sticky
        swipeable
      >
        <template v-if="commonData.ydGoodsList && commonData.ydGoodsList.length === 1">
          <template v-for="(item, key) in commonData.ydGoodsList">
            <GoodsTab
              :key="'ydbg_' + key"
              :preview="props.preview"
              :pagedata="commonData.pageData"
              :items="item.dataList && item.dataList.ad ? item.dataList.ad : []"
              :floor="item.dataList.floor"
              :component-code="item.componentCode"
              :module-id="item.moduleId"
              :act-id="props.actId"
              :floor-id="item.floorId"
              :floor-sort="item.floorSort"
              :selector-key="item.selectorKey"
              :configure="props.configure"
              tpl-id="4"
              :show-page-datasingle="true"
              :page-datasingle4="commonData.pageDatasingle4"
              :show-share="showShare"
              :show-subtitle="item.selectorKey !== 3"
              :move-name="commonData.moveName"
              :active-name="commonData.activeName"
              :get-goods-data="getGoodsData"
              @emitPrecisionMarketing="emitPrecisionMarketing"
            />
          </template>
        </template>
        <Tab
          v-else-if="commonData.ydGoodsList && commonData.ydGoodsList.length > 1"
          title="移动业务"
          name="q1"
        >
          <Tabs
            v-model="commonData.moveName"
            :before-change="beforeChangeMoveName"
            sticky
            type="card"
            class="vant-tabs-tpl4"
          >
            <template v-for="(item, key) in commonData.ydGoodsList">
              <GoodsTab
                :key="'ydbg_' + key"
                :preview="props.preview"
                :pagedata="commonData.pageData"
                :items="
                  item.dataList && item.dataList.ad ? item.dataList.ad : []
                "
                :floor="item.dataList.floor"
                :component-code="item.componentCode"
                :module-id="item.moduleId"
                :act-id="props.actId"
                :floor-id="item.floorId"
                :floor-sort="item.floorSort"
                :selector-key="item.selectorKey"
                :configure="props.configure"
                tpl-id="4"
                :show-page-datasingle="true"
                :page-datasingle4="commonData.pageDatasingle4"
                :show-share="showShare"
                :show-subtitle="item.selectorKey !== 3"
                :move-name="commonData.moveName"
                :active-name="commonData.activeName"
                :get-goods-data="getGoodsData"
                @emitPrecisionMarketing="emitPrecisionMarketing"
              />
            </template>
          </Tabs>
        </Tab>
        <template v-for="(item, key) in commonData.otherGoodsList">
          <GoodsTab
            :key="'other_' + key"
            :preview="props.preview"
            :items="item.dataList && item.dataList.ad ? item.dataList.ad : []"
            :floor="item.dataList.floor"
            :move-name="commonData.moveName"
            :component-code="item.componentCode"
            :module-id="item.moduleId"
            :act-id="props.actId"
            :floor-id="item.floorId"
            :floor-sort="item.floorSort"
            :selector-key="item.selectorKey"
            :configure="props.configure"
            tpl-id="4"
            :show-share="showShare"
            :active-name="commonData.activeName"
            :get-goods-data="getGoodsData"
          />
        </template>
      </Tabs>
    </div>
    <template v-if="!commonData.showGoodsTab && props.configure == 1">
      <p style="text-align: center; margin-top: 100px; font-size: 20px">
        商品正在准备中，敬请期待…
      </p>
    </template>
  </div>
</template>

<script setup>
import "vant/lib/index.css"
import Serviceshare from "@/components/index/serviceShare"
import Contact from "@/components/index/contact"
import Single from "@/components/index/single"
import CarouselA from "@/components/index/carouselA"
import GuideB from "@/components/index/guideNew"
import GuideNew from "@/components/index/guideNew.vue"
import IndexHomeRecommend from "@/components/index/indexHomeRecommend"
import indexHomePageAggregation from "@/components/index/indexHomePageAggregation"
import insertCode from "@/utils/insertCode"
import {
  getImgUrl,
  getAllSearchParamsArray,
  setSearchParamsArray,
  getCookie,
  copy
} from "@/utils/utils"
import stqurl from "@/utils/stqurl"
import {inject,reactive,ref,defineProps,getCurrentInstance,computed, watch} from "vue"
import { Toast, Tab, Tabs, Icon } from "vant"
import GoodsTab from "@/components/index/goodsTab.vue"
import Goods from "@/components/index/goods"
import encryption from "@/api/encryption"
import PrecisionMarketing from "@/model/precisionMarketing"
import shopAPI from "@/api/shop"
import EventBus from "@/api/eventbus"
import UA from "@/utils/ua"
let getSon = inject("getSon")
const props = defineProps({
  skin: {//皮肤
    type: String,
    default: null,
  },
  pagedata: {//页面数据
    type: Array,
    default: (data) => {
      return data
    },
  },
  preview: {//预览页面
    type: String,
    default: (data) => {
      return data
    },
  },
  configure: {//配置页面
    type: String,
    default: (data) => {
      return data
    },
  },
  actId: {//预览页面标识
    type: String,
    default: (data) => {
      return data
    },
  },
  logined: {//是否登录
    type: [Boolean, Function],
    default: null,
  },
  merchantCtrl: {
    //1:店长配置 2:商户控制
    type: Number,
    default: () => {
      return 1
    },
  }
})
const commonData = reactive({
  pageData: null,
  actTitle: null,
  actDesc: null,
  isLogined: null,
  noticeContent: "公告内容",
  guideList: [],
  singleX: null,
  activeName: "q1",
  moveName: 13, //getData的layOut里边的排序顺序，floorSort
  cardvip: "cardvip",
  pageDatasingle4: null,
  hasbanner: false,
  quanlink: null,
  //移动业务tab里的商品类型
  showYdKeys: [1, 2, 3, 4, 8],
  //移动业务同tab里的商品类型
  otherGoodsList: [],
  ydGoodsList: [],
  showGoodsTab: false,
  showOneTab: false,
  getApoloQueryData: {
    apoloPageSize: 18,
    apoloPageNumber: 1,
    isGetApolo: false,
  }
})

const gettersData = reactive({
  pageInfo:null,
  user:null
})
const getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null
if(proxy){
  gettersData.pageInfo = proxy.$store.getters.pageInfo
  gettersData.user = proxy.$store.getters.user
}
const showShare = computed(()=>{
  if (gettersData.user && gettersData.user.userInfo) {
    return (
      gettersData.user.userInfo.shopId == gettersData.pageInfo.pageInfo.shopId &&
      (gettersData.user.userInfo.RuleId == 1 || gettersData.user.userInfo.RuleId == 2)
    )
  } else {
    return false
  }
})
const pageInfoSendData = computed(()=>{
  if(getSon){
    return getSon.pageInfofn()
  }
  return null
})
const isShanghai = computed(()=>{
  if(getSon && getSon.provinceFn){
    let provinceId =  getSon.provinceFn()
    return (provinceId == '210')
  }
  return false
})

const myShowData = computed(()=>{
  if(getSon && getSon.myShowDatafn){
    return getSon.myShowDatafn()
  }else{
    return {}
  }
})
watch(myShowData,(val)=>{
  if(val){
    commonData.guideNewList = val.iconList
  }
},{immediate:true})

const showSubMsgIcon = computed(()=>{
  if(myShowData.value && myShowData.value.dingYueXiaoXiIcon){
    return false
  }
  if(gettersData.user && gettersData.user.userInfo && gettersData.user.userInfo.UserName && UA.isApp){
    return true
  }
  return false
})


// 打开订阅弹框
function Submsg(){
  return getSon.Submsg()
}
watch(()=>props.pagedata,(val)=>{
  if (val && proxy) {
    initPageData(val)
  }
},{immediate: true})
// 精准营销商品分享回调
let contactref = ref(null)
function emitPrecisionMarketing(item) {
  // console.log("分享", commonData.$refs.Contact)
  PrecisionMarketing.emitPrecisionMarketing(contactref.value, item)
}

// 点击套餐必须登录
function beforeChangeMoveName(index) {
  return PrecisionMarketing.beforeChangeMoveName(
    index,
    (callback,showCancle)=>{
      openloginSelf(callback,showCancle)
    },
    commonData.pageData
  )
}

//跳转优惠券加密参数
async function getEncryption() {
  await encryption
    .getEncryption({
      shopId: gettersData.pageInfo.pageInfo.shopId,
      type: "type1",
    })
    .then((res) => {
      if (res.code == 0 && res.data) {
        commonData.quanlink = res.data.shareParam
        EventBus.$emit("shareparam", res.data.shareParam)
      } else {
        Toast(res.message)
      }
    })
}
/* 点击移动logo的在线看店 */
function handleOnlineWatchShop() {
  if (gettersData.pageInfo.pageInfo.shopOnlineStatus != 1) {
    insertCode("yd_index_" + gettersData.pageInfo.pageInfo.shopId + "_icon_zxkd")
    Toast("该功能正在开放中，敬请期待")
    return false
  }
  insertCode(
    "yd_index_" + gettersData.pageInfo.pageInfo.shopId + "_icon_zxkd",
    "/yundian/onlinewatchshop/index.html?shopId=" +
      gettersData.pageInfo.pageInfo.shopId
  )
}
async function initPageData(val) {
  val = JSON.parse(JSON.stringify(val))
  commonData.pageData = val
  commonData.guideList = [
    {
      adLink: "https://paidui.coc.10086.cn/zhyytQueueTakeNum/#/",
      imgSrc: require("@/assets/index_normal/guide_icon1.gif"),
      title: "排队取号",
      linkId: "quhao",
      queueStatus: gettersData.pageInfo.pageInfo.queueStatus,
      appointmentStatus: gettersData.pageInfo.pageInfo.appointmentStatus,
      dcsId: "pdqh",
      class: "pdqh",
    },
    {
      adLink: stqurl.packremainqry, //套餐余量链接
      imgSrc: require("@/assets/index_normal/guide_icon2.png"),
      title: "套餐余量",
      dcsId: "tcyl",
      class: "tcyl",
    },
  ]
  commonData.ydGoodsList = []
  commonData.otherGoodsList = []
  val.forEach((element) => {
    if (element.adSource == "cmData" && element.dataList.ad) {
      element.dataList.ad.forEach((item) => {
        item.imgSrc = getImgUrl(item.imgsrc)
        item.adLink = item.link_skuid
      })
    }
    if (element.moduleCode == "goods") {
      if (commonData.showYdKeys.indexOf(element.selectorKey) !== -1) {
        commonData.ydGoodsList.push(element)
      } else {
        commonData.otherGoodsList.push(element)
      }
    }
    if (
      element.componentCode == "carouselA" &&
      element.dataList &&
      element.dataList.ad &&
      element.dataList.ad.length > 0
    ) {
      commonData.hasbanner = true //用于没有banner图的时候使得导航部分padding-top要大一些
    }
    if (
      element.componentCode == "single" &&
      element.adPosition == "goods"
    ) {
      commonData.pageDatasingle4 = element
    }
    if (element.componentCode == "iconlink") {
      if (UA.isApp) {
        let flowUrl = new URL(element.dataList.floor.flowLink)
        element.dataList.floor.flowLink =
          "https://flow.clientaccess.10086.cn/leadeon-flow/pages/flow/payFlow.html" +
          flowUrl.search
      }
      let allParams = getAllSearchParamsArray(location.href)
      let ac_id = allParams["WT.ac_id"] || getCookie("ac_id")
      let arr = [
        {
          adLink: ac_id
            ? setSearchParamsArray(element.dataList.floor.flowLink, {
              "WT.ac_id": ac_id,
            })
            : element.dataList.floor.flowLink, //流量充值
          imgSrc: require("@/assets/index_normal/guide_icon3.png"),
          title: "流量直充",
          dcsId: "llzc",
          class: "llzc",
        },
        {
          adLink: ac_id
            ? setSearchParamsArray(element.dataList.floor.creditLink, {
              "WT.ac_id": ac_id,
            })
            : element.dataList.floor.creditLink, //话费充值
          imgSrc: require("@/assets/index_normal/guide_icon4.png"),
          title: "话费直充",
          dcsId: "hfzc",
          class: "hfzc",
        },
        {
          adLink:
            "/yundian/coupon/index.html?shopId=" +
            gettersData.pageInfo.pageInfo.shopId,
          imgSrc: require("@/assets/index_normal/guide_icon5.gif"),
          title: "领券中心",
          dcsId: "lqzx",
          class: "lqzx",
        },
        {
          adLink: stqurl.busiqrydeal,
          imgSrc: require("@/assets/index_normal/guide_icon6.png"),
          title: "已订业务",
          dcsId: "ydyw",
          class: "ydyw",
        }
      ]
      let arr1 = []
      if(isShanghai.value){
        arr1 = [
          {
            adLink:
              "/yundian/onlinewatchshop/index.html?shopId=" +
              gettersData.pageInfo.pageInfo.shopId, //在线看店
            imgSrc: require("@/assets/index_normal/guide_icon8.png"),
            title: "在线看店",
            linkId: "shopOnline",
            dcsId: "zxkd",
            class: "zxkd",
          }
        ]
      }else{
        arr1 = [
          {
            adLink: `/yundian/broadband/usersidelist.html?shopId=${gettersData.pageInfo.pageInfo.shopId}`,
            sectionId: element.dataList.floor.sectionId,
            imgSrc: require("@/assets/index_normal/guide_icon7.png"),
            title: "预约专区",
            linkId: "broadband",
            dcsId: "kdyy",
            class: "kdyy",
          },
          {
            adLink:
              "/yundian/onlinewatchshop/index.html?shopId=" +
              gettersData.pageInfo.pageInfo.shopId, //在线看店
            imgSrc: require("@/assets/index_normal/guide_icon8.png"),
            title: "在线看店",
            linkId: "shopOnline",
            dcsId: "zxkd",
            class: "zxkd",
          }
        ]
      }

      commonData.guideList = commonData.guideList.concat(arr,arr1)
    }
  })
  if (props.configure != 1) {
    await getEncryption()
  }
  if (commonData.ydGoodsList.length === 1 && parseInt(props.configure) !== 1) {
    commonData.ydGoodsList[0].dataList.floor.title = "移动业务"
  }
  if (commonData.ydGoodsList.length > 0 || commonData.otherGoodsList.length > 0) {
    //有数据才会展示tab栏
    commonData.showGoodsTab = true
  } else {
    commonData.showGoodsTab = false
  }
  //仅有一条数据展示tab栏左对齐
  if (
    (commonData.ydGoodsList.length === 0 && commonData.otherGoodsList.length === 1) ||
    (commonData.ydGoodsList.length > 0 && commonData.otherGoodsList.length === 0)
  ) {
    commonData.showOneTab = true
  } else {
    commonData.showOneTab = false
  }
  // 如果sessionStorage有的话, 并且已经登录了, 并且是点击套餐进来的首页
  let guide_4 = document.querySelector(".guide_4")
  PrecisionMarketing.istclogin(guide_4, () => {
    commonData.moveName = 70
  })
  if (props.merchantCtrl === 2) {
    //爆款潮品sort是90，如果是运营位配置的数据单独请求接口覆盖
    let apoloData = await getApoloData()
    let floorSort90Index = commonData.pageData.findIndex(
      (item) => item.floorSort == 90
    )
    let otherGoodsListIndex90 = commonData.otherGoodsList.findIndex(
      (item) => item.floorSort == 90
    )
    let element = JSON.parse(
      JSON.stringify(commonData.pageData[floorSort90Index])
    )
    element.selectorKey = 1000
    element.sortKey = 1000
    element.dataList.ad = apoloData
    // commonData.$set(commonData.otherGoodsList, otherGoodsListIndex90, element)
    let otherGoodsList = copy(commonData.otherGoodsList)
    otherGoodsList[otherGoodsListIndex90] = element
    commonData.otherGoodsList = copy(otherGoodsList)
  }
}
// 拉起登录
function openloginSelf(callback,showCancle) {
  return getSon.openlogin(callback,showCancle)
}
// 点击登录按钮登录
function openBtnlogin(callback,showCancle) {
  insertCode(
    "yd_index_" + gettersData.pageInfo.pageInfo.shopId + "_loginbar_login"
  )
  openloginSelf(callback,showCancle)
}
// 点击优惠券
function openQuan() {
  if (!props.logined) {
    //强登
    openloginSelf(gotoQuanlogined,true)
  } else {
    insertCode(
      "yd_index_" + gettersData.pageInfo.pageInfo.shopId + "_loginbar_coupon",
      `https://touch.10086.cn/i/mobile/mycoupons.html?shareParam=` +
        commonData.quanlink
    )
  }
}
//点击优惠券，拉起登录回调
function gotoQuanlogined(res) {
  if (res && res.UserName > "") {
    insertCode(
      "yd_index_" + gettersData.pageInfo.pageInfo.shopId + "_loginbar_coupon",
      `https://touch.10086.cn/i/mobile/mycoupons.html?shareParam=` +
        commonData.quanlink
    )
  }
}
// 跳转店铺商品搜索
function goToSearch() {
  proxy.$router.push({
    path: "/searchlist/index.html?shopId=" + gettersData.pageInfo.pageInfo.shopId,
  })
}
//获取异业商品
async function getApoloData() {
  let AllData = []
  if (!commonData.getApoloQueryData.isGetApolo) {
    AllData = await shopAPI
      .getFashionCmData({
        shopId: gettersData.pageInfo.pageInfo.shopId,
      })
      .then((r) => {
        if (r.code === 0 && r.data && r.data.ad) {
          return r.data.ad
        } else {
          return []
        }
      })
    AllData.forEach((value) => {
      value.goodsStatus = 51
    })
    commonData.getApoloQueryData.isGetApolo = true
  }
  if (commonData.getApoloQueryData.isGetApolo) {
    let apoloData = await shopAPI
      .getFashionApoloData({
        shopId: gettersData.pageInfo.pageInfo.shopId,
        pageSize: commonData.getApoloQueryData.apoloPageSize,
        pageNum: commonData.getApoloQueryData.apoloPageNumber,
      })
      .then((r) => {
        if (r.code === 0 && r.data && r.data.ad) {
          return r.data.ad
        } else {
          return []
        }
      })
    if (apoloData.length >= commonData.getApoloQueryData.apoloPageSize) {
      commonData.getApoloQueryData.apoloPageNumber++
    } else {
      commonData.getApoloQueryData.finished = true
    }
    AllData = AllData.concat(apoloData)
  }
  return AllData
}
// 商品分页接口
async function  getGoodsData(sort, pageSize, pageNum) {
  // 爆款潮品sort是90，如果是运营位配置的数据单独请求接口覆盖
  if (props.merchantCtrl === 2 && sort === 90) {
    if (commonData.getApoloQueryData.finished) {
      return []
    }
    return await getApoloData()
  } else {
    return await getYdGoodsData({ sort, pageSize, pageNum })
  }
}
// 异业商品分页
async function getYdGoodsData({ sort, pageSize, pageNum }) {
  let { shopId, actId, preview, configure } = pageInfoSendData.value
  let ydscData = await shopAPI
    .getDataBySort({
      shopId,
      actId,
      preview,
      configure,
      sort,
      pageSize,
      pageNum,
    })
    .then((r) => {
      if (r.code === 0 && r.data && r.data.ad) {
        return r.data.ad
      } else {
        return []
      }
    })
  return ydscData
}

</script>
<style>
* {
  box-sizing: border-box;
  outline: none;
}
body {
  background: #f1f1f1 !important;
  font-size: 12px;
}
.pt12 {
  padding-top: 12px;
}
.fs12 {
  font-size: 12px;
}
.fs14 {
  font-size: 14px;
}
.bgwhite {
  width: 100%;
  background: #fff;
}
.mt7 {
  margin-top: 7px !important;
}
a {
  color: #333333;
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
  user-select: none;
  -moz-user-focus: none;
  -moz-user-select: none;
}
.head {
  z-index: 10;
}
.index {
  background: #fff !important;
}
.van-tabs__content .van-sticky {
  top: 43.875px !important;
}
</style>
<style lang="scss" scoped>
.index {
  color: #333333;
  a {
    color: #333333;
  }
}
.icon {
  display: block;
  width: 39px;
  height: 39px;
  margin: 0 12px;
}
.title {
  background-size: cover;
  height: 180px;
  position: absolute;
  width: 100%;
}
.def_title {
  background: #fff url(~@/assets/index_normal/bg.png) 0 0 no-repeat;
  background-size: cover;
}
.normal_4 {
  padding-top: 35px;
}
.flex {
  display: flex;
  height: 47px;
  align-items: center;
}
.userName {
  flex: 1;
  line-height: 47px;
  font-size: 14px;
  color: #fff;
  font-size: 16px;
  font-weight: bold;
}
.icon_user {
  background: url(~@/assets/index_normal/user.png) 0 0 no-repeat;
  background-size: contain;
}
.icon_quan {
  width: 52px;
  height: 20px;
  background: url(~@/assets/index_normal/quan.png) 0 0 no-repeat;
  background-size: contain;
}
.icon_search {
  width: 18px;
  height: 18px;
  background: url(~@/assets/searchlist/search2.png) 0 0 no-repeat;
  background-size: contain;
  margin-right: 2px;
}
.icon-subscribe{
  font-size: 18px;
  color:#fff;
}
.icon_vip {
  background: url(~@/assets/index_normal/vip_user.png) 0 0 no-repeat;
  width: 14px;
  height: 12.5px;
  display: inline-block;
  background-size: contain;
  margin-left: 6px;
}
.vant-tabs-tpl4 {
  :deep(.van-tabs__nav--card) {
    border: none !important;
    margin-top: 13px;
    margin-bottom: 13px;
    .van-tab {
      border: none;
      font-size: 13px;
      font-weight: 500;
      text-align: center;
      color: #999999;
      &.van-tab--active {
        width: 64px;
        height: 32px;
        background: rgba(#f55050, 0.1);
        border-radius: 16px;
        color: #f55050;
      }
    }
  }
  :deep(.van-sticky--fixed) {
    background: #fff;
    .van-tabs__nav--card {
      margin-top: 0;
      margin-bottom: 5px;
    }
  }
}

// .showOneTab >>> div:not(.van-tabs__content){
//   >>> .van-tabs__nav:first-of-type{
//     width: 200px!important;
//     .van-tabs__line{
//       transform:translateX(75px)!important;
//       width:50px;
//     }
//   }
// }
</style>
<style lang="scss">
.showOneTab {
  & > div:first-child {
    .van-tabs__nav:first-of-type {
      width: 80px !important;

      .van-tabs__line {
        transform: translateX(14px) !important;
        width: 50px;
      }
    }
  }
}
</style>
