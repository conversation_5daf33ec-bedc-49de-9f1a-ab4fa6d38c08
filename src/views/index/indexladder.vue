<template>
  <div></div>
</template>
<script>
import LadderShopApi from "@/api/ladder/ladder-shop"
export default {
  name: "Indexladder",
  components: {
  },
  data() {
    return {
      isladder: false,
      shopId:null
    }
  },
  async created() {
    const url = new URL(location.href)
    this.shopId = url.searchParams.get("shopId")
    this.getladder(this.shopId)
  },
  methods: {
    async getladder(shopId) {
      this.isladder = await LadderShopApi.includeStore({ shopId }).then(res => {
        if (res.code === 0 && res.data && res.data.includeStore === 1) {
          return true
        } else {
          return false
        }
      })
      this.$store.commit("SET_ISLADDER", this.isladder)
      if (this.isladder) {
        this.$router.push({
          path: "/ladderplan/home.html?shopId=" + this.shopId
        })
      }else{
        this.$router.push({
          path: "/index.html?shopId=" + this.shopId
        })
      }
    }
  }
}
</script>
