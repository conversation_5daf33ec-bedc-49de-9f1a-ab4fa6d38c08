<template>
  <div>
    <!-- 18810776328 -->
    <LoginDialog
      :islogin="shopListData.is4gLogined"
      :is-force-login="true"
      :isloginfn="logined"
      :is-auto-login="shopListData.autoLogin"
    />
    <Shopname
      v-if="shopListData.showHead && shopListData.storeList && shopListData.storeList.length>1"
      :show-back="false"
      content="店铺列表"
    />
    <div
      class="minheight storelist"
      :class="{
        'padding-top-34': shopListData.showHead
      }"
    >
      <List
        v-if="shopListData.userInfo && shopListData.storeList && shopListData.storeList.length"
        v-model="shopListData.loading"
        :finished="finished"
        finished-text="没有更多内容了"
        class="broadband-list ul"
      >
        <Cell v-for="(item, key) in shopListData.storeList" :key="key" class="li">
          <div
            class="flex store_item"
            :class="{ active: item.channelId === shopListData.currentShopInfo.channelId }"
          >
            <div class="user_img">
              <img
                src="~@/assets/index_img/userpic.png"
                width="100%"
                height="100%"
              />
            </div>
            <div class="store_info">
              <p class="store_name">
                {{ item.shopShortName || item.channelName }}
              </p>
              <p class="store_content">
                店铺状态：
                {{
                  parseInt(item.shopStatus) === 1
                    ? "未开通"
                    : parseInt(item.shopStatus) === 2
                      ? "待审核"
                      : parseInt(item.shopStatus) === 3
                        ? "已启用"
                        : parseInt(item.shopStatus) === 4
                          ? "已关闭"
                          : parseInt(item.shopStatus) === 5
                            ? "审核未通过"
                            : "未注册"
                }}
              </p>
              <p class="store_content">
                角色：{{ parseInt(item.ruleId) === 2 ? "店长" : "店员" }}
              </p>
            </div>
            <div class="store_btn">
              <div class="store_btn_img" @click="goStore(item)">
                {{ parseInt(item.shopStatus) === 0 ? "我要注册" : "进入" }}
                <i class="arrow arrow-right"></i>
              </div>
            </div>
          </div>
        </Cell>
      </List>
    </div>
  </div>
</template>
<script setup>
import UA from "@/utils/ua"
import {reactive,inject,getCurrentInstance} from "vue"
import shopAPI from "@/api/shop"
import Shopname from "@/components/index/headercon.vue"
import loginUtils from "@/utils/login"
import insertCode from "@/utils/insertCode"
import LoginDialog from "@/components/login/index"
import { mapGetters } from "vuex"
import { List, Cell, Message, Toast } from "vant"
const reload = inject("reload")
const shopListData = reactive({
  //店铺列表
  storeList: [],
  loading: false,
  // 是否登录
  is4gLogined: null,
  // 是否打开登录
  autoLogin: false,
  // 用户信息
  userInfo: {},
  // 是否展示页头
  showHead: !UA.isWechat &&
      !UA.isWechatWork &&
      !UA.isApp &&
      !UA.isIosQQ &&
      !UA.isAndroidQQ,
  // 已选店铺信息
  currentShopInfo: {}
})
const getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy :null
let shopId = null
if(proxy){
  shopId = proxy.$store.getters.shopId
}
function logined(res) {
  shopListData.userInfo = res
  shopListData.isLogined = res && res.UserName > ""
  if (!res.shops || res.shops.length === 0) {
    //游客进入
    window.location.href = "/hd/xskd/index.html"
  }
  if (res.shops && res.shops.length > 1) {
    //一家店铺以上
    getStoreList()
  }
  if (res.shops && res.shops.length === 1) {
    //只有一家店铺
    goStore(res.shops[0])
  }
}
function autoLoginCb(res) {
  shopListData.is4gLogined = false
  shopListData.autoLogin = true
}
function getStoreList() {
  shopListData.loading = true
  shopAPI
    .getStoreList()
    .then(res => {
      shopListData.loading = false
      shopListData.finished = true
      if (res.code) {
        Toast(res.message)
      } else {
        shopListData.storeList = res.data
      }
    })
    .catch(error => {
      // console.log(error)
    })
}
function goStore(shopInfo) {
  //切换缓存用
  shopListData.currentShopInfo = shopInfo
  insertCodeIndex()
  if (Number(shopInfo.shopStatus) === 3) {
    //已开通店铺
    reload(shopInfo.shopId)
    if(proxy){
      proxy.$router.push({
        path: "/index.html",
        query: { shopId: shopInfo.shopId }
      })
    }
  } else if (Number(shopInfo.shopStatus) === 0) {
    //店铺未注册
    window.location.href = "/hd/xskd/register.html"
  } else {
    window.location.href = "/hd/xskd/index.html?shopId="+shopInfo.shopId
  }
}
async function insertCodeIndex() {
  if (UA.isApp) {
    insertCode("source_app")
  } else if (UA.isWechat || UA.isWechatWork) {
    if (!UA.isWechatWork && (await UA.isWeChatMiniApp())) {
      insertCode("source_wechat_miniprograms")
    } else {
      insertCode("source_wechat")
    }
  } else {
    insertCode("source_explorer")
  }
}
insertCodeIndex()
loginUtils.login(true, true, logined, false, true, autoLoginCb, "0")
</script>
<style>
.app-wrapper {
  background: #f6f7f9;
}
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
$bgcolor: #f6f7f9;
p {
  margin: 0;
}
.minheight {
  min-height: calc(100vh - 80px);
  overflow-y: scroll;
  background: $bgcolor;
}
.padding-top-34 {
  padding-top: 34px;
}
.storelist {
  .title {
    font-size: 16px;
  }
  .van-list{
    padding:15px;
  }
  .van-cell {
    background: $bgcolor;
    padding: 0px;
    margin-bottom:15px;
    background: #ffffff;
    border-radius: 5px;
    box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.08);
  }
  :deep(.van-list__finished-text) {
    color: #666;
    font-size: 12px;
  }
  .store_item {
    display: flex;
    height: 97px;

    // margin-bottom: 15px;
    position: relative;
    &.active {
      border: 1px solid #ed2668;
      background: #ffffff url("~@/assets/index_img/activeStore.png") right -4px no-repeat;
    }
    .user_img {
      position: relative;
      width: 50px;
      height: 50px;
      margin: 22px 12px 25px 20px;
    }
    .store_info {
      padding-top: 18px;
      width: 75%;
      .store_name {
        font-size: 16px;
        font-weight: 400;
        color: #333333;
        line-height: 22px;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .store_content {
        font-size: 12px;
        font-weight: 400;
        text-align: left;
        color: #999999;
        line-height: 18px;
      }
    }
    .store_btn_img {
      position: absolute;
      right: 14px;
      bottom: 17px;
      height: 22px;
      border: 1px solid #5fade8;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 400;
      color: #5fade8;
      line-height: 21px;
      padding: 0 12px;
      cursor: pointer;
      .arrow-right {
        display: inline-block;
        width: 8px;
        height: 8px;
        border: 1px solid;
        border-color: transparent;
        border-bottom-color: #5fade8;
        border-right-color: #5fade8;
        transform: rotate(-45deg);
      }
    }
  }
}
.text-left {
  text-align: left;
}
.user_img {
  width: 50px;
}
</style>
