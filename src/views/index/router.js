export default [
  {
    path: '/index.html',
    name: 'HomeIndex',
    component: resolve => require(['./index.vue'], resolve),
    meta: {
      title: '店铺首页',
      keepAlive:true,
      role:[2]
    }
  },
  {
    path: '/indexladder.html',
    name: 'Index',
    component: resolve => require(['./indexladder.vue'], resolve),
    meta: {
      title: '店铺首页',
      keepAlive:false
    }
  },
  {
    path: '/myshop.html',
    name: 'Myshop',
    component: resolve => require(['./myshop.vue'], resolve),
    meta: {
      title: '店铺列表',
      keepAlive:true,
      login:true,
      role:[0,1,2]
    }
  }
]


