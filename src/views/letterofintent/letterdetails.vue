<template>
  <div class="intent">
    <header-nav :title="data.title"></header-nav>
    <div v-if="data.orderDetail" class="couponMain-details">
      <a
        v-if="data.orderDetail.status == 'RO'"
        class="ljclbt"
        @click="openChangeOrderDialog()"
      >立即处理</a>
      <h2>意向单信息</h2>
      <div class="bd-con clearfix">
        <div class="bd-con-ner clearfix">
          <p class="bd-hs-ys clearfix">
            <span class="bd-let">意向人姓名：</span>
            <span class="w80 breakAll">{{
              data.orderDetail.customerName
            }}</span>
          </p>
          <div class="bd-hs-ys clearfix">
            <span class="bd-let">电话号码：</span>
            <div class="dl-bd">
              <span class="bd-phone">{{ getPassPhone(data.orderDetail.customerPhone) }}</span>
              <a
                v-if="data.shopState.indexOf(data.orderDetail.status) !== -1 && data.stateText[data.orderDetail.status]=='已接单'"
                :href="'tel:'+data.orderDetail.customerPhone"
                class="icon-phone-img"
              >
                <img src="~@/assets/index_normal/phone.png" alt="" />
              </a>
            </div>
          </div>
          <p class="bd-hs-ys clearfix">
            <span class="bd-let">地址：</span>
            <span class="w80 breakAll">{{
              data.orderDetail.province +
                ' ' +
                data.orderDetail.city +
                ' ' +
                data.orderDetail.county
            }}</span>
          </p>
          <p class="bd-hs-ys clearfix">
            <span class="bd-let">详细地址：</span>
            <span class="w80 breakAll">{{
              data.orderDetail.province +
                ' ' +
                data.orderDetail.city +
                ' ' +
                data.orderDetail.county +
                ' ' +
                data.orderDetail.address
            }}</span>
          </p>
          <p class="bd-hs-ys clearfix">
            <span class="bd-let">意向活动：</span>{{ data.orderDetail.productAttr }}
          </p>
          <p class="bd-hs-ys clearfix">
            <span class="bd-let">甩单类型：</span>
            {{ data.orderDetail.coopType===COOPTYPES.BROADBAND ? "宽带甩单" : "终端甩单" }}
          </p>
          <p
            v-if="
              (data.orderDetail.model ||
                data.orderDetail.brand ||
                data.orderDetail.colour ||
                data.orderDetail.runningMemory ||
                data.orderDetail.bodyMemory ||
                data.orderDetail.price) &&
                data.orderDetail && data.orderDetail.coopType == COOPTYPES.TERMINAL
            "
            class="bd-hs-ys clearfix"
          >
            <span class="bd-let">手机配置：</span>
            <span class="w80 breakAll">
              <span>{{ data.orderDetail.brand }},</span>
              <span>{{ data.orderDetail.model }},</span>
              <span>{{ data.orderDetail.colour }},</span>
              <span>
                {{ data.orderDetail.runningMemory ? data.orderDetail.runningMemory + 'G,' : '' }}
              </span>
              <span>
                {{ data.orderDetail.bodyMemory ? data.orderDetail.bodyMemory + 'G,' : '' }}
              </span>
              <span>
                {{ data.orderDetail.price ? data.orderDetail.price + '元' : '' }}
              </span>
            </span>
          </p>
          <p class="bd-hs-ys clearfix">
            <span class="bd-let">意向单号：</span>{{ data.orderDetail.orderId }}
          </p>
          <p class="bd-hs-ys clearfix">
            <span class="bd-let">意向单状态：</span>
            <span>
              {{
                data.shopState.indexOf(data.orderDetail.status) !== -1
                  ? data.stateText[data.orderDetail.status]
                  : ''
              }}
            </span>
          </p>
          <p
            v-if="
              data.orderDetail.status == 'SS' &&
                data.orderDetail && data.orderDetail.coopType == COOPTYPES.TERMINAL"
            class="bd-hs-ys clearfix"
          >
            <span class="bd-let">终端设备串号：</span>
            <span class="w80 breakAll">{{ data.orderDetail.imei }}</span>
          </p>
          <p
            v-if="
              data.orderDetail.status == 'CL' &&
                data.orderDetail && data.orderDetail.coopType == COOPTYPES.TERMINAL
            "
            class="bd-hs-ys clearfix"
          >
            <span class="bd-let">取消原因：</span>
            <span class="w80 breakAll">{{
              data.orderDetail.cannelReason
            }}</span>
          </p>
        </div>

        <p class="hs bd-hs-ys clearfix">
          <span class="bd-let">下单时间：</span>{{ parseTime(data.orderDetail.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </p>
        <p v-if="data.orderDetail.modifyTime" class="hs bd-hs-ys clearfix">
          <span class="bd-let">状态更新时间：</span>
          {{ parseTime(data.orderDetail.modifyTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </p>
      </div>
    </div>
    <!-- 弹框 -->
    <van-dialog
      v-model="data.showChangeOrderStatus"
      title="意向单处理"
      show-cancel-button
      confirm-button-color="#5fade8"
      :before-close="changeOrderStatus"
    >
      <van-radio-group
        v-model="data.sendData1.status"
        direction="horizontal"
        class="dialogContent"
      >
        <van-radio name="SS">
          甩单成功
        </van-radio>
        <van-radio name="CL">
          甩单取消
        </van-radio>
      </van-radio-group>
      <van-field
        v-if="
          data.sendData1.status == 'SS' &&
            data.orderDetail && data.orderDetail.coopType == COOPTYPES.TERMINAL
        "
        v-model="data.sendData1.imei"
        class="boradbandInput"
        type="text"
        placeholder="请输入设备的终端串号"
        label-width="100px"
        clearable
      />
      <p
        v-if="
          data.sendData1.status == 'SS' &&
            data.orderDetail && data.orderDetail.coopType == COOPTYPES.TERMINAL
        "
        class="tips"
      >
        限20个字符长度
      </p>
      <van-field
        v-if="
          data.sendData1.status == 'CL'&&
            data.orderDetail && data.orderDetail.coopType == COOPTYPES.TERMINAL"
        v-model="data.sendData1.cannelReason"
        class="boradbandInput"
        type="textarea"
        :autosize="{ maxHeight: 100 }"
        :maxlength="256"
        placeholder="请输入取消原因"
        label-width="100px"
        clearable
      />
      <p
        v-if="
          data.sendData1.status == 'CL'&&
            data.orderDetail && data.orderDetail.coopType == COOPTYPES.TERMINAL"
        class="tips"
      >
        限30个字符长度
      </p>
    </van-dialog>
    <Footer />
  </div>
</template>

<script setup>
import headerNav from '@/views/my/components/headerNav'
import loginUtils from '@/utils/login'
import intentOrderApi from '@/api/letterofintent'
import { Toast, Dialog, RadioGroup, Radio, Field, Form } from 'vant'
import { mapGetters } from 'vuex'
import 'vant/lib/index.css'
import Vue, { reactive, onMounted, getCurrentInstance, computed } from 'vue'
import { parseTime,getPassPhone } from "@/utils/utils"
Vue.use(Toast).use(Dialog).use(RadioGroup).use(Radio).use(Field).use(Form)
const COOPTYPES = {
  BROADBAND:"1",
  TERMINAL:"2"
}
let data = reactive({
  title: '意向单详情',
  activeNames: ['1'],
  info: '',
  orderDetail: null,
  orderId: null,
  shopId: null,
  showChangeOrderStatus: false,
  showCancleReservation: false,
  sendData1: {
    status: 'SS',
    imei: '',
    cannelReason: '',
  },
  stateText: {
    RO: '已接单',
    SS: '已完成',
    CL: '已取消',
    PO: '待接单',
  },
  // 商户看到的状态 待接单PO，已接单RO，已完成SS，已取消CL
  shopState: ['RO', 'SS', 'CL', 'PO'],
})

let logined = (res) => {
  data.user = res
  if (data.user && res.shopId) {
    data.shopId = res.shopId
    data.sendData1.staffId = res.staffId
    getIntentDetail()
    data.hasrole = true
  } else {
    data.hasrole = false //游客看不了
    data.tabMain = []
    data.nodataMessage = '无权查看，请去自己的店铺'
  }
}
let getIntentDetail = () => {
  intentOrderApi
    .intentOrderDetail({ orderId: data.orderId, shopId: data.shopId })
    .then((res) => {
      if(res.data){
        data.orderDetail = res.data
      }
    })
}
let openChangeOrderDialog = () => {
  data.showChangeOrderStatus = true
}
let changeOrderStatus = (action, done) => {
  if (action == 'cancel') {
    let sendData = {
      status: 'SS',
      imei: '',
      cannelReason: '',
    }
    data.sendData1 = sendData
    done()
    return false
  }
  let emojiReg = /[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g
  if (data.sendData1.status == 'SS') {
    //预约成功
    if (data.sendData1.imei && data.sendData1.imei.length > 20) {
      Toast('限20个字符长度')
      done(false)
      return false
    }
    if (data.sendData1.imei && emojiReg.test(data.sendData1.imei)) {
      Toast('输入不合规')
      done(false)
      return false
    }
    delete data.sendData1.cannelReason
  }
  if (data.sendData1.status == 'CL') {
    //预约失败
    let myreason = data.sendData1.cannelReason
      ? data.sendData1.cannelReason.replace(/[\n|\s]+/g, '')
      : null
    if (myreason && myreason.length > 30) {
      Toast('限30个字符长度')
      done(false)
      return false
    }
    if (myreason && emojiReg.test(myreason)) {
      Toast('输入不合规')
      done(false)
      return false
    }
    delete data.sendData1.imei
  }
  data.sendData1.orderId = data.orderDetail.orderId
  intentOrderApi.updateOrderState(data.sendData1).then((res) => {
    if (res.code) {
      Toast(res.message)
      done(false)
    } else {
      //刷新页面
      done()
      refresh()
    }
  })
}
let refresh = () => {
  getIntentDetail()
}
onMounted(() => {
  const url = new URL(location.href)
  data.orderId = url.searchParams.get('orderId')
  //没有订单号，跳到附近店铺
  // const getVueInstance = getCurrentInstance()
  // const proxy = getVueInstance ? getVueInstance.proxy : null
  // let myuser = proxy ? proxy.$store.getters.user : null
  loginUtils.login(true, true, logined, false, false, '',null,5)
})
</script>
<style>
body {
  background: #f9fafc;
}
.header-nav {
  height: 52.5px !important;
}
.header-nav__title {
  font-size: 18.75px !important;
}
</style>
<style lang="scss" scoped>
.dl-bd{
  display:inline-flex;
  align-items: center;
}
.intent {
  min-height: calc(100vh - 80px);
}
.breakAll {
  white-space: normal;
  word-break: break-all;
  word-wrap: break-word;
}
.bd-hs-ys {
  margin-top: 10px;
}
.couponMain-details {
  width: 100%;
  height: 100%;
  overflow: hidden;
  color: #333;
  background: #f9fafc;
  font-size: 14px;
  .clearfix:after {
    content: '.';
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
  h2 {
    line-height: 37.5px;
    padding: 0 15px;
  }
  .bd-con {
    background: #fff;
    padding-right: 7.5px;
    padding-bottom: 7.5px;
    line-height: 30px;
    min-height: calc(100vh - 170px);
    .bd-con-ner {
      padding: 7.5px 0;
      border-bottom: 1px solid #f9fafc;
      margin-bottom: 7.5px;
    }
    .bd-let {
      float: left;
      width: 105px;
      text-align: right;
    }
    .w80 {
      display: inline-block;
      width: 262px;
      line-height: 22.5px;
      padding: 4px 0;
    }
    .hs {
      color: #999;
    }
    .ls {
      color: #00ad29;
    }
    .red {
      color: #ed2668;
    }
    .yll {
      color: #e97a00;
    }
  }
}
.dialogContent {
  font-size: 14px;
  margin: 19px 56px 11px;
}
.boradbandInput {
  :deep(.van-field__body) {
    width: 94%;
    margin: 0 auto;
    border: 1px solid #ccc;
    padding: 3.75px 7.5px;
    border-radius: 7.5px;
  }

}
.van-cell::after{
  border-bottom: none!important;
}
.ljclbt {
  width: 75px;
  height: 26px;
  line-height: 37.5px;
  text-align: center;
  color: #0085d0;
  margin-left: 10px;
  float: right;
}
.tips{
  padding-left:30px;
  color:red;
  font-size: 14px;
  margin-bottom: 5px;;
}
.icon-phone-img{
  width:20px;
  height:20px;
  margin-left: 10px;

  img{
    width:100%;
  }
}
.bd-phone,.icon-phone-img{
  float: left;
}
</style>
