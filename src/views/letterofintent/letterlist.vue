<template>
  <div class="broadband">
    <header-nav :title="data.title"></header-nav>
    <div class="couponMain">
      <Icon
        v-if="data.hasrole"
        name="filter-o"
        class="searchbt"
        size="18"
        @click="data.showSearch = true"
      />
      <Tabs v-model="data.bdName" :before-change="beforeChange">
        <LetterTab
          :floor="data.tabTitle"
          :items="data.tabMain"
          :refresh="refresh1111"
          :nodata-message="data.nodataMessage"
          :loading="data.loading"
          :finished="data.finished"
          :onload="intentOrderList"
        />
      </Tabs>
    </div>
    <ActionSheet
      v-model="data.showSearch"
      closeable
      :round="false"
      get-container="body"
    >
      <div class="nav">
        <Form
          ref="searchForm"
          :label-width="100"
          @submit="search('search')"
        >
          <div style="display: flex">
            <Button
              plain
              block
              type="info"
              style="margin-right: 187.5px"
              native-type="button"
              color="linear-gradient(to right, #60C7FF, #8374FE)"
              @click="reset"
            >
              重置
            </Button>
            <Button
              plain
              block
              type="info"
              native-type="submit"
              color="linear-gradient(to right, #60C7FF, #8374FE)"
            >
              确定
            </Button>
          </div>
          <Field
            v-model="coopType"
            name="coopType"
            label="甩单类型"
            readonly
            placeholder="请选择甩单类型"
            colon
            label-width="100px"
            data-name="addfocus"
            @click.prevent="openSetTime('coopType')"
          />
          <Field
            v-model="data.sendData.orderId"
            name="orderId"
            label="意向单号"
            placeholder="请填写意向单号"
            colon
            label-width="100px"
            data-name="addfocus"
          />
          <Field
            v-model="data.sendData.customerName"
            name="customerName"
            label="意向用户姓名"
            placeholder="请填写意向用户姓名"
            colon
            label-width="100px"
            data-name="addfocus"
          />
          <Field
            v-model="data.sendData.customerPhone"
            name="customerPhone"
            label="意向电话号码"
            type="tel"
            placeholder="请填写意向电话号码"
            colon
            label-width="100px"
            data-name="addfocus"
          />
          <Field
            readonly
            clickable
            name="createTimeStart"
            :value="data.createTimeStartName"
            label="起始日期"
            colon
            placeholder="点击选择日期"
            @click.prevent="openSetTime('start')"
          />
          <Field
            readonly
            clickable
            name="createTimeEnd"
            :value="data.createTimeEndName"
            label="截止日期"
            colon
            placeholder="点击选择日期"
            @click.prevent="openSetTime('end')"
          />
        </Form>
      </div>
    </ActionSheet>
    <ActionSheet v-model="data.showpicker" closeable :round="false">
      <div class="nav">
        <DatetimePicker
          v-if="data.showPickerStart"
          v-model="data.createTimeStart"
          type="date"
          title="选择起始时间"
          :min-date="data.minDate"
          :max-date="data.maxDate"
          @confirm="confirmstart"
          @cancel="
            data.showPickerStart = false;
            data.showpicker = false;
          "
        />
        <DatetimePicker
          v-if="data.showPickerEnd"
          v-model="data.createTimeEnd"
          type="date"
          title="选择截止时间"
          :min-date="data.minDate"
          :max-date="data.maxDate"
          @confirm="confirmEnd"
          @cancel="
            data.showPickerEnd = false;
            data.showpicker = false;
          "
        />
        <Picker
          v-if="data.showPickerType"
          show-toolbar
          title="请选择甩单类型"
          :columns="coopTypes"
          @cancel="
            data.showPickerType = false;
            data.showpicker = false;
          "
          @confirm="onTypeConfirm"
        />
      </div>
    </ActionSheet>
    <Footer />
  </div>
</template>

<script setup>
import headerNav from "@/views/my/components/headerNav"
import loginUtils from "@/utils/login"
import intentOrderApi from "@/api/letterofintent"
import { iosInputHandle } from "@/utils/ioscompatible"
import {
  Toast,
  Tab,
  Tabs,
  Field,
  Form,
  Button,
  DatetimePicker,
  ActionSheet,
  Icon,
  Popup,
  Picker,
} from "vant"
import "vant/lib/index.css"
import Vue, { onMounted, reactive, ref,getCurrentInstance } from "vue"
import LetterTab from "@/components/letterofintent/letterTab.vue"
import utils from "@/utils/utils"
const STATETEXT = [
  { code: null, text: "全部" },
  { code: "RO", text: "已接单" },
  { code: "SS", text: "已完成" },
  { code: "CL", text: "已取消" },
]
//RO 已接单 SS 已完成 CL 已取消
let data = reactive({
  title: '意向单列表',
  showSearch: false,
  showpicker: false,
  activeNames: ['1'],
  user: null,
  info: '',
  tabTitle: ['全部', '已接单', '已完成', '已取消'],
  tabMain: [],
  bdName: 0,
  minDate: new Date(2020, 0, 1),
  maxDate: new Date(2120, 0, 1),
  createTimeStart: new Date(),
  createTimeStartName: '',
  createTimeEnd: new Date(),
  createTimeEndName: '',
  showPickerStart: false,
  showPickerEnd: false,
  showPickerType: false,
  sendData: {
    shopId: null,
    status: null,
    pageNo: 1,
    pageSize: 20,
  },
  nodataMessage: '',
  hasrole: true,
  loading: false,
  finished: false,
  shopId: null,
})
let logined = (res) => {
  data.user = res
  if (data.user && res.shopId) {
    data.sendData.shopId = res.shopId
    intentOrderList()
    data.hasrole = true
  } else {
    data.hasrole = false //游客看不了
    data.tabMain = []
    data.nodataMessage = '无权查看，请去自己的店铺'
  }
}
let beforeChange = (index) => {
  if (!data.hasrole) {
    //不是自己的店铺不能点击
    return false
  }
  data.tabMain = []
  // 已接单RO，已完成SS，已取消CL
  data.sendData.status = null
  data.sendData.status = STATETEXT[index].code
  data.sendData.pageNo = 1
  data.nodataMessage = ''
  data.finished = false
  intentOrderList()
  // 返回 Promise 来执行异步逻辑
  return new Promise((resolve) => {
    // 在 resolve 函数中返回 true 或 false
    resolve(index + 1)
  })
}
let search = (value) => {
  if (
    data.sendData.createTimeStart &&
    data.sendData.createTimeEnd &&
    new Date(data.sendData.createTimeStart) >
      new Date(data.sendData.createTimeEnd)
  ) {
    Toast("截止日期不能早于起始日期")
    return false
  }
  data.showSearch = false
  data.sendData.pageNo = 1
  data.nodataMessage = ''
  data.finished = false
  intentOrderList(value)
}
let reset = () => {
  data.sendData = {
    shopId: data.sendData.shopId,
    pageSize: data.sendData.pageSize,
    status: data.sendData.status,
    coopType : null
  }
  coopType.value = null
  data.createTimeStartName = ""
  data.createTimeEndName = ""
  data.createTimeStart = new Date()
  data.createTimeEnd = new Date()
}
let refresh1111 = () => {
  data.sendData.pageNo = 1
  data.nodataMessage = ''
  data.finished = false
  intentOrderList()
}
let intentOrderList = (value) => {
  if (!data.hasrole) {
    return false
  }
  data.loading = true
  intentOrderApi.intentOrderList(data.sendData).then((res) => {
    if (res.code == 0) {
      if (data.sendData.pageNo == 1) {
        data.tabMain = []
      }
      data.tabMain = data.tabMain.concat(res.data.orderList)
      data.loading = false
      if (data.tabMain && data.tabMain.length == 0) {
        if (data.sendData.status == null && value != 'search') {
          data.nodataMessage = '暂无意向单信息'
        } else {
          data.nodataMessage = '暂未查到符合条件的意向单'
        }
        data.finished = true
        return false
      }
      if (res.data.totalPages > res.data.pageNo) {
        data.sendData.pageNo++
      } else {
        data.nodataMessage = '没有更多了'
        data.finished = true
      }
    } else {
      Toast(res.message)
    }
  })
}
let confirmstart = (time) => {
  data.createTimeStart = time
  data.createTimeStartName = utils.parseTime(time, '{y}-{m}-{d}')
  data.sendData.createTimeStart = utils.parseTime(
    time,
    '{y}-{m}-{d} {h}:{i}:{s}'
  )
  data.showPickerStart = false
  data.showpicker = false
}
let confirmEnd = (time) => {
  data.createTimeEnd = time
  let setData = new Date(new Date(time).getTime() + 24 * 60 * 60 * 1000 - 1)
  data.createTimeEndName = utils.parseTime(setData, '{y}-{m}-{d}')
  data.sendData.createTimeEnd = utils.parseTime(
    setData,
    '{y}-{m}-{d} {h}:{i}:{s}'
  )
  data.showPickerEnd = false
  data.showpicker = false
}

// <!-- 1：宽带甩单，2：终端甩单 -->
let coopTypes = [
  { key: "1", text: "宽带甩单" },
  { key: "2", text: "终端甩单" },
]

let coopType = ref(null)

let onTypeConfirm = (value) => {
  data.showPickerType = false
  data.showpicker = false
  data.sendData.coopType = value.key
  coopType.value = value.text
}
let setScrollTop = () => {
  let scrollTop =
    document.documentElement.scrollTop ||
    window.pageYOffset ||
    document.body.scrollTop
  if (scrollTop == 0) {
    window.scrollTo(0, 1)
  }
}
let openSetTime = (type) => {
  data.showPickerEnd = false
  data.showPickerStart = false
  data.showPickerType = false
  if (type == "start") {
    data.showPickerStart = true
  }
  if (type == "end") {
    data.showPickerEnd = true
  }
  if (type == "coopType") {
    data.showPickerType = true
  }
  switch (type) {
  case "start":
    data.showPickerStart = true
    break
  case "end":
    data.showPickerEnd = true
    break
  default:
    data.showPickerType = true
  }
  data.showpicker = true
  setScrollTop()
}
onMounted(() => {
  const url = new URL(location.href)
  data.shopId = url.searchParams.get('shopId')
  const getVueInstance = getCurrentInstance()
  const proxy = getVueInstance ? getVueInstance.proxy : null
  if (proxy) {
    proxy.$store.commit("SET_SHOPID", data.shopId)
  }
  iosInputHandle()
  loginUtils.login(true, true, logined, false, false, '', null, 5)
})
</script>
<style>
.header-nav {
  height: 52.5px !important;
}
.header-nav__title {
  font-size: 18.75px !important;
}
.broadband .van-tabs__wrap {
  background: #fff;
}
.broadband .van-tabs__nav--line {
  width: 90%;
}
</style>
<style lang="scss" scoped>
ul {
  li {
    margin: 0;
    padding: 0;
    list-style: none;
  }
}
.broadband {
  // min-height: 101vh;
  min-height: calc(101vh - 73px);

  .nav {
    padding: 0;
    background: #ffffff;
    .van-search {
      width: 355px;
      height: 36px;
      background: #f7f7f7;
      border-radius: 21px;
      margin: 0 auto;
      .van-search__content {
        background: none;
        padding-left: 0;
      }
    }
  }
  .searchbt {
    position: absolute;
    right: 10px;
    top: 12px;
    z-index: 1;
    border-left: 2px solid #979797;
    padding-left: 5px;
  }
  .couponMain {
    position: relative;
    .tabTilte {
      width: 100%;
      display: flex;
      li {
        flex: 1;
        line-height: 40px;
        font-size: 16px;
        text-align: center;
        border-bottom: #fff 1px solid;
        color: #333;
      }
      .active {
        border-bottom-color: #ff7300;
        color: #ff7300;
      }
    }
    .tabTitCon {
      width: 100%;
      background: #e2e1e1;
      display: flex;
      li {
        flex: 1;
        line-height: 40px;
        font-size: 12px;
        text-align: center;
        color: #333;
      }
      .conactive {
        color: rgb(255, 115, 0);
      }
    }
  }
  .tabContentCon {
    text-align: center;
    font-size: 13.5px;
    line-height: 30px;
    color: #999;
    img {
      display: inline-block;
      width: 75px;
      padding-top: 37.5px;
    }
  }
}
.nav :deep(.van-field .van-field__label) {
  width: 103px !important;
}
</style>
