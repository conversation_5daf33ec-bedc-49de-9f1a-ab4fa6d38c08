<!--
 * @FilePath: \yundian-m\src\views\wisdomScreen\unTouchScreen.vue
-->
<template>
  <div ref="wisdomScreenTouchScreen" class="wisdomScreenTouchScreen">
    <empty
      v-if="empty"
      :is-header="false"
      :is-return-prev="false"
      :is-return-home="false"
      :title="emptyTitle"
    />

    <wisdomScreentHeader
      :title="shopShortName"
      :is-return-prev="false"
      :is-return-home="false"
    />

    <div v-if="!empty" class="mainContent">
      <template v-if="listData.length">
        <swiper v-if="listData.length" ref="mySwiper" :options="swiperOption">
          <swiper-slide v-for="(item, index) in listData" :key="index">
            <div class="item">
              <div class="img">
                <img
                  :src="item.imgListFilterSrc && item.imgListFilterSrc[0]"
                  alt=""
                />
              </div>

              <div class="unChild">
                <div class="title">
                  {{ item.modelName }}
                </div>
                <div v-if="item.goodsSubtitle" class="subtitle">
                  {{ item.goodsSubtitle }}
                </div>
                <div v-if="item.description" class="desc">
                  <span>商品介绍:</span>
                  <span>{{ item.description }}</span>
                </div>
                <div class="other">
                  <div class="left">
                    <div class="price">
                      <!-- <span> -->
                      <strong :class="['minPrice',item.priceNumber.type==3?'mr3':'']">{{ item.priceNumber.min }}</strong>
                      <i v-if="item.priceNumber.type==2">-</i>
                      <span :class="['subPrice', item.priceNumber.type==3?'type2Style':'']">
                        <i v-if="item.priceNumber.type == 2 || item.priceNumber.type == 3">{{ item.priceNumber.max }}</i>
                      </span>
                      <!-- </span> -->
                      <!-- <i>
                        <template v-if="!item.priceUnit"> 元 </template>
                        <template v-else>
                          {{ item.priceUnit }}
                        </template>
                      </i> -->
                    </div>
                    <span class="txt">扫码下单</span>
                  </div>
                  <div class="right">
                    <div ref="qrCode" class="qrcode"></div>
                  </div>
                </div>
              </div>
            </div>
          </swiper-slide>
          <div slot="pagination" class="swiper-pagination"></div>
        </swiper>
      </template>
      <template v-else>
        <div class="contan">
          <img src="./images/empty.png" alt="" />
          <span>没有相关商品数据</span>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import wisdomScreentHeader from "./components/wisdomScreentHeader"
import { swiper, swiperSlide } from "vue-awesome-swiper"
import QRCode from "qrcodejs2"
import { getImgUrl, getUrl, regFenToYuan3 } from "@/utils/utils"
import "swiper/dist/css/swiper.css"
import wisdomScreen from "@/api/wisdomScreen"
import empty from "./components/empty"
import shopApi from "@/api/shop"

export default {
  components: {
    wisdomScreentHeader,
    swiper,
    swiperSlide,
    empty,
  },
  data() {
    return {
      empty: false,
      emptyTitle: null,
      listData: {},
      swiperOption: {
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
        slidesPerView: 3,
        speed: 1000,
        slidesPerGroup: 1,
        spaceBetween: 50,
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        centeredSlides: true,
        loop: true,
        autoplay: {
          delay: 10000,
          disableOnInteraction: false,
        },
      },
      shopShortName: "",
    }
  },
  mounted() {
    document.querySelector(".icpinfo").style.display = "none"
    this.$wisdomScreentLoading.show()

    this.setHeightClient()

    if (!this.$route.query.shopId) {
      this.empty = true
      this.emptyTitle = "缺少店铺编码"
      this.$wisdomScreentLoading.hide()
      return
    }

    shopApi.getShopStatus({ shopId: this.$route.query.shopId }).then((res) => {
      if (res.code == 0) {
        if (res.data) {
          this.shopShortName = res.data.shopShortName
        }
      }
    })

    wisdomScreen
      .queryBigScreenStatus({
        shopId: this.$route.query.shopId,
      })
      .then((resource) => {
        if (resource.data.status != 1) {
          this.empty = true
          this.emptyTitle = "店铺智慧大屏暂未开通/已关闭"
          this.$wisdomScreentLoading.hide()
        } else {
          wisdomScreen
            .queryMenus({
              shopId: this.$route.query.shopId || "10000003",
            })
            .then((res) => {
              if (res.code == 0) {
                if (!res.data.length) {
                  this.empty = true
                  if (!this.emptyTitle) {
                    this.emptyTitle = "您查看的店铺/商品不存在"
                  }
                  this.$wisdomScreentLoading.hide()
                  return
                }

                let catalogId = res.data.find(
                  (item) => item.type == 1
                ).id
                wisdomScreen
                  .queryBigScreenStatus({
                    shopId: this.$route.query.shopId,
                  })
                  .then((res) => {
                    if (res.data.status == 1) {
                      wisdomScreen
                        .queryGoods({
                          shopId: this.$route.query.shopId || "10000003",
                          catalogId,
                          pageSize: 100,
                        })
                        .then((res) => {
                          if (res.code == 0) {
                            if (!res.data) {
                              setTimeout(() => {
                                this.$wisdomScreentLoading.hide()
                              }, 500)
                              return
                            }

                            if (!res.data.goodsList.length) {
                              setTimeout(() => {
                                this.$wisdomScreentLoading.hide()
                              }, 500)
                              return
                            }

                            let goodsList = res.data.goodsList.map((item) => {
                              let imgList = null
                              let imgListFilterSrc = null
                              if (item.source == 3) {
                                imgList = JSON.parse(item.pictures)
                                imgListFilterSrc = imgList
                              } else {
                                imgList = JSON.parse(item.pictures.replace("/"))
                                imgListFilterSrc = imgList.map((item) => {
                                  return getImgUrl(item)
                                })
                              }

                              // item.priceFrom = 2
                              // 处理价格 price_form 1-一口价 2-价格区间 3-显示折扣价
                              let priceNumber = {
                                type:item.priceFrom,
                                max:item.priceSection,
                                min:item.shopPrice?item.shopPrice:item.price,
                              }

                              // 区间间隔的第一个价格单位要去掉
                              if(priceNumber.type == 2){
                                priceNumber.min = regFenToYuan3(priceNumber.min, " ")
                              }else{
                                priceNumber.min = regFenToYuan3(priceNumber.min, "元")
                              }
                              priceNumber.max = regFenToYuan3(priceNumber.max, "元")

                              // // 处理价格
                              // let priceNumber = null
                              // if (item.price == 0) {
                              //   priceNumber = 0
                              // } else {
                              //   priceNumber = regFenToYuan3(item.price, " ")
                              // }

                              return {
                                ...item,
                                priceNumber,
                                imgList,
                                imgListFilterSrc,
                              }
                            })

                            if (goodsList.length <= 2) {
                              this.swiperOption.loop = false
                            } else {
                              this.swiperOption.loop = true
                            }

                            this.listData = goodsList

                            this.$nextTick(() => {
                              this.creatQrCode()
                            })
                          }

                          setTimeout(() => {
                            this.$wisdomScreentLoading.hide()
                          }, 500)
                        })
                    } else {
                      this.empty = true
                      if (!this.emptyTitle) {
                        this.emptyTitle = "店铺智慧大屏暂未开通/已关闭"
                      }
                      this.$wisdomScreentLoading.hide()
                    }
                  })
                  .catch(() => {
                    this.empty = true
                    if (!this.emptyTitle) {
                      this.emptyTitle = "店铺智慧大屏暂未开通/已关闭"
                    }
                    this.$wisdomScreentLoading.hide()
                  })
              } else {
                this.empty = true
                if (!this.emptyTitle) {
                  this.emptyTitle = "您查看的店铺/商品不存在"
                }
                this.$wisdomScreentLoading.hide()
              }
            })
            .catch(() => {
              this.empty = true
              if (!this.emptyTitle) {
                this.emptyTitle = "您查看的店铺/商品不存在"
              }
              this.$wisdomScreentLoading.hide()
            })
        }
      })
  },
  methods: {
    /* 计算16:9的差高度 */
    setHeightClient() {
      let width = (document.body.clientWidth * 9) / 16
      let height = document.body.clientHeight
      if (height <= width) {
        this.$refs.wisdomScreenTouchScreen.style.height = width + "px"
      }
    },

    /* 创建二维码 */
    creatQrCode() {
      const slider = Array.from(document.querySelectorAll(".swiper-slide"))
      slider.forEach((item, index) => {
        let curIndex = null
        if (this.swiperOption.loop) {
          curIndex = parseInt(item.getAttribute("data-swiper-slide-index"))
        } else {
          curIndex = index
        }

        const curCode = item.querySelector(".qrcode")
        const data = this.listData[curIndex]
        let filterText = data && data.goodsUrl

        if (!filterText) {
          return
        }

        if (!filterText.startsWith("http")) {
          filterText = getUrl(filterText)
        }

        new QRCode(curCode, {
          text: filterText,
          colorDark: "#3B8AA9",
          colorLight: "#ffffff",
          correctLevel: QRCode.CorrectLevel.H,
        })
      })
    },
  },
}
</script>

<style lang="scss">
body,
html,
.wisdomScreenTouchScreen,
#app {
  height: 100%;
}
.wisdomScreenTouchScreen {
  padding: 8px;
  background: linear-gradient(to right, #ffb901, #febd11, #ff5b69);

  display: flex;
  flex-direction: column;
  &,
  * {
    box-sizing: border-box;
  }

  .mainContent {
    display: flex;
    background: rgba(255, 255, 255, 0.9);
    padding: 0px 10px;
    flex: 1;
    min-height: 0;
    align-items: flex-start;
    .contan {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      img {
        width: 60px;
      }
      span {
        font-size: 6px;
        color: #525252;
        margin-top: 10px;
        padding-bottom: 10px;
      }
    }
    .swiper-container {
      padding: 5px;
      padding: 8px;
      width: 100%;
      height: 100%;
      .swiper-pagination {
        bottom: 0.375px;
        padding-bottom: 0px;
        margin-top: 3px;
      }
      .swiper-pagination-bullet {
        width: 5px;
        display: inline-block;
        height: 5px;
        transform: scale(0.7);
        margin: 0 1px;
        box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.2) inset;
        background: #fff;
        border: 1px solid rgba(0, 0, 0, 0.2);
        opacity: 1;
        &.swiper-pagination-bullet-active {
          background: #ed2668;
        }
      }
      .swiper-slide {
        &.swiper-slide-active {
          padding: 0;
          .img {
            width: 67px;
            height: 67px;
          }
          .item {
            padding: 5px 8px;
            .title {
              font-size: 6px;
            }
            .subtitle {
              font-size: 5px;
            }
            .desc {
              font-size: 5px;
            }
            .other {
              .left {
                .price {
                  font-size: 5px;
                  .subPrice {
                    // font-size: 7px;
                    // margin-left: 0;
                    i{
                    }
                  }
                }
              }
              .right {
                width: 30px;
                height: 30px;
                .qrcode {
                  width: 30px;
                  height: 30px;
                  &::before {
                    content: none;
                  }
                }
                img {
                  width: 30px;
                  height: 30px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                }
              }
            }
          }
        }
        padding-bottom: 3px;
        padding-top: 15px;
        transition: 0.3s ease all;
        .item {
          display: flex;
          flex-direction: column;
          box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.2);
          background: #fff;
          padding: 6px 10px;
          border-radius: 4px;
          height: 100%;
        }
        .unChild {
          display: flex;
          flex-direction: column;
          flex: 1;
          min-width: 0;
          .other {
            margin-top: auto;
          }
        }
        .img {
          width: 60px;
          height: 60px;
          margin-left: auto;
          margin-right: auto;
          // flex:1;
          // min-height:0;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 5px 1px;
          overflow: hidden;

          // width:60px;
          //  height:55px;
          img {
            max-width: 100%;
            max-height: 100%;
          }
        }
        .title {
          font-size: 5px;
          color: #000;
          // line-height: 8px;
          // line-height:normal;
          line-height: 1.7;
          transition: 0.3s ease all;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .subtitle {
          font-size: 4px;
          // line-height: 5px;
          line-height: 1.6;
          color: #ed2668;
          // margin-top: 1px;
          // margin:3px 0;
          margin: 1.125px 0;
          transition: 0.3s ease all;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .desc {
          color: #666666;
          font-size: 4px;
          margin-bottom: 5px;
          span {
            display: block;
            &:nth-of-type(1) {
              margin-bottom: 3px;
              transition: 0.3s ease all;
              margin-top: 3px;
            }
            &:nth-of-type(2) {
              line-height: 7px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              transition: 0.3s ease all;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
          }
        }
        .other {
          margin-top: auto;
          display: flex;
          justify-content: space-between;
          align-items: flex-end;
          height: 38px;
          .left {
            .price {
              display: flex;
              // align-items: flex-end;
              font-size: 3.5px;
              color: #ed2668;
              transition: 0.3s ease all;
              margin-bottom: 2px;
              line-height: normal;
              flex-flow:wrap;
              align-items: center;
              .minPrice{
                &.mr3{
                  margin-right: 2.8px;
                }
                &.mr35{
                }
              }

              .subPrice {
                // font-size: 7px;
                margin-left: 0;
                margin-right: 0;
                font-weight:normal;
                &.type2Style{
                  font-weight:normal;
                  i{
                  text-decoration: line-through;
                  color:#999;
                  font-size:3px;
                  font-weight:normal;
                }
                }
                
              }

              span,
              i {
                display: flex;
                align-items: flex-end;
                line-height: normal;
              }
              i {
                font-style: normal;
              }
              span {
                // font-size: 5px;
                font-weight: bold;
                margin-right: 2px;
                line-height: 1;
              }
            }
            .txt {
              padding-top: 2px;
              display: block;
              font-size: 5px;
              color: #333;
              line-height: 7px;
            }
          }
          .right {
            overflow: hidden;
            height: 26px;
            width: 26px;

            .qrcode {
              canvas {
                width: 100%;
                height: 100%;
              }
              position: relative;
              width: 26px;
              height: 26px;
              display: flex;
              align-items: center;
              justify-content: center;
              &::before {
                position: absolute;
                width: 100%;
                height: 100%;
                background: rgba(255, 255, 255, 0.8);
                content: "";
              }
            }
            img {
              transition: 0.3s ease all;
              height: 26px;
              width: 26px;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }
    }
  }
}
.wisdomScreenTouchScreen .mainContent .swiper-container .swiper-slide .other .left{
  flex:1;
  min-width:0;
  margin-right: 7.5px;
}
.wisdomScreenTouchScreen .mainContent .swiper-container .swiper-slide .other .left .price{
  // display:block;
}
.wisdomScreenTouchScreen .mainContent .swiper-container .swiper-slide .other .left .price i{
  display:inline;
}
.wisdomScreenTouchScreen .mainContent .swiper-container .swiper-slide .other .left .price span{
  display: inline;
}
</style>
