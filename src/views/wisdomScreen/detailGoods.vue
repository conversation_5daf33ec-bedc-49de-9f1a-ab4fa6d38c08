<!--
 * @Author: 李亚龙
 * @FilePath: \yundian-m\src\views\wisdomScreen\detailGoods.vue
-->
<template>
  <div
    v-touch:right.self="eventFun"
    class="WisDomScreentDetailGoods"
    :class="[mobile ? 'mobile' : '']"
  >
    <empty
      v-if="empty"
      v-touch:right="eventFun"
      v-touch:left="eventFun2"
      :title="emptyTitle"
    />

    <wisdomScreentHeader v-touch:right="eventFun" :title="'商品详情'" />

    <div v-if="!empty" v-touch:right.self="eventFun" class="mainContent">
      <div class="left">
        <div class="top">
          <swiper ref="mySwiper" :options="swiperOption">
            <swiper-slide
              v-for="(item, index) in detailData.imgListFilterSrc"
              :key="index"
            >
              <img :src="item" alt="" />
            </swiper-slide>
          </swiper>
        </div>

        <div class="bottom">
          <div class="swiper-thumbs swiper-container">
            <div class="swiper-wrapper">
              <div
                v-for="(pic, index) in detailData.imgListFilterSrc"
                :key="'thumb' + index"
                :class="[
                  'swiper-slide',
                  currentIndex == index ? 'swiper-slide-active' : '',
                ]"
                @click="handleSwiperSlide(index)"
              >
                <img :src="pic" alt="" />
              </div>
            </div>
            <div slot="button-prev" class="swiper-button-prev"></div>
            <div slot="button-next" class="swiper-button-next"></div>
          </div>
          <!-- <swiper ref="mySwiper2" :options="swiperOption2">
            <swiper-slide v-for="(item, index) in 5" :key="index">
              <img src="./images/jasjdaxisiasd1.png" alt="">
            </swiper-slide>

          </swiper> -->
        </div>
      </div>

      <div v-touch:right="eventFun" class="right">
        <div class="text">
          <template v-if="mobile">
            <div class="topcon">
              <div class="leftdiv">
                <div class="title">
                  {{ detailData.modelName }}
                </div>
                <div class="subtitle2">
                  {{ detailData.goodsSubtitle }}
                </div>
                <div v-if="detailData.priceNumber && detailData.priceNumber.min" class="price">
                  <div>
                    {{ detailData.priceNumber.min }}
                    <span :class="['subPrice', detailData.priceNumber.type==3?'type2Style':'']"><i v-if="detailData.priceNumber.type==2">-</i> <i v-if="detailData.priceNumber.type == 2 || detailData.priceNumber.type == 3">{{ detailData.priceNumber.max }}</i></span>
                  </div>
                </div>
              </div>
              <div class="wxcode">
                <div ref="qrCodeUrl" class="qrCodeUrl"></div>
                <span>扫码下单</span>
              </div>
            </div>

            <div class="bottomcon">
              <div class="desctitle">
                商品详情介绍
              </div>
              <div class="desc">
                {{ detailData.description }}
              </div>
            </div>
          </template>
          <template v-else>
            <div class="pongetitle">
              {{ detailData.modelName }}
            </div>
            <div class="subtitle">
              {{ detailData.goodsSubtitle }}
            </div>
            <div v-if="detailData.description" class="desc">
              <div class="title">
                商品介绍:
              </div>
              <div class="txt">
                {{ detailData.description }}
              </div>
            </div>
            <div class="con">
              <div v-if="detailData.priceNumber && detailData.priceNumber.min" class="price">
                <div>
                  {{ detailData.priceNumber.min }}
                  <span :class="['subPrice', detailData.priceNumber.type==3?'type2Style':'']"><i v-if="detailData.priceNumber.type==2">-</i> <i v-if="detailData.priceNumber.type == 2 || detailData.priceNumber.type == 3">{{ detailData.priceNumber.max }}</i></span>
                </div>
              </div>
              <div class="wxcode">
                <div ref="qrCodeUrl" class="qrCodeUrl"></div>
                <span>扫码下单</span>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { swiper, swiperSlide } from "vue-awesome-swiper"
import wisdomScreentHeader from "./components/wisdomScreentHeader"
import "swiper/dist/css/swiper.css"
import wisdomScreen from "@/api/wisdomScreen"
import QRCode from "qrcodejs2"
import { getImgUrl, getUrl, regFenToYuan3 } from "@/utils/utils"
import empty from "./components/empty"

export default {
  components: {
    wisdomScreentHeader,
    swiper,
    swiperSlide,
    empty,
  },
  data() {
    const _this = this
    return {
      empty: false,
      emptyTitle: null,
      // swiperOption2:{
      //   spaceBetween: 10,
      //   slidesPerView: 4,
      //   freeMode: true,
      //   watchSlidesProgress: true,
      //   on:{
      //     click: function(event) {
      //       _this.swiper1.slideTo(this.clickedIndex)
      //       //this.clickedIndex - 1  使激活的slide滚动到一个合适的位置
      //       this.slideTo(
      //         this.clickedIndex >= 1 ? this.clickedIndex - 1 : this.clickedIndex
      //       )
      //     },
      //   }
      // },

      /* 详情数据 */
      detailData: {},

      /* 轮播图索引 */
      currentIndex: 0,

      swiperOption: {
        spaceBetween: 10,
        // thumbs: {
        //   swiper: {
        //     el: '.swiper-thumbs',
        //     slidesPerView: 4,
        //     spaceBetween: 10,
        //   }
        // },
        on: {
          slideChangeTransitionStart: function() {
            _this.currentIndex = this.activeIndex
          },
        },
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
      },

      mobile: false,
    }
  },
  computed: {
    swiper1() {
      return this.$refs.mySwiper.swiper
    },
    swiper2() {
      return this.$refs.mySwiper2.swiper
    },
  },
  created() {
    wisdomScreen
      .fetchDisplay({
        shopId: this.$route.query.shopId,
        createRole: this.$route.query.createRole,
      })
      .then((res) => {
        let displayType = res.data.displayType || 0
        if (displayType == 1) {
          this.mobile = true
        } else {
          this.mobile = false
        }
      })
  },
  mounted() {
    this.$nextTick(() => {
      this.swiperPopOpened()
    })
    document.querySelector(".icpinfo").style.display = "none"

    if(this.$wisdomScreentLoading){
      this.$wisdomScreentLoading.show()
    }

    if (!this.$route.query.shopId) {
      this.empty = true
      this.emptyTitle = "缺少店铺编码"

      if(this.$wisdomScreentLoading){
        this.$wisdomScreentLoading.hide()
      }
      return
    }

    if (!this.$route.query.screenGoodsId) {
      this.empty = true
      this.emptyTitle = "您查看的店铺/商品不存在"
      if(this.$wisdomScreentLoading){
        this.$wisdomScreentLoading.hide()
      }
      return
    }

    wisdomScreen
      .queryBigScreenStatus({
        shopId: this.$route.query.shopId,
      })
      .then((res) => {
        if (res.data.status == 1) {
          wisdomScreen
            .goodsDetail({
              shopId: parseInt(this.$route.query.shopId),
              screenGoodsId: parseInt(this.$route.query.screenGoodsId),
              createRole: parseInt(this.$route.query.createRole),
              goodsType: this.$route.query.goodsType || null,
            })
            .then((res) => {
              if (res.code == 0) {
                const data = res.data

                // 下架商品弹出提示并且跳转页面
                if (data.source == 2 && data.goodsStatus != 51) {
                  this.empty = true
                  this.emptyTitle = "商品已下架或已删除"
                  if(this.$wisdomScreentLoading){
                    this.$wisdomScreentLoading.hide()
                  }

                  setTimeout(() => {
                    this.$router.replace({
                      path: "/wisdomScreen/listGoods.html",
                      query: {
                        shopId: this.$route.query.shopId,
                      },
                    })
                  }, 2000)
                  return
                }

                if(data){
                  const isEmpty = (obj) => {
                    for (let i in obj) {
                      return true
                    }
                    return false
                  }
                  if (!isEmpty(data)) {
                    this.empty = true
                    this.emptyTitle = "商品已下架或已删除"
                    if(this.$wisdomScreentLoading){
                      this.$wisdomScreentLoading.hide()
                    }
                    return
                  }
                }

                if(data && data.goodsUrl){
                  this.creatQrCode(data.goodsUrl)
                }

                let imgList = null
                let imgListFilterSrc = null
                if (data.source == 3) {
                  if(data&&data.pictures){
                    imgList = JSON.parse(data.pictures)
                    imgListFilterSrc = imgList
                  }
                } else {
                  if(data && data.pictures && data.pictures.replace){
                    imgList = JSON.parse(data.pictures.replace("/"))
                    imgListFilterSrc = imgList.map((item) => {
                      return getImgUrl(item)
                    })
                  }
                }

                // 处理价格 price_form 1-一口价 2-价格区间 3-显示折扣价
                let priceNumber = {
                  type:data.priceFrom,
                  max:data.priceSection,
                  min:data.shopPrice !== null?data.shopPrice:data.price,
                }

                if (data.priceUnit) {
                  // 区间间隔的第一个价格单位要去掉
                  if(priceNumber.type == 2){
                    priceNumber.min = regFenToYuan3(priceNumber.min, " ")
                  }else{
                    priceNumber.min = regFenToYuan3(priceNumber.min, data.priceUnit)
                  }

                  priceNumber.max = regFenToYuan3(priceNumber.max, data.priceUnit)
                } else {
                  // 区间间隔的第一个价格单位要去掉
                  if(priceNumber.type == 2){
                    priceNumber.min = regFenToYuan3(priceNumber.min, " ")
                  }else{
                    priceNumber.min = regFenToYuan3(priceNumber.min, "元")
                  }

                  priceNumber.max = regFenToYuan3(priceNumber.max, "元")
                }

                this.detailData = {
                  ...data,
                  priceNumber,
                  imgList,
                  imgListFilterSrc,
                }
              }
              setTimeout(() => {
                this.$wisdomScreentLoading.hide()
              }, 500)
            })
            .catch(() => {
              setTimeout(() => {
                this.$wisdomScreentLoading.hide()
              }, 500)
            })
        } else {
          this.empty = true
          this.emptyTitle = "您暂未开通/已关闭了智慧大屏"
          this.$wisdomScreentLoading.hide()
        }
      })
      .catch(() => {
        this.empty = true
        this.emptyTitle = "您暂未开通/已关闭了智慧大屏"
        this.$wisdomScreentLoading.hide()
      })
  },
  methods: {
    handleSwiperSlide(index) {
      this.currentIndex = index
      this.swiper1.slideTo(index)
    },

    eventFun() {
      if (this.mobile) {
        this.$router.back()
      }
    },
    eventFun2() {
      if (this.mobile) {
        this.$router.go(1)
      }
    },

    /* 创建二维码 */
    creatQrCode(text) {
      let filterText = text
      if (!filterText.startsWith("http")) {
        filterText = getUrl(filterText)
      }

      new QRCode(this.$refs.qrCodeUrl, {
        text: filterText,
        colorDark: "#3B8AA9",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.H,
      })
    },

    //确保异步数据渲染完成后，更新swiper
    swiperPopOpened() {
      this.$nextTick(() => {
        this.swiper1.update()
      })
    },
  },
}
</script>

<style lang="scss">
body,
html,
.WisDomScreentDetailGoods,
#app {
  height: 100%;
}
.WisDomScreentDetailGoods {
  padding: 8px;
  background: linear-gradient(to right, #ffb901, #febd11, #ff5b69);
  display: flex;
  flex-direction: column;

  &,
  * {
    box-sizing: border-box;
  }

  .mainContent {
    display: flex;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 15px;
    flex: 1;
    min-height: 0;
    align-items: flex-start;

    & > div {
      height: 100%;
    }

    .left {
      width: 123px;
      display: flex;
      flex-direction: column;
      .top {
        background: #fff;
        flex: 1;
        min-height: 0;
        .swiper-container {
          height: 100%;
          .swiper-slide {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px 1px;
            img {
              max-height: 100%;
              max-width: 100%;
            }
          }
        }
      }

      .bottom {
        margin-top: 6px;
        height: 22px;
        padding-left: 12px;
        padding-right: 12px;
        position: relative;
        .swiper-container {
          position: static;
        }
        .swiper-button-prev,
        .swiper-button-next {
          margin-top: 0;
          top: 0;
          background-size: 5px auto;
          background-position: center center;
          background-color: rgba(0, 0, 0, 0.3);
          width: 9px;
          height: 22px;
        }
        .swiper-button-prev {
          border-radius: 0 5px 5px 0;
          left: 0;
          background-image: url(./images/left.png);
        }
        .swiper-button-next {
          border-radius: 5px 0 0 5px;
          right: 0;
          background-image: url(./images/right.png);
        }
        .swiper-slide {
          height: 22px;
          max-width: 22px;
          background: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 3px 1px;
          margin-right: 3px;
          border: 1px solid transparent;
          &.swiper-slide-active {
            border: 1px solid #5987f9;
          }
          img {
            max-width: 100%;
            max-height: 100%;
          }
        }
      }
    }

    .right {
      flex: 1;
      min-width: 0;
      margin-left: 20px;

      .text {
        height: 100%;
        display: flex;
        flex-direction: column;
        .pongetitle {
          font-size: 7px;
          color: #000;
          line-height: 10px;
        }
        .subtitle {
          color: #ed2668;
          line-height: 9px;
          font-size: 6px;
          padding-top: 4px;
          padding-bottom: 6px;
          border-bottom: 1px solid #d4d4d4;
        }
        .desc {
          flex: 1;
          min-height: 0;
          .title {
            font-size: 6px;
            color: #525252;
            line-height: 12px;
            margin-top: 6px;
            margin-bottom: 3px;
          }
          .txt {
            font-size: 6px;
            color: #525252;
            line-height: 10px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            transition: 0.3s ease all;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        }
        .con {
          margin-top: auto;
          display: flex;
          justify-content: space-between;
          align-items: flex-end;
          .price {
            font-size: 6px;
            color: #ed2668;
            line-height: 9px;
          }
          .wxcode {
            display: flex;
            flex-direction: column;
            .qrCodeUrl {
              width: 33px !important;
              height: 33px !important;
              overflow: hidden;
              display: flex;
              align-items: center;
              justify-content: center;
              canvas {
                width: 100%;
                height: 100%;
              }
              img {
                width: 33px !important;
                height: 33px !important;
                display: flex;
                align-items: center;
                justify-content: center;
              }
              // height:43px;
            }
            span {
              line-height: 6px;
              font-size: 5px;
              color: #272727;
              text-align: center;
              padding-top: 5px;
            }
          }
        }
      }
    }
  }
}

$WisDomScreentDetailGoodsmobiel: 2px;
.WisDomScreentDetailGoods.mobile {
  padding: 20px;
  background: linear-gradient(109deg, #fdb62f 9%, #fb5a67 92%);
  .headerTop {
    margin-bottom: 10px;
    .returnHistory {
      width: 40px;
      height: 17px;
      img {
        width: 9px;
        height: 9px;
      }
      span {
        font-size: 7px;
      }
    }
    .title {
      font-size: 11px;
    }
  }

  .mainContent {
    flex-direction: column;
    padding: 13 * $WisDomScreentDetailGoodsmobiel;
    .left {
      width: 100%;
      height: auto;
      flex: 1;
      min-height: auto;

      .top {
        // height: 202 * 1.3px;
        flex: 1;
        min-height: auto;
      }
      .bottom {
        margin-top: 5 * $WisDomScreentDetailGoodsmobiel;
        height: 35 * 1.3px;
        padding: 0 20 * 1.3px;
        .swiper-slide {
          height: 35 * 1.3px;
          max-width: 35 * 1.3px;
          margin-right: 5 * $WisDomScreentDetailGoodsmobiel;
        }
        .swiper-button-prev,
        .swiper-button-next {
          width: 15 * 1.3px;
          height: 35 * 1.3px;
          background-size: 9 * 1.3px auto;
        }
        .swiper-button-prev {
          border-radius: 0 10 * 1.3px 10 * 1.3px 0;
        }
        .swiper-button-next {
          border-radius: 10 * 1.3px 0 0 10 * 1.3px;
        }
      }
    }
    .right {
      width: 100%;
      margin-left: 0;
      margin-top: 10 * $WisDomScreentDetailGoodsmobiel;
      flex: unset;
      height: auto;
      display: flex;

      .text {
        height: auto;
        width: 100%;
      }

      .topcon {
        display: flex;
        padding-bottom: 5 * $WisDomScreentDetailGoodsmobiel;
        border-bottom: 1 solid rgba(0, 0, 0, 0.05);
        .leftdiv {
          flex: 1;
          min-width: 0;
          margin-right: 12 * $WisDomScreentDetailGoodsmobiel;
          .title {
            font-size: 11 * $WisDomScreentDetailGoodsmobiel;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            color: #000;
            line-height: 1.4;
            word-break: break-all;
          }
          .subtitle2 {
            color: rgba(0, 0, 0, 0.4);
            font-size: 9 * $WisDomScreentDetailGoodsmobiel;
            margin-top: 4 * $WisDomScreentDetailGoodsmobiel;
            margin-bottom: 5 * $WisDomScreentDetailGoodsmobiel;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 1.4;
          }
          .price {
            font-size: 10 * $WisDomScreentDetailGoodsmobiel;
            color: #f12467;
            .subPrice.type2Style i{
              font-size: 6 * $WisDomScreentDetailGoodsmobiel;
            }
          }
        }
        .wxcode {
          &,
          .qrCodeUrl,
          img {
            width: 47.5 * $WisDomScreentDetailGoodsmobiel;
            height: 47.5 * $WisDomScreentDetailGoodsmobiel;
          }
          img {
            max-width: 100%;
            width: 100%;
            height: 100%;
            max-height: 100%;
          }
          .qrCodeUrl {
            width: 47.5 * $WisDomScreentDetailGoodsmobiel;
            height: 47.5 * $WisDomScreentDetailGoodsmobiel;
          }
          span {
            font-size: 7 * $WisDomScreentDetailGoodsmobiel;
            text-align: center;
            color: #000;
            padding: 3.5 * $WisDomScreentDetailGoodsmobiel 0;
            display: block;
          }
        }
      }

      .bottomcon {
        .desctitle {
          font-size: 9 * $WisDomScreentDetailGoodsmobiel;
          color: #000;
          padding-top: 7 * $WisDomScreentDetailGoodsmobiel;
          padding-bottom: 5 * $WisDomScreentDetailGoodsmobiel;
        }
        .desc {
          font-size: 7 * $WisDomScreentDetailGoodsmobiel;
          color: rgba(0, 0, 0, 0.4);
          margin-top: 0px;
          line-height: 1.6;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          word-break: break-all;
        }
      }
    }
  }
}
.WisDomScreentDetailGoods.mobile .mainContent .left .top .swiper-container .swiper-slide img{
  max-width:50%;
}
</style>
<style lang="scss" scoped>
.price{
  .subPrice{
    &.type2Style{
      i{
        text-decoration: line-through;
      color:#999;
      font-size:3px;
      }
    }
  }
}
</style>