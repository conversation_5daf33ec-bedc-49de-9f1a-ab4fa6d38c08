<!--
 * @Author: 李亚龙
 * @FilePath: \yundian-m\src\views\wisdomScreen\listGoods.vue
-->

<template>
  <div v-touch:right="eventFun" v-touch:left="eventFun2" class="wisdomScreenListGoods" :class="[mobile ? 'mobile' : '']">
    <!-- 上下按钮 -->
    <div v-if="mobile && bs" class="btnlist">
      <div class="top" @click="handleTop">
        <img src="./images/jiantou.png" alt="" />
      </div>
      <div class="bottom" @click="handleBottom">
        <img src="./images/jiantou.png" alt="" />
      </div>
    </div>

    <empty v-if="empty" v-touch:right="eventFun" v-touch:left="eventFun2" :title="emptyTitle" />

    <wisdomScreentHeader :title="'商品列表'" />

    <div class="mainContent">
      <template v-if="mobile">
        <div v-if="goodsList.length" ref="mainChild" class="mainChild">
          <ul>
            <li ref="slideLi" class="swiper-slide">
              <div
                v-for="(item2, index2) in goodsList"
                :key="index2"
                class="item"
                @click="handleToview(item2)"
              >
                <div class="child">
                  <div class="img">
                    <img :src="item2.imgListFilterSrc && item2.imgListFilterSrc[0]" alt="" />
                  </div>
                  <div class="title">
                    {{ item2.modelName }}
                  </div>
                  <div class="subtitle">
                    {{ item2.goodsSubtitle }}
                  </div>
                  <div class="price">
                    <div>
                      {{ item2.priceNumber.min }}
                      <span :class="['subPrice', item2.priceNumber.type == 3 ? 'type2Style' : '']"><i
                        v-if="item2.priceNumber.type == 2"
                      >-</i> <i
                        v-if="item2.priceNumber.type == 2 || item2.priceNumber.type == 3"
                      >{{
                        item2.priceNumber.max }}</i></span>
                    </div>
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>
        <div v-else class="contan">
          <img src="./images/empty.png" alt="" />
          <span>没有相关商品数据</span>
        </div>
      </template>
      <template v-else>
        <template v-if="goodsList.length">
          <swiper ref="mySwiper" :options="swiperOption">
            <swiper-slide v-for="(item, index) in goodsList" :key="index">
              <div v-for="(item2, index2) in item" :key="index2" class="item" @click="handleToview(item2)">
                <div class="child">
                  <div class="img">
                    <img :src="item2.imgListFilterSrc && item2.imgListFilterSrc[0]" alt="" />
                  </div>
                  <div class="title">
                    {{ item2.modelName }}
                  </div>
                  <div class="subtitle">
                    {{ item2.goodsSubtitle }}
                  </div>
                  <div class="price">
                    <div>
                      {{ item2.priceNumber.min }}
                      <span :class="['subPrice', item2.priceNumber.type == 3 ? 'type2Style' : '']"><i
                        v-if="item2.priceNumber.type == 2"
                      >-</i> <i
                        v-if="item2.priceNumber.type == 2 || item2.priceNumber.type == 3"
                      >{{
                        item2.priceNumber.max }}</i></span>
                    </div>
                  </div>
                </div>
              </div>
            </swiper-slide>
          </swiper>
          <div class="swiper-button-prev"></div>
          <div class="swiper-button-next"></div>
        </template>
        <template v-else>
          <div class="contan">
            <img src="./images/empty.png" alt="" />
            <span>没有相关商品数据</span>
          </div>
        </template>
      </template>
    </div>
  </div>
</template>

<script>
import "swiper/dist/css/swiper.css"
import { getImgUrl, regFenToYuan3 } from "@/utils/utils"
import wisdomScreen from "@/api/wisdomScreen"
import { swiper, swiperSlide } from "vue-awesome-swiper"
import wisdomScreentHeader from "./components/wisdomScreentHeader"
import empty from "./components/empty"
import BScroll from "@better-scroll/core"

export default {
  components: {
    swiper,
    swiperSlide,
    wisdomScreentHeader,
    empty,
  },
  data() {
    return {
      /* 商品列表 */
      goodsList: [],

      empty: false,
      emptyTitle: "暂无数据",

      swiperOption: {
        speed: 1000,
        observer: true,
        observeParents: true,
        slidesPerColumnFill: "row",
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
        on: {
          slideChangeTransitionStart: function() { },
        },
      },

      mobile: false,
      bs: null,
    }
  },
  created() {
    wisdomScreen
      .fetchDisplay({
        shopId: this.$route.query.shopId,
        createRole: this.$route.query.createRole,
      })
      .then((res) => {
        let displayType = res.data.displayType || 0
        if (displayType == 1) {
          this.mobile = true
        } else {
          this.mobile = false
        }
      })
  },
  mounted() {
    document.querySelector(".icpinfo").style.display = "none"
    this.$wisdomScreentLoading.show()

    if (!this.$route.query.shopId) {
      this.empty = true
      this.emptyTitle = "缺少店铺编码"
      this.$wisdomScreentLoading.hide()
      return
    }

    wisdomScreen
      .queryBigScreenStatus({
        shopId: this.$route.query.shopId,
      })
      .then((res) => {
        if (res.data.status == 1) {
          wisdomScreen
            .queryGoods({
              catalogId: this.$route.query.id,
              shopId: this.$route.query.shopId,
              createRole: this.$route.query.createRole,
              pageSize: 9999,
            })
            .then((res) => {
              if (res.code == 0) {
                if (!res.data.goodsList.length) {
                  setTimeout(() => {
                    this.$wisdomScreentLoading.hide()
                  }, 500)
                  return
                }
                const goodsList = res.data.goodsList.map((item) => {
                  let imgList = null
                  let imgListFilterSrc = null
                  if (item.source == 3) {
                    imgList = JSON.parse(item.pictures)
                    imgListFilterSrc = imgList
                  } else {
                    imgList = JSON.parse(item.pictures.replace("/"))
                    imgListFilterSrc = imgList.map((item) => {
                      return getImgUrl(item)
                    })
                  }

                  // 处理价格 price_form 1-一口价 2-价格区间 3-显示折扣价
                  let priceNumber = {
                    type: item.priceFrom,
                    max: item.priceSection,
                    min: item.shopPrice !== null ? item.shopPrice : item.price,
                  }

                  if (item.priceUnit) {
                    // 区间间隔的第一个价格单位要去掉
                    if (priceNumber.type == 2) {
                      priceNumber.min = regFenToYuan3(priceNumber.min, " ")
                    } else {
                      priceNumber.min = regFenToYuan3(priceNumber.min, item.priceUnit)
                    }

                    priceNumber.max = regFenToYuan3(priceNumber.max, item.priceUnit)
                  } else {
                    // 区间间隔的第一个价格单位要去掉
                    if (priceNumber.type == 2) {
                      priceNumber.min = regFenToYuan3(priceNumber.min, " ")
                    } else {
                      priceNumber.min = regFenToYuan3(priceNumber.min, "元")
                    }

                    priceNumber.max = regFenToYuan3(priceNumber.max, "元")
                  }

                  return {
                    ...item,
                    priceNumber,
                    imgList,
                    imgListFilterSrc,
                  }
                })

                let swiperList = []
                goodsList.forEach((item, index) => {
                  const lIndex = Math.floor(index / 10)
                  if (!swiperList[lIndex]) {
                    swiperList[lIndex] = []
                  }
                  swiperList[lIndex].push(item)
                })

                if (this.mobile) {
                  this.goodsList = [...goodsList]
                } else {
                  this.goodsList = swiperList
                }

                if (this.mobile) {
                  this.$nextTick(() => {
                    const mainChildHeight = this.$refs.mainChild.clientHeight
                    const slideLiHeight = this.$refs.slideLi.clientHeight

                    if (mainChildHeight < slideLiHeight) {
                      this.bs = new BScroll(this.$refs.mainChild, {
                        probeType: 3,
                        click: true,
                        scrollbar: {
                          fade: true,
                          interactive: false,
                        },
                        bounce: true,
                      })
                    } else {
                      this.bs = null
                    }
                  })
                }
              }

              setTimeout(() => {
                this.$wisdomScreentLoading.hide()
              }, 500)
            })
            .catch(() => { })
        } else {
          this.empty = true
          this.emptyTitle = "您暂未开通/已关闭了智慧大屏"
          this.$wisdomScreentLoading.hide()
        }
      })
      .catch(() => {
        this.empty = true
        this.emptyTitle = "您暂未开通/已关闭了智慧大屏"
        this.$wisdomScreentLoading.hide()
      })
  },
  methods: {
    eventFun() {
      if (this.mobile) {
        this.$router.back()
      }
    },
    eventFun2() {
      if (this.mobile) {
        this.$router.go(1)
      }
    },

    handleToview(item) {
      this.$router.push({
        path: "/wisdomScreen/detailGoods.html",
        query: {
          shopId: this.$route.query.shopId,
          screenGoodsId: item.screenGoodsId,
          goodsType: item.goodsType,
          createRole: item.createRole
        },
      })
    },

    handleBottom() {
      if (this.bs.y <= this.bs.maxScrollY) {
        return
      }
      this.bs.scrollTo(0, this.bs.y - 200, 200)
    },
    handleTop() {
      if (this.bs.y >= this.bs.minScrollY) {
        return
      }
      this.bs.scrollTo(0, this.bs.y + 200, 200)
    },
  },
}
</script>

<style lang="scss">
body,
html,
.wisdomScreenListGoods,
#app {
    height: 100%;
}

.wisdomScreenListGoods {
    * {
        box-sizing: border-box;
    }

    background: linear-gradient(to right, #ffb901, #febd11, #ff5b69);
    padding: 8px;
    display: flex;
    flex-direction: column;

    .mainContent {
        flex: 1;
        background: rgba(255, 255, 255, 0.9);
        padding: 0 13px;
        padding-top: 6.5px;
        padding-bottom: 6.5px;
        display: flex;
        min-height: 0;
        position: relative;

        .swiper-button-prev,
        .swiper-button-next {
            width: 12px;
            background-size: 9px auto;
            height: 21px;
            background-color: rgba(0, 0, 0, 0.4);
            margin-top: -10.5px;
        }

        .swiper-button-prev {
            border-radius: 0 8px 8px 0;
            left: 0;
            background-image: url(images/left.png);
            background-position: 2.9px center;
        }

        .swiper-button-next {
            border-radius: 8px 0 0 8px;
            right: 0;
            background-image: url(images/right.png);
            background-position: 3px center;
        }

        .swiper-container {
            width: 100%;
        }

        .contan {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            img {
                width: 60px;
            }

            span {
                font-size: 6px;
                color: #525252;
                margin-top: 10px;
                padding-bottom: 10px;
            }
        }

        .swiper-slide {
            padding: 0 0px;
            height: auto;
            display: flex;
            flex-flow: wrap;

            .item {
                height: 50%;
                width: (100%/5);
                padding: 3.5px 3.5px;

                .child {
                    height: 100%;
                    padding: 4px 6px;
                    flex-direction: column;
                    background: #fff;
                    display: flex;
                }

                .img {
                    width: 46px;
                    flex: 1;
                    min-height: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    overflow: hidden;

                    img {
                        max-width: 100%;
                        max-height: 100%;
                    }
                }

                .title {
                    font-size: 4px;
                    color: #333;
                    line-height: 5px;
                    padding: 3px 0;
                    padding-bottom: 0;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }

                .subtitle {
                    font-size: 3px;
                    color: #999;
                    line-height: 3px;
                    margin: 3px 0;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    transition: 0.3s ease all;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }

                .price {
                    color: #ed2668;
                    font-size: 4px;
                }
            }
        }
    }
}

.wisdomScreenListGoods.mobile {
    padding: 20px;
    background: linear-gradient(109deg, #fdb62f 9%, #fb5a67 92%);

    .btnlist {
        position: absolute;
        right: 2px;
        top: 40%;

        img {
            width: 11px;
            height: auto;
        }

        .top,
        .bottom {
            display: flex;
            align-items: center;
            justify-content: center;
            user-select: none;
        }

        .top {
            width: 18px;
            height: 50px;
            border-radius: 20.5px 20.5px 0px 0px;
            background: rgba(0, 0, 0, 0.5);
        }

        .bottom {
            width: 18px;
            height: 50px;
            border-radius: 0px 0px 20.5px 20.5px;
            background: rgba(0, 0, 0, 0.5);
            margin-top: 2px;

            img {
                transform: rotate(180deg);
            }
        }
    }

    .headerTop {
        margin-bottom: 10px;

        .returnHistory {
            width: 40px;
            height: 17px;

            img {
                width: 9px;
                height: 9px;
            }

            span {
                font-size: 7px;
            }
        }

        .title {
            font-size: 11px;
        }
    }

    .mainContent {
        display: block;
        overflow: hidden;
        border-radius: 3px;
        padding: 13px;
        padding-bottom: 0;

        .mainChild {
            height: 100%;
            overflow: hidden;
        }

        .swiper-slide {
            .item {
                width: (100%/3);
                padding: 6px;
                margin-bottom: 0px;

                // padding-right: 6px;
                // &:nth-of-type(2n + 0) {
                //   padding-left: 6px;
                //   padding-right: 0;
                // }
                &:last-child {
                    margin-bottom: 6px;
                }

                .child {
                    padding: 10px 8px;
                    border-radius: 3px;

                    .img {
                        flex: inherit;
                        height: 50px;
                        width: 50px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: 0 auto;
                    }
                }

                .title {
                    font-size: 8px;
                    line-height: 1.4;
                    margin-top: 3px;
                }

                .subtitle {
                    font-size: 6px;
                    line-height: 1.3;
                    margin-top: 5px;
                    margin-bottom: 0;
                    height: 16px;
                }

                .price {
                    font-size: 6px;
                    margin-top: 5px;

                    .subPrice.type2Style i {
                        font-size: 4.5px;
                    }
                }
            }
        }
    }
}
</style>
<style lang="scss" scoped>
.price {
    .subPrice {
        &.type2Style {
            i {
                text-decoration: line-through;
                color: #999;
                font-size: 3px;
            }
        }
    }
}
</style>
