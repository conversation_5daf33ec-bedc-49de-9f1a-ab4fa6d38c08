<!--
 * @FilePath: \yundian-m\src\views\wisdomScreen\touchScreen.vue
-->
<template>
  <div
    v-touch:right.self="eventFun"
    v-touch:left.self="eventFun2"
    class="wisdomScreen"
    :class="[mobile ? 'mobile' : '']"
  >

    <!-- <button
      class="vanScreen"
      type="danger"
      style="position: fixed;top:0;left:0;z-index:999"
      round
      @click="handlerScreen"
    >{{ isScreen?'取消全屏':'全屏' }}</button> -->

   

    <empty
      v-if="empty"
      v-touch:right="eventFun"
      v-touch:left="eventFun2"
      :is-header="false"
      :is-return-prev="false"
      :is-return-home="false"
      :title="emptyTitle"
    />
    <div v-touch:right="eventFun" v-touch:left="eventFun2" class="left">
      <div
        v-for="(item, index) in classificationData"
        :key="index"
        :class="['title']"
      >
        <div class="par" @click="handleGoods(item)">
          <span>{{ item.name }}</span>
        </div>
      </div>
    </div>

    <div v-touch:right="eventFun" v-touch:left="eventFun2" class="topTitle" @click="handlerScreen">
      <span>{{ shopShortName }}</span>
    </div>

    <div v-if="advertisingList.length" class="right">
      <template v-if="mobile">
        <swiper ref="mySwiper" :options="swiperOption">
          <swiper-slide
            v-for="(item2, index2) in advertisingList"
            :key="index2"
          >
            <div
              :style="{ backgroundImage: `url(${item2.imgUrl})` }"
              class="divstyle"
              @click="handleSwiper(item2)"
            ></div>
          </swiper-slide>
          <div slot="pagination" class="swiper-pagination"></div>
          <div slot="button-prev" class="swiper-button-prev"></div>
          <div slot="button-next" class="swiper-button-next"></div>
        </swiper>
      </template>
      <template v-else>
        <swiper ref="mySwiper" :options="swiperOption">
          <swiper-slide
            v-for="(item2, index2) in advertisingList"
            :key="index2"
          >
            <img :src="item2.imgUrl" alt="" @click="handleSwiper(item2)" />
          </swiper-slide>
          <div slot="pagination" class="swiper-pagination"></div>
          <div slot="button-prev" class="swiper-button-prev"></div>
          <div slot="button-next" class="swiper-button-next"></div>
        </swiper>
      </template>
    </div>
    <div v-else class="emptyRight">
      <div class="emptyChild">
        <img src="./images/hashdhasdhahsd2.jpg" alt="" />
      </div>
    </div>
  </div>
</template>

<script>
import "swiper/dist/css/swiper.css"
import { swiper, swiperSlide } from "vue-awesome-swiper"
import wisdomScreen from "@/api/wisdomScreen"
import { getImgUrl } from "@/utils/utils"
import empty from "./components/empty"
import shopApi from "@/api/shop"
import lodash from 'lodash'

export default {
  components: {
    swiper,
    swiperSlide,
    empty,
  },
  data() {
    return {
      empty: false,
      emptyTitle: null,
      /* 分类列表 */
      classificationData: [],
      /* 广告列表 */
      advertisingList: [],

      // 循环
      swiperOption: {
        spaceBetween: 10,
        loop: true,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
        autoplay: {
          delay: 10000,
          disableOnInteraction: false,
        },
      },

      swiperOption2: {
        spaceBetween: 10,
        slidesPerView: 4,
        freeMode: true,
        watchSlidesProgress: true,
      },

      shopShortName: "",

      mobile: false,

      /* 是否全屏 */
      isScreen:false
    }
  },
  computed: {
    swiper() {
      return this.$refs.mySwiper.swiper
    },
  },
  created() {
    wisdomScreen.queryMenus({
      shopId: this.$route.query.shopId
    }).then(res => {
      let data0 = res.data[0]
      if(data0){
        const createRole = data0.createRole

        this.createRole = createRole
        
        wisdomScreen.fetchDisplay({
          shopId: this.$route.query.shopId,
          createRole
        }).then(res => {
          let displayType = res.data.displayType || 0
          if (displayType == 1) {
            this.mobile = true
          } else {
            this.mobile = false
          }
        })
      }
    })
  },
  mounted() {
    this.$wisdomScreentLoading.show()
    document.querySelector(".icpinfo").style.display = "none"
    if (!this.$route.query.shopId) {
      this.empty = true
      this.emptyTitle = "缺少店铺编码"
      this.$wisdomScreentLoading.hide()
      return
    }
    shopApi.getShopStatus({ shopId: this.$route.query.shopId }).then((res) => {
      if (res.code == 0) {
        if (res.data) {
          this.shopShortName = res.data.shopShortName
        }
      }
    })
    wisdomScreen
      .queryBigScreenStatus({
        shopId: this.$route.query.shopId,
      })
      .then((res) => {
        if (res.data.status == 1) {
          wisdomScreen.queryMenus({
            shopId: this.$route.query.shopId || "10000003",
          }).then(res => {
            let createRole = lodash.get(res,'data[0].createRole', 0)

            Promise.all([
              wisdomScreen.queryMenus({
                shopId: this.$route.query.shopId || "10000003",
              }),
              wisdomScreen.busiBgsQueryShopAds({
                shopId: this.$route.query.shopId || "10000003",
                createRole
              }),
            ])
              .then(([res1, res2]) => {
                if (res1.code == 0) {
                  this.classificationData = res1.data
                }
                if (res2.code == 0) {
                  this.advertisingList = res2.data.advertisings.map((item) => {
                    return {
                      ...item,
                      imgUrl: getImgUrl(item.img),
                    }
                  })
                }
                setTimeout(() => {
                  this.$wisdomScreentLoading.hide()
                }, 500)
              })
              .catch(() => {
                setTimeout(() => {
                  this.$wisdomScreentLoading.hide()
                }, 500)
              })
          })
        } else {
          this.empty = true
          this.emptyTitle = "店铺智慧大屏暂未开通/已关闭"
          this.$wisdomScreentLoading.hide()
        }
      })
      .catch(() => {
        this.empty = true
        this.emptyTitle = "店铺智慧大屏暂未开通/已关闭"
        this.$wisdomScreentLoading.hide()
      })
  },
  methods: {
    /* 全屏 */
    handlerScreen(){
      if(this.isScreen){
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.webkitExitFullscreen) { /* Safari */
          document.webkitExitFullscreen()
        } else if (document.msExitFullscreen) { /* IE11 */
          document.msExitFullscreen()
        }

        this.isScreen = false
      }else{
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen()
        } else if (document.documentElement.webkitRequestFullscreen) { /* Safari */
          document.documentElement.webkitRequestFullscreen()
        } else if (document.documentElement.msRequestFullscreen) { /* IE11 */
          document.documentElement.msRequestFullscreen()
        }

        this.isScreen = true
      }
      
    },


    eventFun() {
      if (this.mobile) {
        this.$router.back()
      }
    },
    eventFun2() {
      if (this.mobile) {
        this.$router.go(1)
      }
    },

    /* 点击 */
    handleGoods(item) {
      this.$router.push({
        path: "/wisdomScreen/listGoods.html",
        query: {
          shopId: this.$route.query.shopId,
          id: item.id,
          createRole: item.createRole
        },
      })
    },

    handleSwiper(item) {
      this.$router.push({
        path: "/wisdomScreen/detailGoods.html",
        query: {
          shopId: this.$route.query.shopId,
          screenGoodsId: item.screenGoodsId,
          createRole: this.createRole
        },
      })
    },
  },
}
</script>

<style lang="scss">
#app {
  height: 100%;
}
html,
body {
  width: 100%;
  box-sizing: border-box;
  height: 100%;
}
.wisdomScreen {
  display: flex;
  width: 100%;
  box-sizing: border-box;
  height: 100%;
  background: linear-gradient(to right, #fec42d, #fe7e49);
  padding: 0 8px;

  .topTitle {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    margin: auto;
    opacity: 0.68;
    display: flex;
    justify-content: center;
    span {
      background: linear-gradient(180deg, #ffffff, #ffd1b1);
      border-radius: 2px 2px 7px 7px;
      color: #763500;
      padding: 2.9px 18px;
      font-size: 4px;
    }
  }

  .emptyRight {
    width: 100%;
    height: 100%;
    display: block;
    padding: 8px 0;
    box-sizing: border-box;
    .emptyChild {
      width: 100%;
      height: 100%;
      display: block;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 5px;
      display: flex;
      font-size: 16px;
      overflow: hidden;
      color: #333;
      align-items: center;
      justify-content: center;
    }
    img {
      display: block;
      width: 100%;
      height: 100%;
    }
    .contan {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      img {
        width: 60px;
      }
      span {
        font-size: 6px;
        color: #525252;
        margin-top: 10px;
        padding-bottom: 10px;
      }
    }
  }

  .left {
    display: flex;
    flex-direction: column;
    width: 62px;
    margin-right: 5px;
    padding: 5px 0;
    .title {
      // padding-bottom: 5px;
      padding: 3px 0;
      height: (100%/7);
      position: relative;
      &.active {
        .par {
          &:hover {
            color: #fff;
            &::before {
              opacity: 1;
            }
          }
        }
      }
      .par {
        height: 100%;
        overflow: hidden;
        background: linear-gradient(180deg, #fff5cd 3%, #fff6d4 91%, #ffd858);
        transition: 0.3s ease all;
        cursor: pointer;
        border: 2px solid #fff2d7;
        border-radius: 3px;
        box-shadow: 0px 2px 8px 0px rgba(77, 37, 2, 0.1);
        color: #1a1919;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 7px;
        position: relative;
        &:hover {
          color: #fff;
          &::before {
            opacity: 1;
          }
        }
        &::before {
          position: absolute;
          width: 100%;
          height: 100%;
          left: 0;
          top: 0;
          background: linear-gradient(
            180deg,
            #ffb32d,
            #ffa71d 71%,
            #f78501 95%
          );
          opacity: 0;
          z-index: 1;
          content: "";
          transition: 0.3s ease all;
        }
      }
      span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 0 6px;
      }

      span {
        position: relative;
        z-index: 2;
      }
    }
  }

  .right {
    flex: 1;
    overflow: hidden;
    min-width: 0;
    padding: 8px 0;
    .swiper-container {
      border-radius: 3px;
    }
    .block,
    .child,
    .swiper-container {
      height: 100%;
    }
    .swiper-slide {
      img {
        max-width: 100%;
        height: auto;
      }
    }
    .swiper-pagination-bullets {
      bottom: 10px;
    }
    .swiper-pagination-bullet {
      background: rgba(255, 255, 255, 0.5);
      opacity: 1;
      width: 5px;
      height: 5px;
      margin: 0 3px;
      transition: 0.3s ease all;
      &.swiper-pagination-bullet-active {
        background: #ed2668;
      }
    }
    .swiper-button-prev {
      border-radius: 0 8px 8px 0;
      left: 0;
      background-image: url(images/left.png);
      background-position: 2.9px center;
    }
    .swiper-button-next {
      border-radius: 8px 0 0 8px;
      right: 0;
      background-image: url(images/right.png);
      background-position: 4px center;
    }
    .swiper-button-prev,
    .swiper-button-next {
      width: 15px;
      background-size: 10px auto;
      height: 21px;
      background-color: rgba(0, 0, 0, 0.4);
      margin-top: -10.5px;
    }
  }
}

.wisdomScreen.mobile {
  flex-direction: column-reverse;

  .topTitle {
    span {
      padding-top: 5px;
      padding-bottom: 7px;
      font-size: 10px;
      border-radius: 0 0 28px 28px;
      padding-left: 25px;
      padding-right: 25px;
    }
  }

  .left {
    flex-direction: row;
    width: 100%;
    margin-bottom: 27px;
    margin-right: 0;
    padding: 0;
    justify-content: space-evenly;
    .title {
      height: auto;
      width: 34px;
      .par {
        font-size: 12px;
        background: linear-gradient(90deg, #faeeb9 80%, #fae08f 100%, #fdd769);
        padding-top: 12px;
        padding-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        &::before {
          background: linear-gradient(
            90deg,
            #fdaf3e 72%,
            #f68927 100%,
            #f47e1c
          );
        }
      }
      span {
        white-space: normal;
        letter-spacing: 4px;
        // writing-mode: vertical-rl;
        font-weight: bold;
        text-align: center;
        text-orientation: upright;
        word-break: break-all;
      }
    }
  }

  .right {
    padding-top: 0;
    margin-top: 32px;
    .divstyle {
      width: 100%;
      height: 100%;
      background-repeat: no-repeat;
      background-position: center center;
      background-size: cover;
    }
    .swiper-button-prev,
    .swiper-button-next {
      width: 24px;
      height: 37px;
      background-size: 18px auto;
      background-color: rgba(0, 0, 0, 0.5);
    }
    .swiper-button-prev {
      border-radius: 0 12px 12px 0;
    }
    .swiper-button-next {
      border-radius: 12px 0 0 12px;
    }
  }
}
.wisdomScreen.mobile .left::before{
    content: '';
    display: block;
}
.wisdomScreen.mobile .left::after {
    content: '';
    display: block;
}
.wisdomScreen.mobile .left{
    justify-content: space-between;
}
.wisdomScreen.mobile .emptyRight{
    height:auto;
    flex: 1;
    min-height: 0;
}
</style>
<style lang="scss" scoped>

</style>