<!--
 * @Author: 李亚龙
 * @Date: 2021-11-08 16:40:41
 * @LastEditTime: 2021-11-08 17:26:13
 * @LastEditors: your name
 * @Description:
 * @FilePath: \yundian-m\src\views\wisdomScreen\components\wisdomScreentLoading\wisdomScreentLoading.vue
-->
<template>
  <div v-show="isShow" class="loadings">
    <div class="box">
      <div class="loader-08"></div>
    </div>
    <!-- <div class="loading">
      <span></span>
      <span></span>
      <span></span>
      <span></span>
      <span></span>
    </div> -->
    <!-- <div class="c-loading">
      <div class="c-l-loading"></div><span>{{ text }}</span>
    </div> -->
  </div>
</template>

<script>
export default {
  props: {
    isShow: <PERSON><PERSON><PERSON>,
    text: {
      type: String,
      default:'正在加载中...'
    }
  },
  data() {
    return {
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/style" scope>
.loader-08 {
    position: relative;
    width:20px;
    height:20px;
}
.loader-08:before,
.loader-08:after {
    content: '';
    width: inherit;
    background: #f60;
    height: inherit;
    border-radius: 50%;
    background-color: currentcolor;
    opacity: 0.6;
    position: absolute;
    top: 0;
    left: 0;
    -webkit-animation: loader-08 2.0s infinite ease-in-out;
    animation: loader-08 2.0s infinite ease-in-out;
}
.loader-08:after {
    -webkit-animation-delay: -1.0s;
    animation-delay: -1.0s;
}
@-webkit-keyframes loader-08 {
    0%, 100% {
        -webkit-transform: scale(0);
        transform: scale(0);
    }
    50% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}
@keyframes loader-08 {
    0%, 100% {
        -webkit-transform: scale(0);
        transform: scale(0);
    }
    50% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}
.box {

}

.loader-16 {
    -webkit-transform: rotateZ(45deg);
    transform: rotateZ(45deg);
    -webkit-perspective: 1000px;
    perspective: 1000px;
    border-radius: 50%;
}
.loader-16:before,
.loader-16:after {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: inherit;
    height: inherit;
    border-radius: 50%;
    -webkit-animation: 1s spin linear infinite;
    animation: 1s spin linear infinite;
}
.loader-16:before {
    -webkit-transform: rotateX(70deg);
    transform: rotateX(70deg);
}
.loader-16:after {
    -webkit-transform: rotateY(70deg);
    transform: rotateY(70deg);
    -webkit-animation-delay: .4s;
    animation-delay: .4s;
}
@-webkit-keyframes rotate {
    0% {
        -webkit-transform: translate(-50%, -50%) rotateZ(0deg);
        transform: translate(-50%, -50%) rotateZ(0deg);
    }
    100% {
        -webkit-transform: translate(-50%, -50%) rotateZ(360deg);
        transform: translate(-50%, -50%) rotateZ(360deg);
    }
}
@keyframes rotate {
    0% {
        -webkit-transform: translate(-50%, -50%) rotateZ(0deg);
        transform: translate(-50%, -50%) rotateZ(0deg);
    }
    100% {
        -webkit-transform: translate(-50%, -50%) rotateZ(360deg);
        transform: translate(-50%, -50%) rotateZ(360deg);
    }
}
@-webkit-keyframes rotateccw {
    0% {
        -webkit-transform: translate(-50%, -50%) rotate(0deg);
        transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
        -webkit-transform: translate(-50%, -50%) rotate(-360deg);
        transform: translate(-50%, -50%) rotate(-360deg);
    }
}
@keyframes rotateccw {
    0% {
        -webkit-transform: translate(-50%, -50%) rotate(0deg);
        transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
        -webkit-transform: translate(-50%, -50%) rotate(-360deg);
        transform: translate(-50%, -50%) rotate(-360deg);
    }
}
@-webkit-keyframes spin {
    0%, 100% {
        box-shadow: .2em 0px 0 0px currentcolor;
    }
    12% {
        box-shadow: .2em .2em 0 0 currentcolor;
    }
    25% {
        box-shadow: 0 .2em 0 0px currentcolor;
    }
    37% {
        box-shadow: -.2em .2em 0 0 currentcolor;
    }
    50% {
        box-shadow: -.2em 0 0 0 currentcolor;
    }
    62% {
        box-shadow: -.2em -.2em 0 0 currentcolor;
    }
    75% {
        box-shadow: 0px -.2em 0 0 currentcolor;
    }
    87% {
        box-shadow: .2em -.2em 0 0 currentcolor;
    }
}
@keyframes spin {
    0%, 100% {
        box-shadow: .2em 0px 0 0px currentcolor;
    }
    12% {
        box-shadow: .2em .2em 0 0 currentcolor;
    }
    25% {
        box-shadow: 0 .2em 0 0px currentcolor;
    }
    37% {
        box-shadow: -.2em .2em 0 0 currentcolor;
    }
    50% {
        box-shadow: -.2em 0 0 0 currentcolor;
    }
    62% {
        box-shadow: -.2em -.2em 0 0 currentcolor;
    }
    75% {
        box-shadow: 0px -.2em 0 0 currentcolor;
    }
    87% {
        box-shadow: .2em -.2em 0 0 currentcolor;
    }
}

.loading{
            height: 20px;
        }

        .loading span{
            display: inline-block;
            width: 6px;
            height: 100%;
            border-radius: 4px;
            background: #ffb901;
            -webkit-animation: load 1s ease infinite;
        }
        @-webkit-keyframes load{
            0%,100%{
                height: 20px;
                background: #febd11;
            }
            50%{
                height: 50px;
                margin: -15px 0;
                background: #ff5b69;
            }
        }
        .loading span:nth-child(2){
            -webkit-animation-delay:0.2s;
        }
        .loading span:nth-child(3){
            -webkit-animation-delay:0.4s;
        }
        .loading span:nth-child(4){
            -webkit-animation-delay:0.6s;
        }
        .loading span:nth-child(5){
            -webkit-animation-delay:0.8s;
        }
  .loadings {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(255,255,255,1);
    background: #f6bf7f;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    z-index: 10000;


     @keyframes mymove
      {
        from {width:0px;}
        to {width:300px;}
      }
      .c-loading{
        width:300px;
        height:5px;
        border-radius: 10px;
        position: relative;
        background:#f3f3f3;
        span{
          float: right;
          margin-right: -60px;
          margin-top: -10px;
        }
      }
      .c-l-loading{
        width:0px;
        height:5px;
        border-radius: 5px;
        background:#409EFF;
        animation:mymove 10s ease;
        animation-fill-mode:forwards;
      }
  }
</style>
