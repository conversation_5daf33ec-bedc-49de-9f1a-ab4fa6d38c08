<!--
 * @Author: 李亚龙
 * @Date: 2021-11-09 17:42:02
 * @LastEditTime: 2021-12-07 19:08:17
 * @LastEditors: your name
 * @Description:
 * @FilePath: \yundian-m\src\views\wisdomScreen\components\empty.vue
-->
<template>
  <div class="empty">
    <wisdomScreentHeader v-if="isHeader" :is-return-prev="isReturnPrev" :is-return-home="isReturnHome" :title="''" />

    <div class="contan">
      <img src="../images/empty.png" alt="">
      <span>{{ title }}</span>
    </div>
  </div>
</template>

<script>
import wisdomScreentHeader from './wisdomScreentHeader'

export default {
  components: {
    wisdomScreentHeader
  },
  props: {
    isHeader: {
      type: Boolean,
      default: true
    },
    isReturnHome: {
      type: Boolean,
      default: true
    },
    isReturnPrev: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    }
  },
  mounted() {
    // console.log(this.isHeader)
  }
}
</script>

<style lang="scss" scoped>
.empty {
    :deep(.headerTop) {
        width: 100%;
    }

    position: fixed;
    z-index:3;
    left:0;
    top:0;
    width:100%;
    height:100%;
    // background: #efefef;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    // & /deep/ .van-empty__image{
    //   width:80px;
    //   height:80px;
    // }

    background: linear-gradient(to right, #ffb901, #febd11, #ff5b69);
    padding:7px;

    .contan {
        background: rgba(255, 255, 255, .9);
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        img {
            width: 60px;
        }

        span {
            font-size: 6px;
            color: #525252;
            margin-top: 10px;
            padding-bottom: 10px;
        }
    }
}
</style>
