<!--
 * @Author: 李亚龙
 * @Date: 2021-11-06 19:11:09
 * @LastEditTime: 2022-05-24 15:36:47
 * @LastEditors: Please set LastEditors
 * @Description:
 * @FilePath: \yundian-m\src\views\wisdomScreen\components\wisdomScreentHeader.vue
-->
<template>
  <div class="headerTop">
    <div v-if="isReturnPrev" class="returnHistory" @click="$router.back()">
      <img src="../images/returntop.png" alt="" />
      <span>返回</span>
    </div>

    <div :class="['title', !isReturnPrev ? 'active' : '']">
      {{ title }}
    </div>

    <div v-if="isReturnHome" class="returnHistory" @click="handleHome">
      <img src="../images/m.png" alt="" />
      <span>主页</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: "商品列表",
    },
    isReturnPrev: {
      type: Boolean,
      default: true,
    },
    isReturnHome: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    handleHome() {
      this.$router.push({
        path: "/wisdomScreen/touchScreen.html",
        query: {
          shopId: this.$route.query.shopId,
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.headerTop {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  .title {
    color: #fff;
    font-size: 8px;
    text-align: center;
    flex: 1;
    min-width: 0;
    &.active {
      padding-bottom: 4px;
    }
  }
  .returnHistory {
    display: flex;
    width: 31px;
    cursor: pointer;
    height: 13px;
    border: 2px solid #ffffff;
    border-radius: 1px;
    justify-content: center;
    align-items: center;
    img {
      width: 8px;
      height: 8px;
    }
    span {
      font-size: 6px;
      color: #fff;
      line-height: 9px;
      margin-left: 3px;
    }
  }
}
</style>
