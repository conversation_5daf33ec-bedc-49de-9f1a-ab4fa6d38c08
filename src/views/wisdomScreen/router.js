/*
 * @Author: 李亚龙
 * @Date: 2021-11-06 10:22:13
 * @LastEditTime: 2021-11-10 11:20:47
 * @LastEditors: your name
 * @Description:
 * @FilePath: \yundian-m\src\views\wisdomScreen\router.js
 */
export default[
  {
    path: '/wisdomScreen/init.html',
    name: 'wisdomScreenInit',
    component: resolve => require(['./init.vue'], resolve),
    meta: {
      title: '选择店铺'
    }
  },
  {
    path: '/wisdomScreen/touchScreen.html',
    name: 'wisdomScreenIndex',
    component: resolve => require(['./touchScreen.vue'], resolve),
    meta: {
      title: '触屏-主页'
    }
  },
  {
    path: '/wisdomScreen/listGoods.html',
    name: 'wisdomScreenListGoods',
    component: resolve => require(['./listGoods.vue'], resolve),
    meta: {
      title: '商品列表'
    }
  },
  {
    path: '/wisdomScreen/detailGoods.html',
    name: 'wisdomScreenDetailGoods',
    component: resolve => require(['./detailGoods.vue'], resolve),
    meta: {
      title: '商品详情'
    }
  },
  {
    path: '/wisdomScreen/unTouchScreen.html',
    name: 'wisdomScreenUnTouchScreen',
    component: resolve => require(['./unTouchScreen.vue'], resolve),
    meta: {
      title: '非触屏首页'
    }
  },
  // {
  //   path: '/shopdata/statistics/query.html',
  //   name: 'statisticalQuery',
  //   component: resolve => require(['./statistics/query.vue'], resolve),
  //   meta: {
  //     title: '统计查询'
  //   }
  // }
]
