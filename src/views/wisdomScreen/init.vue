<template>
  <main :class="['ScreenInit', isScreenType === 'h' ? 'h' : '']">


    <!-- 设置全屏的悬浮按钮 -->
    <div :class="['vanScreen', isButtonScreen ? 'active' : '']" @click="handlerScreen">
      {{ isScreen ? '取消全屏' : '全屏' }}
    </div>
    <!-- 标题 -->
    <div :class="['title', isScreenType === 'h' ? 'mb20' : '']">
      <img v-if="isScreenType === 'w'" class="wtitleimg" src="./images/folder_title.png" alt="">
      <img v-if="isScreenType === 'h'" class="htitleimg" src="./images/folder_title_m.png" alt="">
    </div>

    <!-- 主题内容 -->
    <div :class="['blocka', isScreenType === 'w' ? 'flex1' : '']">

      <div class="logoorInput">
        <!-- logo -->
        <div class="logo">
          <img src="./images/yundianLogo.png" alt="">
        </div>

        <!-- 输入框 -->
        <div v-if="isScreenType == 'w'" style="display:flex;padding:20px;">
          <van-radio-group v-model="radio" checked-color="#ffb12e" icon-size="50" direction="horizontal">
            <van-radio name="2" tabindex="3">
              非触屏版
            </van-radio>
            <van-radio name="1" tabindex="2">
              触屏版
            </van-radio>
          </van-radio-group>
        </div>
        <div class="inputText">
          <!-- <div v-show="isOntouchstart" class="input">
            {{ value?value:'请输入店铺ID' }}
          </div> -->
          <input
            ref="getFocus"
            v-model="value"
            tabindex="3"
            class="input"
            oninput="value=value.replace(/\D/g,'')"
            type="text"
            placeholder="请输入店铺ID"
            @keyup.enter="handleClick({ confirm: true })"
          >
          <img v-show="value" src="./images/remove.png" class="remove" alt="" @click="value = ''">

          <!-- 键盘 -->
          <!-- <div v-show="isOntouchstart" class="keyword">
            <ul>
              <li v-for="(item,index) in keywordData" :key="index" :class="[item.active?'active':'']" @click="handleClick(item)">
                <div class="child">
                  <span v-if="item.title">{{ item.title }}</span>
                  <span v-else-if="item.remove">
                    <img class="backSpace" src="./images/backSpace.png" alt="">
                  </span>
                  <span v-else-if="item.confirm" class="confirm">确认</span>
                </div>
              </li>
            </ul>
          </div> -->
        </div>

      </div>
    </div>

    <!-- 输入弹框 -->
    <van-dialog
      v-model="diaLogoShow"
      confirm-button-color="#ffb12e"
      width="60%"
      show-cancel-button
      :title="`您输入的店铺ID为： ${value}`"
      @cancel="inputFocus"
      @confirm="confirm"
    >
      <div class="bl">
        <van-radio-group v-model="radio" checked-color="#ffb12e" icon-size="50" direction="horizontal">
          <van-radio name="1" tabindex="2">
            触屏版
          </van-radio>
          <van-radio name="2" tabindex="3">
            非触屏版
          </van-radio>
        </van-radio-group>
      </div>
    </van-dialog>
  </main>
</template>

<script>
import { Dialog, RadioGroup, Radio, Notify } from "vant"
import Vue from "vue"
Vue.use(Dialog).use(RadioGroup).use(Radio).use(Notify)

export default {
  data() {
    return {
      /* 弹出键盘 */
      keyBoardShow: true,

      /* 店铺id */
      value: '',

      /* 键盘数据 */
      keywordData: [
        { title: '1' },
        { title: '2' },
        { title: '3' },
        { title: '4' },
        { title: '5' },
        { title: '6' },
        { title: '7' },
        { title: '8' },
        { title: '9' },
        { remove: true },
        { title: '0' },
        { confirm: true },
      ],

      /* 弹框显示 */
      diaLogoShow: false,

      /* 触屏1 非触屏2 */
      radio: '2',

      /* w 横屏， h竖屏 */
      isScreenType: 'w',

      /* 判断是否触摸屏 */
      isOntouchstart: true,

      /* 是否全屏 */
      isScreen: false,

      /* 全屏按钮是否展开 */
      isButtonScreen: false
    }
  },

  created() {
    this.getShopId()

  },
  mounted() {
    this.setIntervalScreen()
    this.hideFooter()
    this.isLocalStorageShopId()
    this.renderResize()
    this.isOntouchstartBollean()
    this.inputFocus()
    this.isScreen = this.isInnerScreent()
    this.disableScrren()

    window.addEventListener("keyup", this.setGolbalEnter, false)
    window.addEventListener("resize", this.renderResize, false)
    window.addEventListener('pageshow', this.inputFocus)
  },
  destroyed() {
    window.removeEventListener("keyup", this.setGolbalEnter, false)
    window.removeEventListener("resize", this.renderResize, false)
    window.removeEventListener('pageshow', this.inputFocus)
  },

  methods: {
    /* 禁用全屏按钮 */
    disableScrren() {
      document.addEventListener('keydown', function(event) {
        if (event.key === 'F11') {
          event.preventDefault()
        }
      })
    },

    /* 定时任务判断是否全屏 */
    setIntervalScreen() {
      setInterval(() => {
        this.isScreen = this.isInnerScreent()
      }, 500)
    },

    /* 判断当前屏幕是否全屏 */
    isInnerScreent() {
      return window.innerWidth === screen.width && window.innerHeight === screen.height
    },

    /* 全屏 */
    handlerScreen() {
      if (!this.isButtonScreen) {
        this.isButtonScreen = true
        return
      }

      this.isScreen = this.isInnerScreent()

      if (this.isInnerScreent()) {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.webkitExitFullscreen) { /* Safari */
          document.webkitExitFullscreen()
        } else if (document.msExitFullscreen) { /* IE11 */
          document.msExitFullscreen()
        }
      } else {
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen()
        } else if (document.documentElement.webkitRequestFullscreen) { /* Safari */
          document.documentElement.webkitRequestFullscreen()
        } else if (document.documentElement.msRequestFullscreen) { /* IE11 */
          document.documentElement.msRequestFullscreen()
        }
      }

      this.isButtonScreen = false
    },

    /* 全局增加, 删除回车事件 */
    setGolbalEnter() {
      let key = window.event.keyCode
      if (key === 13) {
        this.handleClick({ confirm: true })
      }
    },

    /* 获取shopId */
    getShopId() {
      const url = new URL(location.href)
      const shopId = url.searchParams.get("shopId")
      if (shopId) {
        this.shopId = shopId
      }
    },

    /* 进入页面input, focus */
    inputFocus() {
      this.$refs.getFocus.focus()
    },

    /* 判断是否触摸屏 */
    isOntouchstartBollean() {
      this.isOntouchstart = 'ontouchstart' in document.documentElement
    },

    /* 记忆之前输入的店铺ID信息 */
    isLocalStorageShopId() {
      let wisdomScreenInitShopId = window.localStorage.getItem('wisdomScreenInitShopId')

      if (this.shopId) {
        this.value = this.shopId
        return
      }

      if (!wisdomScreenInitShopId) {
        return
      }

      this.value = wisdomScreenInitShopId
    },

    /* 隐藏协议隐藏 */
    hideFooter() {
      document.querySelector(".icpinfo").style.display = "none"
    },

    /* 点击数字按键 */
    handleClick(item) {
      if (item.title) {
        this.value += item.title
        return
      }

      if (item.remove) {
        this.value = this.value.slice(0, -1)
        return
      }

      if (item.confirm) {

        if (this.value.length <= 0) {
          Notify({ type: 'danger', message: '请输入店铺ID' })
          return
        }

        //this.diaLogoShow = true
        this.confirm()
        return
      }
    },

    /* 确定弹框 */
    confirm() {
      window.localStorage.setItem('wisdomScreenInitShopId', this.value)

      if (this.radio === '1' || this.isScreenType == 'h') {
        this.$router.push({
          path: "/wisdomScreen/touchScreen.html",
          query: {
            shopId: this.value,
          },
        })
      } else if (this.radio === '2') {
        this.$router.push({
          path: "/wisdomScreen/unTouchScreen.html",
          query: {
            shopId: this.value,
          },
        })
      }
    },

    /* 判断横竖屏 */
    renderResize() {
      // 判断横竖屏
      let width = document.documentElement.clientWidth
      let height = document.documentElement.clientHeight
      if (width > height) {
        // 横屏
        this.isScreenType = 'w'
      } else {
        // 竖屏
        this.isScreenType = 'h'
      }
    }
  }
}
</script>

<style lang="scss">
#app {
    height: 100%;
}

html,
body {
    width: 100%;
    box-sizing: border-box;
    height: 100%;
}
</style>
<style lang="scss" scoped>
.ScreenInit {
    width: 100%;
    box-sizing: border-box;
    height: 100%;
    background-image: url(./images/folder.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    :deep(.van-radio__label) {
        font-size: 8px;
    }

    .vanScreen {
        position: fixed;
        bottom: 50px;
        left: -18px;
        transition: .4s cubic-bezier(.3, .55, .1, 1), opacity .1s;

        &.active {
            left: 0;
        }

        background: rgb(255, 160, 27);
        z-index:2;
        border-radius:0 3px 3px 0px;
        display: flex;
        width:20px;
        height:8px;
        align-items: center;
        justify-content: center;
        color:#fff;
        font-size: 3px;
        padding: 0px 0px;
    }

    &.h {
        background-image: url(./images/folder_m.jpg);

        .blocka {
            padding-bottom: 60px;

            .logoorInput {
                width: 280px;
                height: 194px;

                .logo {
                    img {
                        width: 60px
                    }
                }

                .inputText {
                    width: 200px;
                    height: 24px;

                    .remove {
                        width: 12px;
                    }

                    .input {
                        font-size: 10px;
                    }
                }
            }

            .keyword {
                padding: 10px 6px;
                border-radius: 6px;

                ul {
                    li {
                        padding: 0 6px;

                        .child {
                            font-size: 10px;
                            margin-bottom: 6px;
                            border-radius: 6px;
                            height: 20px;

                            .backSpace {
                                width: 20px;
                            }
                        }
                    }
                }
            }

        }
    }


    :deep(.van-number-keyboard) {
        position: static;
        width: 80%;
        margin-left: auto;
        margin-right: auto;
    }

    .title {
        display: flex;
        justify-content: center;
        margin-bottom: 13px;
        padding-top: 8px;

        &.mb20 {
            margin-bottom: 30px;
        }

        .htitleimg {
            width: 300px;
        }
    }

    .blocka {
        &.flex1 {
            flex: 1;
            min-height: 0;
        }

        .logoorInput {
            position: relative;
            width: 190px;
            height: 125px;
            background: #fff5ea;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;

            .logo,
            .inputText {
                position: relative;
                z-index: 2;
            }

            .logo {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 12px;

                img {
                    width: 35px;
                }
            }

            .inputText {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-left: auto;
                margin-right: auto;
                width: 100px;
                border: 2px solid #cccccc;
                background: #fff;
                border-radius: 3px;
                height: 14px;
                padding: 0 3px;

                .remove {
                    width: 8px;
                    cursor: pointer;
                    user-select: none;
                }

                .input {
                    flex: 1;
                    min-width: 0;
                    border: 0;
                    display: flex;
                    align-items: center;
                    user-select: none;
                    background: none;
                    height: 100%;
                    font-size: 6px;
                    outline: none;
                    color: #4f4f4f;
                }
            }
        }

        .keyword {
            width: 100%;
            position: absolute;
            background: #f0f0f0;
            left: 0;
            top: 120%;
            border-radius: 3px;
            padding: 5px 3px;

            ul {
                display: flex;
                flex-flow: wrap;

                li {
                    width: (100%/3);
                    padding: 0 3px;

                    &.active {
                        .child {
                            background: #c4e6ff;
                            border: 2px solid #63beff;
                            color: #63beff;
                        }
                    }

                    .child {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 10px;
                        cursor: pointer;
                        background: #ffffff;
                        justify-content: center;
                        font-size: 5px;
                        color: #5c5c5c;
                        border-radius: 3px;
                        box-sizing: border-box;
                        margin-bottom: 3px;
                        border: 2px solid #e8e8e8;
                        overflow: hidden;

                        span {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }

                        .backSpace {
                            width: 10px;
                        }

                        .confirm {
                            width: 100%;
                            height: 100%;
                            color: #fff;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            background: linear-gradient(to right, #60c4ff, #8277fe);
                        }
                    }
                }
            }
        }

        border-radius:5px;
    }

    .bl {
        display: flex;
        justify-content: center;
        width: 100%;
        margin: 10px 0;

    }

    :deep(.bl) {
        font-size: 10px;
    }

    .dialog {
        .van-dialog__footer {
            font-size: 8px;
        }
    }

    :deep(.van-dialog__header) {
        font-size: 10px;
        padding-top: 10px;
    }

    :deep(.van-dialog__footer) {
        .van-button {
            font-size: 8px;
        }

        .van-dialog__cancel,
        .van-dialog__confirm {
            height: 30px;
        }
    }

    :deep(.van-radio--horizontal) {
        margin-right: 10px;
        margin-left: 10px;
    }
}
</style>
