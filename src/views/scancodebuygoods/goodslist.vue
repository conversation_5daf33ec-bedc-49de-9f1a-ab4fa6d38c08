<template>
  <div>
    <Shopname
      v-if="data.showHead"
      bg-color="#fff"
      :show-back="true"
      content="终端扫码购机专区"
    />
    <div
      class="minheight"
      :class="{
        'padding-top-34': data.showHead,
      }"
    >
      <CarouselA :items="data.imglist" carousel-class="circle-right" page-name="smgj" />
      <ul v-if="data.allValidCataList" :class="['sctionlist', data.returnNavClass]">
        <template v-for="(item, key) in data.allValidCataList">
          <li
            v-if="item.isShow === '1'"
            :key="key"
            :class="{
              active: item.flag === data.section,
            }"
            @click="changeSection(item.flag)"
          >
            <span> {{ item.cataName }} </span>
          </li>
        </template>
      </ul>
      <ul
        v-if="data.allValidCataList && data.allValidCataList.length > 0"
        class="sortlist"
      >
        <li
          v-for="(item, key) in data.sortlist"
          :key="key"
          :class="{ active: item.sortedFlag === data.sortedFlag }"
          @click="changeFlag(item.sortedFlag)"
        >
          {{ item.name }}
        </li>
      </ul>
      <GoodsList
        v-if="data.allValidCataList && data.allValidCataList.length > 0"
        :items="data.goodslist ? data.goodslist : []"
        component-code="list"
        tpl-id="2"
        :page-num="data.pageNum"
        :loading="data.loading"
        :finished="data.finished"
        :onload="getGoodsList"
        :show-share="data.showShare"
        bg-color="#f6f7f9"
        :shopshare="shareConfig"
      />
      <div
        v-if="data.allValidCataList && data.allValidCataList.length === 0"
        class="notUrl"
      >
        本省暂未在中移云店开通扫码购机活动
      </div>
    </div>
  </div>
</template>
<script setup>
import UA from "@/utils/ua"
import shopAPI from "@/api/shop"
import Shopname from "@/components/index/headercon.vue"
import GoodsList from "@/components/scancodegoodslist/goodslist.vue"
import CarouselA from "@/components/index/carouselA"
import loginUtils from "@/utils/login"
import insertCode from "@/utils/insertCode"
import ScancodeApi from "@/api/scancodebuygoods"
import shareUtilApi from "@/utils/share"
import { getImgUrl } from "@/utils/utils"
import {reactive,ref,onMounted,computed,getCurrentInstance} from "vue"
import { Toast } from "vant"


const shareConfig = reactive({
  title: "中国移动扫码购机专区",
  url: window.location.href,
  desc: window.location.href,
  imgUrl: "https://img1.shop.10086.cn/goods/tc2txqenygq3nkry_940x7200"
})

const data = reactive({
  shopId: null,
  isLogined: false,
  section: 0,
  sortedFlag: 1,
  allValidCataList: null,
  imglist: null,
  pageNum: 1,
  loading: false,
  finished: false,
  goodslist: [],
  flag: false,
  pageNo: 1,
  showShare: false,
  pageInfo: {},
  showHead: false,
  sortlist:[
    {
      name: "综合推荐",
      sortedFlag: 1,
    },
    {
      name: "价格升序",
      sortedFlag: 2,
    },
    {
      name: "价格降序",
      sortedFlag: 3,
    },
  ],
  proxy:null
})

const returnNavClass = computed(()=>{
  if (data.allValidCataList && data.allValidCataList.length == 1) {
    return "first"
  } else if (data.allValidCataList && data.allValidCataList.length == 2) {
    return "two"
  } else {
    return ""
  }
})

const getGoodsList = () => {
  data.flag = true
  data.loading = true
  ScancodeApi.getGoodsList({
    section: data.section,
    shopId: data.shopId,
    sortedFlag: data.sortedFlag,
    isPaging: 0,
    pageNo: data.pageNo,
    pageSize: 15,
  }).then((res) => {
    data.flag = false
    if (!res.code) {
      data.loading = false
      data.allValidCataList = res.data.allValidCataList
      if (
        res.data.allValidCataList &&
        res.data.allValidCataList.length > 0
      ) {
        data.pageInfo.provinceId = res.data.allValidCataList[0].province
        if(data.proxy){
          data.proxy.$store.commit("SET_PAGEINFO", data.pageInfo)
        }
      } else {
        return false
      }
      let goodslist = res.data.productListBySection
      goodslist.forEach((item) => {
        item.price = item.priceMin
        item.subTitle = item.goodsTitleSub
        item.goodsSource = item.goodsFrom
        item.goodsLink = item.shortShareURL
        item.goodsName = item.goodsTitle
        item.imgUrl = getImgUrl(item.picture)
      })
      if (data.pageNo === 1) {
        data.goodslist = []
      }
      data.section = res.data.flag
      data.goodslist = data.goodslist.concat(goodslist)
      if (res.data.totalPages > res.data.pageNo) {
        data.pageNo++
      } else {
        data.finished = true
      }
    } else {
      Toast(res.message)
    }
  })
}

const logined = (res) => {
  data.isLogined = res && res.UserName > ""
  if (data.isLogined && res.shopId === data.shopId) {
    data.showShare = true
  } else {
    data.showShare = false
  }
  if (data.shopId) {
    data.finished = false
    queryShopStaTus()
    getGoodsList()
    getImglist()
  } else {
    if(data.proxy){
      data.proxy.$router.push({
        name: "nearby",
      })
    }
  }
}

const queryShopStaTus = () => {
  shopAPI.getShopStatus({ shopId: data.shopId }).then((r) => {
    data.shopStatus = r.data.status
    if (r.data.status !== 3) {
      Toast(r.message)
    } else {
      const { address, shopShortName, status } = r.data
      data.pageInfo = {
        address,
        status,
        shopId: data.shopId,
        shortName: shopShortName,
        provinceId: 100,
      }
      if(data.proxy){
        data.proxy.$store.commit("SET_PAGEINFO", data.pageInfo)
      }
    }
  })
}

const getImglist = () => {
  ScancodeApi.getRotationList({
    shopId: data.shopId,
  }).then((res) => {
    if (!res.code) {
      let imglist =
        res.data && res.data.length > 0
          ? res.data
          : [
            {
              goodsUrl: "",
              rotationPicture:
                  "https://img1.shop.10086.cn/goods/tc2txqenygq3nkry_940x7200",
            },
          ]
      imglist.forEach((item) => {
        item.imgSrc = getImgUrl(item.rotationPicture, "material")
        item.adLink = item.goodsUrl
      })
      data.imglist = imglist
      shareConfig.imgUrl = data.imglist[0].imgSrc.indexOf("http")=== -1 ? "https:"+data.imglist[0].imgSrc : data.imglist[0].imgSrc
      share()
    }
  })
}

const emptyGoods = ()=>{
  data.pageNo = 1
  data.finished = false
  data.goodslist = []
}

const changeFlag = (sortedFlag) => {
  if (data.flag) {
    Toast("您点的太快啦~")
    return false
  }
  data.sortedFlag = sortedFlag
  emptyGoods()
  getGoodsList()
}
const changeSection = (section) => {
  if (data.flag) {
    Toast("您点的太快啦~")
    return false
  }
  data.section = section
  data.sortedFlag = 1
  emptyGoods()
  getGoodsList()
}

const share = () => {
  if (UA.isApp) {
    shareUtilApi.appShare(shareConfig)
  } else {
    shareUtilApi.changeWxShareConfig(shareConfig)
  }
}

onMounted(()=>{
  data.showHead =
      !UA.isWechat &&
      !UA.isWechatWork &&
      !UA.isApp &&
      !UA.isIosQQ &&
      !UA.isAndroidQQ
  const url = new URL(location.href)
  data.shopId = url.searchParams.get("shopId")
  const getVueInstance = getCurrentInstance()
  data.proxy = getVueInstance ? getVueInstance.proxy : null
  if(data.proxy){
    data.proxy.$store.commit("SET_SHOPID", data.shopId)
  }
  loginUtils.login(false, false, logined, false, false, "", "0")
  insertCode("yd_smgj_"+data.shopId+"_index")
})

</script>
<style lang="scss" scoped>
  $bgcolor: #f6f7f9;
  .sctionlist {
    display: flex;
    // margin: 10px 11.3px;
    padding: 12.5px;

    &.first {
      li {
        width: 100%;
        display: flex;
        align-items: flex-start;
        span {
          margin-left: 9px;
          padding-left: 17px;
          padding-right: 17px;
          letter-spacing: 2px;
          padding-top: 5px;
          padding-bottom: 5px;
        }
        &::after {
          content: none !important;
        }
        &:nth-of-type(1) {
          background-image: url(~@/assets/scancode/tab1-1.png);
        }
      }
    }
    &.two {
      li {
        flex: 1;
        min-width: 0;
        display: flex;
        align-items: flex-start;
        &.active {
          &::after {
            left: 85%;
            border: 7px solid $bgcolor;
            bottom: -15px;
          }
        }
        span {
          margin-left: 9px;
          padding-left: 17px;
          padding-right: 17px;
          letter-spacing: 2px;
          padding-top: 5px;
          padding-bottom: 5px;
        }
        &:nth-of-type(1) {
          background-image: url(~@/assets/scancode/tab2-1.png);
          span {
            background: #c68c5d;
          }
        }
        &:nth-of-type(2) {
          background-image: url(~@/assets/scancode/tab2-2.png);
          span {
            background: #e26666;
          }
        }
      }
    }

    li {
      width: 110px;
      height: 70px;
      text-align: center;
      position: relative;
      padding-top: 9px;
      span {
        display: inline-block;
        padding: 4px 6px;
        border-radius: 20px;
        color: #fff;
        font-size: 12px;
        font-weight: bold;
        vertical-align: text-top;
      }
      &.active:after {
        content: "";
        display: block;
        width: 0;
        border: 6px solid $bgcolor;
        border-bottom-color: transparent;
        border-right-color: transparent;
        transform: rotate(45deg) translate(-8px, 0px);
        position: absolute;
        bottom: -13px;
        left: 50%;
      }
      &:first-child {
        background: url(~@/assets/scancode/tab-1.png) no-repeat;
        background-size: contain;
        span {
          background: #59beff;
        }
      }
      &:nth-child(2) {
        background: url(~@/assets/scancode/tab-2.png) no-repeat center;
        background-size: contain;

        span {
          background: #ee5e68;
        }
        &:not(:last-child) {
          flex: 1;
        }
        &:last-child {
          margin-left: 11px;
        }
      }
      &:nth-child(3) {
        background: url(~@/assets/scancode/tab-3.png) no-repeat;
        background-size: contain;
        span {
          background: #cd8d59;
        }
      }
    }
  }
  .sortlist {
    display: flex;
    margin-left: 15px;
    height: 36px;
    // border:1px solid #ccc;
    li {
      font-size: 12px;
      color: #666;
      line-height: 50px;
      width: 80px;
      @mixin base-icon {
        content: "";
        display: inline-block;
        width: 14px;
        height: 14px;
        transform: translate(-3px, 3px);
      }
      &:nth-child(2):after {
        @include base-icon();
        background: url(~@/assets/scancode/icon1_shen.png) no-repeat;
        background-size: contain;
      }
      &:nth-child(3):after {
        @include base-icon();
        background: url(~@/assets/scancode/icon1_jiang.png) no-repeat;
        background-size: contain;
      }
      &.active {
        font-size: 13px;
        color: #333;
        font-weight: bold;
        &:nth-child(2):after {
          @include base-icon();
          background: url(~@/assets/scancode/icon2_shen.png) no-repeat;
          background-size: contain;
          transform: translate(-6px, 3px);
        }
        &:nth-child(3):after {
          @include base-icon();
          background: url(~@/assets/scancode/icon2_jiang.png) no-repeat;
          background-size: contain;
          transform: translate(-6px, 3px);
        }
      }
    }
  }
  .notUrl {
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-Regular;
    text-align: left;
    color: #333333;
    line-height: 24px;
    width: 100%;
    text-align: center;
    padding-top: 100px;
  }
  .minheight {
    min-height: calc(100vh - 80px);
    overflow-y: scroll;
    background: $bgcolor;
  }
  .padding-top-34 {
    padding-top: 34px;
  }
</style>
