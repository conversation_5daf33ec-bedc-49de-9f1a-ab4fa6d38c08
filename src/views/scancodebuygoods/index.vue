<template>
  <div>
    <Shopname
      v-if="data.showHead"
      content="扫码购机管理"
      bg-color="#fff"
      :show-back="true"
    />
    <div
      v-if="data.isMerchantConfig === 0 && data.goodsListUrl"
      class="minheight"
      :class="{ 'padding-top-34': data.showHead }"
    >
      <div class="goodslisturl">
        <dl>扫码购机地址：</dl>
        <dd>
          {{ data.goodsListUrl }}
        </dd>
        <a
          v-clipboard:copy="data.goodsListUrl"
          v-clipboard:success="onCopy"
          v-clipboard:error="onError"
          class="iconfont icon-copy"
        ></a>
      </div>
      <div class="scanCodeUrl">
        <h1>扫码购机推广二维码</h1>
        <div ref="scanCodeUrl" class="code"></div>
      </div>
      <a
        :href="data.goodsListUrl"
        class="goodslisturlA"
      >扫码购机目录页<Icon
        name="arrow"
      /></a>
    </div>
    <div
      v-if="data.isMerchantConfig === 1"
      class="notUrl minheight"
      :class="{ 'padding-top-34': data.showHead }"
    >
      本省暂未在中移云店开通扫码购机活动
    </div>
    <Footers :list="iconList" :active="1" :user="data.user" />
  </div>
</template>
<script setup>
import UA from '@/utils/ua'
import Vue,{ref,reactive,getCurrentInstance,onMounted,nextTick} from 'vue'
import Footers from '@/components/myfooter'
import QRCode from 'qrcodejs2'
import { Icon,Toast } from 'vant'
import Shopname from '@/components/index/headercon.vue'
import loginUtils from '@/utils/login'
import ScancodeApi from '@/api/scancodebuygoods'
import VueClipboard from 'vue-clipboard2'
Vue.use(VueClipboard)
const iconList = reactive({
  shopindex: {
    inactive: require('@/assets/my/other.png'),
    active: require('@/assets/my/others-active.png'),
    title: '店铺首页',
    key: 'shopindex',
    links: '',
    isactive: false,
    showLink: true,
  },
  manage: {
    inactive: require('@/assets/my/management.png'),
    active: require('@/assets/my/management-active.png'),
    title: '店铺管理',
    key: 'manage',
    links: '',
    isactive: false,
    showLink: false,
  },
})

const data = reactive({
  goodsListUrl: null,
  scanCode: null,
  isLogined: false,
  isMerchantConfig: null,
  shopId: null,
  user: null,
  showHead: false,
  proxy:null
})

const logined = (res) => {
  data.user = res
  data.isLogined = res && res.UserName > ''
  if (data.isLogined && data.shopId === res.shopId) {
    getEntryurl()
    iconList.manage.showLink = true
    iconList.manage.links = '/hd/xskd/index.html?shopId=' + data.shopId
  } else {
    iconList.manage.showLink = false
    if(data.proxy){
      data.proxy.$router.push({
        name: 'nearby',
      })
    }
  }
}
const getEntryurl = ()=> {
  ScancodeApi.getEntryurl({
    entryURL: 'yundian/goodslist/index.html',
  }).then((res) => {
    if (!res.code) {
      data.goodsListUrl = res.data.merchantActivityShortURL
      data.isMerchantConfig = res.data.isMerchantConfig //商户是否配置了扫码购机活动页面。 0: 已配置；1:未配置
      if (data.isMerchantConfig === 0 && data.goodsListUrl) {
        nextTick(() => {
          creatQrCode()
        })
      }
    } else {
      data.isMerchantConfig = 1
    }
  })
}
const onCopy = (e) => {
  Toast('复制成功')
}
const onError = (e) => {
  Toast('复制失败')
}
const scanCodeUrl = ref(null)
const creatQrCode = () => {
  //防止dialog首次加载不出二维码 在dialog上加@opened 回调
  data.scanCode = new QRCode(scanCodeUrl.value, {
    text: data.goodsListUrl,
    width: 150,
    height: 150,
    colorDark: '#000000',
    colorLight: '#ffffff',
  })
}
onMounted(()=>{
  data.showHead =
      !UA.isWechat &&
      !UA.isWechatWork &&
      !UA.isApp &&
      !UA.isIosQQ &&
      !UA.isAndroidQQ
  const url = new URL(location.href)
  const shopId = (data.shopId = url.searchParams.get('shopId'))
  if (!shopId) {
    Toast('没有shopId')
  }
  const getVueInstance = getCurrentInstance()
  data.proxy = getVueInstance ? getVueInstance.proxy : null
  if(data.proxy){
    data.proxy.$store.commit('SET_SHOPID', shopId)
  }
  
  loginUtils.login(true, true, logined, false, false, '', null, 5)
})
</script>
<style lang="scss" scoped>
  .minheight {
    background: #f6f6f6;
    height: calc(100vh - 150px);
  }
  .padding-top-34 {
    padding-top: 34px;
  }
  @mixin base-font() {
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-Regular;
    text-align: left;
    color: #333333;
    line-height: 24px;
  }
  .notUrl {
    @include base-font();
    width: 100%;
    text-align: center;
    padding-top: 200px;
  }
  .goodslisturl {
    @include base-font();
    font-weight: bold;
    // height:74px;
    width: 350px;
    margin: 20px auto;
    background: #fff;
    border-radius: 8px;
    padding: 15px 13px;
    position: relative;
    dd {
      white-space: normal;
      word-wrap: break-word;
      word-break: break-all;
      width: 300px;
    }
    .icon-copy {
      position: absolute;
      right: 13px;
      top: 40px;
      color: #1d80ef;
      font-size: 22px;
    }
  }
  .scanCodeUrl {
    width: 200px;
    margin: 30px auto 80px;
    font-size: 15px;
    h1 {
      margin-bottom: 20px;
      text-align: center;
    }
    .code {
      width: 150px;
      height: 150px;
      margin: 0 auto;
      :deep(img) {
        width: 150px !important;
      }
    }
  }
  .goodslisturlA {
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: bold;
    text-align: left;
    color: #1d80ef;
    line-height: 26px;
    margin-left: 15px;
    border-bottom: 2px solid #1d80ef;
    position: relative;
  }
  .van-icon-arrow {
    font-weight: bold;
    position: absolute;
    right: -13px;
    top: 1px;
  }
</style>
