export default [
  {
    path: "/ydmanage/index.html",
    name: "ydManage",
    component: resolve => require(["./index.vue"], resolve),
    meta: {
      title: "管理",
      login:true,
      role:[1,2]
    }
  },
  {
    path: '/ydmanage/fanlist.html',
    name: 'fanList',
    component: resolve => require(['./fanlist/index.vue'], resolve),
    meta: {
      title: '店铺粉丝列表',
      login:true,
      role:[1,2]
    }
  },
  {
    path: '/ydmanage/sharelist.html',
    name: 'shareList',
    component: resolve => require(['./sharelist/index.vue'], resolve),
    meta: {
      title: '分享任务列表',
      login:true,
      role:[1,2]
    }
  }
]
