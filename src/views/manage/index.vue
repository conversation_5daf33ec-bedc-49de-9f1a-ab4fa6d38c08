<template>
  <div class="manage">
    <div class="manage-content">
      <div class="main-header clearfix">
        <div class="idx-userinfo clearfix">
          <div class="pic">
            <img src="@/assets/manage/idx_userpic.png" alt="" />
          </div>
          <div v-if="data.logUser" class="info">
            <span class="tel">{{ data.logUser.UserName }}</span>
            <p>
              <span class="shopname">{{ data.logUser.shopShortName }}</span>
              <a v-if="data.storeNum > 1" href="javascript:void(0);" class="icon-switch" @click="changeShop()">
                <img src="@/assets/manage/switch.png" alt="" />
                <span class="">切换店铺</span>
              </a>
            </p>
          </div>
          <div class="right">
            <a href="javascript:void(0);" class="icon-exit" @click="goOut()">
              <img src="@/assets/manage/exit.png" alt="" />
              <span class="">退出</span>
            </a>
          </div>
        </div>
        <div class="fans">
          <dl class="fans-amount" @click="fanslist">
            <dt>{{ data.fansTotalNum }}</dt>
            <dd>粉丝数</dd>
          </dl>
          <span></span>
          <dl class="fans-qrcode" @click="showQrCode">
            <dt>
              <span class="iconfont icon-qrcode"></span>
            </dt>
            <dd>粉丝邀请</dd>
          </dl>
        </div>
      </div>
      <!--<{*店铺管理*}>-->
      <template v-for="(manageTpl,index) in data.shopManageData">
        <div v-if="manageTpl && manageTpl.length>0" :key="index" class="idx-icons clearfix">
          <h4 class="idx-tt">
            {{ titleList[index].title }}
          </h4>
          <template v-for="item in manageTpl">
            <div
              v-if="!(data.logUser && data.logUser.RuleId == 1 && blacks.includes(item.key))"
              :key="item.key"
              class="iconCon"
              @click="clickMange(item)"
            >
              <img :src="shopManage[item.key] ? shopManage[item.key].url : getImgUrl(item.imgSrc)" alt="" />
              <span>{{ shopManage[item.key] ? shopManage[item.key].title : item.title }}</span>
            </div>
          </template>
        </div>
      </template>
    </div>
    <van-popup v-model="data.show" :style="{ borderRadius: '9px' }">
      <div class="out">
        <div class="point">
          确认退出
        </div>
        <div class="select">
          <p class="cancle" @click="cancle()">
            取消
          </p>
          <p @click="sure()">
            确定
          </p>
        </div>
      </div>
    </van-popup>
    <FansShare ref="fansShareRef" :showshare="showshare" :share-info="shopInfo" @close="showshare = false" @open="showshare = true" />
    <Footers :user-info="data.logUser" isactive="manage" />
    <van-dialog
      v-model="data.showPoster"
      :show-cancel-button="false"
      :show-confirm-button="false"
      class="dialog-container"
    >
      <PosterSwiper v-if="shopId" :shop-id="shopId" :poster-rule-info-list="data.posterRuleInfoList" />
      <a href="javascript:void(0)" class="dialog-btn-exit" @click="closeSubMsgDialog"></a>
    </van-dialog>

  </div>
</template>
<script setup>
//组件
import Footers from "@/views/my/components/myfooter.vue"
import FansShare from "@/components/home/<USER>"
import PosterSwiper from "./poster/index.vue"
import { getToken, removeLocalStorage } from "@/utils/login/localStorage.js"
import { Popup, Toast, Dialog } from "vant"
import Vue, { reactive, ref, getCurrentInstance, onBeforeMount } from "vue"
import "vant/lib/index.css"
import { getUrl, delCookie, editCookie, getImgUrl } from "@/utils/utils.js"
import loginApi from "@/api/login"
import cmApi from "@/api/shop.js"
import shopManageApi from "@/api/netshop.js"
import { shopManage, BLACK, SHOPSTATUS ,OPENSTATUS,DIRECTLYENTER } from "./iconlist.js"
import { toHome } from "@/utils/wxMiniProgram"
import UA from "@/utils/ua"
Vue.use(Dialog)
Vue.use(Popup)
const data = reactive({
  logUser: null,
  myuser: null,
  status: '',
  shopAttribute:'',
  statusMge:'',
  storeNum:0,
  wcsb:0,
  statusNc:0,
  fansProduct:0,
  fansTotalNum:0,
  isLogined: false,
  show: false,
  shopManageData:{},
  isWeChatMiniApp:false,
  showPoster: false,
  posterRuleInfoList:[]
})

const blacks = BLACK
function arrayToObj(arraylist,key,value){
  let Obj = {}
  arraylist.forEach(element => {
    Obj[element[key]] = element[value]
  })
  return Obj
}
const shopStatusData = arrayToObj(SHOPSTATUS,"status","errorMsg")
onBeforeMount(async()=>{
  loginApi.getUserInfo(getToken(),null,5).then((res)=>{
    if (res.code == 0) {
      logined(res.data)
    } else if (res.code == 1) {
      removeLocalStorage()
      return null
    }
  })
  data.isWeChatMiniApp = await UA.isWeChatMiniApp()
  delCookie("ChannelControl")
  editCookie("uhide","1")
})

const titleList = {
  manageList:{
    title:'店铺管理',
  },
  toolList:{
    title:'我的工具',
  },
  customList:{
    title:'融合管理',
  }
}

/** 获取店铺状态 */
function getShopIconConfig() {
  return shopManageApi.getShopIconConfig({ shopId:shopId.value }).then((r) => {
    if(r.code==RESULTCODE.SUCCESS && r.data){
      data.shopManageData = r.data
      Object.keys(data.shopManageData).forEach((manageItem)=>{
        let arr = []
        data.shopManageData[manageItem].forEach((item)=>{
          if(item.key=="openaShop"){//申请开店
            if(OPENSTATUS.includes(parseInt(data.status))){
              arr.push(item)
            }
          }else if(item.key=='shopStyle'){ //店铺配置
            if(data.logUser.merchantId!='1000073'&&data.statusNc==1){
              arr.push(item)
            }
          }else if(item.key=='statisticalQuery'){//营销查询
            if(data.logUser.merchantId=='1000073'){
              arr.push(item)
            }
          }else{
            arr.push(item)
          }
        })
        data.shopManageData[manageItem] = arr
      })
    }else{
      data.statusMge=r.message
    }
  })
}

const shopId = ref(null)
async function logined(res) {
  if(!res){
    return false
  }
  data.logUser = res
  data.isLogined = res && res.UserName > ""
  if(res.shopId == null){
    //未开店
    data.status = 0
    return false
  }
  shopId.value = res.shopId
  await getQueryshopidandstatusNc()
  getShopIconConfig()
  Promise.all([getShopdetailFromO2o(),getStoreList(),getShopFansTotal(),getCmData(),getQrCode()])
  getAdList()
}
// 退出登录
function goOut() {
  data.show = true
}
// 退出登录取消按钮
function cancle() {
  data.show = false
}
// 退出登录确认按钮
function sure() {
  data.show = false
  getLogout()
}
//退出登录
function getLogout() {
  loginApi.logout({}).then((res) => {
    if (res.code == 0) {
      window.localStorage.removeItem("yundian")
      location.reload()
    }else{
      Toast(res.message)
    }
  })
}
// 切换店铺
function changeShop() {
  location.href="/hd/xskd/storelist.html"
}
const RESULTCODE={
  SUCCESS:0
}
/** 获取店铺状态 */
function getQueryshopidandstatusNc() {
  return shopManageApi.getQueryshopidandstatusNc({ shopId:shopId.value}).then((r) => {
    if(r.code==RESULTCODE.SUCCESS && r.data){
      data.status=r.data.status
      data.shopAttribute=r.data.shopAttribute
      if(r.data.homePageSwitch!=2&&r.data.merchantCtrl!=2){
        data.statusNc=1
      }
      return r.data
    }else{
      data.statusMge=r.message
    }
  })
}
const shopInfo = ref({})
/** 获取支付链接 */
function getShopdetailFromO2o() {
  return shopManageApi.getShopdetailFromO2o({ shopId:shopId.value }).then((r) => {
    if(r.code==RESULTCODE.SUCCESS && r.data){
      shopInfo.value =  r.data
      shopManage['jinkePay'].link = shopManage['jinkePay'].link + r.data.uniChannelIdEncry
    }else{
      Toast(r.message)
    }
  })
}
/** 获取店铺列表接口 */
function getStoreList() {
  return cmApi.getStoreList().then((r) => {
    if(r.code==RESULTCODE.SUCCESS && r.data){
      data.storeNum=r.data.length
    }
  })
}
/** 获取poster列表接口 */
function getAdList() {
  shopManageApi.getShopPosterRuleInfo({ shopId:shopId.value}).then((r) => {
    if(r.code==RESULTCODE.SUCCESS && r.data){
      // console.log(r, 'posterRuleInfoList')
      data.posterRuleInfoList = r.data.posterRuleInfoList.map(item => {
        return {
          ...item,
          slotname : item.posterId
        }
      })
      // console.log(data.posterRuleInfoList, 'posterRuleInfoList')
      if (data.posterRuleInfoList.length > 0) {
        data.showPoster = true
      }
    }
  })
}
/** 关闭海报弹窗 */
function closeSubMsgDialog(){
  data.showPoster = false
}
// 问查算比
function getCmData(){
  return cmApi.getCmData({
    shopId: shopId.value,
    cmCodeList:["wenchasuanbi"]
  }).then((res) => {
    if (res.code == 0 && res.data) {
      if(res.data.wenchasuanbi){
        let cmdata = res.data.wenchasuanbi.data[1]
        if(cmdata.tabTitle!=null&&cmdata.tabTitle!=''){
          shopManage["askedCheck"].title =cmdata.tabTitle
        }
        shopManage["askedCheck"].link = getUrl("/hd/qd.html?url=/hd/wcsb1/index.html&key="+res.data.key+"&WT.ac_id=yd_h5_me_wcsb&shopId="+shopId.value)
        // data.wcsb=1
        return res.data
      }
    }
  })
}
// 获取粉丝数据
function getShopFansTotal(){
  return shopManageApi.getShopFansTotal({ shopId:shopId.value,pageNum:1,pageSize:10 }).then((r) => {
    if(r.code==RESULTCODE.SUCCESS && r.data){
      data.fansTotalNum=r.data.fansTotalNum
    }
  })
}
// 店铺管理事件跳转
function clickMange(item){
  if(item.type==2){
    //自定义配置
    jumpLink(item)
    return false
  }
  let iconName = item.key
  //直接进入的模块 (直接跳转，不查店铺状态)
  if( DIRECTLYENTER.includes(iconName)){
    jumpLink(shopManage[iconName])
  }
  // 没有开店只能点击申请开店
  if(data.status==0 && iconName!="openaShop"){
    Toast('您还没有申请开店，请先申请线上开店')
    return false
  }
  // status 0,提示，1跳转，2可查看不可修改，3店长可查看修改，店员可查看，4可查看不可修改，5所有信息都可修改
  if(iconName == "shopInfo"){
    jumpLink(shopManage[iconName])
    return false
  }
  checkRole(iconName)
}
// 根据店铺状态判断是否跳转
function checkRole(iconName){
  // status 0,未开店，1未填写店铺信息，2审核中，3已开店，4已关闭，5未通过审核
  switch (data.status) {
  case 0:
  case 1:
  case 2:
  case 5:
    Toast(shopStatusData[data.status])
    break
  case 4:
    //已关闭 店铺数据和云海报可查看
    if(iconName=='shopData'||iconName=='posters'){
      jumpLink(shopManage[iconName])
    }else{
      Toast(shopStatusData[data.status])
    }
    break
  default:
    //开店状态
    if (iconName == "myShop") {
      if(data.shopAttribute==2){
        Toast('热线渠道店铺，不可查看')
        return false
      }else{
        // 跳转微信首页
        toHome(shopId.value)
        jumpLink(shopManage[iconName])
      }
      return false
    }
    if (iconName == "dailySharing" && data.isWeChatMiniApp) {
      // 跳转每日分享
      let pageUrl = `/subPackages/sharelist/sharelist?shopId=${shopId.value}`
      window.wx.miniProgram.navigateTo({url: pageUrl})
      return false
    }
    jumpLink(shopManage[iconName])
    break
  }
}
function formaturl(str,data){
  return str.replace(/{([^{}]+)}/gi,function(){
    return data[arguments[1]]||''
  })
}
const getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null
function jumpLink(item){
  item.link = formaturl(item.link,{shopId:shopId.value})
  //route yundian是云店链接，hd是商城，outside是第三方 getUrl("/hd/xskd/index.html?cmToken={sourceid:12006}&shopId=" + myFooter.urlInfo.shopId)
  switch(item.route){
  case "yundian":
    proxy.$router.push({path:item.link})
    break
  case "hd":
    // let link =
    location.href = item.link +
      (item.link.includes('?') ? '&cmToken={sourceid:12006}' : '?cmToken={sourceid:12006}')
    break
  default:
    location.href = item.link
  }
}

// 获取二维码地址
function getQrCode(){
  return shopManageApi.getQrCode({shopId:shopId.value}).then(res=>{
    if(!res.code){
      fansShareData.url = res.data.qrCodeUrl
    }
  })
}
function fanslist(){
  jumpLink({
    link:"/ydmanage/fanlist.html?shopId={shopId}",
    route:"yundian"
  })
}
const defaultImg =  require("@/assets/index_img/shop_share_img.png")//默认分享图片
// 分享数据
const fansShareData = {
  url: location.href,
  imgUrl: 'https://img0.shop.10086.cn/favicon.png__100.png',
  dcsId: 'yd_sharestore_share',
  desc: "线上办移动业务，便捷高效，专享优惠邀你来体验",
  title: "一级云店",
  bgUrl:defaultImg
}
const showshare = ref(false)
const fansShareRef = ref(null)
function showQrCode(){
  shopInfo.value = {
    ...shopInfo.value,
    type:"shop",
    fansShareData:fansShareData
  }
  fansShareData.unifiedChannelId = shopInfo.value.uniChannelId
  // { imgUrl, title, desc, url = location.href.split("#")[0] ,unifiedChannelId}
  fansShareRef.value.changeShareConfig(fansShareData)
}
</script>
<style lang="scss" scoped>
  .clearfix:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
  .manage {
    background: #f6f6f6;
    position: relative;
    min-height: calc(100vh - 80px);
    .manage-content {
      padding-bottom: 20px;
      .main-header{
        width: 100%;
        padding-bottom: 20px;
        background: linear-gradient(279deg, #7e88f7 5%, #78a9f8 93%);
        .idx-userinfo{
          width: 92%;
          margin: 0 auto;
          padding-top: 30px;
          .pic{
            float: left;
            img{
              width: 50px;
              height: 50px;
            }
          }
          .info{
            width: 290px;
            padding: 6px 0 0 12px;
            float: left;
            color: #fff;
            text-align: left;
            .tel{
              font-size: 16px;
              height: 20px;
              display: block;
              margin-bottom: 2px;
            }
            p{
              font-size: 13px;
              height: 20px;
              line-height: 20px;
              .shopname{
                margin-right: 20px;
              }
              a{
                display: inline-block;
                color: #fff;
                background: #5C70B9;
                padding: 0 10px;
                border-radius: 10px;
                img{
                  display: inline-block;
                  height: 20px;
                  width: auto;
                  padding: 3px 0;
                  margin-right: 4px;
                  float: left;
                }
              }
            }
          }
          .right{
            position: absolute;
            right: 10px;
            top: 15px;
            width: 50px;
            height: 20px;
            line-height: 20px;
            font-size: 14px;
            a{
              color: #fff;
              img{
                height: 20px;
                float: left;
              }
            }
          }
        }
        .fans{
          display: flex;
          align-items: center;
          justify-content: center;
          padding-top: 20px;
          color: #FFFFFF;
          dl{
            flex: 1;
            font-size: 13px;
            dt,dd{
              text-align: center;
            }
            dt{
              height: 40px;
              font-weight: 650;
              font-size: 28px;
            }
            img{
              width: 32px;
              height: 32px;
            }
          }
          span{
            width: 1px;
            height: 30px;
            background: #fff;
            opacity: .6;
          }
        }
      }
      .idx-icons{
        width: 92%;
        margin: 20px auto;
        min-height: 100px;
        opacity: 1;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 10px 40px -4px rgba(0, 0, 0, .1);
        padding-bottom: 20px;
        .idx-tt {
          font-size: 16px;
          height: 40px;
          line-height: 40px;
          padding: 6px 0 0 20px;
          font-weight: 700;
          color: #000;
          opacity: .8;
          text-align: left;
        }
        .iconCon{
          display: inline-block;
          text-align: center;
          width: 25%;
          margin-top: 12px;
          font-size: 14px;
          line-height: 30px;
          color: #333;
          *{
            display: block;
            margin: 0 auto;
          }
          img{
            width: 30px;
            height: 30px;
          }
        }
      }
    }
    .out {
      width: 272px;
      height: 108px;
      background: #ffffff;
      .point {
        width: 100%;
        height: 66px;
        border-bottom: 1px solid #f2f2f2;
        text-align: center;
        line-height: 66px;
        font-size: 15px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: #000000;
      }
      .select {
        width: 100%;
        height: 40px;
        display: flex;
        p {
          width: 135px;
          height: 40px;
          text-align: center;
          line-height: 40px;
          font-size: 13px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: #5b8edc;
        }
        .cancle {
          border-right: 1px solid #f2f2f2;
        }
      }
    }
  }
  .icon-qrcode{
    font-size: 34px;
    background: transparent !important;
    opacity: 1!important;
  }
  .dialog-container{
    background: transparent;
    width:100%;
  }
  .dialog-btn-exit{
    display: block;
    margin: 0 auto;
    width: 30px;
    height: 44px;
    background: url("~@/assets/home/<USER>") no-repeat 0 0;
    background-size: 100% auto;
  }
</style>
