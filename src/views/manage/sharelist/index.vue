<template>
  <div class="sharelist clearfix">
    <div class="share-title">
      <img v-if="shareListData.pageType==TASKTYPE.HISTORY" src="@/assets/manage/sharelist/cur_banner.png" />
      <img v-else src="@/assets/manage/sharelist/hist_banner.png" />
    </div>
    <div class="share-content">
      <div class="cut-top">
        <span :class="{ select: shareTab.isSelect[TASKTYPE.HISTORY-1].show }" @click="cutSelect(TASKTYPE.HISTORY)">
          当前任务
        </span>
        <span :class="{ select: shareTab.isSelect[TASKTYPE.CURRENT-1].show }" @click="cutSelect(TASKTYPE.CURRENT)">
          历史任务
        </span>
      </div>
      <div v-if="shareListData.pageType==TASKTYPE.HISTORY && (data.getSharingL==true&&data.taskSharingList.length == 0)" class="sharelist-none">
        <img src="@/assets/manage/sharelist/cur_ice.png" />
        <p>当前任务列表暂无信息</p>
      </div>
      <div v-else-if="shareListData.pageType==TASKTYPE.CURRENT && (data.getSharingL==true&&data.taskSharingList.length == 0)" class="sharelist-none">
        <img src="@/assets/manage/sharelist/hist_ice.png" />
        <p>历史任务列表暂无信息</p>
      </div>
      <div v-else class="share-list">
        <van-list
          v-if="data.taskSharingList && data.taskSharingList.length > 0"
          v-model="data.loading"
          :finished="data.finished"
          :finished-text="data.showNoData ? '' : '没有更多了'"
          class="ul"
          offset="100"
          @load="getSharingList"
        >
          <div v-for="(item,key) in data.taskSharingList" :key="key" class="li">
            <div class="share-left">
              <dl class="fans-amount">
                <dt>商品名称：</dt>
                <dd>{{ item.goodsName }}</dd>
              </dl>
              <dl class="fans-amount">
                <dt>营销语：</dt>
                <dd>{{ item.marketTitle }}</dd>
              </dl>
              <dl class="fans-amount">
                <dt>任务开始时间：</dt>
                <dd>{{ item.startTime }}</dd>
              </dl>
              <dl class="fans-amount">
                <dt>任务结束时间：</dt>
                <dd>{{ item.endTime }}</dd>
              </dl>
            </div>
            <div class="share-right">
              <img v-if="item.imageSrc" :src="getImgUrl(item.imageSrc)">
              <span @click="goShare(data.shopId,item)">去分享</span>
            </div>
          </div>
        </van-list>
      </div>
    </div>
    <FansShare ref="fansShareRef" :showshare="showshare" :share-info="shopInfo" @shareSuccesses="getSharingRecord(1)" @shareFailures="getSharingRecord(2)" @close="showshare = false" @open="showshare = true" />
  </div>
</template>
<script setup>
// 组件
import FansShare from "@/components/home/<USER>"
import Vue, { reactive, ref, onBeforeMount } from 'vue'
import { List, Toast } from 'vant'
import { getImgUrl } from "@/utils/utils.js"
import { hiddenShare } from "@/utils/share.js"
import sharetasklistApi from "@/api/sharetasklist"
import UA from '@/utils/ua'
import loginApi from "@/api/login"
import { getToken, removeLocalStorage } from "@/utils/login/localStorage.js"
import {getShopdetailFromO2o} from "@/api/netshop.js"
Vue.use(List)
const data = reactive({
  title: '每日分享任务列表',
  isLogined: false,
  users: null,
  shopId: null,
  taskSharingList: [],
  loading:false,
  finished:false,
  showNoData:false,
  getSharingL:false
})
const shareTab = reactive({
  isSelect:[
    {
      show: true
    },{
      show: false
    }
  ]
})
// 任务列表类型
const TASKTYPE = {
  HISTORY:1,
  CURRENT:2
}
// 分享任务列表接口入参
const shareListData = reactive({
  pageType: 1,
  pageNum: 1,
  pageSize:20,
  shopId: null
})
const RESULTCODE={
  SUCCESS:0
}
onBeforeMount(() => {
  const url = new URL(location.href)
  data.shopId = url.searchParams.get('shopId') || url.searchParams.get('shop_id')
  //强登
  loginApi.getUserInfo(getToken(),null,5).then((res)=>{
    if (res.code == 0) {
      logined(res.data)
    } else if (res.code == 1) {
      removeLocalStorage()
      return null
    }
  })
})
const shopInfo = ref({})
// 登录成功回调
function logined(res) {
  data.users = res
  data.isLogined = res && res.UserName > ''
  shopInfo.value =  res
  getSharingList()
  getShopdetail(data.shopId)
  if (UA.isApp) {
    hiddenShare()
  }
}
// 历史任务/当前任务切换
function cutSelect(index){
  shareListData.pageType=index
  let ind=index-1
  if(shareTab.isSelect[ind].show==false){
    data.taskSharingList=[]
    shareTab.isSelect[0].show=false
    shareTab.isSelect[1].show=false
    shareTab.isSelect[ind].show=true
    shareListData.pageNum=1
    data.getSharingL= false
    data.showNoData = true
    getSharingList()
  }
}
// 每日分享任务列表
function getSharingList(){
  shareListData.shopId = data.shopId
  data.loading = true
  sharetasklistApi.getSharingList(shareListData).then((r) => {
    data.loading = false
    data.getSharingL=true
    if(r.code==RESULTCODE.SUCCESS && r.data){
      r.data.taskSharingList.forEach((value, item) => {
        data.taskSharingList.push(value)
      })
      if (r.data.total > r.data.pageNum * r.data.pageSize) {
        shareListData.pageNum++
        data.showNoData = true
        data.finished = false
      } else {
        data.showNoData = false
        data.finished = true
        return
      }
    }else{
      Toast(r.message)
      data.showNoData = true
      data.finished = true
      return
    }
  })
}
// 分享数据
const showshare = ref(false)
const fansShareRef = ref(null)
const goShareId = ref(null)
function goShare(shopid,item){
  let godsLink = item.goodsLink+(item.goodsLink.indexOf('?')==-1?'?':'&')+'WT.ac_id=SHOP_DAILY_SHARETASK'
  let fansShareData = {
    unifiedChannelId : shopInfo.value.unifiedChannelId,
    url : godsLink,
    imgUrl : getImgUrl(item.imageSrc,"goods",true),
    dcsId : 'yd_sharestore_share_'+item.goodsId,
    desc : item.marketTitle,
    title : item.goodsName,
    bgUrl : getImgUrl(item.imageSrc,"goods"),
  }
  shopInfo.value = {
    shopShortName: shopInfo.value.shopShortName,
    provinceId: shopInfo.value.userProvince,
    address: shopInfo.value.address,
    price:item.price,
    goodTitle: item.goodsName,
    type:"goods",
    fansShareData:fansShareData // { imgUrl, title, desc, url = location.href.split("#")[0] ,unifiedChannelId}
  }
  fansShareRef.value.changeShareConfig(fansShareData)
  goShareId.value=item.id
}
// 获取店铺地址
function getShopdetail(shopId) {
  getShopdetailFromO2o({ shopId:shopId }).then((r) => {
    if(r.code==RESULTCODE.SUCCESS && r.data){
      shopInfo.value.address =  r.data.address
    }else{
      Toast(r.message)
    }
  })
}
// 每日分享任务状态记录
function getSharingRecord(share){
  sharetasklistApi.getSharingRecord({ shopId:data.shopId,taskId:goShareId.value,status:share}).then((r) => {
  })
}
</script>
<style lang="scss" scoped>
  .clearfix:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
  body{
    background: #f7f8f9 !important;
  }
  .sharelist {
    position: relative;
    min-height: calc(100vh - 80px);
    width: 100%;
    background: #f7f8f9;
    .share-title{
      img{
        width: 100%;
      }
    }
    .share-content{
      padding-top: 16px;
      .cut-top{
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        font-weight: 500;
        line-height: 50px;
        span{
          flex: 1;
          text-align: center;
          color: #666666;
        }
        .select{
          background: #fff;
          color: #4497F4;
        }
      }
      .sharelist-none{
        margin: 0 auto;
        padding: 80px 0;
        text-align: center;
        font-size: 14px;
        line-height: 30px;
        img{
          width: 180px;
        }
      }
      .share-list{
        border-radius: 12px;
        width: 100%;
        margin: 0 auto;
        font-size: 13px;
        div.li{
          background: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 20px 14px;
          margin-bottom: 16px;
        }
        .share-left{
          flex: 1;
          font-size: 12px;
          color: #323232;
          dl{
            display: flex;
            justify-content: center;
            line-height: 20px;
            padding: 10px 0;
            dt{
              width: 92px;
              text-align: right;
            }
            dd{
              flex: 1;
              padding-right: 10px;
            }
          }
        }
        .share-right{
          width: 100px;
          text-align: center;
          img{
            width: 100px;
            display: block;
          }
          span{
            display: block;
            margin-top: 17px;
            line-height: 32px;
            background: #2baf14;
            border-radius: 16px;
            color: #ffffff;
            font-size: 13px;
          }
        }
      }
    }
  }
</style>
