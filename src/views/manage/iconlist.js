import { reactive } from "vue"
export const shopManage= reactive({
  openaShop:{
    title: "申请开店",
    url: require("@/assets/manage/ic_apply.png"),
    key:'openaShop',
    rule: [2],
    link: "/hd/xskd/register.html",
    route:"hd",
  },
  myShop:{
    title: "我的店铺",
    url: require("@/assets/manage/ic_myshop.png"),
    key:'myShop',
    rule: [1,2],
    link: "/index.html?shopId={shopId}",
    route:"yundian",

  },
  shopInfo:{
    title: "店铺信息",
    url: require("@/assets/manage/ic_shopinfo.png"),
    key:'shopInfo',
    rule: [1,2],
    link: "/hd/xskd/shopinfo.html?shopId={shopId}",
    route:"hd",
  },
  shopStyle:{
    title: "店铺配置",
    url: require("@/assets/manage/ic_shopstyle.png"),
    key:'shopStyle',
    rule: [2],
    link: "/index.html?configure=1&shopId={shopId}",
    route:"yundian"
  },
  clerkManage:{
    title: "店员管理",
    url: require("@/assets/manage/ic_guanli.png"),
    key:'clerkManage',
    rule: [2],
    link: "/hd/xskd/clerk.html?shopId={shopId}",
    route:"hd"
  },
  shopOrder:{
    title: "店铺订单",
    url: require("@/assets/manage/ic_order.png"),
    key:'shopOrder',
    rule: [1,2],
    link: "/shoporder/statistics.html?shopId={shopId}",
    route:"yundian",
  },
  shopData:{
    title: "店铺数据",
    url: require("@/assets/manage/ic_data.png"),
    key:'shopData',
    rule: [1,2],
    link: "/shopdata/index.html?shopId={shopId}",
    route:"yundian",

  },
  statisticalQuery:{
    title: "营销查询",
    url: require("@/assets/manage/ic_statisticalquery.png"),
    key:'statisticalQuery',
    rule: [1,2],
    link: "/shopdata/statistics/query.html?shopId={shopId}",
    route:"yundian",
  },
  broadband:{
    title: "预约管理",
    url: require("@/assets/manage/ic_broadband.png"),
    key:'broadband',
    rule: [1,2],
    link: "/broadband/list.html?shopId={shopId}",
    route:"yundian",
  },
  shopHotp:{
    title: "热销商品",
    url: require("@/assets/manage/ic_hotp.png"),
    key:'shopHotp',
    rule: [2],
    link: "/appnearby/hotproduct.html?shopId={shopId}",
    route:"yundian",
  },
  onlineShop:{
    title: "在线看店",
    url: require("@/assets/manage/ic_onlineshop.png"),
    key:'onlineShop',
    rule: [2],
    link: "/onlinewatchshop/deploy.html?shopId={shopId}",
    route:"yundian",
  },
  jinkePay:{
    title: "支付申请",
    url: require("@/assets/manage/ic_pay.png"),
    key:'jinkePay',
    rule: [2],
    link: "https://pay.cmft.com.cn/merchant/login?platform_code=10CM01&key=",
    route:"outside",
  },
  appletHome:{
    title: "店铺推广",
    url: require("@/assets/manage/ic_appletcode.png"),
    key:'appletHome',
    rule: [1,2],
    link: "/appletcode/index.html?shopId={shopId}",
    route:"yundian",

  },
  intent:{
    title: "意向管理",
    url: require("@/assets/manage/ic_intent.png"),
    key:'intent',
    rule: [1,2],
    link: "/letter/list.html?shopId={shopId}",
    route:"yundian",

  },
  numberOrder:{
    title: "号卡订单",
    url: require("@/assets/manage/ic_numberorder.png"),
    key:'numberOrder',
    rule: [1,2],
    link: "/order/list.html?shopId={shopId}",
    route:"yundian",

  },
  posters:{
    title: "云海报",
    url: require("@/assets/manage/ic_haibao.png"),
    key:'posters',
    link: "/hd/xskd/posters.html?shopId={shopId}",
    route:"hd",
    rule: [1,2]
  },
  coupons:{
    title: "卡券核销",
    url: require("@/assets/manage/ic_kaquan.png"),
    key:'coupons',
    link: "https://b2c.cmccsa.cn/cnr-web/couponCancell",
    route:"outside",
    rule: [1,2]
  },
  privilege:{
    title: "特权申请",
    url: require("@/assets/manage/ic_wdtq.png"),
    key:'privilege',
    link: "/privilegedactivities/index.html?shopId={shopId}",
    route:"yundian",
    rule: [1,2]
  },
  scancode:{
    title: "扫码购机",
    url: require("@/assets/manage/ic_scancode.png"),
    key:'scancode',
    link: "/scancodebuygoods/index.html?shopId={shopId}",
    route:"yundian",
    rule: [1,2]
  },
  askedCheck:{
    title: "问查算比",
    url: require("@/assets/manage/ic_wcsb.png"),
    key:'askedCheck',
    wcsb:1,
    link: "",
    rule: [1,2],
    route:"outside"
  },
  eventPromotion:{
    title: "活动推广",
    url: require("@/assets/manage/ic_hdtg.png"),
    key:'eventPromotion',
    link: "/aggregatedshare/index.html?shopId={shopId}",
    route:"yundian",
    rule: [1,2]
  },
  feedback:{
    title: "表单反馈",
    url: require("@/assets/manage/ic_bdfk.png"),
    key:'feedback',
    link: "/formfeedback/list.html?shopId={shopId}",
    route:"yundian",
    rule: [1,2]
  },
  opportunity:{
    title: "商机管理",
    url: require("@/assets/manage/ic_opportunity.png"),
    key:'opportunity',
    link: "/opportunity/list.html?shopId={shopId}",
    route:"yundian",
    rule: [1,2]
  },
  fansoperation:{
    title: "粉丝运营",
    url: require("@/assets/manage/ic_fansoperation.png"),
    key:'fansoperation',
    fansProduct:1,
    link: "/fansoperation/fans.html?shopId={shopId}",
    route:"yundian",
    rule: [1,2]
  },
  dailySharing:{
    title: "每日分享",
    url: require("@/assets/manage/ic_dailysharing.png"),
    key:'dailySharing',
    link: "/ydmanage/sharelist.html?shopId={shopId}",
    route:"yundian",
    rule: [1,2]
  },
  goodsPublicize:{
    title: "商品推广",
    url: require("@/assets/manage/ic_goodspublicize.png"),//ic_goodspublicize
    key:'goodsPublicize',
    link: "/marketingtemplate/config/index.html?shopId={shopId}",
    route:"yundian",
    rule: [1,2]
  }
})
export const iconList = reactive({
  shopindex: {
    inactive: require('@/assets/my/other.png'),
    active: require('@/assets/my/others-active.png'),
    title: '店铺首页',
    key: 'shopindex',
    links: '',
    isactive: true,
    showLink: true,
    class: 'shopindex'
  },
  manage: {
    inactive: require('@/assets/my/management.png'),
    active: require('@/assets/my/management-active.png'),
    title: '管理',
    key: 'manage',
    links: '',
    isactive: false,
    showLink: false,
    class: 'manage'
  }
})
export const SHOPSTATUS = [
  {
    status:0,
    errorMsg:"您还没有申请开店，请先申请线上开店"
  },
  {
    status:1,
    errorMsg:"请先填写店铺信息"
  },
  {
    status:2,
    errorMsg:"您的店铺申请还在审核中，暂不能操作"
  },
  {
    status:3,
    errorMsg:"已开店"
  },
  {
    status:4,
    errorMsg:"店铺已关闭，如有疑问，请联系省商户管理员"
  },
  {
    status:5,
    errorMsg:"您的店铺还未通过审核，暂不能操作"
  },
  {
    status:6,
    errorMsg:"您还没有申请开店，请先申请线上开店"
  }
]
//店员不可看
export const BLACK = ["openaShop","shopStyle","clerkManage","shopHotp","onlineShop","jinkePay"]
//申请开店可见状态列表
export const OPENSTATUS = [0,6]
// //营销查询/支付申请/卡券核销/问查算比 (直接跳转，不查店铺状态)
export const DIRECTLYENTER = ['statisticalQuery','jinkePay','coupons','askedCheck']
export default {
  shopManage,
  iconList,
  BLACK,
  SHOPSTATUS,
  OPENSTATUS,
  DIRECTLYENTER
}
