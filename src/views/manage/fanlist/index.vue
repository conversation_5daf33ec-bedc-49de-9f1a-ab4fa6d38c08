<template>
  <div class="fanlist">
    <Shopname
      v-if="showShopName"
      bg-color="#78a9f8"
      :content="data.title"
      :show-back="true"
    />
    <div :class="{ 'padding-top-34': showShopName }" class="fan-content">
      <div class="fan-title">
        <dl class="fans-amount">
          <dt>{{ data.fansTotalNum }}</dt>
          <dd>全部粉丝数</dd>
        </dl>
        <dl class="fans-amount">
          <dt>{{ data.addConcernNumToday }}</dt>
          <dd>当日新增关注</dd>
        </dl>
        <dl class="fans-amount">
          <dt>{{ data.cancelConcernNumToday }}</dt>
          <dd>当日取消关注</dd>
        </dl>
      </div>
      <div class="fan-list">
        <div class="ul-top">
          <span class="fan-number">粉丝手机号码</span>
          <span class="fan-time">关注时间</span>
        </div>
        <van-list
          v-if="data.shopFansDetailInfoList && data.shopFansDetailInfoList.length > 0"
          v-model="data.loading"
          :finished="data.finished"
          :finished-text="data.showNoData ? '' : '没有更多了'"
          class="ul"
          offset="100"
          @load="getShopFansTotal()"
        >
          <div v-for="(item,key) in data.shopFansDetailInfoList" :key="key" class="li">
            <span class="fan-number">{{ item.phone }}</span>
            <span class="fan-time">{{ parseTime(item.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </div>
        </van-list>
      </div>
    </div>
  </div>
</template>
<script setup>
// 组件
import Shopname from '@/components/index/headercon'
import Vue, { reactive, ref, onMounted } from 'vue'
import { List, Toast } from 'vant'
import loginApi from "@/api/login"
import { getToken, removeLocalStorage } from "@/utils/login/localStorage.js"
import shopApi from "@/api/netshop.js"
import UA from '@/utils/ua'
import { parseTime } from "@/utils/utils"
Vue.use(List)
const data = reactive({
  title: '店铺粉丝列表',
  isLogined: false,
  users: null,
  fansTotalNum: 0,
  addConcernNumToday: 0,
  cancelConcernNumToday: 0,
  shopFansDetailInfoList: [],
  loading:false,
  finished:false,
  showNoData:false,
  pageNum: 1
})
const RESULTCODE={
  SUCCESS:0
}
const shopId = ref(null)
onMounted(() => {
  const url = new URL(location.href)
  shopId.value =
      url.searchParams.get('shopId') || url.searchParams.get('shop_id')
  //强登
  // loginUtils.login(true, true, logined, false, false, '', null, 5)
  loginApi.getUserInfo(getToken(),null,5).then((res)=>{
    if (res.code == 0) {
      logined(res.data)
    } else if (res.code == 1) {
      removeLocalStorage()
      return null
    }
  })
})
const showShopName =
  !UA.isWechat &&
  !UA.isWechatWork &&
  !UA.isApp &&
  !UA.isIosQQ &&
  !UA.isAndroidQQ
function logined(res) {
  data.users = res
  data.isLogined = res && res.UserName > ''
  getShopFansTotal()
}
// 粉丝数据详情列表
const pnum = ref(null)
function getShopFansTotal(){
  data.loading = true
  if(pnum.value!=data.pageNum){
    shopApi.getShopFansTotal({ shopId:shopId.value,pageNum:data.pageNum,pageSize:10 }).then((r) => {
      data.loading = false
      pnum.value=data.pageNum
      if(r.code==RESULTCODE.SUCCESS && r.data){
        if(data.pageNum==1){
          data.fansTotalNum=r.data.fansTotalNum
          data.addConcernNumToday=r.data.addConcernNumToday
          data.cancelConcernNumToday=r.data.cancelConcernNumToday
        }
        r.data.shopFansDetailInfoList.forEach((value, item) => {
          data.shopFansDetailInfoList.push(value)
        })
        if (r.data.total > r.data.pageNum * r.data.pageSize) {
          data.pageNum++
          data.showNoData = true
          data.finished = false
        } else {
          data.showNoData = false
          data.finished = true
          return
        }
      }else{
        data.showNoData = false
        Toast(r.message)
      }
    })
  }
}
</script>
<style lang="scss" scoped>
  .head{
    font-size: 15px;
    color: #fff;
    i{
      font-size: 20px;
    }
  }
  .fanlist {
    position: relative;
    min-height: calc(100vh - 80px);
    background: #f6f6f6;
    .fan-content{
      padding: 20px 0;
      .fan-title{
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 6px;
        dl{
          flex: 1;
          font-size: 14px;
          background: #fff;
          margin: 6px;
          border-radius: 10px;
          padding: 26px 0;
          line-height: 26px;
          dt,dd{
            text-align: center;
          }
          dt{
            font-size: 15px;
          }
        }
      }
      .fan-list{
        background: #fff;
        border-radius: 12px;
        padding: 0 12px;
        width: 98%;
        margin: 0 auto;
        font-size: 13px;
        min-height: 390px;
        div.ul-top,.li{
          line-height: 50px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-bottom: 2px solid #f6f6f6;
        }
        .fan-number{
          flex: 1;
          padding-left: 16px;
        }
        .fan-time{
          width: 140px;
          text-align: center;
        }
      }
    }
  }
  .padding-top-34 {
    padding-top: 34px !important;
  }
</style>
