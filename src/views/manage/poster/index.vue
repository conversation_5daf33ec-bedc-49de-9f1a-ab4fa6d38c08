<template>
  <div>
    <carouselSliperv
      :items="props.posterRuleInfoList"
      :configure="false"
      :prop-options="posterData.options"
    >
      <template
        v-for="adItem in props.posterRuleInfoList"
        #[adItem.slotname]
      >
        <!-- {{ adItem }} -->
        <div :key="adItem.id" class="swiper-ad">
          <div class="title">
            {{ adItem.posterName }}
          </div> <!-- 显示标题 -->
          <img :src="adItem.thumbPicture" />
          <div class="share-btn" @click="handleClick(adItem.posterId)">
            分享海报
          </div>
        </div>
      </template>
    </carouselSliperv>
  </div>
</template>
<script setup>
import carouselSliperv from "@/components/index/carouselSliperv.vue"
import { getUrl } from "@/utils/utils"
import Vue,{reactive}from "vue"
const posterData = reactive({
  options: {
    navigation: {
      nextEl: '.swiper-button-next',
      prevEl: '.swiper-button-prev',
    },
    autoplay: { delay: 3000 },
    loop:false,
  }
})
const props = defineProps({
  shopId: {
    type: String,
    required: true
  },
  posterRuleInfoList: {
    type: Array,
    required: true
  }
})
function handleClick(posterId) {
  let adLink = getUrl(`/hd/xskd/posters.html?shopId=${props.shopId}&poster_id=${posterId}&cmToken={sourceid:12006}`)
  location.href = adLink
}
</script>
<style lang="scss">
.swiper-ad{
  width:360px;
  margin:0 auto;
  background-color: #fff;
  padding:20px;
  box-sizing: border-box;
  .title {
    text-align: center;
    padding: 10px 0;
    font-weight: bold;
  }
  img{
    width: 320px;
    height: 320px;
  }
  .share-btn{
    width:320px;
    height: 50px;
    line-height: 50px;
    border-radius: 25px;
    background-color: #39B967;
    color: white;
    text-align: center;
    margin-top:20px;
  }
}
.sliprev{
  padding-right: 10px;;
}
</style>
