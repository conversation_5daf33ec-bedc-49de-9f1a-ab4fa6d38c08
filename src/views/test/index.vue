<template>
  <div>
    <p>phoneInfo: {{ phoneInfo }}</p>
    <p>userinfo: {{ userinfo }} </p>
  </div>
</template>
<script>
import loginApi from "@/api/login"
import {Toast} from "vant"
export default {
  data(){
    return {
      phoneInfo:null,
      userinfo: null,
    }
  },
  mounted(){
    this.autoLogin()
  },
  methods: {
    async autoLogin(){
      let tokenRes = await loginApi.getAutoLoginToken()
      // console.log(tokenRes)
      if(tokenRes.resultCode == 0 ) {

        const img = new Image()
        img.onload = ()=> {
          this.getPhoneInfo(tokenRes.data.token)
          document.body.removeChild(img)

        }
        img.onerror= ()=> {
          document.body.removeChild(img)
          Toast('取号失败')
        }
        img.src = location.origin.replace('https','http') + "/yundian/netShop/4glogin/4GLogin?token=" + tokenRes.data.token
        document.body.appendChild(img)
      }
    },
    async getPhoneInfo(token){
      let phoneInfo = await loginApi.getAutoLoginInfo(token)
      let artifact = await loginApi.autoLogin(token)
      this.phoneInfo = phoneInfo.data
      if(artifact.resultCode == 0) {
        this.getUserInfo(artifact.data.artifact)
      }
    },
    getUserInfo(artifact,type = '00'){
      loginApi.getToken({artifact,type}).then(res=> {
        // console.log(res)
        if(res.code == 0 ){
          this.userinfo = res.data
          loginApi.getUserInfo(res.data.token).then(res=> {
            if(res.code == 0) {
              this.userinfo = res.data
            }
          })
        }
      })
    }
  }
}
</script>
