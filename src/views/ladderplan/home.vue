<template>
  <div @click="serviceImgDisappear">
    <Shopname
      v-if="showHead"
      bg-color="#fff"
      :show-back="true"
      content="首页"
    />
    <div
      ref="homebody"
      class="minheight"
      :class="{
        'padding-top-34': showHead,
      }"
    >
      <div class="home-bg"></div>
      <div class="flex">
        <div
          class="tabs"
          :class="{ active: activeTab === 'tb1' }"
          @click="changeTab('tb1')"
        >
          <van-badge class="badge">
            发现
          </van-badge>
        </div>
        <div
          class="tabs"
          :class="{ active: activeTab === 'tb2' }"
          @click="changeTab('tb2')"
        >
          <!-- isUpdate:0 //	是否有更新（0：无更新 1：有更新） -->
          <van-badge v-if="isUpdate && isUpdate === 1" dot class="badge">
            上新
          </van-badge>
          <van-badge v-else class="badge">
            上新
          </van-badge>
        </div>
        <div class="tabs search-con" @click="gotoSearch">
          <span class="icon icon-search-grey"></span>
        </div>
      </div>
      <div v-if="activeTab === 'tb1'" class="tab1">
        <CarouselA
          v-if="imglist && imglist.length>0"
          :items="imglist"
          carousel-class="circle-center"
          page-name="ythome"
          :isforcelogin="true"
          :go-to-link="goToLink"
        />
        <header class="headerTitle">
          周边精选
        </header>
        <carouselSliperv :items="peripheralSelectList">
          <template
            v-for="goodsinfo in peripheralSelectList"
            #[goodsinfo.slotname]
          >
            <GoodsItem
              :key="goodsinfo.id"
              size-class="normal"
              :goods-info="goodsinfo"
              :shop-id="shopId"
              :change-current-coupon="changeCurrentCoupon"
              :change-follow="changeFollow"
              :goto-detail="gotoDetail"
              :service-class="serviceClass"
              :service-img-disappear="serviceImgDisappear"
            />
          </template>
        </carouselSliperv>
        <header class="headerTitle">
          云店特惠
        </header>
        <div class="ladplist">
          <div class="introduce">
            <p>
              <span class="icon icon-add_collec"></span>
              <span>关注商家</span>
            </p>
            <p>
              <span class="icon icon-add_quan"></span>
              <span>加入券包</span>
            </p>
          </div>
          <div
            v-for="item in netShopPreferential"
            :key="item.id"
            class="goodsItem"
          >
            <GoodsItem
              size-class="large"
              :goods-info="item"
              :show-quan="false"
              :shop-id="shopId"
              :change-current-coupon="changeCurrentCoupon"
              :change-follow="changeFollow"
              :goto-detail="gotoDetail"
              :service-class="serviceClass"
              :service-img-disappear="serviceImgDisappear"
            />
          </div>
          <div class="searchBottom">
            <div class="hrStyle">
              <hr
                style="background-color: #dadada; border: none; height: 1px"
              />
            </div>
            <span class="hrContent">
              {{ preferentialLoadingText }}
            </span>
            <div class="hrStyle">
              <hr
                style="background-color: #dadada; border: none; height: 1px"
              />
            </div>
          </div>
        </div>
      </div>
      <div v-if="activeTab === 'tb2'" class="tab2" @click="changeTab('tb2')">
        <ul>
          <li
            :class="{ active: activeSubTab === 'tb1' }"
            @click="changeSubTab('tb1')"
          >
            <!-- isUpdate:0 //	是否有更新（0：无更新 1：有更新） -->
            <van-badge v-if="isUpdate && isUpdate === 1" dot class="badge">
              关注
            </van-badge>
            <van-badge v-else class="badge">
              关注
            </van-badge>
          </li>
          <li
            :class="{ active: activeSubTab === 'tb2' }"
            @click="changeSubTab('tb2')"
          >
            <van-badge class="badge">
              官方
            </van-badge>
          </li>
        </ul>
        <van-list
          v-if="couponFollowRecordList && couponFollowRecordList.length > 0"
          v-bind="loading"
          :finished="finished"
          finished-text="我是有底线的"
          class="goods-list"
        >
          <van-cell v-for="(item, key) in couponFollowRecordList" :key="key">
            <div
              class="imgItem"
              :class="{
                collecLabel: activeSubTab === 'tb1',
                guanfangLabel: activeSubTab === 'tb2',
              }"
              @click="gotoDetail(item.id)"
            >
              <img :src="item.voucherimg" alt="" width="100%" />
            </div>
          </van-cell>
        </van-list>
        <div v-else class="tabNoGoods">
          <img src="~@/assets/ladderplan/empty.png" />
          <p>您还没有关注商家</p>
          <p>先看看“官方”活动吧</p>
        </div>
      </div>
    </div>
    <div class="customer_service" :class="serviceClass" @click.stop="goService()">
      <div
        class="service_user_img"
        :class="{
          user_girl: serviceGender === 1,
          user_boy: serviceGender === 0,
        }"
      ></div>
      <div class="bubble first">
        上号吗
      </div>
      <div class="bubble second">
        带你开黑
      </div>
      <div class="bubble third">
        我可不是机器人
      </div>
    </div>
    <ReceiveCoupon
      v-if="showReceiveDialog"
      :coupon-detail="currentCoupon"
      :receive-coupon-success-fn="receiveCouponSuccessFn"
    ></ReceiveCoupon>
    <Footers v-if="iconList" :list="iconList" :active="0" :isladder="true" />
  </div>
</template>
<script>
import UA from "@/utils/ua"
import Vue from "vue"
import Shopname from "@/components/index/headercon.vue"
import "swiper/dist/css/swiper.css"
import CarouselA from "@/components/index/carouselA"
import CarouselSliperv from "@/components/index/carouselSliperv"
import GoodsItem from "@/components/ladderplan/goodsitem"
import loginUtils from "@/utils/login"
import LadderApi from "@/api/ladder/ladder"
import shareUtilApi from "@/utils/share"
import { getImgUrl } from "@/utils/utils"
import { iconList, imglist } from "@/views/ladderplan/json.js"
import VueAwesomeSwiper from "vue-awesome-swiper"
import { List, Cell, Badge } from "vant"
import Footers from "@/components/myfooter"
import ReceiveCoupon from "@/components/ladderplan/receivecoupon.vue"
Vue.use(VueAwesomeSwiper).use(List).use(Cell).use(Badge)
export default {
  name: "Ladphome",
  components: {
    CarouselA,
    CarouselSliperv,
    Shopname,
    GoodsItem,
    Footers,
    ReceiveCoupon,
  },
  data() {
    return {
      shopId: null,
      isLogined: false,
      serviceGender: null,
      showReceiveDialog: false,
      pageNo: 1,
      loading: false,
      finished: false,
      preferentialLoading: false,
      preferentialFinished: false,
      preferentialLoadingText: "加载中",
      goodslist: [],
      imglist: null,
      flag: false,
      showShare: false,
      pageInfo: {},
      showHead: false,
      shareConfig: {
        title: "云梯首页",
        url: window.location.href,
        desc: window.location.href,
        imgUrl: "https://img1.shop.10086.cn/goods/tc2txqenygq3nkry_940x7200",
      },
      netShopPreferential: null, //云店特惠
      peripheralSelectList: null, //周边精选
      couponFollowRecordList: null, //关注商铺资源更新列表
      activeTab: "tb1",
      activeSubTab: "tb1",
      preferentialSendData: {
        pageNum: 1,
        pageSize: 10,
      },
      isUpdate: 0, //	是否有更新（0：无更新 1：有更新）
      iconList: null,
      currentCoupon: null,
      storelist: [],
      serviceClass:"",
      scrollTopNumber:0,
    }
  },
  async created() {
    this.showHead = false
    const url = new URL(location.href)
    this.shopId = url.searchParams.get("shopId")
    this.$store.commit("SET_SHOPID", this.shopId)
    this.iconList = iconList
    this.imglist = imglist
    this.getServiceGender()
    loginUtils.login(false, false, this.logined, false, false, "", "0")
  },
  mounted() {
    this.listenScroll()
  },
  beforeDestroy() {
    this.removeScroll()
  },
  methods: {
    logined(res) {
      if (this.shopId) {
        this.finished = false
        this.getCouponSelectedList()
        this.getCouponPreferentialList()
      } else {
        this.$router.push({
          name: "nearby",
        })
      }
      this.isLogined = res && res.UserName > ""
      if (this.isLogined) {
        if (this.shopId) {
          this.followedResourceUpdate()
        }
        if (res.staffId && this.shopId == res.shopId) {
          this.iconList.manage.showLink = true
        } else {
          this.iconList.manage.showLink = false
        }
      }
    },
    goToLink(item) {
      // console.log(item)
      this.gotoDetail(item.id)
    },
    gotoDetail(couponId) {
      let that = this,setTime = 0
      if(this.serviceClass.indexOf("service_img_bubble_show")!==-1){
        this.serviceClass = "service_img_bubble_hide"
        setTime=2000
      }
      setTimeout(()=>{
        let detail = (res)=>{
          if (res && res.UserName > "") {
            that.$router.push({
              path:
                "/ladderplan/detail.html?shopId=" +
                that.shopId +
                "&couponId=" +
                couponId,
            })
          }
        }
        loginUtils.login(true,true,detail,false,false,"", "0")
      },setTime)
    },
    changeFollow(storeId, isFollow) {
      if (this.storelist[storeId]) {
        this.storelist[storeId].forEach((element) => {
          this.netShopPreferential[element].isFollow = isFollow
        })
      }
      if (this.peripheralSelectList) {
        this.peripheralSelectList.forEach((item) => {
          if (item.storeId === storeId) {
            item.isFollow = isFollow
          }
        })
      }
    },
    getServiceGender() {
      let serviceGender = localStorage.getItem("serviceGender")
      if (serviceGender) {
        this.serviceGender = Number(serviceGender)
      } else {
        this.serviceGender = Math.round(Math.random())
      }
    },
    serviceImgDisappear(){
      if(this.serviceClass.indexOf("service_img_bubble_show")!==-1){
        this.serviceClass = "service_img_bubble_hide"
      }
    },
    goService() {
      localStorage.setItem("serviceGender", this.serviceGender)
      if(this.serviceClass==="" || this.serviceClass === "service_img_bubble_hide"){
        this.serviceClass = "service_img_bubble_show"
      }else if(this.serviceClass.indexOf("service_img_bubble_show")!==-1){
        let search = ()=>{
          if (this.serviceGender === 1) {//女孩头像
            window.location.href = "https://work.weixin.qq.com/kfid/kfc5035e8675804177c"
          } else {//男孩头像
            window.location.href = "https://work.weixin.qq.com/kfid/kfcdca788cde6e2e49f"
          }
        }
        loginUtils.login(true,true,search,false,false,"", "0")
      }else if(this.serviceClass.indexOf("service_img_disappear")!==-1){//点击小的出来大的
        this.serviceClass = "service_img_active service_img_bubble_show"
      }else{
        this.serviceClass = "service_img_active"
      }
    },
    changeCurrentCoupon(item) {
      this.showReceiveDialog = true
      this.currentCoupon = item
    },
    receiveCouponSuccessFn() {
      this.currentCoupon.couponStatus = 1
    },
    onLoad() {
      this.getCouponPreferentialList()
    },
    //云店特惠
    getCouponPreferentialList() {
      this.preferentialSendData.shopId = this.shopId
      LadderApi.getCouponPreferentialList(this.preferentialSendData)
        .then((res) => {
          if (this.preferentialSendData.pageNum == 1) {
            this.netShopPreferential = []
          }
          if (res.code) {
            this.$toast(res.message)
            this.preferentialLoading = false
            this.preferentialFinished = true
          } else {
            if (!res.data || !res.data.netShopPreferential||res.data.netShopPreferential.length === 0) {
              this.preferentialLoading = false
              this.preferentialFinished = true
              return false
            }
            res.data.netShopPreferential.forEach((item, index) => {
              item.voucherimg = getImgUrl(item.imgUrl, "goods")
              item.logoImg = getImgUrl(item.logoImg, "goods")
              item.vouchertitle = item.couponName
              item.vouchersubTitle = item.couponPropaganda
              if (this.storelist[item.storeId]) {
                this.storelist[item.storeId].push(
                  this.netShopPreferential.length
                )
              } else {
                this.storelist[item.storeId] = [this.netShopPreferential.length]
              }
              this.netShopPreferential.push(item)
            })
            this.preferentialLoading = false
            if (res.data.total > this.netShopPreferential.length) {
              this.preferentialSendData.pageNum++
            } else {
              this.preferentialFinished = true
            }
          }
        })
        .catch((err) => {
          // console.log(err)
        })
    },
    //周边精选
    getCouponSelectedList() {
      LadderApi.getCouponSelectedList({
        shopId: this.shopId,
      })
        .then((res) => {
          if (res.data) {
            this.peripheralSelectList = res.data.peripheralSelectList || []
            this.peripheralSelectList.forEach((item) => {
              item.voucherimg = getImgUrl(item.imgUrl, "goods")
              item.logoImg = getImgUrl(item.logoImg, "goods")
              item.vouchertitle = item.couponName
              item.vouchersubTitle = item.couponPropaganda
              item.slotname = item.id
            })
          }
        })
        .catch((err) => {
          // console.log(err)
        })
    },
    //关注列表
    followedResourceUpdate() {
      LadderApi.followedResourceUpdate({
        shopId: this.shopId,
        pageNum: 1,
        pageSize: 20,
      })
        .then((res) => {
          if (res.data) {
            this.isUpdate = res.data.isUpdate //是否有更新（0：无更新 1：有更新）
            this.couponFollowRecordList = res.data.couponFollowRecordList || []
            this.couponFollowRecordList.forEach((item) => {
              item.voucherimg = getImgUrl(item.imgUrlNew, "goods")
            })
          }else{
            this.couponFollowRecordList = []
          }
        })
        .catch((err) => {
          // console.log(err)
        })
    },
    listenScroll() {
      let that = this
      window.addEventListener("scroll", that.scrollEv, true)
    },
    scrollEv() {
      if(this.scrollTopNumber>0){
        this.serviceClass = "service_img_disappear"
      }
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop
      this.scrollTopNumber++
      if (
        document.body.scrollHeight <=
        window.screen.height + Math.ceil(scrollTop)
      ) {
        this.preferentialLoading = true
        if (!this.preferentialFinished) {
          this.onLoad()
          this.preferentialLoadingText = "加载中"
        } else {
          this.preferentialLoadingText = "我是有底线的"
        }
      }
    },
    removeScroll() {
      let that = this
      window.removeEventListener("scroll", that.scrollEv, true)
    },
    changeTab(value) {
      if(value==="tb2"){
        loginUtils.login(true,true,()=>{
          this.activeTab = value
        },false,false,"", "0")
      }else{
        this.activeTab = value
      }
    },
    changeSubTab(value) {
      this.activeSubTab = value
      if (this.activeSubTab === "tb2") {
        this.couponFollowRecordList = this.imglist
      }else{
        this.followedResourceUpdate()
      }
    },
    share() {
      if (UA.isApp) {
        shareUtilApi.appShare(this.shareConfig)
      } else {
        shareUtilApi.changeWxShareConfig(this.shareConfig)
      }
    },
    gotoSearch() {
      let search = ()=>{
        this.$router.push({
          path: "/ladderplan/searchlist.html?shopId=" + this.shopId,
        })
      }
      loginUtils.login(true,true,search,false,false,"", "0")
    },
  },
}
</script>
<style lang="scss" scoped>
$bgcolor: #f6f7f9;
.home-bg {
  position: absolute;
  z-index: 0;
  top: 0;
  height: 200px;
  width: 100%;
  background: linear-gradient(115deg, #ffe14d 48%, #fec34d 76%);
}
.flex {
  display: flex;
  height: 47px;
  align-items: center;

  .tabs {
    width: 54px;
    font-size: 16px;
    font-weight: 400;
    text-align: left;
    color: #333333;
    &.active {
      font-size: 20px;
      font-weight: 600;
      text-align: left;
      line-height: 28px;
    }
    .badge {
      padding-right: 5px;
    }
    &:first-child {
      margin-left: 22px;
    }
    &:nth-child(2) {
      flex: 1;
    }
    &.province {
      font-size: 14px;
      width: 100px;
      margin-left: 5px;
      z-index: 2;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    &.search-con {
      width: 82px;
      height: 26px;
      background: #ffffff;
      border-radius: 13px;
      margin-right: 13px;
      position: relative;
      .icon-search-grey {
        width: 14px;
        height: 14px;
        position: absolute;
        background: url(~@/assets/ladderplan/search.png) 0 0 no-repeat;
        background-size: contain;
        right: 9px;
        top: 6px;
      }
    }
  }
  .icon-location {
    width: 13px;
    height: 13px;
    background: url(~@/assets/ladderplan/location_black.png) 0 0 no-repeat;
    background-size: contain;
    z-index: 2;
  }
}
.headerTitle {
  height: 22px;
  font-size: 16px;
  font-weight: 500;
  text-align: left;
  color: #a67d48;
  line-height: 22px;
  padding-left: 12px;
  margin-top: 16px;
  margin-bottom: 8px;
}
.ladplist {
  padding: 0 10px;
  min-height: 100px;
  .goodsItem {
    position: relative;
    margin-bottom: 9px;
    float: right;
    &:nth-child(odd) {
      float: left;
    }
  }
}
.introduce {
  width: 173px;
  height: 76px;
  background: #e3b64d;
  border-radius: 8px;
  padding: 16px 20px;
  box-shadow: 0px 2px 5px 0px rgba(225, 225, 225, 0.5);
  float: left;
  margin-bottom: 9px;
  p {
    display: flex;
    font-size: 14px;
    font-weight: 500;
    text-align: left;
    color: #ffffff;
    margin-bottom: 15px;
  }
  .icon {
    width: 13px;
    height: 13px;
    display: block;
    align-self: center;
    margin-right: 12px;
  }
  .icon-add_collec {
    background: url(~@/assets/ladderplan/add_collec.png);
    background-size: contain;
  }
  .icon-add_quan {
    background: url(~@/assets/ladderplan/add_quan.png);
    background-size: contain;
  }
}
.tab1 {
  position: relative;
}
.tab2 {
  width: 355px;
  min-height: 538px;
  background: #ffffff;
  margin: 0 auto;
  border-radius: 10px;
  position: relative;
  ul {
    width: 315px;
    border-bottom: 1px solid rgba(#979797, 0.2);
    margin: 0 auto;
    &::after {
      content: "";
      display: table;
      clear: both;
    }
    li {
      float: left;
      width: 50%;
      text-align: center;
      height: 40px;
      // line-height: 40px;
      font-size: 16px;
      font-weight: 400;
      color: #aaaaaa;
      .badge {
        height: 22px;
        line-height: 22px;
        padding-right: 5px;
        margin-top: 10px;
      }
      &.active {
        font-weight: 600;
        color: #a67d48;
        position: relative;
        &::after {
          content: "";
          width: 8px;
          height: 8px;
          border: 1px solid rgba(#979797, 0.2);
          border-bottom-color: transparent;
          border-right-color: transparent;
          position: absolute;
          bottom: 0;
          left: 50%;
          background: #fff;
          transform: rotate(45deg) translate(0, 8px);
        }
      }
    }
  }
  .goods-list {
    padding-bottom: 20px;
  }
  .van-cell {
    padding: 10px 12px 0;
    .imgItem {
      height: 109px;
      border-radius: 10px;
      overflow: hidden;
      position: relative;
      &:after {
        content: "";
        width: 21px;
        height: 24px;
        position: absolute;
        right: 9px;
      }
      &.guanfangLabel:after {
        background: url(~@/assets/ladderplan/guanfang_label.png) no-repeat;
        background-size: contain;
      }
      &.collecLabel:after {
        background: url(~@/assets/ladderplan/collec_label.png) no-repeat;
        background-size: contain;
      }
    }
    &:after {
      border: none;
    }
  }
}
.hrStyle {
  width: 110px;
  hr {
    margin-top: 12px;
  }
}
.searchBottom {
  display: flex;
  padding: 10px 15px;
  box-sizing: border-box;
  clear: both;
}
.hrContent {
  flex: 1;
  text-align: center;
  color: #dadada;
  font-size: 12px;
  font-weight: 400;
  line-height: 25px;
  display: inline-block;
  height: 25px;
}
.minheight {
  min-height: calc(100vh - 80px);
  overflow-y: scroll;
  background: $bgcolor;
}
.padding-top-34 {
  padding-top: 34px;
}
@keyframes mymove_right {
    0% {
        transform:scale(1,1);
        right:0;
    }
    100%{
        transform:scale(.3,.3);
        right: -48px;
    }
}
@keyframes mymove_left {
    0% {
        transform:scale(.3,.3);
        right: -48px;
    }
    100% {
        transform:scale(1,1);
        right:0;
    }
}
@keyframes bubbleburst_hide {
    0% {
        opacity: 1;
    }
    100%{
        opacity: 0;
    }
}
@keyframes bubbleburst_show {
    0% {
        opacity: 0;
    }
    100%{
        opacity: 1;
    }
}
.customer_service {
  position: fixed;
  width: 111px;
  height: 219px;
  right: 0;
  bottom: 50px;
  z-index: 20;
  .service_user_img {
    width: 111px;
    height: 219px;
    z-index: 1;
    position: absolute;
    &.user_girl {
      background: url(~@/assets/ladderplan/user_girl.png) no-repeat;
      background-size: contain;
    }
    &.user_boy {
      background: url(~@/assets/ladderplan/user_boy.png) no-repeat;
      background-size: contain;
    }
  }
  .bubble {
    z-index: 0;
    border-radius: 50%;
    position: absolute;
    background: linear-gradient(115deg, #ffe14d, #fbcd59 48%, #fec34d);
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
    opacity: 0;
    &.first {
      width: 50px;
      height: 50px;
      top: -33px;
      left: 5px;
      padding-top: 16px;
    }
    &.second {
      width: 50px;
      height: 50px;
      top: 5px;
      left: -30px;
      padding: 8px 7px;
    }
    &.third {
      width: 70px;
      height: 70px;
      top: 91px;
      left: -76px;
      padding: 17px 2px;
    }
  }
}
.tabNoGoods {
  padding-top: 80px;
  img {
    display: block;
    height: 120px;
    width: auto;
    margin: 0 auto 13px;
  }
  p {
    display: block;
    line-height: 22.5px;
    font-size: 15px;
    font-weight: 400;
    color: #bfbfbf;
    text-align: center;
  }
}
//人物展示
.service_img_active{
  animation:mymove_left 2s;
  animation-fill-mode: forwards;
}
//人物缩小
.service_img_disappear{
  animation:mymove_right 2s;
  animation-fill-mode: forwards;
}
//气泡展示
.service_img_bubble_show{
  .bubble{
    animation:bubbleburst_show 2s;
    animation-fill-mode: forwards;
  }
}
//气泡消失
.service_img_bubble_hide{
  .bubble{
    animation:bubbleburst_hide 3s;
    animation-fill-mode: forwards;
  }
}
</style>
<style lang="scss">
.icpinfo {
  margin-bottom: 68px;
}
</style>
