<template>
  <div>
    <Shopname
      v-if="showHead"
      content="优惠详情"
      :styleindex="20"
      :show-back="true"
    />
    <div
      class="voucher-detail"
      :class="{'padding-top-34':showHead}"
    >
      <div v-if="couponDetail.imgUrl" class="img-container">
        <img :src="couponDetail.imgUrl" alt="" width="100%" />
      </div>
      <div class="voucher-content top">
        <div class="title">
          {{ couponDetail.couponName }}
        </div>
      </div>
      <div class="voucher-content">
        <div class="title">
          {{ couponDetail.storeName }}
        </div>
        <div class="subtitle">
          <span class="icon icon-location"></span>
          <span class="content">
            {{ couponDetail.storeAddress }}
          </span>
        </div>
        <!-- （0：取关 1：关注） -->
        <div class="btn_follow" :class="{followed:couponDetail.isFollow===1}" @click="followShop()">
          <span class="icon-collec"></span>
          <span v-if="couponDetail.isFollow===1">已关注</span>
          <span v-else>关注商家</span>
        </div>
      </div>
      <p class="couponTitle">
        可用优惠
      </p>
      <ul>
        <!--couponStatus 0 就是未领取，1 是领取 -->
        <li class="voucher-item">
          <img :src="couponDetail.imgUrl" />
          <div class="voucher-con">
            <p class="title elli">
              {{ couponDetail.couponName }}
            </p>
            <p class="subtitle elli2">
              {{ couponDetail.couponPropaganda }}
            </p>
            <p class="subtitle">
              有效期：{{ couponDetail.termOfValidity }}
            </p>
            <span class="couponBtn" :class="{added:couponDetail.couponStatus===1}" @click="toggleReceive()">
              {{ couponDetail.couponStatus===1 ? "已领取" : "领取" }}
              <span v-if="couponDetail.couponStatus===1" class="icon icon-right"></span>
            </span>
          </div>
        </li>
      </ul>
      <div class="bottomBtn" @click="gotoVoucherlist()">
        <span class="icon icon-conpon"></span>
        <span>查看券包</span>
      </div>
    </div>
    <ReceiveCoupon v-if="showReceiveDialog" :coupon-detail="couponDetail"></ReceiveCoupon>
    <Footers v-if="iconList" :list="iconList" :active="0" :isladder="true" />
  </div>
</template>
<script>
import Vue from 'vue'
import Shopname from "@/components/index/headercon.vue"
import Footers from "@/components/myfooter"
import ReceiveCoupon from "@/components/ladderplan/receivecoupon.vue"
import insertCode from "@/utils/insertCode"
import { getImgUrl } from "@/utils/utils"
import LadderShopApi from "@/api/ladder/ladder-shop"
import UA from "@/utils/ua"
import { iconList } from "@/views/ladderplan/json.js"
import { Search , Icon ,DropdownMenu, DropdownItem ,Button ,Loading} from 'vant'
import loginUtils from "@/utils/login"
Vue.use(Search).use(Icon).use(DropdownMenu).use(DropdownItem).use(Button).use(Loading)
export default {
  name:"Ladpsearchlist",
  components:{
    Shopname,
    Footers,
    ReceiveCoupon
  },
  data(){
    return {
      ua:null,
      couponId:null,
      couponDetail:{},
      showReceiveDialog:false,
      shopId:null,
      iconList: null,
    }
  },
  watch:{
    showReceiveDialog:{
      handler:function(value){
        if(!value){
          this.getDetail()
        }
      }
    }
  },
  created(){
    this.showHead =
      !UA.isWechat &&
      !UA.isWechatWork &&
      !UA.isApp &&
      !UA.isIosQQ &&
      !UA.isAndroidQQ
    const url = new URL(location.href)
    this.couponId = url.searchParams.get("couponId")
    this.shopId = url.searchParams.get("shopId")
    this.ua = UA
    this.iconList = iconList
    this.getDetail()
    loginUtils.login(false, false, this.logined, false, false, "", "0")
  },
  methods:{
    logined(res) {
      this.isLogined = res && res.UserName > ""
      if (this.isLogined) {
        if (res.staffId && this.shopId == res.shopId) {
          this.iconList.manage.showLink = true
        } else {
          this.iconList.manage.showLink = false
        }
      }
    },
    insertCodeGoods(key,goodsId,source,url){
      let dcs_id = key+'_'+goodsId+'_'+source
      insertCode("goods_"+dcs_id,url)
    },
    getDetail(){
      LadderShopApi.resourceDetail({id:this.couponId}).then((res)=>{
        if(res.code) {
          this.$toast(res.message)
        }else {
          this.couponDetail=res.data
          this.couponDetail.imgUrl = getImgUrl(res.data.imgUrl, "goods")
          this.couponDetail.logoImg = getImgUrl(res.data.logoImg, "goods")
        }
      }).catch((err)=>{
        // console.log(err)
      })
    },
    govoucher(){
      // console.log(888)
    },
    toggleReceive(){
      if(this.couponDetail.couponStatus===1){
        this.$toast("在“我的-我的券包”查看优惠券")
        return false
      }
      //0：常规领取 1：跳转链接 2：填写表单
      if(this.couponDetail.receiveType===0){
        //0 直接领取
        this.receiveCoupon()
      }
      if(this.couponDetail.receiveType===1){
        //1：跳转链接
        this.$toast("请在“移云店”小程序中领取")
      }
      if(this.couponDetail.receiveType===2){
        //2：填写表单
        this.showReceiveDialog = true
      }
    },
    receiveCoupon(){
      let {id,receiveType} = this.couponDetail
      LadderShopApi.receiveCoupon({
        couponId:id,receiveType
      }).then((res)=>{
        // console.log(res)
        if(res.code===0){
          this.$toast("领取成功")
          this.getDetail()
        }else{
          this.$toast(res.message)
        }
      })
    },
    followShop(){
      let operateType = 1
      if(this.couponDetail.isFollow===1){
        operateType = 0
      }
      LadderShopApi.followShop({
        operateType,
        storeId:this.couponDetail.storeId
      }).then((res)=>{
        if(res.code===0){
          if(operateType===0){
            this.$toast("已取消关注")
          }
          if(operateType===1){
            this.$toast("已关注商家")
          }
          this.getDetail()
        }else{
          this.$toast(res.message)
        }
      })
    },
    gotoVoucherlist(){
      this.$router.push({
        path: "/ladderplan/voucherlist.html?shopId=" + this.shopId 
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "@/styles/minix.scss";
  $bgcolor:#f7f8f9;
  .voucher-detail{
    background: $bgcolor;
    padding-top:15px;
  }
  .elli {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .elli2{
    @include ellipsis($clamp: 2);
  }
  .padding-top-34{
    padding-top:34px;
  }
  .img-container{
    height:375px;
    width:375px;
    overflow: hidden;
  }
  .voucher-content{
    background: #fff;
    padding:10px 15px;
    position: relative;
    &.top{
      border-bottom:1px solid #e2e2e2;
    }
    .title{
      font-size: 17px;
      font-weight: 500;
      text-align: left;
      color: #333333;
      line-height: 24px;
      margin-bottom:6px;
      width:240px;
    }
    .subtitle{
      font-size: 14px;
      font-weight: 400;
      text-align: left;
      color: #999999;
      line-height: 20px;
      display: flex;
      align-items: center;
      width:240px;
      .icon-location {
        width: 13px;
        height: 13px;
        background: url(~@/assets/ladderplan/location_red.png) 0 0 no-repeat;
        background-size: contain;
        margin-right: 6px;
      }
      .content{
        flex: 1;
      }
    }
    .btn_follow{
      width: 107px;
      height: 31px;
      border: 1px solid #fec74d;
      border-radius: 17px;
      font-size: 14px;
      font-weight: 400;
      text-align: left;
      color: #ffac2a;
      line-height: 22px;
      position: absolute;
      right:10px;
      top:20px;
      display: flex;
      line-height:31px;
      .icon-collec{
        height:17px;
        width:17px;
        align-self: center;
        background: url("~@/assets/ladderplan/collec_1.png") no-repeat;
        background-size: contain;
        margin-left:14px;
        margin-right:8px;
      }
      &.followed{
        background:rgba( #e9b758,.5);
        border: none;
        color: #ffffff;
        .icon-collec{
          background: url("~@/assets/ladderplan/collec_added_1.png") no-repeat;
          background-size: contain;
        }
      }
    }
  }
  .couponTitle{
    height: 20px;
    font-size: 14px;
    font-weight: 400;
    text-align: left;
    color: #999999;
    line-height: 20px;
    margin:7px 15px;
  }
  .couponBtn{
    width: 36px;
    height: 100%;
    opacity: 0.63;
    background: #ff4430;
    font-size: 14px;
    padding:27px 12px 0 10px;
    position: absolute;
    right:0;
    top:0;
    font-weight: 400;
    text-align: left;
    color: #ffffff;
    line-height: 16px;
    border-left:1px dashed #fff;
    &.added{
      background: #E54242;
      padding-top:16px;
    }
    .icon-right{
      width:14px;
      height:14px;
      display: inline-block;
      background: url("~@/assets/ladderplan/right.png") no-repeat;
      background-size: contain;
    }
  }
  .voucher-item{
    list-style: none;
    position: relative;
    overflow: hidden;
    display:flex;
    font-size: 13px;
    line-height: 20px;
    padding:10px 12px;
    width:355px;
    margin:0 auto 10px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.08); 
    .voucher-con{
      flex:1
    }
    img{
      width:70px;
      height:70px;
      float:left;
      margin-right:18px;                                        
    }
    p.title{
      font-size: 15px;
      font-weight: 500;
      text-align: left;
      color: #666666;
      line-height: 21px;
      width:200px;
    }
    p.subtitle{
      font-size: 12px;
      font-weight: 400;
      text-align: left;
      color: #cccccc;
      line-height: 17px;
      width:200px;
      &.elli2{
        height:33px;
      }
    }
    .quanCon{
      display: flex;
      flex-wrap: wrap;
      .quan{
        padding:0px 7px;
        line-height: 13px;
        height:15px;
        margin: 4px 0 5px;
        border: 1px solid #ff8b7e;
        font-size: 10px;
        font-weight: 400;
        color: #ff8b7e;
        border-radius: 4px;
        position:relative;
        .circle{
          border:1px solid #ff8b7e;
          width:5px;
          height:5px;
          border-radius: 5px;
          position: absolute;
          top: 4px;
          border-left-color: transparent;
          border-bottom-color: transparent;
          background: #fff;
          transform: rotate(41deg);
          &.circle_left{
            left: -3px;
            transform: rotate(41deg);
          }
          &.circle_right{
            right:-3px;
            transform: rotate(-145deg);
          }
        }
      }
    }
    p.btn{
      border: 1px solid #fec74d;
      border-radius: 11px;
      font-size: 12px;
      font-weight: 400;
      text-align: left;
      color: #f5ae10;
      line-height: 17px;
      display: inline-block;
      padding:0 12px;
    }
    > .icon{
      position: absolute;
      width: 62px;
      height: 62px;
      top:22px;
      right:7px;
    }
    .notuse{
      background: url(~@/assets/ladderplan/notuse.png) 0 0 no-repeat;
      background-size: contain;
    }
    .used{
      background: url(~@/assets/ladderplan/used.png) 0 0 no-repeat;
      background-size: contain;
    }
  }
  .bottomBtn{
    width: 355px;
    height: 46px;
    font-size:17px;
    background: linear-gradient(138deg,#ffde3a 0%, #ffad09 97%);
    border-radius: 23px;
    margin:10px;
    display: flex;
    align-items: center;
    color:#fff;
    line-height:17px;
    .icon-conpon{
      width:17px;
      height:17px;
      margin:0 8px 0 130px;
      background: url("~@/assets/ladderplan/quanbao.png") no-repeat;
      background-size: contain;
    }
  }
</style>
<style lang="scss">
.icpinfo {
  margin-bottom: 68px;
}
</style>