import { getImgUrl } from "@/utils/utils"
export let iconList= {
  ladhome: {
    inactive: require("@/assets/ladderplan/home.png"),
    active: require("@/assets/ladderplan/home_active.png"),
    title: "首页",
    key: "ladhome",
    links: "",
    isactive: true,
    showLink: true,
    class: "ladhome"
  },
  shopindex: {
    inactive: require("@/assets/ladderplan/yundian.png"),
    active: require("@/assets/ladderplan/yundian_active.png"),
    title: "云店",
    key: "shopindex",
    links: "",
    isactive: false,
    showLink: true,
    class: "shopindex"
  },
  onlineshop: {
    inactive: require("@/assets/my/onlineShop.png"),
    active: require("@/assets/my/onlineShop-active.png"),
    title: "在线看店",
    key: "onlineshop",
    links: "",
    isactive: false,
    showLink: false,
    class: "onlineshop"
  },
  service: {
    inactive: require("@/assets/my/customerService.png"),
    active: require("@/assets/my/customerService-active.png"),
    title: "客服",
    key: "service",
    links: "",
    isactive: false,
    showLink: false,
    class: "service"
  },
  manage: {
    inactive: require("@/assets/my/management.png"),
    active: require("@/assets/my/management-active.png"),
    title: "店铺管理",
    key: "manage",
    links: "",
    isactive: false,
    showLink: false,
    class: "manage"
  },
  live: {
    inactive: require("@/assets/my/live.png"),
    active: require("@/assets/my/live-active.png"),
    title: "直播",
    key: "live",
    links: "",
    isactive: false,
    showLink: true,
    class: "live"
  },
  my: {
    inactive: require("@/assets/my/my.png"),
    active: require("@/assets/my/my-active.png"),
    title: "我的",
    key: "my",
    links: "",
    isactive: false,
    showLink: true,
    class: "my"
  }
}
export let imglist=[
  {
    "merchantId": 1000002,
    "province": "100",
    "imgSrc": "//img1.shop.10086.cn/fs/goods/fs_632c8a79e4b0fdd3b5a92172.png",//轮播图
    "voucherimg":"//img1.shop.10086.cn/fs/goods/fs_632c8f12e4b0fdd35ddd2f14.png",//关注
    "title": "中健健身·七天体验卡",
    "weight": 51,
    "id":174
  },
  {
    "merchantId": 1000064,
    "province": "100",
    "imgSrc": "//img1.shop.10086.cn/fs/goods/fs_632c8a57e4b0fdd36b797c2e.png",
    "voucherimg": "//img1.shop.10086.cn/fs/goods/fs_632c8efce4b0fdd36b797c33.png",
    "title": "兰兔轻食·美味特惠",
    "weight": 51,
    "id":172
  },
  {
    "merchantId": 1000064,
    "province": "100",
    "imgSrc": "//img1.shop.10086.cn/fs/goods/fs_632c89f6e4b0fdd35ddd2f10.png",
    "voucherimg": "//img1.shop.10086.cn/fs/goods/fs_632c8edee4b0fdd35ddd2f13.png",
    "title": "艾比嘉尔·九折券",
    "weight": 51,
    "id":173
  }
]

export default {
  iconList,
  imglist
}