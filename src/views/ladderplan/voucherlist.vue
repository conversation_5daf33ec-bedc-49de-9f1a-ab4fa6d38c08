<template>
  <div class="">
    <Shopname 
      v-if="showHead" 
      content="我的券包" 
      bg-color="#fff"
      :show-back="true"
    />
    <div
      v-if="myuser"
      class=""
      :class="{'padding-top-34':showHead}"
    > 
      <div class="button-cont">
        <div class="btn-item" :class="{active:couponType===1}" @click="changeConType(1)">
          优惠券
        </div>
        <div class="btn-item" :class="{active:couponType===2}" @click="changeConType(2)">
          已使用
        </div>
        <div class="btn-item" :class="{active:couponType===3}" @click="changeConType(3)">
          未使用
        </div>
      </div>
      <van-list
        v-model="loading"
        :finished="finished"
        class="vant-clearfix goods-list"
        :finished-text="couponUserRecordList&&couponUserRecordList.length>0?'我是有底线的':''"
      >
        <template v-if="couponUserRecordList&&couponUserRecordList.length>0">
          <div v-for="(item, key) in couponUserRecordList" :key="key" class="goods-item" @click="govoucher(item.couponId)">
            <a href="javascript:void(0)">
              <img :src="item.imgUrl" />
              <p class="title elli">
                {{ item.couponName }}
              </p>
              <p class="subtitle elli" :class="{margin9:couponType>1}">
                {{ item.couponPropaganda }}
              </p>
              <p class="address">
                <span class="content elli">有效期：{{ item.termOfValidity }}</span>
                <span v-if="couponType===1" class="btn" @click="govoucher(item.couponId)">
                  使用
                </span>
              </p>
            </a>
            <div v-if="couponType===3" class="icon notuse"></div>
            <div v-if="couponType===2" class="icon used"></div>
          </div>
        </template>
        <ul
          v-else-if="couponUserRecordList&&couponUserRecordList.length==0"
          class="goods-tab-no"
          :class="{'isApp':!showHead}"
        >
          <li class="tab-no-con">
            <div>
              <img src="~@/assets/ladderplan/empty.png" />
              <p>暂无优惠券</p>
            </div>
          </li>
        </ul>
        <div v-else></div>
      </van-list>
    </div>
  </div>
</template>

<script>
import UA from "@/utils/ua"
import Vue from "vue"
import Shopname from "@/components/index/headercon"
import loginUtils from "@/utils/login"
import { getImgUrl } from "@/utils/utils"
import { Button ,List } from 'vant'
import ladderApi from "@/api/ladder/ladder-discount"

Vue.use(List)
  .use(Button)
export default {
  name: "Voucherlist",
  components:{
    Shopname
  },
  data() {
    return {
      loading:false,
      finished: false,
      ua:null,
      myuser:null,
      shopId:null,
      couponType:1,
      couponUserRecordList:null,
      showHead:false,
      sendData:{
        couponStatus:1, //1已领取 2 已核销 3 已过期
        pageNum:1,
        pageSize:20
      }
    }
  },
  created() {
    this.showHead =
      !UA.isWechat &&
      !UA.isWechatWork &&
      !UA.isApp &&
      !UA.isIosQQ &&
      !UA.isAndroidQQ
    const url = new URL(location.href)
    this.sendData.shopId = url.searchParams.get("shopId")
    loginUtils.login(false, false, this.logined, false, false, this.autoLoginCb, "0")
  },
  mounted() {
  },
  methods: {
    logined(res) {
      this.myuser = res
      this.isLogined = res && res.UserName > ""
      if(this.isLogined){
        this.getList()
      }
    },
    myGetImg(src){
      return getImgUrl(src)
    },
    changeConType(type){
      this.couponType = type
      this.sendData.couponStatus = type
      this.getList()
    },
    govoucher(couponId){
      this.$router.push({
        path:"voucher.html?id="+couponId
      })
    },
    getList(){
      ladderApi.couponBagSearch(this.sendData).then(res=>{
        if(this.sendData.pageNum==1){
          this.couponUserRecordList = []
        }
        if(res.code) {
          this.$toast(res.message)
          this.loading = false
          this.finished = true
        }else {
          if(!(res.data&&res.data.couponUserRecordList&&res.data.couponUserRecordList.length>0)){
            this.loading = false
            this.finished = true
            return false
          }
          res.data.couponUserRecordList.forEach((value)=>{
            value.imgUrl = value.imgUrl ? getImgUrl(value.imgUrl,"goods"):""
            this.couponUserRecordList.push(value)
          })
          this.loading = false
          if(res.data.pages > this.sendData.pageNum){
            this.sendData.pageNum ++
          }else{
            this.finished = true
          }
        }
      })
    },
  }
}
</script>
<style>
body {
  background: #f7f8f9 !important;
  font-size: 12px;
}
</style>
<style lang="scss" scoped>
.padding-top-34{
  padding-top:34px;
}
.elli {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.button-cont{
  padding:13px 10px;
  display: flex;
  .btn-item{
    width: 81px;
    height: 27px;
    background: #ffffff;
    border: 0.5px solid #dcdcdc;
    border-radius: 14px;
    font-size: 14px;
    font-weight: 400;
    text-align: center;
    color: #333333;
    line-height: 27px;
    margin-right:10px;
    &.active{
      background: linear-gradient(138deg,#ffde3a 0%, #ffad09 97%);
      color:#fff;
    }
  }
}
.goods-list{
  display: flex;
  // flex-direction: row;
  flex-flow:row wrap;
  :deep(.van-list__finished-text){
    text-align: center;
    width: 100%!important;
  }
  .goods-item{
    list-style: none;
    position: relative;
    font-size: 13px;
    line-height: 20px;
    padding:13px 13px 13px 10px;
    width:355px;
    margin:0 auto 10px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.08); 
    img{
      width:80px;
      height:80px;
      float:left;
      margin-right:12px;
      border-radius: 9px;;
    }
    p.title{
      font-size: 14px;
      font-weight: 500;
      text-align: left;
      color: #333333;
      line-height: 20px;
      margin-bottom:4px;
      margin-top: 6px;
      width:200px;
    }
    p.subtitle{
      font-size: 13px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      text-align: left;
      color: #f5ae10;
      line-height: 18px;
      margin-bottom:5px;
      width:200px;
      &.margin9{
        margin-bottom:9px;
      }
    }
    p.address{
      display: flex;
      font-size: 12px;
      font-weight: 400;
      text-align: left;
      color: #cacaca;
      line-height: 13px;
      align-items: center;
      .content{
        flex: 1;
      }
      .btn{
        width: 50px;
        height: 22px;
        border: 1px solid #7496ff;
        border-radius: 14px;
        color:#7496ff;
        line-height: 22px;
        text-align: center;
      }
    }
    p.btn{
      border: 1px solid #fec74d;
      border-radius: 11px;
      font-size: 12px;
      font-weight: 400;
      text-align: left;
      color: #f5ae10;
      line-height: 17px;
      display: inline-block;
      padding:0 12px;
    }
    > .icon{
      position: absolute;
      width: 62px;
      height: 62px;
      top:22px;
      right:7px;
    }
    .notuse{
      background: rgba(256,256,256,.6) url(~@/assets/ladderplan/notuse.png) 0 0 no-repeat;
      background-size: contain;
    }
    .used{
      background: url(~@/assets/ladderplan/used.png) 0 0 no-repeat;
      background-size: contain;
    }
  }
}
.goods-tab-no{
  width: 100%;
  padding: 100px 0 0;
  height:calc(100vh - 166px);
  text-align: center;
  &.isApp{
    height:calc(100vh - 131px);
  }
  .tab-no-con {
    img{
      display: block;
      height: 120px;
      width: auto;
      margin: 0 auto 13px;
    }
    p{
      display: inline-block;
      line-height: 22.5px;
      font-size: 15px;
      font-weight: 400;
      color: #bfbfbf;
    }
  }
}
</style>
