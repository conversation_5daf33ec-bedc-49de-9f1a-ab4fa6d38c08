<template>
  <div>
    <Shopname v-if="showHead" :show-back="true" content="搜索" />
    <div class="searchlist" :class="{ 'padding-top-50': showHead }">
      <div class="search-bar">
        <div class="middle">
          <van-search
            v-model="searchvalue"
            shape="round"
            :left-icon="searchImg"
            placeholder="查找优惠"
            @search="search"
            @clear="search"
          />
        </div>
        <div class="icon-container" @click="goHome">
          取消
        </div>
      </div>
      <div class="shopcouponList-container">
        <ul
          v-if="showLoading"
          class="goods-tab-no"
          :class="{ isApp: !showHead }"
        >
          <li class="tab-no-con">
            <van-loading type="spinner" />
            <p>LOADING...</p>
          </li>
        </ul>
        <div
          v-else-if="shopCouponList && shopCouponList.length"
          class="goods-list-con"
        >
          <ul class="goods-list">
            <li v-for="(item, key) in shopCouponList" :key="key">
              <a
                href="javascript:void(0)"
                @click="gotoDetail(item.id)"
              >
                <img :src="item.imgUrl" />
                <p class="title elli">
                  {{ item.couponName }}
                </p>
                <p class="subtitle elli">
                  {{ item.couponPropaganda }}
                </p>
                <p class="address">
                  <span class="icon icon-location"></span>
                  <span class="content elli">
                    {{ item.storeAddress }}
                  </span>
                  <!-- <span class="distance">
                    2.2km
                  </span> -->
                </p>
              </a>
            </li>
          </ul>
        </div>
        <ul
          v-else-if="notForst"
          class="goods-tab-no"
          :class="{ isApp: !showHead, 'padding-top-50': showHead }"
        >
          <li class="tab-no-con">
            <div>
              <img src="~@/assets/ladderplan/empty.png" />
              <p>搜索的优惠不存在哦</p>
            </div>
          </li>
        </ul>
        <ul v-else class="goods-list-con" :class="{ isApp: !showHead }">
          <p v-if="keyWordList" class="goodsTitle">
            历史搜索
            <span class="icon_garbage" @click="clearKeyWord"></span>
          </p>
          <div class="btnCon">
            <div v-for="item in keyWordList" :key="item" class="btn elli" @click="fastSearch(item)">
              {{ item }}
            </div>
          </div>
        </ul>
        <div
          v-if="shopCouponList && shopCouponList.length"
          class="searchBottom"
        >
          <div class="hrStyle">
            <hr style="background-color:#DADADA;border:none;height:1px" />
          </div>
          <span class="hrContent">
            我是有底线的
          </span>
          <div class="hrStyle">
            <hr style="background-color:#DADADA;border:none;height:1px" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Vue from "vue"
import Shopname from "@/components/index/headercon.vue"
import { getImgUrl } from "@/utils/utils"
import LadderApi from "@/api/ladder/ladder"
import UA from "@/utils/ua"
import { Search, Icon, DropdownMenu, DropdownItem, Button, Loading } from "vant"
Vue.use(Search)
  .use(Icon)
  .use(DropdownMenu)
  .use(DropdownItem)
  .use(Button)
  .use(Loading)
export default {
  name: "Ladpsearchlist",
  components: {
    Shopname
  },
  data() {
    return {
      ua: null,
      value: 0,
      searchvalue: "",
      searchImg: require("@/assets/ladderplan/search.png"),
      shopCouponList: null,
      priceTitleClass: "focus",
      sortTitleClass: "focus",
      searchData: {
        shopId:null,
        keyWord: ""
      },
      keyWordList: ["优惠", "茶馆"],
      notForst: false,
      showLoading: false,
      showGoodsTab: false,
      showHead: false
    }
  },
  created() {
    this.showHead =
      !UA.isWechat &&
      !UA.isWechatWork &&
      !UA.isApp &&
      !UA.isIosQQ &&
      !UA.isAndroidQQ
    const url = new URL(location.href)
    this.searchData.shopId = url.searchParams.get('shopId')
    if(!this.searchData.shopId){
      this.$router.push({
        name: "nearby"
      })
    }
    this.ua = UA
    this.getSearchList()
  },
  methods: {
    homePageSearch() {
      if (this.searchData.keyWord) {
        this.notForst = true
      } else {
        this.notForst = false
      }
      this.showLoading = true
      LadderApi.homePageSearch(this.searchData).then(res => {
        if (this.searchData.keyWord) {
          this.updateSearchList(this.searchData.keyWord)
        }
        this.showLoading = false
        if (res.code) {
          this.$toast(res.message)
        } else {
          this.shopCouponList = []
          if (res.data && res.data.shopCouponList) {
            res.data.shopCouponList.forEach(item => {
              item.imgUrl = getImgUrl(item.imgUrl, "goods")
            })
            this.shopCouponList = res.data.shopCouponList
          }
        }
      })
    },
    fastSearch(keyWord){
      this.searchData.keyWord = keyWord
      this.searchvalue = keyWord
      this.homePageSearch()
    },
    updateSearchList(keyWord) {
      let keyWordStr = localStorage.getItem("keyWordList")
      if (keyWordStr) {
        let keyWordList = keyWordStr.split(",")
        if(keyWordList.indexOf(keyWord)===-1){
          keyWordList.push(keyWord)
          localStorage.setItem("keyWordList", keyWordList.join(","))
        }
      } else {
        localStorage.setItem("keyWordList", keyWord)
      }
      this.getSearchList()
    },
    getSearchList() {
      let keyWordStr = localStorage.getItem("keyWordList")
      if (keyWordStr) {
        this.keyWordList = keyWordStr.split(",")
      } else {
        this.keyWordList = null
      }
    },
    clearKeyWord(){
      this.keyWordList = null
      localStorage.removeItem("keyWordList")
    },
    search() {
      this.searchData.keyWord = this.searchvalue
      this.homePageSearch()
    },
    cancle(){
      this.shopCouponList =null
      this.notForst = false
    },
    goHome() {
      history.go(-1)
    },
    gotoDetail(couponId) {
      this.$router.push({
        path:
          "/ladderplan/detail.html?shopId=" +
          this.shopId +
          "&couponId=" +
          couponId
      })
    }
  }
}
</script>
<style lang="scss" scoped>
$bgcolor: #fff;
.searchlist {
  background: $bgcolor;
  padding-top: 15px;
}
.elli {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.search-bar {
  display: flex;
  padding: 0 20px;
  box-sizing: border-box;
  .icon-container {
    height: 36px;
    line-height: 36px;
    font-size: 16px;
    font-weight: 500;
    color: #5f5f5f;
    align-self: center;
  }
  .middle {
    flex: 1;
    margin-right: 9px;
    position: relative;
    .van-search {
      padding: 0;
      height: 36px;
      background: $bgcolor;
      .van-search__content {
        background: #fff;
        padding-left: 10px;
        border: 2px solid #ffd64d;
        box-sizing: border-box;
      }
      :deep(.van-icon) img {
        width: 20px;
        height: 20px;
        margin-top: 2px;
      }
    }
  }
}
.goods-list-con {
  padding: 20px;
  .goodsTitle {
    font-size: 16px;
    font-weight: 500;
    text-align: left;
    color: #4f4f4f;
    line-height: 22px;
    margin-bottom: 10px;
    .icon_garbage{
      width:23px;
      height: 23px;
      float:right;
      background: url(~@/assets/ladderplan/garbage.png) 0 0 no-repeat;
      background-size: contain;
    }
  }
  .btnCon {
    display: flex;
    flex-wrap: wrap;
    margin-top: 16px;
    .btn {
      height: 32px;
      background: #f6f6f6;
      border-radius: 16px;
      font-size: 16px;
      font-weight: 400;
      text-align: center;
      color: #727272;
      line-height: 32px;
      padding-left: 19px;
      padding-right: 19px;
      margin-right: 17px;
      margin-bottom: 10px;
    }
  }
}
.goods-list {
  display: flex;
  flex-flow: row wrap;
  li {
    list-style: none;
    position: relative;
    font-size: 13px;
    line-height: 20px;
    padding: 14px 0;
    width: 335px;
    margin: 0 auto;
    img {
      width: 80px;
      height: 80px;
      float: left;
      margin-right: 12px;
      border-radius: 9px;
    }
    p.title {
      font-size: 14px;
      font-weight: 500;
      text-align: left;
      color: #333333;
      line-height: 20px;
      margin-bottom: 4px;
      margin-top: 6px;
    }
    p.subtitle {
      font-size: 13px;
      font-weight: 400;
      text-align: left;
      color: #f5ae10;
      line-height: 18px;
      margin-bottom: 10px;
    }
    p.address {
      display: flex;
      font-size: 11px;
      font-weight: 400;
      text-align: left;
      color: #cacaca;
      line-height: 13px;
      margin-bottom: 10px;
      .icon-location {
        width: 13px;
        height: 13px;
        background: url(~@/assets/ladderplan/location_red.png) 0 0 no-repeat;
        background-size: contain;
        margin-right: 6px;
      }
      .content {
        flex: 1;
      }
      .distance {
        color: #cea04a;
      }
    }
    p.btn {
      border: 1px solid #fec74d;
      border-radius: 11px;
      font-size: 12px;
      font-weight: 400;
      text-align: left;
      color: #f5ae10;
      line-height: 17px;
      display: inline-block;
      padding: 0 12px;
    }
  }
}
.goods-tab-no {
  width: 100%;
  padding: 100px 0 0;
  height: calc(100vh - 166px);
  text-align: center;
  &.isApp {
    height: calc(100vh - 131px);
  }
  .tab-no-con {
    img {
      display: block;
      height: 120px;
      width: auto;
      margin: 0 auto 13px;
    }
    p {
      display: inline-block;
      line-height: 22.5px;
      font-size: 15px;
      font-weight: 400;
      color: #bfbfbf;
    }
  }
}
.hrStyle {
  width: 110px;
  hr {
    margin-top: 12px;
  }
}
.searchBottom {
  display: flex;
  padding: 10px 15px;
  box-sizing: border-box;
}
.padding-top-50 {
  padding-top: 50px;
  min-height: calc(100vh - 80px);
}
.hrContent {
  flex: 1;
  text-align: center;
  color: #dadada;
  font-size: 12px;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  line-height: 25px;
  display: inline-block;
  height: 25px;
}
</style>
