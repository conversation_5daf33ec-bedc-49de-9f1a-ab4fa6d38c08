export default[
  // {
  //   path: '/ladderplan/home.html',
  //   name: 'Ladphome',
  //   component: resolve => require(['./home.vue'], resolve),
  //   meta: {
  //     title: '云梯首页',
  //     keepAlive:true
  //   }
  // },
  // {
  //   path: '/ladderplan/searchlist.html',
  //   name: 'Ladpsearchlist',
  //   component: resolve => require(['./searchlist.vue'], resolve),
  //   meta: {
  //     title: '云梯搜索'
  //   }
  // },
  // {
  //   path: '/ladderplan/detail.html',
  //   name: 'Ladpdetail',
  //   component: resolve => require(['./detail.vue'], resolve),
  //   meta: {
  //     title: '云梯详情'
  //   }
  // },
  // {
  //   path: '/ladderplan/voucherlist.html',
  //   name: 'Ladpvoucherlist',
  //   component: resolve => require(['./voucherlist.vue'], resolve),
  //   meta: {
  //     title: '卡券列表'
  //   }
  // },
  // {
  //   path: '/ladderplan/voucher.html',
  //   name: 'Ladpvoucher',
  //   component: resolve => require(['./voucher.vue'], resolve),
  //   meta: {
  //     title: '卡券详情'
  //   }
  // },
  // {
  //   path: '/ladderplan/voucherreader.html',
  //   name: 'voucherreader',
  //   component: resolve => require(['./voucherreader.vue'], resolve),
  //   meta: {
  //     title: '卡券核销'
  //   }
  // }
]
