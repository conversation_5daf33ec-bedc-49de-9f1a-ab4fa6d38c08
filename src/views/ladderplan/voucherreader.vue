<template>
  <div>
    <Shopname
      v-if="showHead"
      content="我的券包"
      bg-color="#fff"
      :show-back="true"
    />
    <div class="voucher" :class="{ 'has-padding': showHead }">
      <div v-if="couponDetail">
        <voucherComponent :coupon-detail="couponDetail"></voucherComponent>
      </div>
    </div>
  </div>
</template>
<script>
import UA from "@/utils/ua"
import Vue from "vue"
import Shopname from "@/components/index/headercon.vue"
import LadderApi from "@/api/ladder/ladder-discount"
import loginUtils from "@/utils/login"
import voucherComponent from "@/components/ladderplan/voucher.vue"
export default Vue.extend({
  name: "Ladpvoucher",
  components: {
    Shopname,
    voucherComponent
  },
  data() {
    return {
      qrcodeValue: "7656789777777777",
      showHead: false,
      couponDetail: null,
      isLogined: false,
      secretKey:null
    }
  },
  created() {
    this.showHead =
      !UA.isWechat &&
      !UA.isWechatWork &&
      !UA.isApp &&
      !UA.isIosQQ &&
      !UA.isAndroidQQ
    const url = new URL(location.href)
    let encryptKey = url.searchParams.get('encryptKey')
    this.secretKey = encryptKey ? encryptKey.replace(/\s/g,'+') : ''
    loginUtils.login(true, true, this.logined, false, false, this.autoLoginCb, "0")
  },
  methods: {
    logined(res) {
      this.isLogined = res && res.UserName > ""
      if (this.isLogined) {
        this.writeOffResource()
      }
    }, 
    writeOffResource(){
      LadderApi.writeOffResource({ secretKey: this.secretKey })
        .then(res => {
          // console.log(res)
          if (res && res.code) {
            this.$toast(res.message)
          } else {
            this.getDetail(res.data)
          }
        })
        .catch(err => {
          // console.log(err)
        })
    },
    getDetail({id,mobile}) {
      LadderApi.writeOffDetail({ id: id ,mobile:mobile})
        .then(res => {
          // console.log(res)
          if (res && res.code) {
            this.$toast(res.message)
          } else {
            this.couponDetail = res.data
          }
        })
        .catch(err => {
          // console.log(err)
        })
    }
  }
})
</script>
<style scoped lang="scss">
.has-padding {
  padding-top: 64px;
}
.voucher {
  text-align: center;
  min-height: calc(100vh - 114px);
  background: #f7f8f9;
  p {
    margin-bottom: 20px;
  }
  .qrcode {
    width: 150px;
    height: 150px;
    margin: 0 auto 10px;
  }
}
</style>
