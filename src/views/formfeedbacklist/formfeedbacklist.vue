<template>
  <div class="formfeedback">
    <header-nav :title="data.title"></header-nav>
    <div class="couponMain">
      <div v-if="data.hasrole" class="search-bar">
        <div class="search-picker" @click="showPickker()">
          {{ searchName.text }}
          <Icon name="arrow-down" />
        </div>
        <Search 
          v-model="keyWords" 
          :placeholder="searchNameText"
          input-align="left"
        />
        <div class="search-button" @click="search">
          搜索
        </div>
      </div>
      <List
        v-if="data.tabMain && data.tabMain.length"
        v-model="data.loading"
        :finished="data.finished"
        :finished-text="data.nodataMessage"
        class="formfeedback-list ul"
        @load="intentOrderList"
      >
        <div v-for="it in data.tabMain" :key="it.adp_id">
          <div class="form-item" @click="goDetail(it.act_id,it.adp_id)">
            <div class="flex-left">
              <p class="time btm5">
                <span class="w80 breakAll">{{ parseTime(it.act_create_time) }} </span>
              </p>
              <p class="activetyname btm5 breakAll">
                {{ it.act_name }}
              </p>
              <p class="activetyname-id btm5">
                <span class="w80">ID:{{ it.act_id }}</span>
              </p>
              <p class="formname btm5">
                {{ it.adp_name }}
              </p>
              <p class="formname-id">
                <span class="w80">ID:{{ it.adp_id }}</span>
              </p>
            </div>
            <div class="flex-right">
              <p class="number">
                {{ it.adp_submit_total }}
              </p>
              <p>表单反馈数</p>
            </div>
          </div>
        </div>
      </List>
      <ul v-else-if="data.nodataMessage" class="formfeedback-tab-no ul">
        <li class="tab-no-con li">
          <div>
            <img src="~@/assets/index_img/ice_tab_no.png" />
            <p>
              {{ data.nodataMessage }}
            </p>
          </div>
        </li>
      </ul>
    </div>
    <Popup
      v-model="showpicker"
      round
      position="bottom"
      class="name-type-picker"
    >
      <Picker
        show-toolbar
        :columns="nametypecolumns"
        :default-index="0"
        title="请选择"
        @cancel="cancel"
        @confirm="nametypeconfirm"
      />
    </Popup>
    <Footer />
  </div>
</template>

<script setup>
import headerNav from '@/views/my/components/headerNav'
import loginUtils from '@/utils/login'
import formfeedbackApi from '@/api/formfeedback'
import { iosInputHandle } from '@/utils/ioscompatible'
import {
  Toast,
  Field,
  Button,
  Popup,
  Cell,
  List,
  Picker,
  Icon,
  Search 
} from 'vant'
import 'vant/lib/index.css'
import Vue, { onMounted,ref, reactive, getCurrentInstance, computed } from 'vue'
import { setScrollTop,parseTime } from '@/utils/utils'
//RO 已接单 SS 已完成 CL 已取消
let data = reactive({
  title: '表单反馈',
  user: null,
  tabMain: [],
  sendData: {
    shopId: null,
    page: 1,
    pageSize: 20,
  },
  nodataMessage: '',
  hasrole: true,
  loading: false,
  finished: false,
  shopId: null
})
let logined = (res) => {
  data.user = res
  if (data.user && res.shopId) {
    data.sendData.shopId = parseInt(res.shopId)
    intentOrderList()
    data.hasrole = true
  } else {
    data.hasrole = false //游客看不了
    data.tabMain = []
    data.nodataMessage = '无权查看，请去自己的店铺'
  }
}

let intentOrderList = (value) => {
  if (!data.hasrole) {
    return false
  }
  data.loading = true
  data.sendData.titleName = keyWords.value
  data.sendData.titleType = searchName.value.key
  formfeedbackApi.getFromStatisticInfo(data.sendData).then((res) => {
    if (res.code == 0) {
      if (data.sendData.page == 1) {
        data.tabMain = []
      }
      data.tabMain = data.tabMain.concat(res.data.rows)
      data.loading = false
      if (data.tabMain && data.tabMain.length == 0) {
        if (value != 'search') {
          data.nodataMessage = '暂无数据'
        } else {
          data.nodataMessage = '暂未查到符合条件的活动'
        }
        data.finished = true
        return false
      }
      if (res.data.count > data.sendData.page*data.sendData.pageSize) {
        data.sendData.page++
        data.finished = false
      } else {
        data.nodataMessage = '没有更多了'
        data.finished = true
      }
    } else {
      Toast(res.message)
    }
  })
}
// 搜索类型选择
let showpicker = ref(false)
// 标题类型(1.活动标题2.板块标题）
let nametypecolumns = ref([ 
  { key: 1, text: "按活动" },
  { key: 2, text: "按表单" }
])
let searchName = ref(nametypecolumns.value[0])
let searchNameText = computed(()=>{
  let text = searchName.value.text ?searchName.value.text.slice(1,) : "活动"
  return "搜索" + text + "名称"
})
let showPickker = () => {
  showpicker.value = true
  setScrollTop()
}
let nametypeconfirm = (res) =>{
  searchName.value = res
  cancel()
}
let cancel = () =>{
  showpicker.value = false
}

// 搜索
let keyWords = ref("")
let search = (value) => {
  data.sendData.page = 1
  intentOrderList("search")
}

//去详情页
let goDetail = (actId,adpId) =>{
  if(!proxy){
    return false
  }
  proxy.$router.push({
    path:"/formfeedback/details.html",
    query: { actId,adpId }
  })
}
let proxy = null
onMounted(() => {
  const url = new URL(location.href)
  data.shopId = url.searchParams.get('shopId')
  const getVueInstance = getCurrentInstance()
  proxy = getVueInstance ? getVueInstance.proxy : null
  if (proxy) {
    proxy.$store.commit('SET_SHOPID', data.shopId)
  }
  iosInputHandle()
  loginUtils.login(true, true, logined, false, false, '', null, 5)
})
</script>
<style>
.formfeedback .van-tabs__wrap {
  background: #fff;
}
.formfeedback .van-tabs__nav--line {
  width: 90%;
}
.header-nav__title {
  font-size: 18.75px !important;
}
</style>
<style lang="scss" scoped>
ul {
  li {
    margin: 0;
    padding: 0;
    list-style: none;
  }
}
.formfeedback {
  // min-height: 101vh;
  min-height: calc(101vh - 87px);
  background: #f6f5f6;
  color: #323233;
  .nav {
    padding: 0;
    background: #ffffff;
    .van-search {
      width: 355px;
      height: 36px;
      background: #f7f7f7;
      border-radius: 21px;
      margin: 0 auto;
      .van-search__content {
        background: none;
        padding-left: 0;
      }
    }
  }
  .searchbt {
    position: absolute;
    right: 10px;
    top: 12px;
    z-index: 1;
    border-left: 2px solid #979797;
    padding-left: 5px;
  }
  .couponMain {
    position: relative;
    background: #f6f5f6;
    padding-top:15px;
    .form-item{
      display: flex;
      padding:0 15px 0;
      margin:0 10px 10px;
      background: #fff;
      border-radius: 10px;
      .flex-left{
        flex: 1;
        padding:5px 0 20px 0;
      }
      .flex-right{
        width:100px;
        border-left:1px solid #ebedf0;
        display: flex;
        flex-flow: column;
        align-items:center;
        justify-content:center;
        font-size: 14px;
        font-weight: 400;
        .number{
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 5px;
        }
      }
      .time{
        font-size:12px;
        height: 20px;
        line-height: 20px;
        color:#999;
        border-bottom: 1px solid #ebedf0;
      }
      .activetyname{
        font-size:16px;
        font-weight: bold;
        margin-top: 10px;
      }
      .activetyname-id{
        font-size:12px;
        font-weight: 400;
        margin-top: 8px;
      }
      .formname{
        font-size: 12px;
        margin-top: 20px;
        color:#666;
        line-height: 15px;
        padding-right:5px;
      }
      .formname-id{
        font-size: 10px;
        color:#666;
      }
      .btm5{
        margin-bottom: 5px;
      }
    }
    .search-bar{
      display: flex;
    }
  }
  
}
.tab-no-con {
  background: none;
  text-align: center;
  padding:200px 0 0;
  p {
    padding-top: 6px;
    font-size: 14px;
  }
}
:deep(.van-cell::after){
  border:none;
}
.search-bar{
  padding:0 10px 10px;
  .search-picker{
    font-size:14px;
    width:66px;
    line-height: 30px;
  }
  .van-search{
    padding:0;
    height:30px;
    background: transparent;
    flex:1;
    .van-search__content{
      background-color: #fff;
      border-radius: 15px;
      font-size: 12px;
    }
  }
  .search-button{
    height: 30px!important;
    line-height: 30px;
    color:#fff;
    background: #149ad4;
    width:60px;
    text-align: center;
    border-radius: 15px;
    margin-left: 5px;
    font-size: 14px;
  }
}
.breakAll {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width:220px;
}
.name-type-picker{
  :deep(.van-picker-column){
    border-top:1px solid #333;
    margin:0 10px;
  }
  :deep(.van-picker__confirm),:deep(.van-picker__cancel){
    color:#1ba9f0
  }
}

</style>
