<template>
  <div class="intent">
    <header-nav :title="data.title"></header-nav>
    <div 
      v-if="data.hasrole"
      class="filter-bar" 
      @click="openSearchDialog"
    >
      <span>
        <Icon
          name="filter-o"
          class="searchbt"
          size="16"
        />
        筛选项
      </span>
      <Icon
        v-if="data.hasrole"
        name="arrow"
        class="arrow-grey"
      />
    </div>
    <div class="couponMain-details">
      <List
        v-if="data.tabMain && data.tabMain.length"
        v-model="data.loading"
        :finished="data.finished"
        :finished-text="data.nodataMessage"
        class="formfeedback-list ul"
        @load="getIntentDetail()"
      >
        <div v-for="(it,index) in data.tabMain" :key="it.id">
          <div class="form-item">
            <div class="flex-left">
              <div class="item-title" @click="toogle(it,index)">
                <span>ID：{{ it.id }}</span>
                <span class="w80">提交时间：{{ parseTime(it.create_time) }} </span>
                <span 
                  class="iconfont icon-arrow-down-circled"
                  :class="{'arrow-down':!it.showAll}"
                ></span>
                <div v-if="it.view_status=='0'" class="message-tip">
                  <span class="circle bule"></span>
                  <span>新消息</span>
                </div>
                <div v-else class="message-tip">
                  <span class="circle grey"></span>
                  <span>已查看</span>
                </div>
                <Icon
                  v-if="it.mark_status=='1'"
                  name="star"
                  class="icon-star"
                  size="12"
                />
              </div>
              <template v-if="it.showAll">
                <div v-if="it.comment_content" class="remark-content">
                  <p>备注：</p>
                  <p>{{ it.comment_content }}</p>
                  <p>{{ parseTime(it.comment_time) }}</p>
                </div>
                <div v-if="it.referrer_mobile" class="reference">
                  <p>分享人手机号：{{ getPassPhone(it.referrer_mobile) }}</p>
                  <p>分享人工号：{{ it.referrer_num }}</p>
                </div>
                <ul class="question-list">
                  <li v-for="(item,key) in it.custom" :key="item.question">
                    <div class="question">
                      {{ key+1 }}、{{ item.question }}
                    </div>
                    <div class="answer">
                      {{ item.answer ? item.answer : "（空）" }}
                    </div>
                  </li>
                </ul>
                <div class="operate">
                  <div 
                    v-if="it.mark_status=='1'" 
                    class="btn-group mark is-marked" 
                    @click="markerState(it)"
                  >
                    <p class="icon">
                      <Icon
                        name="star"
                        class="arrow-grey"
                      />
                    </p>
                    <p>标记</p>
                  </div>
                  <div v-else class="btn-group mark" @click="markerState(it)">
                    <p class="icon">
                      <Icon
                        name="star-o"
                        class="arrow-grey"
                      />
                    </p>
                    <p>标记</p>
                  </div>
                  <div class="btn-group remarks" @click="openSetRemark(it)">
                    <p class="icon">
                      <Icon
                        name="records"
                        class="arrow-grey"
                      />
                    </p>
                    <p>{{ it.comment_content ? "修改备注" : "添加备注" }}</p>
                  </div>
                  <div class="btn-group retract" @click="toogle(it,index)">
                    <p class="icon">
                      <!-- <Icon
                        name="arrow"
                        class="icon-arrow arrow-grey"
                      /> -->
                      <span 
                        class="iconfont icon-arrow-down-circled arrow-grey"
                      ></span>
                    </p>
                    <p>
                      收起
                    </p>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </List>
      <ul v-else-if="data.nodataMessage" class="formfeedback-tab-no ul">
        <li class="tab-no-con li">
          <div>
            <img src="~@/assets/index_img/ice_tab_no.png" />
            <p>
              {{ data.nodataMessage }}
            </p>
          </div>
        </li>
      </ul>
    </div>
    <!--筛选 弹框 -->
    <ActionSheet
      v-model="searchData.showSearch"
      :round="true"
      get-container="body"
    >
      <div class="nav">
        <Form ref="searchForm" :label-width="100">
          <div class="search-form">
            筛选
            <span class="cancle" @click="searchData.showSearch = false">×</span>
          </div>
          <div class="content">
            <p class="date-content-title">
              选择时间
            </p>
            <div class="date-content">
              <Field
                readonly
                clickable
                name="startTime"
                :value="searchData.startTimeName"
                right-icon="notes-o"
                placeholder="开始时间"
                @click.prevent="openSetTime('start')"
              />
              <div class="date-mid">
                至
              </div>
              <Field
                readonly
                clickable
                name="endTime"
                :value="searchData.endTimeName"
                colon
                right-icon="notes-o"
                placeholder="结束时间"
                @click.prevent="openSetTime('end')"
              />
              <div class="reset-btn" @click="reset">
                <Icon
                  name="replay"
                  class=""
                  size="18"
                />
              </div>
            </div>
            <p class="date-content-title">
              是否标记
            </p>
            <div class="mark-content">
              <div 
                v-for="markItem in MARKSTATUS" 
                :key="markItem.key" 
                class="mark-item"
                :class="{'active':markItem.key==currentMark.key}"
                @click="markChange(markItem)"
              >
                {{ markItem.value }}
              </div>
            </div>
          </div>
          <div class="search-btn" @click="submitForm('search')">
            确定
          </div>
        </Form>
      </div>
    </ActionSheet>
    <!--时间选择器弹框 -->
    <ActionSheet v-model="searchData.showpicker" closeable :round="false">
      <div class="nav">
        <DatetimePicker
          v-if="searchData.showPickerStart"
          v-model="searchData.startTime"
          type="date"
          title="选择时间"
          :min-date="searchData.minDate"
          :max-date="searchData.maxDate"
          @confirm="confirmstart"
          @cancel="
            searchData.showPickerStart = false
            searchData.showpicker = false
          "
        />
        <DatetimePicker
          v-if="searchData.showPickerEnd"
          v-model="searchData.endTime"
          type="date"
          title="选择时间"
          :min-date="searchData.minDate"
          :max-date="searchData.maxDate"
          @confirm="confirmEnd"
          @cancel="
            searchData.showPickerEnd = false
            searchData.showpicker = false
          "
        />
      </div>
    </ActionSheet>
    <!--添加备注弹框 -->
    <ActionSheet
      v-model="showMark"
      closeable
      :round="true"
      get-container="body"
    >
      <div class="nav">
        <Form ref="searchForm1" :label-width="100" @submit="setRemark">
          <div style="display: flex">
            <Button
              plain
              block
              type="info"
              style="margin-right: 5rem"
              native-type="button"
              color="linear-gradient(to right, #60C7FF, #8374FE)"
              @click="showMark = false"
            >
              取消
            </Button>
            <Button
              plain
              block
              type="info"
              native-type="submit"
              color="linear-gradient(to right, #60C7FF, #8374FE)"
            >
              确定
            </Button>
          </div>
          <Field
            v-model="mark"
            rows="2"
            type="textarea"
            maxlength="50"
            class="mark-textarea"
            placeholder="请输入备注"
            show-word-limit
            clearable
          />
        </Form>
      </div>
    </ActionSheet>
    <Footer />
  </div>
</template>

<script setup>
import headerNav from '@/views/my/components/headerNav'
import loginUtils from '@/utils/login'
import formfeedbackApi from '@/api/formfeedback'
import {
  Toast,
  Field,
  Form,
  List,
  Button,
  DatetimePicker,
  ActionSheet,
  Icon,
  Cell,
  Calendar
} from 'vant'
import { mapGetters } from 'vuex'
import 'vant/lib/index.css'
import { iosInputHandle } from '@/utils/ioscompatible'
import Vue, { reactive, ref , onMounted, getCurrentInstance, computed } from 'vue'
import { setScrollTop, getPassPhone,parseTime,getAllSearchParamsArray } from '@/utils/utils'
let data = reactive({
  title: '表单反馈',
  info: '',
  orderDetail: null,
  parame: null,
  nodataMessage: '',
  hasrole: true,
  loading: false,
  finished: false,
  user: null,
  tabMain: [],
  sendData: {
    shopId: null,
    status: null,
    startTime: null,
    endTime: null,
    makersType:null,
    page: 1,
    pageSize: 20,
  },
  count:0 //表单反馈的总数
})

let logined = (res) => {
  data.user = res
  if (data.user && res.shopId) {
    data.sendData.shopId = res.shopId
    getIntentDetail()
    data.hasrole = true
  } else {
    data.hasrole = false //游客看不了
    data.tabMain = []
    data.nodataMessage = '无权查看，请去自己的店铺'
  }
}
let getIntentDetail = (value) => {
  if (!data.hasrole) {
    return false
  }
  data.loading = true
  data.sendData = {...data.parame,...data.sendData}
  formfeedbackApi.formFeedbackDetails(data.sendData).then((res) => {
    if (res.code == 0) {
      if (data.sendData.page == 1) {
        data.tabMain = []
      }
      res.data.rows.forEach((item)=>{
        if(data.user.RuleId == "2") {//2是店长，1是店员(1已读，0未读)
          item.view_status = item.shopkeeper_view_status
        }else{
          item.view_status = item.assistant_view_status
        }
        item.showAll = false
        item.custom = typeof(item.custom)=="string" ? JSON.parse(item.custom) : item.custom
      })
      data.tabMain = data.tabMain.concat(res.data.rows)
      data.count = res.data.count
      data.loading = false
      if (data.tabMain && data.tabMain.length == 0) {
        if (value != 'search') {
          data.nodataMessage = '没有数据'
        } else {
          data.nodataMessage = '暂未查到符合条件的表单'
        }
        data.finished = true
        return false
      }
      if (res.data.total_page > data.sendData.page) {
        data.sendData.page++
        data.finished = false
      } else {
        data.nodataMessage = '没有更多了'
        data.finished = true
      }
    } else {
      Toast(res.message)
    }
  })
}
// 展示逻辑
// const showAll = ref(false)
function toogle(it,index){
  it.showAll = !it.showAll
  setViewStatus(it)
}
// 查看状态
function setViewStatus(it){
  if(it.view_status=="1"){
    return false
  }
  let sendData = {
    shopId : data.sendData.shopId,
    formId : it.id,
  }
  formfeedbackApi.setViewStatus(sendData).then((res)=>{
    if(res.code){
      Toast(res.message)
    }else{
      it.view_status = 1
      // 查看消息状态：1已读，0未读
    }
  })
}
// 标记/未标记状态接口
function markerState(it){
  let sendData = {
    shopId : data.sendData.shopId,
    formId : it.id,
    //1已标记，0未标记
    makersType:it.mark_status == 1 ? 0 : 1
  }
  formfeedbackApi.markerState(sendData).then((res)=>{
    if(res.code){
      Toast(res.message)
    }else{
      it.mark_status = sendData.makersType
    }
  })
}
//添加修改备注
const mark = ref("")
const currentItem = reactive({
  formId:null
})
const showMark = ref(false)
function openSetRemark(it){
  currentItem.value  = it
  mark.value = it.comment_content
  currentItem.formId = it.id
  showMark.value = true
  setScrollTop()
}
// 添加备注接口
function setRemark(){
  let sendData = {
    shopId : data.sendData.shopId,
    formId : currentItem.formId,
    markContent:mark.value
  }
  formfeedbackApi.setRemark(sendData).then((res)=>{
    if(res.code){
      Toast(res.message)
    }else{
      currentItem.value.comment_content = mark.value
      currentItem.value.comment_time = res.data.comment_time
      showMark.value = false
    }
  })
}
//搜索框
let searchData = reactive({
  showSearch:false,//是否展示搜索面板
  startTimeName:"",//开始时间
  endTimeName:"",//结束时间
  startTime:new Date(),
  endTime:new Date(),
  showpicker:false,//选择时间面板
  minDate: new Date(2020, 0, 1),
  maxDate: new Date(2120, 0, 1),
  showPickerEnd:false,
  showPickerStart:false
})

let openSearchDialog = ()=>{
  searchData.showSearch = true
  setScrollTop()
}

// 打开日期选择器
let openSetTime = (type)=>{
  searchData.showpicker = true
  if (type == 'start') {
    searchData.showPickerStart = true
    searchData.showPickerEnd = false
  } else {
    searchData.showPickerEnd = true
    searchData.showPickerStart = false
  }
  setScrollTop()
}
// 日期选择确认
let confirmstart = (time) => {
  searchData.startTime = time
  searchData.startTimeName = parseTime(time, '{y}-{m}-{d}')
  data.sendData.startTime = parseTime(
    time,
    '{y}-{m}-{d} {h}:{i}:{s}'
  )
  searchData.showPickerStart = false
  searchData.showpicker = false
}
let confirmEnd = (time) => {
  searchData.endTime = time
  let setData = new Date(new Date(time).getTime() + 24 * 60 * 60 * 1000 - 1)
  searchData.endTimeName = parseTime(setData, '{y}-{m}-{d}')
  data.sendData.endTime = parseTime(
    setData,
    '{y}-{m}-{d} {h}:{i}:{s}'
  )
  searchData.showPickerEnd = false
  searchData.showpicker = false
}
let reset = () => {
  searchData.startTimeName = ""
  searchData.endTimeName = ""
  searchData.startTime = new Date()
  searchData.endTime = new Date()
  data.sendData.startTime = ""
  data.sendData.endTime = ""
}

let MARKSTATUS = ref([
  {key:null,value:"全部"},
  {key:1,value:"已标记"},
  {key:0,value:"未标记"}
])
let currentMark = ref({key:null,value:"全部"})
function markChange(markItem){
  currentMark.value = markItem
  data.sendData.makersType = markItem.key
}

function submitForm(value){
  if(data.sendData.startTime && data.sendData.endTime && new Date(data.sendData.startTime)>new Date(data.sendData.endTime)){
    Toast("截止日期不能早于起始日期")
    return false
  }
  searchData.showSearch = false
  data.sendData.page = 1
  data.nodataMessage = ''
  data.finished = false
  getIntentDetail(value)
}

onMounted(() => {
  data.parame = getAllSearchParamsArray(location.href)
  //没有订单号，跳到附近店铺
  // const getVueInstance = getCurrentInstance()
  // const proxy = getVueInstance ? getVueInstance.proxy : null
  // let myuser = proxy ? proxy.$store.getters.user : null
  iosInputHandle()
  window.scrollTo(0, 1)
  loginUtils.login(true, true, logined, false, false, '', null, 5)
})
</script>
<style>
body {
  background: #f9fafc;
}
.header-nav {
  height: 54px;
}
.header-nav__title {
  font-size: 18.75px !important;
}
</style>
<style lang="scss" scoped>
.intent{
  background: #f5f5f5;
  min-height: calc(100vh - 80px);
}

.filter-bar{
  height:30px;
  background: #fff;
  margin:10px;
  line-height: 30px;
  color:#97c2fe;
  font-weight: bold;
  padding:0 15px;
  font-size: 14px;
  position: relative;
  border-radius: 15px;
  .arrow-grey{
    width:10px;
    position: absolute;
    top:7px;
    right: 15px;
    color:#ccc;
  }
}
.form-item {
  margin: 0 10px 7px;
  background: #fff;
  border-radius: 10px;
  padding:10px 10px 5px;
  .item-title{
    display: flex;
    font-size: 14px;
    height:50px;
    line-height: 50px;
    padding-left: 10px;
    position: relative;
    .message-tip{
      position: absolute;
      top:-24px;
      left:-4px;
      font-size: 10px;
      display: flex;
      align-items: center;
      .circle{
        display: block;
        width:10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 4px;
        &.bule{
          background: #03a7ef;
        }
        &.grey{
          background: #d7d6d6;
        }
      }
    }
    >span:first-child{
      margin-right:10px;
      font-weight: bold;
    }
    .icon-arrow-down-circled{
      position: absolute;
      right:10px;
      transform: rotate(180deg);
      font-weight: bold;
      font-size: 16px;
      text-align: center;
      color:#03a7ef;
      &.arrow-down {
        transform: rotate(0deg);
        color:#4c4c4c;
      }
    }
  }
  .arrow-grey.icon-arrow-down-circled{
    text-align: center;
    display: block;
    font-size:18px;
    font-weight: bold;
    color:#4c4c4c;
    transform: rotate(180deg)!important;
  }
  .arrow-grey{
    font-size:18px;
  }
  .reference,.remark-content{
    font-size:10px;
    font-weight: 400;
    line-height: 24px;
    border-top:1px solid #f2f2f1;
    padding:5px 10px;
  }
  .remark-content{
    background: #f2f2f1;
    p:last-child{
      text-align: right;
      color:#aaa
    }
  }
  .question-list{
    border-top:1px solid #f2f2f1;
    padding: 10px 10px;
    li{
      margin-bottom: 20px;
      font-size: 16px;
      line-height: 25px;
      .question{
        font-weight: bold;
        margin-bottom: 5px;
      }
    }
  }
}
.tab-no-con {
  background: none;
  text-align: center;
  padding: 200px 0 0;
  p {
    padding-top: 6px;
    font-size: 14px;
  }
}
.mark-textarea{
    height: 140px;
    border-top: 1px solid #eee;
    padding:10px;
    :deep(.van-cell__value){
      border:1px solid red;
      border-radius: 10px;
      padding:10px;
      textarea{
        min-height: 80px;
      }
    }
}
//标记操作区
.operate{
  border-top:1px solid #f2f2f1;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding:7px 40px 0;
  .btn-group{
    text-align: center;
    display: flex;
    flex-direction: column;
    height: 36px;
    justify-content: space-between;
    font-size: 12px;
  }
  .is-marked{
    color:#facd00;
  }
}
// 表单样式
.content{
  padding:10px;
}
.date-content-title{
  padding:20px 0;
  font-weight: bold;
  &:first-child{
    border-top:1px solid #ccc;
  }
}
.date-content{
  display: flex;
  flex-direction: row;
  align-items: center;
  padding:0 20px;
  :deep(.van-cell){
    border-radius: 18px;
    background: #f6f6f5;
    height:30px;
    line-height: 30px;
    padding:0 15px;
  }
  .date-mid{
    padding:0 5px;
  }
  .reset-btn{
    margin-left: 10px;
  }
}
.mark-content{
  display: flex;
  padding-left: 20px;
}
.mark-item,.search-btn{
  border-radius: 15px;
  background: #f6f6f5;
  height:30px;
  line-height: 30px;
  width:80px;
  text-align: center;
  margin-right: 20px;
  box-sizing: border-box;
  font-size: 14px;
  &.active{
    color:#fff;
    background: #81d3f7;
    border:1px solid #0ba5f0;
  }
}
.search-form{
  text-align: center;
  padding:10px 10px 0;
  font-size: 16px;
  font-weight: bold;
  position: relative;
  .cancle{
    font-size: 30px;
    position: absolute;
    right:20px;
    top:4px;
    font-weight: normal;
  }
}
.content{
  font-size: 14px;
}
.search-btn{
  color:#fff;
  background:#0ba5f0;
  margin:20px auto;
  width: 100px;
}
.icon-star{
  color:#facd00;
  position: absolute;
  right: 0;
  top: -3px;
}
</style>

