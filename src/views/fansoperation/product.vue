<template>
  <div class="index channel">
    <div v-if="productData.loading" class="loading-area">
      <Loading />
    </div>
    <div v-if="productData.noAuthority" id="norights">
      <h3 class="notice-title">
        页面访问提示
      </h3>
      <div class="notice-detail">
        <div class="notice-area">
          <img src="@/assets/home/<USER>">
          <p>暂无权限访问</p>
        </div>
        <div class="result-btn" @click="goShop">
          逛逛店铺
        </div>
        <div class="result-btn" @click="close">
          关闭
        </div>
      </div>
    </div>
    <LoginDialog
      :islogin="autoLoginObj.is4gLogined"
      :is-force-login="true"
      :isloginfn="logined"
      :is-auto-login="autoLoginObj.autoLogin"
      :show-cancle="false"
    />
  </div>
</template>

<script setup>
import { Loading , Dialog } from "vant"
import { reactive, onMounted,getCurrentInstance } from "vue"
import LoginDialog from "@/components/login/index.vue"
import UA from "@/utils/ua"
import fansoperationApi from "@/api/fansoperation.js"
import loginUtils from "@/utils/login"
import leadeonLoader from "@/utils/leadeonloader"
import { getAllSearchParamsArray, getUrl } from "@/utils/utils"
const productData = reactive({
  loading: true,
  noAuthority:false,
  proxy:null
})
let parame = {}

function goShop(){
  if(productData.proxy){
    productData.proxy.$router.push({
      path:'/index.html',query:{shopId:parame.shopId}
    })
  }
}
onMounted(async()=> {
  const getVueInstance = getCurrentInstance()
  productData.proxy = getVueInstance ? getVueInstance.proxy : null
  parame = getAllSearchParamsArray(location.href)
  loginUtils.login(true,true,logined,false,true,autoLoginCb,"0")
})
let autoLoginObj = reactive({
  is4gLogined: null, //是否登录
  autoLogin: false, //是否开启4G免登
})
const autoLoginCb = (res) => {
  autoLoginObj.is4gLogined = false
  autoLoginObj.autoLogin = true
}
function logined(res){
  checkUserInFansList()
}
function dialogFn(confirmtext,callbackfn){
  Dialog.confirm({
    confirmButtonColor: "#06acea",
    confirmButtonText:'确定',
    showCancelButton:false,
    message: confirmtext,
  }).then(res=>{
    callbackfn &&  callbackfn()
  })
}

function checkUserInFansList(){
  let {backurl,...sendData} = {...parame}
  productData.loading = true
  fansoperationApi.checkUserInFansList(sendData).then((res) => {
    if(res.code || (!res.data)){
      productData.loading = false
      productData.noAuthority = true
    }else{
      window.location.href = getUrl("/y/"+backurl)
      productData.loading = true
      setTimeout(()=>{
        productData.loading = false
      },2000)
    }
  })
}

function close(){
  if (UA.isWechat) {
    window.wx.closeWindow()
    return false
  }
  if(UA.isApp){//中国移动app
    leadeonLoader().then((leadeon)=>{
      leadeon.closeCurrentWebView({
        debug: false,
        success: function(res) {},
        error: function(res) {}
      })
    })
    return false
  }
  if(UA.isYDBG){//移动办公
    const context = window
    if(UA.isIos) {
      context.webkit.messageHandlers.closePage.postMessage({})
    }else{
      window.WebContainer.closePage()
    }
    return false
  }
}



</script>
<style lang="scss" scoped>
.channel{
  min-height: 100vh;
}
.get-location{
  position: absolute;
  top:40vh;
  text-align: center;
  left:50%;
  transform: translate(-50%,0);
  width:100px;
  border:1px solid#06acea;
  color:#06acea;
  height:20px;
  line-height: 20px;
  border-radius: 20px;
}
.loading-area {
  width: 40%;
  text-align: center;
  margin: 40px auto;
}
</style>
<style lang="scss">
.icpinfo{
  display: none;
}
.notice-title{
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #666;
  padding: 0 15px;
  background: #f8f8f8;
  border-top: 2px solid #f4f4f4;
  text-align: center;
}
.notice-detail{
  text-align: center;
  margin-top:132px;
  .notice-area img {
    width: 82px;
  }

  .notice-area p {
    padding-top: 20px;
    font-size: 14px;
    color: #999;
    line-height: 1;
    padding-bottom: 42px;
  }
}
.result-btn {
  display: block;
  width: 300px;
  height: 36px;
  line-height: 36px;
  border-radius: 23px;
  margin: 20px auto;
  font-size: 14px;
  color: #fff;
  text-align: center;
  background: #538ff0;
}


</style>
