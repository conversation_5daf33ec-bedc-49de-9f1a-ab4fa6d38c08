<template>
  <div class="fansoperation">
    <header-nav :title="data.title"></header-nav>
    <div v-if="(!data.readonly) && headerisApp" class="exchangebt" @click="intoDetail()">
      <Icon name="exchange" class="iconfont" class-prefix="icon" />
    </div>
    <div class="couponMain">
      <Icon
        v-if="data.hasrole"
        name="filter-o"
        class="searchbt"
        size="18"
        @click="data.showSearch = true"
      />
      <Tabs v-model="data.bdName" :before-change="beforeChange">
        <Tab v-for="item in tabTitle" :key="item" :title="item">
        </Tab>
        <div class="fansoperationcon">
          <List
            v-if="data.tabMain && data.tabMain.length"
            v-model="data.loading"
            :finished="data.finished"
            :finished-text="data.nodataMessage"
            class="fans-list ul"
            offset="100"
            @load="getProductList"
          >
            <Cell v-for="(it) in data.tabMain" :key="it.id" class="li">
              <div class="list-content clearfix">
                <div class="bd-con clearfix" @click="intoDetail(it)">
                  <p class="">
                    <span class="label">产品名称：</span>
                    <span class="w80 breakAll">{{ it.productName }}</span>
                  </p>
                  <p class="">
                    <span class="label">产品分类：</span>
                    <span class="w80 breakAll">{{ it.categoryName }}</span>
                  </p>
                  <p v-if="it.onlineTime" class="">
                    <span class="label">开始时间：</span>
                    <span class="w80 breakAll">{{ parseTime(it.onlineTime) }}</span>
                  </p>
                  <p v-if="it.offlineTime" class="">
                    <span class="label">结束时间：</span>
                    <span class="w80 breakAll">{{ parseTime(it.offlineTime) }}</span>
                  </p>
                </div>
                <div class="img" @click="toUrl(it.productUrl)">
                  <img :src="getImgUrl(it.fsImageId)" width="100%" />
                </div>
              </div>
            </Cell>
          </List>
          <ul
            v-else
            class="broadband-tab-no ul"
          >
            <li class="tab-no-con li">
              <div>
                <img v-if="data.noData" src="~@/assets/index_img/ice_tab_no.png" />
                <p>
                  {{ data.nodataMessage }}
                </p>
              </div>
            </li>
          </ul>
        </div>
      </Tabs>
    </div>
    <van-action-sheet v-model="data.showSearch" closeable :round="false">
      <div class="nav">
        <van-form
          ref="searchForm"
          :label-width="100"
          @submit="search('search')"
        >
          <div style="display: flex">
            <van-button
              plain
              block
              type="info"
              style="margin-right: 187.5px"
              native-type="button"
              color="linear-gradient(to right, #60C7FF, #8374FE)"
              @click="reset"
            >
              重置
            </van-button>
            <van-button
              plain
              block
              type="info"
              native-type="submit"
              color="linear-gradient(to right, #60C7FF, #8374FE)"
            >
              确定
            </van-button>
          </div>
          <van-field
            v-model="sendData.textPhone"
            name="orderId"
            type="number"
            label="手机号码"
            placeholder="请输入手机号码"
            colon
            :readonly="data.readonly"
            label-width="100px"
          />
          <van-field
            v-model="sendData.productName"
            name="orderId"
            type="text"
            label="产品名称"
            placeholder="请输入产品名称"
            colon
            label-width="100px"
          />
          <van-field
            readonly
            clickable
            name="onlineTime"
            :value="sendData.onlineTime"
            label="开始日期"
            colon
            placeholder="年 / 月 / 日"
            right-icon="notes-o"
            @click.prevent="openSetTime('start')"
          />
          <van-field
            readonly
            clickable
            name="offlineTime"
            :value="sendData.offlineTime"
            label="结束日期"
            colon
            placeholder="年 / 月 / 日"
            right-icon="notes-o"
            @click.prevent="openSetTime('end')"
          />
        </van-form>
      </div>
    </van-action-sheet>
    <van-action-sheet v-model="data.showpicker" closeable :round="false">
      <div class="nav">
        <van-datetime-picker
          v-if="dateObj.showPickerStart"
          v-model="dateObj.onlineTime"
          type="date"
          title="选择开始日期"
          :min-date="dateObj.minDate"
          :max-date="dateObj.maxDate"
          @confirm="confirmstart"
          @cancel="
            dateObj.showPickerStart = false
            data.showpicker = false
          "
        />
        <van-datetime-picker
          v-if="dateObj.showPickerEnd"
          v-model="dateObj.offlineTime"
          type="date"
          title="选择结束日期"
          :min-date="dateObj.minDate"
          :max-date="dateObj.maxDate"
          @confirm="confirmEnd"
          @cancel="
            dateObj.showPickerEnd = false
            data.showpicker = false
          "
        />
      </div>
    </van-action-sheet>
    <div v-if="(!data.readonly) && (!headerisApp)" class="exchange-bottom" @click="intoDetail()">
      <Icon class="iconfont" class-prefix="icon" name="exchange" />
    </div>
    <Footer />
  </div>
</template>

<script setup>
import headerNav from "@/views/my/components/headerNav.vue"
import loginUtils from "@/utils/login/index.js"
import fansoperationApi from "@/api/fansoperation.js"
import UA from "@/utils/ua"
import {
  Toast,
  Tab,
  Tabs,
  Field,
  Form,
  Button,
  DatetimePicker,
  ActionSheet,
  Picker,
  Icon,
  List,
  Cell
} from "vant"
import "vant/lib/index.css"
import Vue,{onMounted, reactive,getCurrentInstance} from "vue"
import {setScrollTop,parseTime,getImgUrl} from "@/utils/utils.js"
Vue.use(Field)
  .use(Toast)
  .use(Tab)
  .use(Tabs)
  .use(Form)
  .use(Button)
  .use(DatetimePicker)
  .use(ActionSheet)
  .use(Icon)
const STATETEXT = [
  {value:null,text:"全部"},
  {value:20000,text:"流量"},
  {value:30000,text:"套餐"},
  {value:40000,text:"增值业务"},
  {value:50000,text:"终端"},
  {value:60000,text:"宽带"},
  {value:70000,text:"套卡"},
  {value:80000,text:"5G产品"}
]
function getArray(){
  let title = []
  STATETEXT.forEach((item)=>{
    title.push(item.text)
  })
  return title
}
const headerisApp = !UA.isWechat&&!UA.isWechatWork&&!UA.isApp&&!UA.isIosQQ&&!UA.isAndroidQQ
const tabTitle = getArray()
let data = reactive({
  title: "粉丝产品列表",
  showSearch: false,
  showpicker: false,
  activeNames: ["1"],
  user: null,
  info: "",
  tabMain: [],
  bdName: 0,
  nodataMessage: "",
  noData:false,
  ruleId: null,
  hasrole: true,
  loading: false,
  finished: false,
  shopId: null,
  phone:null,
  readonly:false
})
let logined = (res) => {
  data.user = res
  // console.log(res)
  if (data.user && res.shopId) {
    sendData.shopId = res.shopId
    getProductList()
    data.hasrole = true
  } else {
    data.hasrole = false //游客看不了
    data.tabMain = []
    data.nodataMessage = "无权查看，请去自己的店铺"
  }
}
// 时间变量
const dateObj = reactive({
  minDate: new Date(2020, 0, 1),
  maxDate: new Date(2120, 0, 1),
  onlineTime: new Date(),
  offlineTime: new Date(),
  showPickerStart: false,
  showPickerEnd: false,
})
// 筛选入参
const  sendData = reactive({
  shopId: null,
  onlineTime: null,
  offlineTime: null,
  pageNo: 1,
  pageSize: 10,
  productName:null,
  categoryId:null,
  textPhone:""
})
let confirmstart = (time) => {
  dateObj.onlineTime = time
  sendData.onlineTime = parseTime(time, "{y}/{m}/{d}")
  dateObj.showPickerStart = false
  data.showpicker = false
}
let confirmEnd = (time)=> {
  dateObj.offlineTime = time
  let setData = new Date(new Date(time).getTime() + 24 * 60 * 60 * 1000 - 1)
  sendData.offlineTime = parseTime(setData, "{y}/{m}/{d}")
  dateObj.showPickerEnd = false
  data.showpicker = false
}
let  openSetTime = (type)=> {
  if (type == "start") {
    dateObj.showPickerStart = true
    dateObj.showPickerEnd = false
  } else {
    dateObj.showPickerStart = false
    dateObj.showPickerEnd = true
  }
  data.showpicker = true
  setScrollTop()
}
function beforeChange(index){
  sendData.categoryId = STATETEXT[index].value
  sendData.pageNo = 1
  data.finished = false
  reset()
  getProductList()
  // 返回 Promise 来执行异步逻辑
  return new Promise((resolve) => {
    // 在 resolve 函数中返回 true 或 false
    resolve(index + 1)
  })
}
function search(value){
  if(dateObj.onlineTime && dateObj.offlineTime && (dateObj.onlineTime>dateObj.offlineTime)){
    Toast("结束不能早于开始日期")
    return false
  }
  if((!data.phone) && ((sendData.textPhone && sendData.textPhone.length != 11))){
    Toast("请输入正确的手机号")
    return false
  }
  data.showSearch = false
  sendData.pageNo = 1
  data.finished = false
  getProductList("search")
}
function reset() {
  const filters = ["offlineTime","onlineTime","productName"]
  filters.forEach(item=>{
    sendData[item] = null
  })
  dateObj.onlineTime = new Date()
  dateObj.offlineTime = new Date()
}
function getProductList(value){
  if (!data.hasrole) {
    return false
  }
  if(data.phone){
    sendData.phone = data.phone
  }else{
    sendData.phone = sendData.textPhone
    if(sendData.phone && sendData.phone.length != 11){
      Toast("请输入正确的手机号")
      return false
    }
  }
  data.loading = true
  fansoperationApi.getProductList(sendData).then((res) => {
    if (res.code == 0) {
      if (sendData.pageNo == 1) {
        data.tabMain = []
      }
      data.tabMain = data.tabMain.concat(res.data.list)
      data.loading = false
      if (data.tabMain && data.tabMain.length == 0) {
        if (sendData.categoryId == null && value != "search") {
          data.nodataMessage = "暂无产品信息"
          data.noData = true
        } else {
          data.nodataMessage = "没有搜索到您要的信息"
          data.noData = true
        }
        data.finished = true
        return false
      }
      if (res.data.total > res.data.pageNum * res.data.pageSize) {
        sendData.pageNo++
      } else {
        data.nodataMessage = "没有更多了"
        data.finished = true
      }
    } else {
      Toast(res.message)
    }
  })
}
// 跳转产品推荐详情
function intoDetail(item){
  let query = {shopId:data.shopId}
  if(data.proxy){
    if(item && item.id){
      query.id = item.id
      if(data.phone){
        query.wtPhone = data.phone
      }
      data.proxy.$router.push({
        path:'/fansoperation/details.html',query:query
      })
    }else{
      //返回粉丝列表页面
      data.proxy.$router.push({
        path:'/fansoperation/fans.html',query:query
      })
    }

  }
}
// 跳转产品链接
function toUrl(url){
  window.location.href = url
}

onMounted(()=>{
  const url = new URL(location.href)
  data.shopId = url.searchParams.get("shopId")
  data.phone = url.searchParams.get("wtPhone")
  const getVueInstance = getCurrentInstance()
  data.proxy = getVueInstance ? getVueInstance.proxy : null
  if (data.proxy) {
    data.proxy.$store.commit("SET_SHOPID",data.shopId)
  }
  if(data.phone){
    data.readonly = true
  }else{
    data.readonly = false
  }
  loginUtils.login(true, true, logined, false, false, "", null, 5)
})

</script>
<style lang="scss" scoped>
ul {
  li {
    margin: 0;
    padding: 0;
    list-style: none;
  }
}
.fansoperation {
  min-height: calc(100vh - 80px);
  background: #f9fafc;
  .nav {
    padding: 0 0 20px;
    background: #ffffff;
    .van-search {
      width: 355px;
      height: 36px;
      background: #f7f7f7;
      border-radius: 21px;
      margin: 0 auto;
      .van-search__content {
        background: none;
        padding-left: 0;
      }
    }
  }
  .searchbt {
    position: absolute;
    right: 10px;
    top: 12px;
    z-index: 1;
    border-left: 2px solid #979797;
    padding-left: 5px;
  }
  .couponMain {
    position: relative;
    .tabTilte {
      width: 100%;
      display: flex;
      li {
        flex: 1;
        line-height: 40px;
        font-size: 16px;
        text-align: center;
        border-bottom: #fff 1px solid;
        color: #333;
      }
      .active {
        border-bottom-color: #ff7300;
        color: #ff7300;
      }
    }
    .tabTitCon {
      width: 100%;
      background: #e2e1e1;
      display: flex;
      li {
        flex: 1;
        line-height: 40px;
        font-size: 12px;
        text-align: center;
        color: #333;
      }
      .conactive {
        color: rgb(255, 115, 0);
      }
    }
  }
  .tabContentCon {
    text-align: center;
    font-size: 13.5px;
    line-height: 30px;
    color: #999;
    img {
      display: inline-block;
      width: 75px;
      padding-top: 37.5px;
    }
  }
}
.fansoperation :deep(.header-nav){
  height: 52.5px !important;
  .header-nav__title {
    font-size: 18.75px !important;
  }
}
.fansoperation :deep(.van-tabs__wrap){
  background-color: #fff;
  .van-tabs__nav--line {
    width: 85%;
    margin-left: 13.5px;
    padding-left: 0;
  }
  .van-tab--active,.van-tab{
    color: #000 !important;
  }
  .van-tabs__line{
    background-color: #5fade8 !important;
  }
}
.fansoperationcon{
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding-top: 7.5px;
  color: #000;

  .fans-list :deep(.van-cell){
    padding:0
  }
  .ul{
    .li{
      background: #fff;
      margin-bottom: 7.5px;
      font-size: 12px;
      .clearfix:after {
        content: "";
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
      .breakAll{
        white-space:normal;
        word-break:break-all;
        word-wrap: break-word;
      }
      .list-content{
        display: flex;
        .img{
          display: flex;
          flex: 1;
          padding:15px 8px 15px 0;
          img{
            width:100px;
            height: 100px;
          }
        }
      }
      .bd-con{
        padding: 7.5px 13.5px;
        line-height: 26.25px;
        width: 276px;
        p{
          display: flex;
          min-width: 40%;
          max-width: 100%;
          padding-left:10px;
          .breakAll{
            flex:1;
          }
        }
      }
    }
    .tab-no-con{
        background: none;
        text-align: center;
        padding: 30px;
        p{
          padding-top: 15px;
        }
    }
  }
}
.exchangebt,.exchange-bottom{
  position: fixed;
  right:20px;
  top:18px;
  font-size: 18px;
}
.exchange-bottom{
  bottom:150px;
  top: auto;
  background: #fff;
  right: 0;
  width: 40px;
  height: 35px;
  border-top-left-radius: 18px;
  border-bottom-left-radius: 18px;
  padding-left: 10px;
  display: flex;
  align-items: center;
}
</style>
