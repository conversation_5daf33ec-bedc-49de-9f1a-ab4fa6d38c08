<template>
  <div class="fansoperation">
    <header-nav :title="data.title"></header-nav>
    <div v-if="headerisApp" class="exchangebt" @click="intoDetail()">
      <Icon class="iconfont" class-prefix="icon" name="exchange" />
    </div>
    <div class="fansoperationcon">
      <List
        v-if="data.tabMain && data.tabMain.length"
        v-model="data.loading"
        :finished="data.finished"
        :finished-text="data.nodataMessage"
        class="fans-list ul"
        offset="100"
        @load="getMemberList"
      >
        <Cell v-for="(it) in data.tabMain" :key="it.id" class="li">
          <div class="list-content clearfix">
            <div class="bd-con clearfix">
              <p class="">
                <span class="label">手机号码：</span>
                <span class="w80 breakAll">{{ getPassPhone(it.phone) }}</span>
              </p>
              <p class="">
                <span class="label">关注时间：</span>
                <span class="w80 breakAll">{{ parseTime(it.createTime) }}</span>
              </p>
            </div>
            <div class="btn" @click="intoDetail(it)">
              <div>查看推荐</div>
            </div>
          </div>
        </Cell>
      </List>
      <ul
        v-else
        class="broadband-tab-no ul"
      >
        <li class="tab-no-con li">
          <div>
            <img v-if="data.noData" src="~@/assets/index_img/ice_tab_no.png" />
            <p>
              {{ data.nodataMessage }}
            </p>
          </div>
        </li>
      </ul>
    </div>
    <div v-if="!headerisApp" class="exchange-bottom" @click="intoDetail()">
      <Icon class="iconfont" class-prefix="icon" name="exchange" />
    </div>
    <Footer />
  </div>
</template>

<script setup>
import headerNav from "@/views/my/components/headerNav.vue"
import loginUtils from "@/utils/login/index.js"
import fansoperationApi from "@/api/fansoperation.js"
import UA from "@/utils/ua"
import {
  Toast,
  Tab,
  Tabs,
  Button,
  List,
  Cell,
  Icon
} from "vant"
import "vant/lib/index.css"
import Vue,{onMounted, reactive,getCurrentInstance} from "vue"
import {getPassPhone,parseTime} from "@/utils/utils.js"
Vue.use(Toast)
  .use(Tab)
  .use(Tabs)
  .use(Button)
const headerisApp = !UA.isWechat&&!UA.isWechatWork&&!UA.isApp&&!UA.isIosQQ&&!UA.isAndroidQQ
let data = reactive({
  title: "粉丝列表",
  user: null,
  nodataMessage: "",
  noData:false,
  ruleId: null,
  hasrole: true,
  loading: false,
  finished: false,
  shopId: null,
  tabMain: [],
})
const  sendData = reactive({
  shopId: null,
  pageNo: 1,
  pageSize: 10,
})
let logined = (res) => {
  data.user = res
  // console.log(res)
  if (data.user && res.shopId) {
    sendData.shopId = res.shopId
    getMemberList()
    data.hasrole = true
  } else {
    data.hasrole = false //游客看不了
    data.tabMain = []
    data.nodataMessage = "无权查看，请去自己的店铺"
  }
}
function getMemberList(value){
  if (!data.hasrole) {
    return false
  }
  data.loading = true
  fansoperationApi.getMemberList(sendData).then((res) => {
    if (res.code == 0) {
      if (sendData.pageNo == 1) {
        data.tabMain = []
      }
      data.tabMain = data.tabMain.concat(res.data.list)
      data.loading = false
      if (data.tabMain && data.tabMain.length == 0) {
        data.nodataMessage = "没有搜索到您要的信息"
        data.noData = true
        data.finished = true
        return false
      }
      if (res.data.total > res.data.pageNum * res.data.pageSize) {
        sendData.pageNo++
      } else {
        data.nodataMessage = "没有更多了"
        data.finished = true
      }
    } else {
      Toast(res.message)
    }
  })
}
// 跳转产品推荐详情
function intoDetail(item){
  let query = {shopId:data.shopId}
  if(item && item.wtPhone){
    query.wtPhone = item.wtPhone
  }
  if(data.proxy){
    data.proxy.$router.push({
      path:'/fansoperation/list.html',query:query
    })
  }
}

onMounted(()=>{
  const url = new URL(location.href)
  data.shopId = url.searchParams.get("shopId")
  const getVueInstance = getCurrentInstance()
  data.proxy = getVueInstance ? getVueInstance.proxy : null
  if (data.proxy) {
    data.proxy.$store.commit("SET_SHOPID",data.shopId)
  }
  loginUtils.login(true, true, logined, false, false, "", null, 5)
})

</script>
<style lang="scss" scoped>
ul {
  li {
    margin: 0;
    padding: 0;
    list-style: none;
  }
}
.fansoperation {
  min-height: calc(100vh - 80px);
  background: #f9fafc;
}
.fansoperation :deep(.header-nav){
  height: 52.5px !important;
  .header-nav__title {
    font-size: 18.75px !important;
  }
}
.fansoperation :deep(.van-tabs__wrap){
  background-color: #fff;
  .van-tabs__nav--line {
    width: 85%;
    margin-left: 13.5px;
    padding-left: 0;
  }
  .van-tab--active,.van-tab{
    color: #000 !important;
  }
  .van-tabs__line{
    background-color: #5fade8 !important;
  }
}
.fansoperationcon{
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding-top: 7.5px;
  color: #000;

  .fans-list :deep(.van-cell){
    padding:0
  }
  .ul{
    .li{
      background: #fff;
      margin-bottom: 7.5px;
      font-size: 12px;
      .clearfix:after {
        content: "";
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
      .breakAll{
        white-space:normal;
        word-break:break-all;
        word-wrap: break-word;
      }
      .list-content{
        display: flex;
        align-items: center;
        .btn{
          display: flex;
          flex: 1;
          div{
            height:30px;
            width:80px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #5fade8;
            border-radius: 4px;
            color:#fff;
          }
        }
      }
      .bd-con{
        padding: 7.5px 13.5px;
        line-height: 26.25px;
        width: 276px;
        p{
          display: flex;
          min-width: 40%;
          max-width: 100%;
          padding-left:10px;
          .breakAll{
            flex:1;
          }
        }
      }
    }
    .tab-no-con{
        background: none;
        text-align: center;
        padding: 30px;
        p{
          padding-top: 15px;
        }
    }
  }
}
.exchangebt,.exchange-bottom{
  position: fixed;
  right:20px;
  top:18px;
  font-size: 18px;
  display: block;
}
.exchange-bottom{
  bottom:150px;
  top: auto;
  background: #fff;
  right: 0;
  width: 40px;
  height: 35px;
  border-top-left-radius: 18px;
  border-bottom-left-radius: 18px;
  padding-left: 10px;
  display: flex;
  align-items: center;
}
</style>
