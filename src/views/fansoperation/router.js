export default [
  {
    path: '/fansoperation/list.html',
    name: 'fansoperationList',
    component: resolve => require(['./list.vue'], resolve),
    meta: {
      title: '粉丝产品列表',
      login:true,
      role:[1,2]
    }
  },
  {
    path: '/fansoperation/fans.html',
    name: 'fansoperationFansList',
    component: resolve => require(['./fans.vue'], resolve),
    meta: {
      title: '粉丝列表',
      login:true,
      role:[1,2]
    }
  },
  {
    path: '/fansoperation/details.html',
    name: 'fansoperationDetails',
    component: resolve => require(['./details.vue'], resolve),
    meta: {
      title: '粉丝产品详情',
      login:true,
      role:[1,2]
    }
  },
  {
    path: '/fansoperation/product.html',
    name: 'fansoperationProduct',
    component: resolve => require(['./product.vue'], resolve),
    meta: {
      title: '商品',
      login:true,
      role:[0,1,2]
    }
  }
]
