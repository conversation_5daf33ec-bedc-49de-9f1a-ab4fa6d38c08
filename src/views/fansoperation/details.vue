<!--
 * new page
 * @author: q<PERSON><PERSON><PERSON>
 * @since: 2024-05-11
 * details.vue
-->
<template>
  <div class="fansoperation-detail">
    <header-nav title="粉丝产品详情" :font-size="18.75" :height="55"></header-nav>
    <div class="detail-top">
      <h1>产品信息</h1>
      <div class="fansoperation-content clearfix">
        <div class="bd-con clearfix">
          <p class="">
            <span class="label">产品分类：</span>
            <span class="w80 breakAll">{{ productInfo.categoryName }}</span>
          </p>
          <p class="">
            <span class="label">产品名称：</span>
            <span class="w80 breakAll">{{ productInfo.productName }}</span>
          </p>
          <p class="">
            <span class="label">营销话术：</span>
            <span class="w80 breakAll">{{ productInfo.marketingLanguage }}</span>
          </p>
          <p class="">
            <span class="label">产品描述：</span>
            <span class="w80 breakAll">{{ productInfo.description }}</span>
          </p>
          <p v-if="productInfo.onlineTime" class="">
            <span class="label">开始时间：</span>
            <span class="w80 breakAll">{{ parseTime(productInfo.onlineTime) }}</span>
          </p>
          <p v-if="productInfo.offlineTime" class="">
            <span class="label">结束时间：</span>
            <span class="w80 breakAll">{{ parseTime(productInfo.offlineTime) }}</span>
          </p>
        </div>
        <div class="img">
          <img
            v-if="productInfo.fsImageId"
            :src="getImgUrl(productInfo.fsImageId)"
            alt=""
            width="100%"
            @click="toUrl(productInfo.productUrl)"
          />
          <div class="qr-btn" @click="openDialog">
            营销码上办
          </div>
        </div>
      </div>
    </div>
    <div class="detail-bottom">
      <h1>适用用户</h1>
      <ul class="detail-bottom-title">
        <li class="detail-bottom-title-item" :class="{ 'active': tabActive==0 }" @click="changeTab(0)">
          联系中
        </li>
        <li class="detail-bottom-title-item" :class="{ active: tabActive==1 }" @click="changeTab(1)">
          已联系
        </li>
      </ul>
      <List
        v-if="fansInfo.fansList"
        v-model="fansInfo.loading"
        :finished="fansInfo.finished"
        finished-text="没有更多了"
        class="fans-list ul"
        offset="100"
        @load="getFansList"
      >
        <template v-if="tabActive==0">
          <div v-for="(it) in fansInfo.fansList" :key="it.id" class="li">
            <span class="phone-text">{{ getPassPhone(it.phone) }}</span>
            <span
              v-if="it.qwUserId"
              class="icon"
              @click="goQw(it)"
            >
              <span
                class="iconfont icon-xinxi"
                style="color: #26D325"
              ></span>
            </span>
            <span
              v-if="it.subStatus==1"
              class="icon"
              @click="fansSubscribe(it)"
            >
              <img src="~@/assets/home/<USER>" alt="" />
            </span>
            <span
              v-if="UA.isApp"
              class="icon"
              @click="appSendSms(it)"
            >
              <img src="~@/assets/home/<USER>" alt="" />
            </span>
            <a
              v-else
              v-clipboard:copy="productInfo.smsTemplate+productInfo.referralUrl"
              class="icon"
              @click="appSendSms(it)"
            >
              <img src="~@/assets/home/<USER>" alt="" />
            </a>
            <a
              class="icon"
              @click="feedbackOpenDialog(it)"
            >
              <img src="~@/assets/home/<USER>" alt="" />
            </a>
          </div>
        </template>
        <template v-if="tabActive==1">
          <div v-for="(it) in fansInfo.fansList" :key="it.id" class="li">
            <span class="phone-text">{{ getPassPhone(it.phone) }}</span>
            <span v-if="it.status==1" class="feedback-status success" @click="showFeedback(it)">营销成功</span>
            <span v-if="it.status==0" class="feedback-status fail" @click="showFeedback(it)">营销失败</span>
            <span
              v-if="it.qwUserId"
              class="icon"
              @click="goQw(it)"
            >
              <span
                class="iconfont icon-xinxi"
                style="color: #26D325"
              ></span>
            </span>
          </div>
        </template>
      </List>
    </div>
    <van-dialog
      v-model="dialogQrcodeShow"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      @opened="creatQrCode(productInfo.referralUrl)"
    >
      <div id="qrCodeContainer">
      </div>
      <div style="text-align: center">
        {{ productInfo.productName }}
      </div>
      <div class="close" @click="closeDialog">
        <img src="~@/assets/index_img/icon_close_w.png" />
      </div>
    </van-dialog>
    <!-- 弹框 -->
    <van-dialog
      v-model="feedbackDialogShow"
      title="粉丝运营反馈"
      :show-cancel-button="false"
      :show-confirm-button="false"
      class="feedback-dialog"
    >
      <RadioGroup
        v-model="feedbackData.status"
        direction="horizontal"
        class="dialog-content"
      >
        <Radio name="1">
          营销成功
        </Radio>
        <Radio name="0">
          营销失败
        </Radio>
      </RadioGroup>
      <Field
        v-model="feedbackData.feedback"
        class="boradband-input"
        type="textarea"
        :autosize="{ maxHeight: 100 }"
        placeholder="请填写反馈信息"
        label-width="100px"
        clearable
      />
      <p
        class="tips"
      >
        {{ data.tips }}
      </p>
      <div class="dialog-footer">
        <div
          class="dialog-footer-btn cancel-btn"
          @click="feedbackCloseDialog"
        >
          取消
        </div>
        <div
          class="dialog-footer-btn submit-btn"
          @click="changeOrderStatus"
        >
          提交
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script setup>
import headerNav from "@/views/my/components/headerNav.vue"
import UA from "@/utils/ua"
import leadeonLoader from "@/utils/leadeonloader.js"
import fansoperationApi from "@/api/fansoperation.js"
import loginUtils from "@/utils/login/index.js"
import {getPassPhone,parseTime,getImgUrl} from "@/utils/utils.js"
import { sendChatMessage, getWxCustomer, wechartWorkInit, openEnterpriseChat, getUserIds, getQwUserIds,getContext,shareToExternalContact } from "@/utils/qwapi.js"
import Vue, { onMounted, reactive, getCurrentInstance,ref } from "vue"
import { Toast, Dialog, RadioGroup, Radio, Field, List, Tab } from 'vant'
import VueClipboard from "vue-clipboard2"
import QRCode from "qrcodejs2"
Vue.use(VueClipboard).use(Dialog)
function appSendSms(it){
  if(!it.phone){
    return false
  }
  if(UA.isApp){
    leadeonLoader().then((leadeon)=>{
      leadeon.sendSMS({
        debug: false,
        smsContent: productInfo.value.smsTemplate + productInfo.value.referralUrl || "",
        phoneNum: it.phone,
        success: function(res) {//res为空
          console.log("发送成功")
        },
        error: function(res) {

        }
      })
    })
  }else{
    Dialog.confirm({
      title: "温馨提示",
      message: "短信内容已复制，请在手机短信里粘贴使用",
      confirmButtonColor: "#5099fe",
      confirmButtonText: "打开短信",
      showCancelButton: false
    }).then(() => {
      openSms(it.phone)
    })
  }
}

function openSms(phone){
  var a = document.createElement('a')
  a.href = `sms:${phone}`
  a.click()
}
const data = reactive({
  shopId:null,
  tips:""
})
onMounted(()=>{
  const url = new URL(location.href)
  data.id = url.searchParams.get("id")
  data.shopId = url.searchParams.get("shopId")
  data.phone = url.searchParams.get("wtPhone")
  const getVueInstance = getCurrentInstance()
  data.proxy = getVueInstance ? getVueInstance.proxy : null
  if (data.proxy) {
    data.proxy.$store.commit("SET_SHOPID",data.shopId)
  }
  loginUtils.login(true, true, logined, false, false, "", null, 5)
})
function logined(res){
  if(res.shopId){
    data.shopId = res.shopId
  }
  if(UA.isWechatWork){
    wechartWorkInit()
  }
  getProductDetail({...data})
  getFansList()
}
// 跳转产品链接
function toUrl(url){
  window.location.href = url
}
//产品详情数据
const productInfo = ref({})
function getProductDetail({id,shopId}){
  fansoperationApi.getProductDetail({
    id:id,
    shopId:shopId
  }).then((res) => {
    if(res.code){
      Toast(res.message)
    }else{
      productInfo.value = res.data
    }
  })
}
/**企微客户id */
const customerUserIds = ref({})
// 获取用户列表信息
const fansInfo = reactive({
  fansList:null,
  loading:false,
  finished:false,
  pageNo:1,
  pageSize:10,
  isContact:0
})
// 获取用户数据
async function getFansList(tab) {
  const { pageNo, pageSize, isContact } = fansInfo
  fansInfo.loading = true
  const sendData = {
    shopId: data.shopId,
    id: data.id,
    pageNo,
    pageSize,
    isContact
  }
  if (data.phone) {
    sendData.phone = data.phone
  }
  try {
    const res = await fansoperationApi.getFansList(sendData)
    if (pageNo === 1) {
      fansInfo.fansList = []
    }
    if (!res.code) {
      const currentList = res.data.list
      // 并行处理所有客户信息查询
      if(UA.isWechatWork && currentList.length > 0) {
        let phones = currentList.map(item => item.phone)
        const result = await getMyCustomer(phones.join(','))
        if (result) {
          currentList.forEach(item => {
            item.qwUserId = customerUserIds.value[item.phone] || null
          })
        }
        console.log(currentList,'currentList')
      }
      fansInfo.fansList = fansInfo.fansList.concat(currentList)
      if (res.data.total > res.data.pageNum * res.data.pageSize) {
        fansInfo.pageNo++
      } else {
        fansInfo.finished = true
      }
    } else {
      fansInfo.finished = true
    }
  } catch (error) {
    console.error('获取粉丝列表失败:', error)
    fansInfo.finished = true
  } finally {
    fansInfo.loading = false
  }
}
let qrcode = null
function creatQrCode(data) {
  if (qrcode) {
    qrcode.clear()
    document.querySelector('#qrCodeContainer').innerHTML = ""
  }
  //防止dialog首次加载不出二维码 在dialog上加@opened 回调 qrCodeUrlCopy.value
  qrcode = new QRCode(document.querySelector('#qrCodeContainer'), {
    text: data,
    width: 200,
    height: 200,
    colorDark: "#000000",
    colorLight: "#ffffff",
    correctLevel: QRCode.CorrectLevel.H,
  })
}
const dialogQrcodeShow = ref(false)
function openDialog(){
  dialogQrcodeShow.value = true
}
function closeDialog(){
  dialogQrcodeShow.value = false
}
const tabActive = ref(0)
function changeTab(tab){
  tabActive.value = tab
  fansInfo.fansList = []
  fansInfo.isContact = tab //0 lianxizhong  1 yilianxi
  Object.assign(fansInfo,{
    loading:false,
    finished:false,
    pageNo:1,
    pageSize:10
  })
  getFansList(tab)
}
// 反馈
const feedbackDialogShow = ref(false)
// 当前反馈数据
const curFeedback = ref(null)
// 反馈状态
const feedbackData = reactive({
  feedback: '',
  status: "1",
})

function feedbackOpenDialog(data){
  feedbackDialogShow.value = true
  curFeedback.value = data
  feedbackData.phone = data.phone
  feedbackData.feedback = ''
  feedbackData.status = "1"
}
function feedbackCloseDialog(){
  feedbackDialogShow.value = false
}
// 反馈提交
function changeOrderStatus(){
  let reg = /^[\u4E00-\u9FA5A-Za-z0-9_%&',;=?]+$/
  if(feedbackData.feedback && (feedbackData.feedback.length>50)){
    data.tips = "限输入50个字符长度"
    return false
  }
  if(feedbackData.feedback && (!reg.test(feedbackData.feedback))){
    data.tips = "请重新填写反馈内容"
    return false
  }
  data.tips = ""
  let sendData = Object.assign({},productInfo.value,feedbackData)
  sendData.shopId = data.shopId
  fansoperationApi.fansFeedbackAdd(sendData).then((res) => {
    if(!res.code){
      Toast("提交反馈信息成功")
      feedbackCloseDialog()
      changeTab(0)
    }else{
      Toast(res.message)
    }
  })
}
// 展示营销结果
function showFeedback(item){
  if(!item.feedback){
    return false
  }
  Dialog({
    title: "反馈信息",
    message: item.feedback,
    confirmButtonColor: "#5099fe",
    confirmButtonText: "关闭",
  }).then(() => {})
}
//推送粉丝活动开始提醒订阅消息
function fansSubscribe(it){
  let sendData = {
    shopId: data.shopId,
    id: data.id
  }
  if(it.phone){
    sendData.phone = it.phone
  }
  fansoperationApi.getFansSubscribeTo(sendData).then((res) => {
    if(!res.code){
      Toast(res.message)
    }else{
      Toast(res.message)
    }
  })
}
//发送企微消息
async function goQw(it){
  console.log(it)
  const getContexts = await getContext()
  // sendChatMessage({
  //   link: location.href, //H5消息页面url 必填
  //   title: "商品标题", //H5消息标题
  //   desc: "商品摘要", //H5消息摘要
  //   // imgUrl: getImgUrl(productInfo.value.fsImageId), //H5消息封面图片URL
  //   imgUrl:'https://search-operate.cdn.bcebos.com/d054b8892a7ab572cb296d62ec7f97b6.png'
  // })
  shareToExternalContact({
    url: productInfo.value.referralUrl,
    title: productInfo.value.productName,
    desc: productInfo.value.marketingLanguage,
    imgUrl: getImgUrl(productInfo.value.fsImageId),//'https://search-operate.cdn.bcebos.com/d054b8892a7ab572cb296d62ec7f97b6.png',
    userId:it.qwUserId //wmDM1zCwAAtMLXryKT4ltA-WIectdHKw 杨凡  wmDM1zCwAAQrX_QP4v7uJZGBYypxSYcg 胡昌武 wmDM1zCwAAQJNnwTJ6Y3BupEC-AT5R-g 淼
  })
  console.log(getContexts,'当前场景')

}
async function getQwUserId(it){
  const userIds = await getUserIds()
  console.log([userIds,'openEnterpriseChat'])
  //  openEnterpriseChat({externalUserIds:"wmDM1zCwAAtMLXryKT4ltA-WIectdHKw"}) //打开对话框
}

/** 获取手机号对应的外部联系人id */
function getMyCustomer(phones){
  if (!phones) return Promise.resolve(false)
  return new Promise((resolve) => {
    getWxCustomer(phones).then((res) => {
      if((!res.code) && res.data){
        res.data.map(item => {
          customerUserIds.value[item.mobile] = item.externalUserId
        })
        resolve(true)
      }else{
        resolve(false)
      }
    }).catch(() => {
      resolve(false)
    })
  })
}
</script>

<style lang="scss" scoped>
.fansoperation-detail{
  color: #000;
  background-color: #f6f6f6;
  font-size: 12px;
  min-height: calc(100vh - 80px);
  h1{
    font-weight: bold;
    font-size: 13px;
    padding-left:16px;
    border-left:4px solid #659aff;
  }
}
.detail-top,.detail-bottom{
  background-color: #fff;
  margin-top:8px;
  padding-top:30px;
}

.fansoperation-content{
  display: flex;
  padding:0 10px 20px 20px;
  .clearfix:after {
    content: "";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
  .bd-con{
    padding: 10px 9px 0 0;
    line-height: 30px;
    width: 235px;
    p{
      display: flex;
      min-width: 40%;
      max-width: 100%;
      .breakAll{
        white-space:normal;
        word-break:break-all;
        word-wrap: break-word;
        flex:1;
      }
    }
  }
  .img{
    display: flex;
    flex: 1;
    padding-top:15px;
    flex-direction: column;
    img{
      width:110px;
      height: 110px;
    }
    .qr-btn{
      margin-top: 20px;
      color: #fff;
      font-weight: bold;
      background: #628de9;
      height:30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 15px;
    }
  }
}

.detail-bottom{
  .detail-bottom-title{
    margin:20px 20px 0;
    display: flex;
    height:30px;
    background: #d8d8d8;
    border-radius: 15px;
    &-item{
      font-size: 14px;
      width: 50%;
      display: flex;
      align-items:center;
      justify-content: center;
      // transition: all 0.3s;
      &.active{
        color:#2f94ff;
        background: #fff;
        border:2px solid #d8d8d8;
        border-radius: 13px;
      }
    }
  }
 .van-list{
    padding-top:20px;
  }
  .li{
    height: 40px;
    padding:0 20px;
    display: flex;
    // border-top:1px solid #f6f6f6;
    align-items: center;
    .icon{
      display: block;
      width:30px;
      img{
        width:20px;
        height: 20px;
      }
      .iconfont{
        font-size: 20px;
      }
    }
    .phone-text{
      flex:1
    }
  }
}
#qrCodeContainer{
  width:200px;
  height: 200px;
  margin:50px auto 20px;
  canvas,img{
    width:100%;
    height: 100%;
  }
}
.fansoperation-detail :deep(.van-dialog){
  padding-bottom: 20px;
  overflow: visible;
  .close{
    position: absolute;
    width:20px;
    bottom:-40px;
    left: 150px;
    img{
      width:20px;
    }
  }
}
.dialog-content{
  display: flex;
  justify-content: space-between;
  margin-top:20px;
}
.boradband-input{
  border:1px solid #eee;
  margin:10px auto;
  height: 100px;
}
.tips{
  font-size: 14px;
  color:#ee4855;
}
.feedback-dialog{
  padding:0 20px 20px;
}
.van-radio{
  display: flex;
  font-size: 14px;
  :deep(.van-radio__icon){
    display: flex;
    align-items: center;
  }
  :deep(.van-icon-success){
    font-size: 10px;
  }
}
.dialog-footer{
  display: flex;
  height:40px;
  margin:20px auto;
  &-btn{
    flex:1;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    &.submit-btn{
      background-color: #2f94ff;
      color:#fff;
    }
    &.cancel-btn{
      background-color: #f2f2f2;
    }
  }
}
.feedback-status{
  display: flex;
  height: 20px;
  width: 80px;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  font-size: 12px;
  &.success{
    background-color: #2f94ff;
    color:#fff
  }
  &.fail{
    background-color: #d7d7d7;
    color:#333
  }
}
</style>



