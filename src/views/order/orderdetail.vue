<template>
  <div class="orderDetail">
    <headerNav :title="data.title"></headerNav>
    <div v-if="data.orderdetail" class="xskd_ordedetail">
      <ul>
        <!-- 办理信息 -->
        <li v-if="data.orderdetail.numApplyWay === '4'" class="orderdetail_item">
          <div class="order_title">
            办理信息
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              待激活号码：
            </div>
            <div class="order_value">
              {{ data.orderdetail.buyMobile }}

              <span
                class="copyTel"
                @click="handleCopyTel(data.orderdetail.buyMobile)"
              >
                <Icon name="description" />
                <i>复制</i>
              </span>
            </div>
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              联系人：
            </div>
            <div class="order_value">
              {{ data.orderdetail.subscriberName }}
            </div>
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              办理地址：
            </div>
            <div class="order_value banlilh">
              {{ data.orderdetail.address }}
            </div>
          </div>
        </li>
        <!-- 订单信息 -->
        <li class="orderdetail_item">
          <div class="order_title">
            订单信息
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              订单号：
            </div>
            <div class="order_value">
              {{ data.orderdetail.orderId }}
            </div>
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              客户手机号：
            </div>
            <div class="order_value">
              {{ judgePhone(data.orderdetail.mobile) }}

              <a
                v-if="
                  data.orderdetail.numActiveStatus == 'WA' &&
                    data.orderdetail.numApplyWay == 4
                "
                :href="'tel:' + data.orderdetail.mobile"
                class="telradio"
              >
                <Icon color="#fff" name="phone" />
              </a>
            </div>
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              下单时间：
            </div>
            <div class="order_value">
              {{ data.orderdetail.createTime }}
            </div>
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              店铺：
            </div>
            <div class="order_value">
              {{ data.orderdetail.shopName }}
            </div>
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              营业员：
            </div>
            <div class="order_value">
              {{ data.orderdetail.assistantName }}
            </div>
          </div>
        </li>
        <!-- 商品信息 -->
        <li class="orderdetail_item">
          <div class="order_title">
            商品信息
          </div>
          <template v-if="data.orderdetail.orderItems && data.orderdetail.orderItems[0]">
            <div class="flex">
              <div class="order_label orderdetail_label">
                商品名称：
              </div>
              <div class="order_value">
                {{ data.orderdetail.orderItems[0].goodsName }}
              </div>
            </div>
            <div class="order_value">
              {{ data.orderdetail.orderItems[0].goodsName }}
            </div>
            <div v-if="data.orderdetail.orderItems[0].modelId" class="flex">
              <div class="order_label orderdetail_label">
                档位名称：
              </div>
              <div class="order_value">
                {{ data.orderdetail.orderItems[0].skuName }}
              </div>
            </div>
            <div v-if="data.orderdetail.orderItems[0].modelId" class="flex">
              <div class="order_label orderdetail_label">
                档位ID：
              </div>
              <div class="order_value">
                {{ data.orderdetail.orderItems[0].modelId }}
              </div>
            </div>
            <div v-if="data.orderdetail.orderItems[0].doResult" class="flex">
              <div class="order_label orderdetail_label">
                办理结果：
              </div>
              <div class="order_value">
                {{ data.orderdetail.orderItems[0].doResult }}
              </div>
            </div>
          </template>
          <div v-if="data.orderdetail.priceSum + ''" class="flex">
            <div class="order_label orderdetail_label">
              订单金额：
            </div>
            <div class="order_value">
              {{ data.orderdetail.priceSum == 0 ? 0 : data.orderdetail.priceSum / 100 }}
              元
            </div>
          </div>
          <div v-if="data.orderdetail.numActiveStatusName" class="flex">
            <div class="order_label orderdetail_label">
              激活状态：
            </div>
            <div
              class="order_value"
              :class="{
                green: data.orderdetail.numActiveStatus === 'AD',
                red: data.orderdetail.numActiveStatus === 'WA',
                grey: data.orderdetail.numActiveStatus === 'OA',
              }"
            >
              {{ data.orderdetail.numActiveStatusName }}
            </div>
          </div>
        </li>

        <!-- 物流信息详情 -->
        <li v-if="data.orderdetail.address && data.orderdetail.numApplyWay == 1" class="orderdetail_item">
          <div class="order_title">
            物流详情
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              <p>收货地址：</p>
            </div>
            <div class="order_value addressLyl" style="line-height: 1.8">
              {{ data.orderdetail.address }}
            </div>
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              <p>物流信息：</p>
            </div>
            <div class="order_value">
              <div v-if="setLogist()" class="emptyzw">
                <div class="topgs">
                  <span>{{ setLogistCompany() }} ({{ data.orderdetail.trackingNo }})</span>
                </div>

                <div class="listChildren">
                  <div v-show="!data.logisticsInfoShow" class="wuliuStyle wuliuStyle2">
                    <div class="a1">
                      {{ data.orderdetail.logisticsInfo.data[0].time }}
                    </div>
                    <div class="a2">
                      {{ data.orderdetail.logisticsInfo.data[0].context }}
                    </div>
                  </div>

                  <div v-show="!data.logisticsInfoShow" class="modelstyle" @click="data.logisticsInfoShow = true">
                    点击查看更多物流详情
                    <Icon color="#bababa" name="arrow-down" />
                  </div>

                  <dl v-show="data.logisticsInfoShow">
                    <dt v-for="(item,index) in data.orderdetail.logisticsInfo.data" :key="index">
                      <div class="wuliuStyle">
                        <div class="a1">
                          {{ item.time }}
                        </div>
                        <div class="a2">
                          {{ item.context }}
                        </div>
                      </div>
                    </dt>
                  </dl>

                  <div v-show="data.logisticsInfoShow" class="modelstyle modelstyle2" @click="data.logisticsInfoShow = false">
                    收起
                    <Icon color="#bababa" name="arrow-up" />
                  </div>
                </div>
              </div>
              <div v-else>
                暂无
              </div>
            </div>
          </div>
        </li>
      </ul>
    </div>
    <!-- 弹框 -->
    <van-dialog
      v-model="showChangeOrderStatus"
      title="预约处理"
      show-cancel-button
      confirm-button-color="#5fade8"
      confirm-button-text="#5fade8"
      :before-close="changeOrderStatus"
    >
      <RadioGroup
        v-model="data.sendData1.state"
        direction="horizontal"
        class="dialogContent"
      >
        <Radio name="MAS">
          预约成功
        </Radio>
        <Radio name="MAF">
          预约失败
        </Radio>
      </RadioGroup>
      <Field
        v-if="data.sendData1.state == 'MAS'"
        v-model="data.sendData1.productCode"
        class="boradbandInput"
        type="text"
        placeholder="请输入BOSS侧宽带账号"
        label-width="100px"
        clearable
      />
      <Field
        v-if="data.sendData1.state == 'MAF'"
        v-model="data.sendData1.reason"
        class="boradbandInput"
        type="textarea"
        :autosize="{ maxHeight: 100 }"
        :maxlength="256"
        placeholder="请填写预约失败原因，例：用户主动取消"
        label-width="100px"
        clearable
      />
    </van-dialog>
    <Footer />
  </div>
</template>

<script setup>
import headerNav from "@/views/my/components/headerNav"
import loginUtils from "@/utils/login"
import loginApi from "@/api/login"
import OrderApi from "@/api/order"
import Vue,{ref,reactive,onMounted, computed,getCurrentInstance} from "vue"
import { Toast, Dialog, RadioGroup, Radio, Field, Icon } from "vant"
import "vant/lib/index.css"
import lodash from "lodash"
Vue.use(Dialog)

const showChangeOrderStatus = ref(false)
const data = reactive({
  title: "订单详情",
  activeNames: ["1"],
  info: "",
  orderdetail: null,
  orderId: null,
  tplId: null,
  showCancleReservation: false,
  sendData1: {
    state: "MAS",
    productCode: "",
    reason: "",
  },
  ruleId: null,
  staffId: null,

  /* 物流信息展开收起 */
  logisticsInfoShow:false
})

/* 设置物流信息是否显示 */
const setLogist = () => {
  let resdata = lodash.get(data.orderdetail, "logisticsInfo.data", [])
  if (!resdata.length) {
    return false
  }

  return true
}

/* 设置物流公司 */
const setLogistCompany = () => {
  let num = lodash.get(data.orderdetail, "logisticsComCode", -1)

  const obj = {
    1: "EMS",
    2: "顺丰快递",
    3: "宅急送",
    4: "圆通快递",
    5: "申通快递",
    6: "中通快递",
    7: "韵达快递",
    8: "天天快递",
    9: "汇通快递",
    10: "优速快递",
    11: "德邦快递",
    12: "全峰快递",
    13: "信丰物流",
    14: "国通快递",
    15: "港中能达快递",
    16: "快捷速递",
    17: "联昊通快递",
    18: "速尔快递",
    19: "如风达快递",
    20: "新邦物流",
    21: "佳怡物流",
    22: "安捷速递",
    23: "龙邦快件",
    24: "民邦速递",
    25: "全一快递",
    26: "邮政",
    27: "贵州年华科技有限公司",
    28: "中捷通信服务公司",
    29: "上海陆上货运交易中心有限公司",
    30: "埃瑞普",
    31: "深圳兆航",
    32: "深圳飞函",
    33: "广东南都",
    1000: "其他",
  }

  return obj[num]
}
    

/* 复制待激活号码 */
const handleCopyTel = (val) => {
  var oInput = document.createElement("input")
  oInput.value = val
  document.body.appendChild(oInput)
  oInput.select()
  document.execCommand("Copy")
  oInput.className = "oInput"
  oInput.style.display = "none"
  Toast("复制成功!")
}

const logined = (res) => {
  if (res) {
    data.staffId = res.staffId
    getDetail()
  }
}
const activeObj = {
  "WA":"待激活",
  "AD":"已激活",
  "OA":"已取消"
}
const orderDetail = () => {
  OrderApi.getOrderDetail({
    orderId: data.orderId,
    operator: data.staffId || data.proxy.$route.query.staffId,
  }).then((res) => {
    if (res.code) {
      Toast(res.message)
    } else {
      data.orderdetail = res.data
      //激活方式  WA：待激活、AD：已激活 、OA：已取消
      data.orderdetail.numActiveStatusName = activeObj[data.orderdetail.numActiveStatus]
        ? activeObj[data.orderdetail.numActiveStatus]
        :""
    }
  })
}

const getDetail = () => {
  loginApi
    .getUserInfo(localStorage.getItem("yundianToken"), "0")
    .then((res) => {
      data.staffId = res.data.staffId
      orderDetail()
    })
}

const openChangeOrderDialog = (orderId) => {
  data.currentOrderId = orderId
  showChangeOrderStatus.value = true
}

const changeOrderStatus = (action, done) => {
  if (action == "cancel") {
    done()
    return false
  }
  if (data.sendData1.state == "MAS") {
    //预约成功
    if (!data.sendData1.productCode) {
      Toast("请输入BOSS侧宽带账号")
      done(false)
      return false
    } else if (!/^[\w\d]+$/.test(data.sendData1.productCode)) {
      Toast("请输入字母或数字")
      done(false)
      return false
    }
    delete data.sendData1.reason
  }
  if (data.sendData1.state == "MAF") {
    //预约失败
    let myreason = data.sendData1.reason
      ? data.sendData1.reason.replace(/[\n|\s]+/g, "")
      : null
    if (!myreason) {
      Toast("请输入失败原因")
      data.sendData1.reason = ""
      done(false)
      return false
    }
    delete data.sendData1.productCode
  }
  data.sendData1.orderId = data.currentOrderId
  OrderApi.updateBAOrderState(data.sendData1, 5).then((res) => {
    if (res.code) {
      Toast(res.message)
      done(false)
    } else {
      //刷新页面
      done()
      orderDetail()
    }
  })
}


/* 脱敏 */
const judgePhone = (val) =>{
  if (!val) return ""
  let reg = /^(.{3}).*(.{4})$/
  return val.replace(reg, "$1****$2")
}

onMounted(()=>{
  let getVueInstance = getCurrentInstance()
  data.proxy = getVueInstance ? getVueInstance.proxy : null
  if(data.proxy){
    data.user = data.proxy.$store.getters.user
  }

  const url = new URL(location.href)
  data.orderId = url.searchParams.get("orderId")
  if (data.user.userInfo == null) {
    //强登
    loginUtils.login(true, true, logined, false, false, "", "0", "5")
  } else {
    // console.log(data.user.userInfo)
    data.staffId = data.user.userInfo.staffId
    getDetail()
  }
  
})
</script>
<style>
body {
  background: #f9fafc;
}
.header-nav {
  height: 52.5px !important;
}
.header-nav__title {
  font-size: 18.75px !important;
}
</style>
<style lang="scss" scoped>
.telradio {
  border-radius: 50%;
  width: 20px;
  margin-left: 5px;
  display: inline-flex;
  height: 20px;
  background: #00ad298f;
  align-items: center;
  justify-content: center;
  transform: rotate(261deg);
}
.copyTel {
  margin-left: 5px;
}
.orderDetail {
  min-height: calc(100vh - 73px);
}
.breakAll {
  white-space: normal;
  word-break: break-all;
  word-wrap: break-word;
}
.xskd_ordedetail {
  background: #fff;
}

.order_title {
  font-size: 14px;
  color: #666666;
  background: #f6f6f6;
  height: 45px;
  line-height: 45px;
  padding-left: 15px;
}
.flex {
  display: flex;
}
.xskd_ordedetail .flex {
  padding: 0 15px;
}

.flex .orderdetail_label {
  width: 106px;
  margin-right: 17px;
  text-align: right;
}

.orderdetail_item .order_label,
.orderdetail_item .order_value {
  line-height: 40px;
  font-size: 13px;
}
.order_value {
  white-space: normal;
  word-wrap: break-word;
  word-break: break-all;
  flex: 1;
}

.icon {
  display: inline-block;
  width: 66px;
  height: 26px;
  line-height: 20px;
  margin-bottom: -7px;
}

.icon_tobereviewed {
  background: url("~@/assets/order/tobereviewed.png") no-repeat;
  background-size: 100%;
}

.icon_tobepaid {
  background: url("~@/assets/order/tobepaid.png") no-repeat;
  background-size: 100%;
}

.icon_appealable {
  background: url("~@/assets/order/appealable.png") no-repeat;
  background-size: 100%;
}

.icon_settlecancle {
  background: url("~@/assets/order/settlecancle.png") no-repeat;
  background-size: 100%;
}

.icon_settlecomplate {
  background: url("~@/assets/order/settlecomplate.png") no-repeat;
  background-size: 100%;
}

.icon_tobetransferred {
  background: url("~@/assets/order/tobetransferred.png") no-repeat;
  background-size: 100%;
}

.btn_flex {
  position: fixed;
  bottom: 0;
  width: 375px;
}

.detail_btn {
  text-align: center;
  font-size: 14px;
  height: 48px;
  line-height: 48px;
}

.appeal_btn {
  width: 187.5px;
  background: #64bbff;
  color: #ffffff;
}

.back_btn {
  flex: 1;
  background: #d9d9d9;
}
.xskd_orderlist {
  min-height: calc(100vh - 102.375px);
}
.dialogContent {
  font-size: 14px;
  margin: 18.75px 56.25px 11.25px;
}
.boradbandInput :deep(.van-field__body) {
  width: 94%;
  margin: 0 auto;
  border: 1px solid #ccc;
  padding: 3.75px 7.5px;
  border-radius: 7.5px;
}
.red {
  color: #ed2668 !important;
}
.green {
  color: #00ad29 !important;
}
.grey {
  color: #999999 !important;
}
.banlilh {
  line-height: 1.8 !important;
}

.addressLyl {
  padding-top: 9px;
}
.modelstyle{
  color:#bebaba;
  font-size: 10px;
  line-height: 1.8;
  margin-top: 10px;
  margin-bottom: 15px;
}
.wuliuStyle{
  font-size: 13px;
  line-height: 1.8;
  margin-top: 15px;  
}
.wuliuStyle2{
  margin-top: 0;
}
.listChildren{
  dl {
    dt{
      &:first-child{
        .wuliuStyle{
          margin-top: 0;
        }
      }
    }
  }
}
</style>
