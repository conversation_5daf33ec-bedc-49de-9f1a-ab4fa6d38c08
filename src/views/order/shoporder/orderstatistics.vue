<template>
  <div class="orderlist20220614Style">
    <Shopname
      v-if="statisticsConfig.showTitle"
      content="店铺订单"
      bg-color="#fff"
      :show-back="true"
    />
    <div
      v-if="statisticsConfig.myuser && statisticsConfig.hasrole"
      class="order-statistics"
      :class="{
        'padding-top-34':statisticsConfig.showTitle}"
    >
      <section
        :class="{
          top1:statisticsConfig.showTitle}"
        class="title-tab"
      >
        <ul>
          <li
            v-for="item in orderTabList"
            :key="item.value"
            :class="{'active':statisticsConfig.orderTab==item.value}"
            @click="changeTab(item)"
          >
            {{ item.title }}
          </li>
        </ul>
      </section>
      <div class="vant-clearfix">
        <div class="panel">
          <div class="date-range-flex">
            <div class="date-label">
              起止日期
            </div>
            <Field
              readonly
              clickable
              class="date-start"
              name="DatetimePicker "
              :value="statisticsConfig.startTimeName"
              placeholder="请选择"
              right-icon="arrow-down"
              @click="statisticsConfig.showTimePickerStart = true"
            />
            <Field
              readonly
              clickable
              name="DatetimePicker "
              :value="statisticsConfig.endTimeName"
              placeholder="请选择"
              right-icon="arrow-down"
              class="last-date"
              @click="openTimePickerEnd"
            />
          </div>
          <Field
            v-if="statisticsConfig.staffList && statisticsConfig.staffList.length > 2"
            readonly
            clickable
            name="picker"
            :value="statisticsConfig.staffName"
            label="店员姓名"
            placeholder="全部店员"
            right-icon="arrow-down"
            @click="openStaffPicker"
          />
          <Field
            readonly
            clickable
            name="picker"
            :value="statisticsConfig.goodsType"
            label="商品类型"
            right-icon="arrow-down"
            placeholder="全部商品"
            @click="statisticsConfig.showGoodsTypePicker = true"
          />
        </div>
        <div class="panel bg-blue">
          <div class="panel-title">
            <div class="panel-title-name">
              全部订单
            </div>
            <div
              class="panel-title-checkall"
              @click="goToList(1)"
            >
              查看订单
              <Icon
                name="arrow"
                class="filter-icon"
              />
            </div>
          </div>
          <div class="panel-content">
            <p class="panel-content-small">
              交易额（元）
            </p>
            <p class="panel-content-big">
              {{ regFenToYuan(statisticsConfig.statisticsData.orderPriceTotal) }}
            </p>
          </div>
          <div class="panel-footer">
            <div class="panel-footer-item">
              <p>办理成功数</p>
              <p>{{ statisticsConfig.statisticsData.successOrderTotal }}</p>
            </div>
            <div class="panel-footer-mid"></div>
            <div class="panel-footer-item">
              <p>办理用户数</p>
              <p>{{ statisticsConfig.statisticsData.userTotal }}</p>
            </div>
          </div>
        </div>
        <div class="panel bg-blue-light">
          <div class="panel-title">
            <div class="panel-title-name">
              本地订单
            </div>
            <div
              class="panel-title-checkall"
              @click="goToList(2)"
            >
              查看订单
              <Icon
                name="arrow"
                class="filter-icon"
              />
            </div>
          </div>
          <div class="panel-content">
            <p class="panel-content-small">
              交易额（元）
            </p>
            <p class="panel-content-big">
              {{ regFenToYuan(statisticsConfig.statisticsData.localOrderPriceTotal) }}
            </p>
          </div>
          <div class="panel-footer">
            <div class="panel-footer-item">
              <p>办理成功数</p>
              <p>{{ statisticsConfig.statisticsData.localSuccessCount }}</p>
            </div>
            <div class="panel-footer-mid"></div>
            <div class="panel-footer-item">
              <p>办理用户数</p>
              <p>{{ statisticsConfig.statisticsData.localUserCount }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else-if="statisticsConfig.hasrole===null"></div>
    <div v-else class="noActivity1">
      <img src="~@/assets/index_img/ice_tab_no.png" />
      <p>无权查看，请去自己的店铺</p>
    </div>
    <Popup v-model="statisticsConfig.showStaffPicker" position="bottom">
      <Picker
        title="店员姓名"
        show-toolbar
        :columns="statisticsConfig.staffList"
        @confirm="staffConfirm"
        @cancel="statisticsConfig.showStaffPicker = false"
      />
    </Popup>
    <Popup v-model="statisticsConfig.showGoodsTypePicker" position="bottom">
      <Picker
        title="商品类型"
        show-toolbar
        :columns="goodsTypeList"
        @confirm="goodsTypeConfirm"
        @cancel="statisticsConfig.showGoodsTypePicker = false"
      />
    </Popup>
    <Popup v-model="statisticsConfig.showTimePickerStart" position="bottom">
      <DatetimePicker
        type="date"
        :value="statisticsConfig.startTime"
        @confirm="TimeStartConfirm"
        @cancel="statisticsConfig.showTimePickerStart=false"
      />
    </Popup>
    <Popup v-model="statisticsConfig.showTimePickerEnd" position="bottom">
      <DatetimePicker
        type="date"
        :value="statisticsConfig.endTime"
        :min-date="statisticsConfig.endTimeMinDate"
        :max-date="statisticsConfig.endTimeMaxDate"
        @confirm="TimeEndConfirm"
        @cancel="statisticsConfig.showTimePickerEnd=false"
      />
    </Popup>
    <PrivilegedFooter :prop-icon-list="propIconList" :need-login="false" :user-info="statisticsConfig.myuser" />
  </div>
</template>

<script setup>
import Vue ,{onMounted, reactive, ref,getCurrentInstance} from "vue"
import { Col, Row, List, Popup, DatetimePicker,Picker, Toast,Field,Icon } from "vant"
import orderApi from "@/api/order"
import { parseTime,regFenToYuan,checkTime } from "@/utils/utils"
import UA from "@/utils/ua"
import Shopname from "@/components/index/headercon"
import loginUtils from "@/utils/login"
import loginApi from "@/api/login"
import { iosInputHandle } from "@/utils/ioscompatible"
import PrivilegedFooter from "@/views/privilegedactiveties/privilegedFooter.vue"
import {ENV} from "@/utils/env.js"
const statisticsConfig = reactive({
  ua: UA,
  myuser: null,
  staffList:null,
  // 是否展示店员筛选框
  showStaffPicker:false,
  // 是否展示日期筛选框
  showTimePickerStart:false,
  showTimePickerEnd:false,
  // 是否展示商品类型筛选框
  showGoodsTypePicker:false,
  // 选中的商品类型
  goodsType:null,
  // 选中的店员名称
  staffId:null,
  // 选中的起止日期
  startTime:new Date() ,
  endTime:new Date() ,
  endTimeMinDate:new Date(2010,0,0),
  endTimeMaxDate:new Date(2100,0,0),
  startTimeName:"" ,
  endTimeName:"" ,
  // 自有还是异业
  orderTab:1,
  // 是否展示页头
  showTitle:!UA.isWechat &&!UA.isWechatWork &&!UA.isApp &&!UA.isIosQQ &&!UA.isAndroidQQ,
  // 显示数据字段
  statisticsData:{
    orderPriceTotal:0,
    successOrderTotal:0,
    userTotal:0,
    localOrderPriceTotal:0,
    localSuccessCount:0,
    localUserCount:0
  },
  proxy:null,
  hasrole:null
})
const getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null
const orderTabList = ref([])

function changeTab(item){
  if(item.value==2){
    proxy.$router.push({
      path: '/order/list.html',
      query: { shopId:statisticsConfig.shopId }
    })
  }
  if(item.value==3){
    window.location.href = `${ENV.getB2bDomain()}/cnr-web/bossOrder?storeCode=`+statisticsConfig.myuser.unifiedChannelId
  }
}

const nm = '471' // 城市编码 471内蒙
let logined = (res) => {
  statisticsConfig.myuser = res
  if (statisticsConfig.myuser.staffProvince === nm) {
    orderTabList.value = [
      { title: "通信订单", value: 1 },
      { title: "号卡订单", value: 2 },
      { title: "终端订单", value: 3 }
    ]
  } else {
    orderTabList.value = [
      { title: "通信订单", value: 1 },
      { title: "终端订单", value: 3 }
    ]
  }
  if (statisticsConfig.myuser && res.shopId) {
    searchData.shopId = res.shopId
    statisticsConfig.hasrole = true

    if(statisticsConfig.myuser.RuleId == 2){
      getStatistics()
      getStaffs()
    }else{
      getStaffs()
    }
    statisticsConfig.url.searchParams.set("shopId",res.shopId)
    history.replaceState(null, null, statisticsConfig.url)
    if (res.shopId) {
      statisticsConfig.proxy.$store.commit("SET_SHOPID", res.shopId)
    }
  } else {
    statisticsConfig.hasrole = false //游客看不了
    statisticsConfig.orderlist = []
    document.getElementsByClassName("icpinfo")[0].style.display = "none"
  }
}
// 筛选
const searchData = reactive({
  staffId: "",
  goodsType: null,
  createTime: "",
  endTime: "",
  pageNo:1,
  pageSize:20
})
function TimeConfirm(val){
  searchData.createTime = parseTime(val[0],"{y}-{m}")
  searchData.endTime = parseTime(val[1],"{y}-{m}")
  statisticsConfig.timeRange = searchData.createTime + " — "+ searchData.endTime
  statisticsConfig.goodsType = val.text
  statisticsConfig.showTimePicker = false
  search()
}
// 起始时间选择完成
function TimeStartConfirm(val){
  searchData.createTime = parseTime(val,"{y}-{m}-{d}")
  statisticsConfig.startTime = val
  statisticsConfig.startTimeName = parseTime(val,"{y}-{m}-{d}")
  statisticsConfig.showTimePickerStart = false
  let currentYear = new Date(val).getFullYear()
  // 起止范围不超过3个月，根据起始时间来决定截止时间，方便用户操作
  statisticsConfig.endTimeMinDate = new Date(val)
  statisticsConfig.endTimeMaxDate = new Date(currentYear,new Date(val).getMonth()+3,new Date(val).getDate())
  search()
}
function openTimePickerEnd(){
  statisticsConfig.showTimePickerEnd = true
}
function TimeEndConfirm(val){
  searchData.endTime = parseTime(val,"{y}-{m}-{d}")
  statisticsConfig.endTimeName = parseTime(val,"{y}-{m}-{d}")
  statisticsConfig.showTimePickerEnd = false
  search()
}

/* 商品类型数据列表 */
const goodsTypeList = [
  { text: "全部商品", value: null },
  { text: "套餐",     value: 1 },
  { text: "增值业务", value: 2 }
]
/* 商品类型筛选结果 */
function goodsTypeConfirm(item){
  searchData.goodsType = item.value
  statisticsConfig.goodsType = item.text
  statisticsConfig.showGoodsTypePicker = false
  search()
}

function openStaffPicker(){
  if(statisticsConfig.myuser.RuleId ==2) {
    statisticsConfig.showStaffPicker = true
  }
}

/* 获取店员 */
function getStaffs(obj) {
  orderApi
    .getStaffs({
      shopId: statisticsConfig.myuser.shopId,
    })
    .then((res) => {
      if (res.code == 0) {
        let data = res.data
        if (data) {
          let staffObj = {}
          const newData = data.map((item) => {
            staffObj[item.staffId] = item.name
            return {
              text:item.name,
              value:item.staffId
            }
          })
          statisticsConfig.staffList = [{text:"全部店员",value:""}].concat(newData)
          if(statisticsConfig.myuser.RuleId == 1){
            staffConfirm({text:staffObj[statisticsConfig.myuser.staffId],value:statisticsConfig.myuser.staffId})
          }
        }
      }
    })
}
// 店员筛选结果
function staffConfirm(item){
  searchData.staffId = item.value
  statisticsConfig.staffName = item.text
  statisticsConfig.showStaffPicker = false
  search()
}

function search(){
  getStatistics()
}

function getStatistics(){
  if((!searchData.createTime) && searchData.endTime){
    Toast("请选择起始日期")
    return false
  }
  if(searchData.createTime && (!searchData.endTime)){
    Toast("请选择截止日期")
    return false
  }
  if(searchData.createTime && searchData.endTime && (new Date(searchData.createTime) > new Date(searchData.endTime))){
    Toast("截止日期要晚于起始日期")
    return false
  }
  if(checkTime(searchData.createTime,searchData.endTime,3)){
    Toast("起止日期的跨度最大支持3个月内的")
    return false
  }
  orderApi.statisticsOrder(searchData).then((res)=>{
    if(res && res.data){
      statisticsConfig.statisticsData = res.data
    }else{
      Toast(res.message)
      statisticsConfig.statisticsData = {}
    }
  })
}

function goToList(tab){
  if (statisticsConfig.proxy) {
    statisticsConfig.proxy.$router.push({
      path: "/shoporder/list.html",
      query: {
        orderTab: tab,
        shopId:statisticsConfig.shopId
      },
    })
  }
}

const propIconList = {
  shopindex:{
    inactive: require("@/assets/my/other.png"),
    active: require("@/assets/my/others-active.png"),
    title: "店铺首页",
    key:'shopindex',
    links: '',
    isactive:false,
    showLink:true
  },
  manage:{
    inactive: require("@/assets/my/management.png"),
    active: require("@/assets/my/management-active.png"),
    title: "店铺管理",
    key:'manage',
    links: '',
    isactive:true,
    showLink:true
  },
}


onMounted(() => {
  statisticsConfig.url = new URL(location.href)
  statisticsConfig.shopId = statisticsConfig.url.searchParams.get("shopId")
  const getVueInstance = getCurrentInstance()
  statisticsConfig.proxy = getVueInstance ? getVueInstance.proxy : null
  iosInputHandle()
  loginUtils.login(true, true, logined, false, false, "", null, 5)
})
</script>

<style lang="scss" scoped>
.padding-top-34{
  padding-top:34px;
}
.order-statistics {
  font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
    "Source Sans Pro", "Trebuchet MS", Arial;
  font-size: 14px;
  color: #2c2c2c;
  font-weight: 400;
  background: rgb(247, 248, 249);
  min-height: calc(100vh - 80px);
}
.noActivity1 {
  padding-top: 242px;
  text-align: center;
  line-height: 30px;
  font-size: 16px;
  color: #757575;
  font-weight: 400;
}
.vant-clearfix{
  padding-top:60px;
}
.title-tab{
  background: #fff;
  display: flex;
  margin-bottom: 5px;
  position: fixed;
  width:100%;
  padding-top:13px;
  z-index:100;
  ul{
    display: flex;
    justify-content: space-around;
    align-items: center;
    color:#212121;
    font-size:17.36px;
    // padding-left:15px;
    flex:1;
    li:first-child{
      border-right: 1px solid #F2F2F2;
    }
    li:last-child{
      border-left: 1px solid #F2F2F2;
    }
    li{
      width: 100%;
      height: 35px;
      padding:5px 0;
      text-align: center;
      &.active{
        font-size: 17.36px;
        color: #6C9FFF;
        // &:after{
        //   content:"";
        //   display: block;
        //   width: 26px;
        //   height: 4px;
        //   background: #506dfd;
        //   border-radius: 2px;
        //   margin:0 auto;
        //   margin-top:3px;
        // }
      }
    }
  }
  .filter{
    text-align: center;
    padding-right: 15px;
    p{
      font-size: 8px;
    }
  }
}
.panel{
  margin:0px 20px 6px;
  border-radius: 14px;
  overflow: hidden;
  .van-cell .van-field__value,.van-cell .van-field__label{
    padding:0px 3px 0 10px;
    box-sizing: border-box;
    border:1px solid #e5e5e5;
    border-radius: 3.5px;
  }
  .van-cell .van-field__label{
    border-color: transparent;
    text-align: right;
    font-size: 14px;
    font-weight: 700;
    color: #212121;
    width:70px;
    padding-left: 3px;
  }
  .van-cell .van-field__value{
    .van-field__control{
      font-size: 12px;
      font-weight: 500;
      color: #000000;
    }
  }
  .last-date{
    padding-left:2px;
  }
  @mixin blue-font{
    font-size: 11px;
    font-weight: normal;
    color:#d9e1ff;
  }
  @mixin blue-light-font{
    font-size: 11px;
    font-weight: normal;
    color: #686868;
  }
  .panel-title{
    margin:10px 10px 8px;
    display: flex;
    padding:10px;
    border-bottom:1px solid #b8c6ff;
    font-size: 14px;
    font-weight: 700;
    .panel-title-checkall{
      text-align: right;
      flex:1;
      @include blue-font;
    }
  }
  .panel-content{
    text-align: center;
    .panel-content-small{
      @include blue-font;
    }
    .panel-content-big{
      font-size: 35px;
      padding:5px 0 10px;
      font-weight: 500;
    }
  }
  .panel-footer{
    display: flex;
    justify-content: space-between;
    align-items: center;
    .panel-footer-item{
      width:165px;
      text-align: center;
      padding:10px;
      font-size: 21px;
      font-weight: 500;
      p:first-child{
        @include blue-font;
        margin-bottom: 7px;
      }
    }
    .panel-footer-mid{
      width: 1px;
      height: 30px;
      background: #b8c6ff;
    }
  }
  &.bg-blue{
    background: linear-gradient(270deg,#647cff 0%, #809eff 100%);
    color:#fff;
    .panel-footer{
      background: linear-gradient(180deg,#7e93ff 0%, #8ea9ff 100%);
    }
  }
  &.bg-blue-light{
    background: linear-gradient(0deg,#ffffff 0%, #c4e1ff 100%);
    color: #5574fa;
     .panel-title{
        border-bottom-color:rgba(#fff,.4);
     }
    .panel-title-checkall{
      color: #5c7dfb;
    }
    .panel-content-small{
      @include blue-light-font;
    }
    .panel-footer{
      background: #fff;
      .panel-footer-item{
        p:first-child{
          @include blue-light-font;
        }
      }

    }
  }
}
.date-range-flex{
  display: flex;
  width:100%;
  .date-label{
    width:82px;
    border-color: transparent;
    text-align: right;
    font-size: 14px;
    font-weight: 700;
    color: #212121;
    background: #fff;
    line-height: 46px;
  }
  .van-cell{
    width:110px;
    flex:1;
    &.date-start{
      padding-right: 0px;
    }

  }
}
</style>
