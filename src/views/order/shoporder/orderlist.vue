<template>
  <div class="orderlist20220614Style">
    <Shopname
      v-if="listConfig.showTitle"
      content="订单列表"
      bg-color="#fff"
      :show-back="true"
    />
    <div
      v-if="listConfig.myuser"
      class="orderlist1"
      :class="{
        'padding-top-34':listConfig.showTitle}"
    >
      <section
        :class="{
          top1:listConfig.showTitle}"
        class="title-tab"
      >
        <ul>
          <li
            v-for="item in orderTabList"
            :key="item.value"
            :class="{'active':listConfig.activeTab==item.value}"
            @click="changeTab(item)"
          >
            {{ item.title }}
          </li>
        </ul>
        <div class="filter" @click="listConfig.screeningBounced = true; setScrollTop()">
          <Icon
            name="filter-o"
            class="filter-icon"
            size="18"
          />
          <p>
            筛选
          </p>
        </div>

      </section>
      <!-- 筛选1 -->
      <ActionSheet
        v-model="listConfig.screeningBounced"
        :close-on-click-overlay="false"
      >
        <div class="nav">
          <Form
            ref="searchForm"
            :label-width="100"
            @submit="handleScreeningBounced('search')"
          >
            <div class="cancel">
              <Icon name="cross" @click="listConfig.screeningBounced = false" />
            </div>
            <Field
              v-model="listConfig.searchData.goodsName"
              name="goodsName"
              label="商品名称"
              placeholder="请输入商品名称"
              label-width="100px"
            />
            <Field
              v-if="staffList.length > 1"
              readonly
              :value="listConfig.filterConfig.shopAssistantName"
              name="staffNumber"
              label="店员姓名"
              right-icon="arrow-down"
              placeholder="请选择"
              label-width="100px"
              clearable
              @click="openStaffPicker"
            />
            <Field
              v-model="listConfig.searchData.mobile"
              name="staffMobile"
              label="客户手机号"
              type="tel"
              placeholder="请输入手机号码"
              label-width="100px"
            />
            <Field
              readonly
              clickable
              name="picker"
              :value="listConfig.filterConfig.goodsType"
              label="商品类型"
              right-icon="arrow-down"
              placeholder="请选择"
              @click="listConfig.filterConfig.showGoodsTypePicker = true"
            />
            <Field
              readonly
              clickable
              name="picker"
              :value="listConfig.filterConfig.result"
              label="办理结果"
              right-icon="arrow-down"
              placeholder="请选择"
              @click="listConfig.filterConfig.showResultPicker = true"
            />
            <Field
              readonly
              clickable
              name="createTimeStart"
              class="date-style"
              :value="date.createTimeStartName"
              label="起始时间"
              placeholder="0000-00-00"
              right-icon="notes-o"
              @click.prevent="openTimeStart"
            />
            <Field
              readonly
              clickable
              class="date-style"
              name="createTimeEnd"
              :value="date.createTimeEndName"
              label="截止时间"
              right-icon="notes-o"
              placeholder="0000-00-00"
              @click.prevent="openTimeEnd"
            />
            <div class="filter-btn-group">
              <Button
                plain
                block
                type="info"
                native-type="button"
                class="reset-btn filter-btn"
                @click="reset"
              >
                重置
              </Button>
              <Button
                plain
                block
                type="info"
                native-type="submit"
                class="confirm-btn filter-btn"
              >
                确定
              </Button>
            </div>
          </Form>
        </div>
        <Popup v-model="date.createTimeStartShowMaker" position="bottom">
          <DatetimePicker
            :value="date.createTimeStart"
            type="date"
            :min-date="date.minDate"
            :max-date="date.maxDate"
            @cancel="date.createTimeStartShowMaker = false"
            @confirm="confirmstart"
          />
        </Popup>
        <Popup v-model="date.createTimeEndShowMaker" position="bottom">
          <DatetimePicker
            :value="date.createTimeEnd"
            type="date"
            :min-date="date.minDate"
            :max-date="date.maxDate"
            @cancel="date.createTimeEndShowMaker = false"
            @confirm="confirmEnd"
          />
        </Popup>
        <Popup v-model="listConfig.filterConfig.showStaffPicker" position="bottom">
          <Picker
            v-if="listConfig.filterConfig.showStaffPicker"
            title="店员姓名"
            show-toolbar
            :columns="staffList"
            @confirm="staffConfirm"
            @cancel="listConfig.filterConfig.showStaffPicker = false"
          />
        </Popup>
        <Popup v-model="listConfig.filterConfig.showGoodsTypePicker" position="bottom">
          <Picker
            v-if="listConfig.filterConfig.showGoodsTypePicker"
            title="商品类型"
            show-toolbar
            :columns="goodsTypeList"
            @confirm="goodsTypeConfirm"
            @cancel="listConfig.filterConfig.showGoodsTypePicker = false"
          />
        </Popup>
        <Popup v-model="listConfig.filterConfig.showResultPicker" position="bottom">
          <Picker
            v-if="listConfig.filterConfig.showResultPicker"
            title="办理结果"
            show-toolbar
            :columns="resultList"
            @confirm="resultConfirm"
            @cancel="listConfig.filterConfig.showResultPicker = false"
          />
        </Popup>
      </ActionSheet>
      <List
        v-if="listConfig.orderlist && listConfig.orderlist.length > 0"
        v-model="listConfig.loading"
        :finished="listConfig.finished"
        class="vant-clearfix"
        :finished-text="listConfig.orderlist && listConfig.orderlist.length > 0 ? '没有更多啦……' : ''"
        @load="getList"
      >
        <div v-for="(item, key) in listConfig.orderlist" :key="key" class="list-item">
          <div class="list-item-title">
            <div class="titleleft">
              订单号：{{ item.orderId }}
            </div>
          </div>
          <div class="list-item-content" @click="gotodetail(item.orderId)">
            <div class="img">
              <img :src="item.imgId" width="100%" />
            </div>
            <div class="contentlist">
              <div class="content-item">
                <div class="label">
                  下单时间：
                </div>
                <div class="value">
                  {{ item.createTime }}
                </div>
              </div>
              <div class="content-item">
                <div class="label">
                  商品名称：
                </div>
                <div class="value">
                  {{ item.goodsName }}
                </div>
              </div>
            </div>
          </div>
          <div class="list-item-footer">
            <div class="content-item ">
              <span class="list-item-footer-label">
                订单金额：
              </span>
              <span class="list-item-footer-value">
                {{ item.priceSum/100 }} {{ item.unit ? item.unit : "元" }}
              </span>
            </div>
          </div>
        </div>
      </List>
      <div
        v-else-if="listConfig.orderlist && listConfig.orderlist.length == 0"
        class="noActivity1"
      >
        <img src="~@/assets/index_img/ice_tab_no.png" />
        <p>暂无数据，请耐心等待</p>
      </div>
      <div v-else></div>
    </div>
  </div>
</template>

<script setup>
import Vue ,{onMounted, reactive, ref,getCurrentInstance, toRefs} from "vue"
import {Button , List, Popup, Picker,DatetimePicker, Toast,Icon,ActionSheet,Form,Field } from "vant"
import orderApi from "@/api/order"
import { getImgUrl,parseTime,regFenToYuan2,setSearchParamsArray,setScrollTop,checkTime } from "@/utils/utils"
import UA from "@/utils/ua"
import Shopname from "@/components/index/headercon"
import loginUtils from "@/utils/login"
import { iosInputHandle } from "@/utils/ioscompatible"
const listConfig = reactive({
  orderlist: null,
  loading: false,
  finished: false,
  ua: UA,
  myuser: null,
  // 是否展示筛选框
  screeningBounced:false,
  showTitle:!UA.isWechat &&!UA.isWechatWork &&!UA.isApp &&!UA.isIosQQ &&!UA.isAndroidQQ,
  proxy:null,
  filterConfig:{},
  searchData:{},
  activeTab:1
})


const orderTabList = [
  { title: "全部订单", value: 1 },
  { title: "本地订单", value: 2 }
]

function changeTab(item){
  listConfig.searchData.orderTab = item.value //筛选用
  resetGetListConfig()
  listConfig.activeTab = item.value //页面显示用
  history.replaceState({},"",setSearchParamsArray(location.href, {orderTab:listConfig.searchData.orderTab}))
  getList()
}

const date = reactive({
  /* 开始时间 */
  createTimeStartName: "",
  /* 结束时间 */
  createTimeEndName: "",
  createTimeStart:new Date(),
  createTimeEnd:new Date(),
  /* 开始时间选择器显示与隐藏 */
  createTimeStartShowMaker: false,
  /* 结束时间选择器显示与隐藏 */
  createTimeEndShowMaker: false,
  minDate: new Date(2020, 0, 1),
  maxDate: new Date(2120, 0, 1),
})

function openTimeStart(){
  date.createTimeStartShowMaker = true
}

function openTimeEnd(){
  date.createTimeEndShowMaker = true

}

function confirmstart(val) {
  date.createTimeStartShowMaker = false
  listConfig.searchData.createTimeStart = parseTime(val, "{y}-{m}-{d}")
  date.createTimeStartName = parseTime(val, "{y}-{m}-{d}")
}
function confirmEnd(val) {
  date.createTimeEndShowMaker = false
  listConfig.searchData.createTimeEnd = parseTime(val, "{y}-{m}-{d}")
  date.createTimeEndName = parseTime(val, "{y}-{m}-{d}")
}

const filterConfig = {
  // 是否展示店员筛选框
  showStaffPicker:false,
  // 是否展示办理结果筛选框
  showResultPicker:false,
  // 是否展示商品类型筛选框
  showGoodsTypePicker:false,
  // 选中的商品类型
  goodsType:null,
  //办理结果
  result:null,
  //选中的店员
  shopAssistantName:null
}

listConfig.filterConfig = Object.assign({},filterConfig)

let searchData = {
  pageNo: 1,
  pageSize: 20,
  mobile:"",
  goodsType:"",
  goodsName:"",
  result:"",
  shopAssistantName:"",
  createTimeStart:"",
  createTimeEnd:"",
}

listConfig.searchData = Object.assign({},searchData)
listConfig.searchData.orderTab =1

/* 商品类型数据列表 */
const goodsTypeList = [
  { text: "套餐", value: 1 },
  { text: "增值业务", value: 2 },
  { text: "流量充值", value: 3 },
  { text: "话费充值", value: 4 }
]
/* 商品类型筛选结果 */
function goodsTypeConfirm(item){
  listConfig.searchData.goodsType = item.value
  listConfig.filterConfig.goodsType = item.text
  listConfig.filterConfig.showGoodsTypePicker = false
}

const staffList = ref([])
function openStaffPicker(){
  if(listConfig.myuser.RuleId == 2) {
    listConfig.filterConfig.showStaffPicker = true
  }
}
let staffObj = {}
/* 获取店员 */
function getStaffs(obj) {
  orderApi
    .getStaffs({
      shopId: listConfig.myuser.shopId,
    })
    .then((res) => {
      if (res.code == 0) {
        let data = res.data
        if (data) {
          const newData = data.map((item) => {
            staffObj[item.staffId] = item.name
            return {
              text:item.name,
              value:item.staffId
            }
          })
          staffList.value = newData
          if(listConfig.myuser.RuleId == 1){
            staffConfirm({text:staffObj[listConfig.myuser.staffId],value:listConfig.myuser.staffId})
          }
        }
      }
    })
}

// 店员筛选结果
function staffConfirm(item){
  listConfig.searchData.shopAssistantName = item.value
  listConfig.filterConfig.shopAssistantName = item.text
  listConfig.filterConfig.showStaffPicker = false
}

/* 商品类型数据列表 */
const resultList = [
  { text: "办理成功", value: "1" },
  { text: "办理失败", value: "2" },
]
/* 办理结果筛选结果 */
function resultConfirm(item){
  listConfig.searchData.result = item.value
  listConfig.filterConfig.result = item.text
  listConfig.filterConfig.showResultPicker = false
}

function resetGetListConfig(){
  listConfig.searchData.pageNo = 1
  listConfig.finished = false
  listConfig.orderlist = [] //每次筛选前都清空数据
  window.scrollTo(0, 1)
}
/* 筛选确认 */
function handleScreeningBounced() {
  resetGetListConfig()
  if((!listConfig.searchData.createTimeStart) && listConfig.searchData.createTimeEnd){
    Toast("请选择起始日期")
    return false
  }
  if(listConfig.searchData.createTimeStart && (!listConfig.searchData.createTimeEnd)){
    Toast("请选择截止日期")
    return false
  }
  if(listConfig.searchData.createTimeStart && listConfig.searchData.createTimeEnd){
    if( new Date(listConfig.searchData.createTimeStart) > new Date(listConfig.searchData.createTimeEnd)){
      Toast("截止日期要晚于起始日期")
      return false
    }
    if(checkTime(listConfig.searchData.createTimeStart,listConfig.searchData.createTimeEnd,3)){
      Toast("起止日期的跨度最大支持3个月内的")
      return false
    }
  }
  getList()
  listConfig.screeningBounced = false
}

/* 重置 */
function reset() {
  let orderTab = listConfig.searchData.orderTab
  // 日期显示重置
  date.createTimeStartName = ""
  date.createTimeEndName = ""
  date.createTimeStart = new Date()
  date.createTimeEnd = new Date()
  // 筛选项显示和提交参数重置
  listConfig.searchData = Object.assign({},searchData)
  listConfig.filterConfig = Object.assign({},filterConfig)
  listConfig.searchData.orderTab = orderTab
  if(listConfig.myuser.RuleId == 1){//1为店员2为店长
    staffConfirm({text:staffObj[listConfig.myuser.staffId],value:listConfig.myuser.staffId})
  }
}
// 订单列表
function getList() {
  if (!listConfig.myuser.staffId) {
    // Toast("请查看自己店铺的数据")
    listConfig.orderlist = []
    listConfig.loading = false
    listConfig.finished = true
    return false
  }
  listConfig.searchData.shopId = listConfig.myuser.shopId
  if (listConfig.myuser.RuleId == 1) {
    listConfig.searchData.shopAssistantName = listConfig.myuser.staffId
  }

  orderApi.getShopOrderList(listConfig.searchData).then((res) => {
    if (listConfig.searchData.pageNo == 1) {
      listConfig.orderlist = []
    }
    if (res.code) {
      Toast(res.message)
      listConfig.loading = false
      listConfig.finished = true
    } else {
      if (
        !(res.data && res.data.orderList && res.data.orderList.length > 0)
      ) {
        listConfig.loading = false
        listConfig.finished = true
        return false
      }
      res.data.orderList.forEach((value) => {
        value.imgId = value.imgId
          ? getImgUrl(value.imgId)
          : "//img1.staff.ydsc.liuliangjia.cn/file/tw66h3h59tqhhfyh.png"
        listConfig.orderlist.push(value)
      })
      listConfig.loading = false
      if (res.data.total > listConfig.orderlist.length) {
        listConfig.searchData.pageNo++
      } else {
        listConfig.finished = true
      }
    }
  })
}
function gotodetail(orderId) {
  if (listConfig.proxy) {
    listConfig.proxy.$router.push({
      path: "/shoporder/details.html",
      query: {
        orderId: orderId,
        shopId:listConfig.shopId
      },
    })
  }
}

function logined(res){
  listConfig.myuser = res
  if (listConfig.myuser && res.shopId) {
    listConfig.searchData.shopId = res.shopId
    getList()
    getStaffs()
    listConfig.hasrole = true
  } else {
    listConfig.hasrole = false //游客看不了
    listConfig.orderlist = []
  }
}
onMounted(() => {
  const url = new URL(location.href)
  listConfig.shopId = url.searchParams.get("shopId")
  listConfig.searchData.orderTab = url.searchParams.get("orderTab") && url.searchParams.get("orderTab")==2? 2 : listConfig.searchData.orderTab
  listConfig.activeTab = listConfig.searchData.orderTab
  const getVueInstance = getCurrentInstance()
  listConfig.proxy = getVueInstance ? getVueInstance.proxy : null
  if (listConfig.proxy) {
    listConfig.proxy.$store.commit("SET_SHOPID", listConfig.shopId)
  }
  window.scrollTo(0, 1)
  // listConfig.searchData.orderTab
  iosInputHandle()
  loginUtils.login(true, true, logined, false, false, "", null, 5)
})
</script>
<style lang="scss" scoped>
.head{
  height: 50px !important;
  line-height: 50px !important;
  color:#696a6c!important;
  i{
    top:17px!important;
  }
  span{
    font-size: 17px;
    color:#696a6c!important;
    font-weight: 500;
  }
}
</style>
<style lang="scss" scoped>
.padding-top-34{
  padding-top:34px;
}
.icpinfo{
  margin-bottom: 0;
}
.orderlist1 {
  font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
    "Source Sans Pro", "Trebuchet MS", Arial;
  font-size: 14px;
  color: #2c2c2c;
  font-weight: 400;
  background: rgb(247, 248, 249);
  min-height: calc(100vh - 80px);
  .noActivity1 {
    padding-top: 242px;
    text-align: center;
    line-height: 30px;
    font-size: 16px;
    color: #757575;
    font-weight: 400;
  }
}
.list-item {
  width: 334px;
  background: #ffffff;
  border-radius: 8px;
  margin:0 auto 10px;
  &:last-child {
    margin-bottom: 0;
  }
  .list-item-title {
    height: 47px;
    .titleleft,
    .titleright {
      margin: 0 18px;
      line-height: 47px;
      height: 47px;
      font-size: 15px;
      font-weight: 500;
      color: #686868;
    }
    .titleleft {
      float: left;
    }
    .titleright {
      float: right;
    }
  }
  .list-item-content,.list-item-footer {
    display: flex;
    align-items: center;
    padding: 10px;
    .img {
      width: 70px;
      height: 70px;
    }
    .content-item {
      width: 220px;
      font-size: 14px;
      text-align: left;
      color: #686868;
      line-height: 24px;
      display: flex;
      font-weight: 400;
      .label {
        padding-left: 10px;
        text-align: right;
      }
      .value {
        flex: 1;
        text-align: left;
        word-break: break-all;
      }
    }
  }
  .list-item-content{
    border-bottom: 1px solid #eeeeee;
    border-top: 1px solid #eeeeee;
    margin:0 auto;
    width:317px;
  }
  .list-item-footer{
    padding:4px 10px;
    .content-item{
      display: inline;
      width: 100%;
      text-align: right;
      padding-right: 5px;
      .list-item-footer-value{
        color:#fa4e08;
        font-weight: 500;
      }
    }
  }
  .btngroup {
    margin: 10px 15px;
    .btn-item {
      width: 73px;
      height: 24px;
      border: 1px solid #979797;
      border-radius: 12.5px;
      font-weight: 400;
      text-align: left;
      color: #979797;
      line-height: 21px;
      text-align: center;
      float: right;
      margin-left: 10px;
      &.btnblue {
        color: #5099fe;
        border-color: #5099fe;
      }
    }
  }
}

.vant-clearfix{
  padding-top:65px;
}
.title-tab{
  background: #fff;
  display: flex;
  margin-bottom: 15px;
  padding-top:20px;
  width:100%;
  position: fixed;
  ul{
    display: flex;
    color:#212121;
    font-size:14px;
    padding-left:15px;
    flex:1;
    li{
      padding:5px 18px;
      &.active{
        font-size: 17.36px;
        font-weight: 700;
        &:after{
          content:"";
          display: block;
          width: 26px;
          height: 4px;
          background: #506dfd;
          border-radius: 2px;
          margin:0 auto;
          margin-top:3px;
        }
      }
    }
  }
  .filter{
    text-align: center;
    padding-right: 15px;
    p{
      font-size: 8px;
    }
  }
}
.cancel{
  text-align: right;
  height:47px;
  font-size:16px;
  border-bottom:1px solid #ebedf0 ;
  padding:20px;
}
.van-cell,.filter-btn-group{
  width:347px;
  margin:0 auto
}
.filter-btn-group{
  width:315px;
  display: flex;
  justify-content: space-between;
  padding:10px 0;
  border-top:1px solid #ebedf0 ;
  font-size: 12px;
  font-weight: 500;
  border-radius: 3px;
  .reset-btn{
    border: 1px solid #e5e5e5;
    color: #686868;
    background: #e5e5e5;
    height:45px;
    margin-right:6px;
  }
  .confirm-btn{
    border: 1px solid #506dfd;
    color: #fff;
    background: #506dfd;
    height:45px;
  }
}
.van-cell .van-field__label{
  font-size: 14px;
  font-weight: 700;
  color: #212121;
}
.date-style.van-cell:after{
  border:none;
}
.date-style.van-cell .van-field__value{
  padding:0px 3px 0 10px;
  box-sizing: border-box;
  border:1px solid #e5e5e5;
  border-radius: 3.5px;
  width:124px;
}
.date-style.van-cell .van-field__label{
  padding:0px;
}
</style>
