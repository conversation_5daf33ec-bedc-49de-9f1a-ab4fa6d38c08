<template>
  <div class="orderDetail">
    <Shopname 
      v-if="data.showTitle"
      content="订单详情"
      bg-color="#fff"
      :show-back="true"
    />
    <div 
      v-if="data.orderdetail" 
      class="xskd_ordedetail" 
      :class="{
        'padding-top-34':data.showTitle}"
    >
      <ul>
        <!-- 商品信息 -->
        <li class="orderdetail_item">
          <div class="order_title">
            商品信息
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              商品类型：
            </div>
            <div class="order_value">
              {{ data.orderdetail.goodsTypeName }}
            </div>
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              产品编码：
            </div>
            <div class="order_value">
              {{ data.orderdetail.bossId }}
            </div>
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              商品名称：
            </div>
            <div class="order_value">
              {{ data.orderdetail.goodsName }}
            </div>
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              档位名称：
            </div>
            <div class="order_value">
              {{ data.orderdetail.contractsName }}
            </div>
          </div>
          <div v-if="data.orderdetail.priceSum + ''" class="flex">
            <div class="order_label orderdetail_label">
              订单金额：
            </div>
            <div class="order_value red">
              {{ data.orderdetail.priceSum == 0 ? 0 : data.orderdetail.priceSum / 100 }} 
              {{ data.orderdetail.unit ? data.orderdetail.unit : "元" }}
            </div>
          </div>
        </li>
        <!-- 订单信息 -->
        <li class="orderdetail_item">
          <div class="order_title">
            订单信息
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              办理结果：
            </div>
            <div class="order_value">
              {{ data.orderdetail.pushStatus=="1" ? "办理成功" : data.orderdetail.pushStatus=="2" ? "办理失败" :"" }}
              <!-- 1办理成功 2办理失败 -->
            </div>
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              是否本地市用户办理：
            </div>
            <div class="order_value">
              {{ data.orderdetail.orderCity=="1" ? "是" : data.orderdetail.orderCity=="2" ? "不是" :"" }}
              <!-- 1是 2不是 -->
            </div>
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              订单号：
            </div>
            <div class="order_value">
              {{ data.orderId }}
            </div>
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              下单时间：
            </div>
            <div class="order_value">
              {{ data.orderdetail.createTime }}
            </div>
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              客户手机号：
            </div>
            <div class="order_value">
              {{ getPassPhone(data.orderdetail.mobile) }}
            </div>
          </div>
        </li>
        <!-- 推荐信息 -->
        <li class="orderdetail_item">
          <div class="order_title">
            推荐信息
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              推荐人姓名：
            </div>
            <div class="order_value">
              {{ data.orderdetail.name }}
            </div>
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              推荐人工号：
            </div>
            <div class="order_value">
              {{ data.orderdetail.staffNumber }}
            </div>
          </div>
          <div class="flex">
            <div class="order_label orderdetail_label">
              推荐人手机号：
            </div>
            <div class="order_value">
              {{ getPassPhone(data.orderdetail.phone) }}
            </div>
          </div>
        </li>
      </ul>
    </div>
    <Footer />
  </div>
</template>

<script setup>
import headerNav from "@/views/my/components/headerNav"
import Shopname from "@/components/index/headercon"
import loginUtils from "@/utils/login"
import loginApi from "@/api/login"
import OrderApi from "@/api/order"
import Vue,{ref,reactive,onMounted, computed,getCurrentInstance} from "vue"
import { Toast, Dialog, RadioGroup, Radio, Field, Icon } from "vant"
import { getPassPhone } from "@/utils/utils"
import {goodsTypeObj} from "@/utils/goodstype"
import UA from "@/utils/ua"
Vue.use(Dialog)

const showChangeOrderStatus = ref(false)
const data = reactive({
  title: "订单详情",
  orderdetail: null,
  orderId: null,
  ruleId: null,
  shopId: null,
  showTitle:!UA.isWechat &&!UA.isWechatWork &&!UA.isApp &&!UA.isIosQQ &&!UA.isAndroidQQ,
})

const logined = (res) => {
  if (res && res.shopId) {
    data.shopId = res.shopId
    orderDetail()
  }
}

const orderDetail = () => {
  OrderApi.getOrderDetails({
    orderId: data.orderId,
    shopId:data.shopId
  }).then((res) => {
    if (res.code) {
      Toast(res.message)
    } else {
      data.orderdetail = res.data
      data.orderdetail.goodsTypeName = goodsTypeObj[res.data.goodsType]
    }
  })
}

const getDetail = () => {
  loginApi
    .getUserInfo(localStorage.getItem("yundianToken"), "0")
    .then((res) => {
      data.staffId = res.data.staffId
      orderDetail()
    })
}

onMounted(()=>{
  let getVueInstance = getCurrentInstance()
  data.proxy = getVueInstance ? getVueInstance.proxy : null
  if(data.proxy){
    data.user = data.proxy.$store.getters.user
  }

  const url = new URL(location.href)
  data.orderId = url.searchParams.get("orderId")
  loginUtils.login(true, true, logined, false, false, "", null, 5)
})
</script>
<style lang="scss">
body {
  background: #f9fafc;
}
.head{
  height: 50px !important;
  line-height: 50px !important;
  color:#696a6c!important;
  i{
    top:17px!important;
  }
  span{
    font-size: 17px;
    color:#696a6c!important;
    font-weight: 500;
  }
}
</style>
<style lang="scss" scoped>
.telradio {
  border-radius: 50%;
  width: 20px;
  margin-left: 5px;
  display: inline-flex;
  height: 20px;
  background: #00ad298f;
  align-items: center;
  justify-content: center;
  transform: rotate(261deg);
}
.copyTel {
  margin-left: 5px;
}
.orderDetail {
  min-height: calc(100vh - 73px);
}
.breakAll {
  white-space: normal;
  word-break: break-all;
  word-wrap: break-word;
}

.order_title {
  font-size: 14px;
  font-weight: 700;
  color: #212121;
  height: 45px;
  line-height: 45px;
  padding-left: 15px;
  position: relative;
  &::after{
    content:"";
    width:4px;
    height: 14px;
    display: block;
    background: #506DFD;
    position: absolute;
    left:0;
    bottom:15px;
  }
}
.flex {
  display: flex;
}
.xskd_ordedetail{
  padding:15px 0;
  &.padding-top-34{
    padding-top:65px;
  }
}
.xskd_ordedetail .flex {
  padding: 0 15px;
}

.flex .orderdetail_label {
  text-align: right;
}

.orderdetail_item{
  width:335px;
  background: #fff;
  margin:0 auto;
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 20px;
  padding-top:5px;
  &:first-child{
    border-top-left-radius:14px ;
    border-top-right-radius:14px ;
  }
  &:last-child{
    border-bottom-left-radius:14px ;
    border-bottom-right-radius:14px ;
  }
}

.orderdetail_item .order_label,
.orderdetail_item .order_value {
  line-height: 28px;
  font-size: 14px;
  color: #686868;
}
.order_value {
  white-space: normal;
  word-wrap: break-word;
  word-break: break-all;
  flex: 1;
  &.red {
    color:#fa4e08;
    font-weight: 500;
  }
}

.green {
  color: #00ad29 !important;
}
.grey {
  color: #999999 !important;
}
.banlilh {
  line-height: 1.8 !important;
}
</style>
