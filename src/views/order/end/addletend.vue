<template>
  <div class="addletend">
    <div class="addletend_title">
      <h6 v-if="data.user">
        {{ data.user.UserName }}
        <i v-if="data.orderDetail && data.orderDetail.nickname">
          {{ data.orderDetail.nickname }}
        </i>
        <span v-if="data.orderDetail && data.orderDetail.pageUrl" @click="viewDetails()">
          查看详情
        </span>
      </h6>
      <div v-if="data.orderDetail" class="title_con">
        <h1>
          <p v-if="data.orderDetail.orderStatusDesc">
            {{ data.orderDetail.orderStatusDesc }}
          </p>
          <span v-if="data.isWeChatMiniApp" @click="goSubMsg()">
            办理提醒
          </span>
        </h1>
        <div>
          <dl v-if="data.orderDetail.goodsName">
            <dt>业务名称</dt>
            <dd>{{ data.orderDetail.goodsName }}</dd>
          </dl>
          <dl v-if="data.orderDetail.userMobile">
            <dt>办理号码</dt>
            <dd>{{ data.orderDetail.userMobile }}</dd>
          </dl>
          <dl v-if="data.orderDetail.businessDate">
            <dt>办理日期</dt>
            <dd>{{ data.orderDetail.businessDate }}</dd>
          </dl>
          <dl v-if="data.orderDetail.price">
            <dt>业务价格</dt>
            <dd>￥{{ (data.orderDetail.price / 100).toFixed(2) }}</dd>
          </dl>
        </div>
      </div>
      <div v-if="data.detailNull && data.bannerArr.length == 0 && data.firstFloorList.length == 0 && data.secondFloorList.length == 0 && data.thirdFloorList.length == 0" class="title_con">
        <p class="title_con_null">
          系统异常，请稍后重试
        </p>
      </div>
    </div>
    <div v-if="data.bannerArr.length > 0" class="swiper-thumbs swiper-container">
      <swiper
        v-if="data.bannerArr.length > 1"
        class="centerbanner-swiper"
        :options="centerBannerOption"
      >
        <swiper-slide v-for="item in data.bannerArr" :key="item.id">
          <div v-if="item.imageSrc" class="centerbanner" @click="goImgLink(item.bannerUrl)">
            <img :src="getImgUrl(item.imageSrc)" />
          </div>
        </swiper-slide>
        <div slot="pagination" class="swiper-pagination"></div>
      </swiper>
      <div v-if="data.bannerArr.length === 1" class="centerbanner" @click="goImgLink(data.bannerArr[0].bannerUrl)">
        <img :src="getImgUrl(data.bannerArr[0].imageSrc)" />
      </div>
    </div>
    <div v-if="data.firstFloorList.length > 0" class="addletend_con">
      <h2 v-if="data.firstFloorTitle">
        {{ data.firstFloorTitle }}
      </h2>
      <div class="addletend_con_goods addletend_flex">
        <div v-for="(item, key) in data.firstFloorList" :key="item.id">
          <img :src="getImgUrl(item.imageSrc)" @click="insertCodeGoods(key+1,item.goodsId,item.goodsSource,item.goodsLink)" />
          <h6>{{ item.goodsTitle }}</h6>
          <p>{{ item.goodsSubtitle }}</p>
          <a href="javascript:void(0);" @click="insertCodeGoods(key+1,item.goodsId,item.goodsSource,item.goodsLink)">
            <span v-if="item.price && item.price != 0">
              ¥{{ (item.price/100).toFixed(2) }}
            </span>
            <span :class="!item.price || item.price == 0 ? 'hot_goods_font' : ''" class="hot_goods">
              热门商品
            </span>
          </a>
        </div>
      </div>
    </div>
    <div v-if="data.secondFloorList.length > 0" class="addletend_con">
      <h2 v-if="data.secondFloorTitle">
        {{ data.secondFloorTitle }}
      </h2>
      <div class="addletend_con_goods padding_min">
        <div v-for="(item, key) in data.secondFloorList" :key="item.id">
          <img :src="getImgUrl(item.imageSrc)" @click="insertCodeGoods(key+1,item.goodsId,item.goodsSource,item.goodsLink)" />
          <h6>{{ item.goodsTitle }}</h6>
          <p>{{ item.goodsSubtitle }}</p>
          <a href="javascript:void(0);" @click="insertCodeGoods(key+1,item.goodsId,item.goodsSource,item.goodsLink)">
            <span v-if="item.price && item.price != 0">
              ¥{{ (item.price/100).toFixed(2) }}
            </span>
            <span v-if="!item.price || item.price == 0" class="hot_goods hot_goods_font">
              精选推荐
            </span>
          </a>
        </div>
      </div>
    </div>
    <div v-if="data.thirdFloorList.length > 0" class="addletend_con">
      <h2 v-if="data.thirdFloorTitle">
        {{ data.thirdFloorTitle }}
      </h2>
      <div class="addletend_con_goods padding_min">
        <div v-for="(item, key) in data.thirdFloorList" :key="item.id" class="maxwidth">
          <img :src="getImgUrl(item.imageSrc)" @click="insertCodeGoods(key+1,item.goodsId,item.goodsSource,item.goodsLink)" />
          <div class="displ_flex">
            <div :class="!item.price || item.price == 0 ? 'goods_center' : ''">
              <h6>{{ item.goodsTitle }}</h6>
              <p>{{ item.goodsSubtitle }}</p>
            </div>
            <a v-if="item.price && item.price != 0" href="javascript:void(0);" @click="insertCodeGoods(key+1,item.goodsId,item.goodsSource,item.goodsLink)">
              <span>
                ¥{{ (item.price/100).toFixed(2) }}
              </span>
            </a>
          </div>
        </div>
      </div>
    </div>
    <div v-if="data.listArray.length > 0" class="footer">
      <Tabbar>
        <TabbarItem
          v-for="item in data.listArray"
          :key="item.id"
          @click="onNavUrl(item.iconUrl)"
        >
          <template #icon="">
            <img :src="getImgUrl(item.imageSrc)" />
            <p>{{ item.iconTitle }}</p>
          </template>
        </TabbarItem>
      </Tabbar>
    </div>
  </div>
</template>
<script setup>
import Vue , { reactive, onBeforeMount, getCurrentInstance } from "vue"
import { Tabbar, TabbarItem } from "vant"
import UA from '@/utils/ua'
import {getImgUrl} from "@/utils/utils.js"
import shareUtilApi from '@/utils/share'
import loginUtils from '@/utils/login'
import insertCode from "@/utils/insertCode.js"
import orderApi from "@/api/order"
import "swiper/dist/css/swiper.css"
import VueAwesomeSwiper  from 'vue-awesome-swiper'
Vue.use(VueAwesomeSwiper)
const data = reactive({
  title: '服务提醒',
  user: null,
  isLogined: null,
  shopId: null,
  orderType: null,
  orderId: null,
  orderDetail: null,
  bannerArr: [],
  listArray: [],
  firstFloorTitle: null,
  firstFloorList: [],
  secondFloorTitle: null,
  secondFloorList: [],
  thirdFloorTitle: null,
  thirdFloorList: [],
  isWeChatMiniApp: false,
  detailNull: false,
  proxy: null
})
const RESULTCODE = {
  SUCCESS: 0,
  FIRSTFLOOR: 1,
  SECONDFLOOR: 2,
  THIRDFLOOR: 3
}

const shareConfig = {
  title: '服务提醒',
  url: window.location.href,
  desc: '',
  imgUrl: location.origin + require("@/assets/nearby/appnearby/wx_logo.jpg"),
}

const logined = (res) => {
  data.user = res
  data.isLogined = res && res.UserName > ''
  getWxMiniOrderDetail()
  getAdDaseInfo()
  getAdQueryFloorGood()
  shareUtilApi.changeWxShareConfig(shareConfig)
}

const centerBannerOption = reactive({
  pagination: {
    el: '.swiper-pagination',
    type:"fraction"
  },
  autoplay: { delay: 5000 },
  loop:true
})

onBeforeMount(async()=>{
  const url = new URL(location.href)
  data.shopId = url.searchParams.get('shopId')||url.searchParams.get('shop_id')
  data.orderType = url.searchParams.get('orderType')
  data.orderId = url.searchParams.get('orderId')
  const getVueInstance = getCurrentInstance()
  data.proxy = getVueInstance? getVueInstance.proxy:null
  data.proxy = getCurrentInstance().proxy
  data.isWeChatMiniApp = await UA.isWeChatMiniApp()
  loginUtils.login(true, true, logined, false, false, '', null, 5)
})
//订单信息接口
function getWxMiniOrderDetail(){
  orderApi.getWxMiniOrderDetail({
    shopId:data.shopId,
    orderType:data.orderType,
    orderId:data.orderId
  }).then((res) => {
    if(res.code==RESULTCODE.SUCCESS && res.data){
      data.orderDetail= res.data
    }else{
      data.detailNull= true
    }
  })
}
//查看详情
function viewDetails(){
  window.location.href = data.orderDetail.pageUrl
}
//查询banner和icon
function getAdDaseInfo(){
  orderApi.getAdDaseInfo({ shopId:data.shopId }).then((res) => {
    if(res.code == RESULTCODE.SUCCESS && res.data){
      data.bannerArr = res.data.bannerList
      data.listArray = res.data.iconList
    }
  })
}
//查询运营位楼层商品信息
function getAdQueryFloorGood(){
  orderApi.getAdQueryFloorGood({ shopId:data.shopId }).then((res) => {
    let len = res.data.content
    if(res.code == RESULTCODE.SUCCESS && res.data && len && len.length > 0){
      len.forEach((item)=>{
        if(item.titleLocation === RESULTCODE.FIRSTFLOOR){
          data.firstFloorTitle = item.pageTitle
          data.firstFloorList = item.goodsList ? item.goodsList : []
        }else if(item.titleLocation === RESULTCODE.SECONDFLOOR){
          data.secondFloorTitle = item.pageTitle
          data.secondFloorList = item.goodsList ? item.goodsList : []
        }else if(item.titleLocation === RESULTCODE.THIRDFLOOR){
          data.thirdFloorTitle = item.pageTitle
          data.thirdFloorList = item.goodsList ? item.goodsList : []
        }
      })
    }
  })
}
function insertCodeGoods(key,goodsId,source,url){
  let dcs_id = key+'_'+goodsId+'_'+source
  let acid = (url.indexOf('?') > -1 ? '&':'?')+'WT.ac_id=SHOP_MINIPROGRAM_SHARE'
  insertCode("goods_"+dcs_id,url+acid)
}
//弹出小程序信息推送授权提醒
async function goSubMsg(){
  let currentUrl = location.href
  let pageUrl = `/subPackages/submsg/submsg?shopId=${data.shopId}&tmplIdNew=1&backUrl=${encodeURIComponent(currentUrl)}`
  if(data.isWeChatMiniApp){
    window.wx.miniProgram.navigateTo({url: pageUrl})
  }
}
function goImgLink(targetUrl){
  if(targetUrl){
    location.href = targetUrl+(targetUrl.indexOf('?') > -1 ? '&':'?')+'WT.ac_id=SHOP_MINIPROGRAM_SHARE'
  }
}
function onNavUrl(url){
  if(url){
    window.location.href = url+(url.indexOf('?') > -1 ? '&':'?')+'WT.ac_id=SHOP_MINIPROGRAM_SHARE'
  }
}
</script>
<style>
body,#app {
  background: #f7f8f9;
}
.icpinfo {
  margin-bottom: 80px;
}
</style>
<style lang="scss" scoped>
.addletend_title{
  background: linear-gradient(180deg,#2a7af8, rgba(42,122,248,0.82) 28%, rgba(216,216,216,0.00) 94%);
  padding: 12px 15px 10px;
  h6{
    height: 22px;
    font-size: 16px;
    line-height: 22px;
    color: #ffffff;
    position: relative;
    font-weight: Medium;
    margin-bottom: 22px;
    i{
      font-size: 14px;
      padding: 0 8px 0 3px;
    }
    span{
      position: absolute;
      right: 0;
      top: 0;
      font-size: 12px;
      padding-right: 12px;
      background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAAXNSR0IArs4c6QAAAFpJREFUKFN90NEJgDAMhOH7Z3MMXUZcRXdyAbcQTvpQaJvUPIXcFwJBTdnegLOd1Z4BvpJ24BhxB0toO8UBznAKM/wHH0k3sJTF2ekOpdB2QAHOUAYvYM0e/gFtfzALeEcg4QAAAABJRU5ErkJggg==) no-repeat right center;
      background-size: auto 10px;
    }
  }
  .title_con{
    background: #ffffff;
    border-radius: 8px;
    padding-bottom: 13px;
    h1{
      color: #000000;
      line-height: 29px;
      font-size: 21px;
      text-align: center;
      font-weight: Medium;
      padding: 32px 0 16px;
      position: relative;
      p{
        display: inline-block;
        margin: 0 auto;
        padding-left: 40px;
        background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB8AAAAeCAYAAADU8sWcAAAAAXNSR0IArs4c6QAAAnxJREFUSEvFl89rE1EQx78TsVpDbGIIRbEHIfEgehE9KYjiRbLxUkMLoghalYIk0ehRo6CoFbKeBH+A0ougeEnwDxB7FUUvNTkYK20l/qjaak00I/vIrslustlsE3YPgbeZmc/3zZs3+x6hjSeSix1lcl0A84DBjWiKuHIxE7p5z2pIamUYnUqs+fWb82D4Wtlq/xO+9q6g4KOB9BczH1O4lI+/BmOzZajekPAmG5S3NPNvCI9Mp1bxwtyCbajOkdxed2Zd6qdRm+6NSPMif+4UWI3Tu5L8+mWonzkzSflEpdNgNV42mHaBiNVxHTySi5cYWN4tOAHlTEjuMcAjb+NJJox1C6wBGWczG+UbylibuZSLa+notoBsSBZc8SPl4scA3OkkdDQQxR/8xe3ik0ZhR7Ih+a4KV4qsZcOxKm58wyV4l3mE+bXZB3g+/1LvytmQ7FLhHUt5LVghHi9cxkz5k0G3knraXzznqcyVvludlZmdHnyr+BhPv000dHF5e1aTlI8NgenhUuHtgAWLeJjC+cQZYhalr3+2uzfh/NoRTMy/wtXZ+031tQ0GwERJU3gmmNaAk4sFJD/IBgF2wEoQAZcmY0NwNU77aOAA9vXt0IDvSjM49f66NrYLFgEqPNyy4E73H8RuzzYNOF0u4kThCpYEBiAKrtpkTLfaycAgwn07m665WVU3cxJbrQpv2WQO+8OI+vYaYtkBK0te22QstddB3x4c8Uc0ATbBiv//9mol9Spxl2crkv2HMPZxHM9+vLDVHuo+LEoERz+pQoBThwmRPyePUQrfuQNktXwcOzrXlq8jl4ZaAY5dl/SbWFwUQSkA6w0b3MZF8R/ItSaVx8/PCwAAAABJRU5ErkJggg==) no-repeat left center;
        background-size: auto 30px;
      }
      span{
        position: absolute;
        top: 13px;
        right: 15px;
        color: #0088ff;
        line-height: 17px;
        font-size: 12px;
        font-weight: normal;
        padding-left: 16px;
        background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAAXNSR0IArs4c6QAAAKpJREFUKFO9kaEOwjAURc+bwCEYf4AnSARmn8MPELYvoDNIfgeHQBIsGrWQIGceKaNdSik46tqb83pyn5A6RpVS5D0OH1SFmgYYAkdgDtxZM0ZELRwCdqqSU8nNT95qTkvjfuuBWg9kLFnJKbI0OgN2lLLogYSzh1/5v4CNFgj7TzUGShlFp/TL31HG9t4BV8BW2SYXCQNgFG3yC/CMYsDoBZh4UDlTydTdH96IQMnLrqsvAAAAAElFTkSuQmCC) no-repeat left center;
        background-size: auto 12px;
      }
    }
    div{
      background: #f3f8ff;
      border-radius: 8px;
      margin: 0 15px;
      padding: 10px 12px;
      dl{
        display: flex;
        align-items: center;
        color: #333333;
        line-height: 28px;
        font-size: 13px;
        dt{
          width: 52px;
          margin-right: 14px;
        }
        dd{
          flex: 1;
        }
      }
    }
    .title_con_null{
      height: 340px;
      line-height: 340px;
      text-align: center;
    }
  }
}
.swiper-thumbs{
  padding: 10px 0;
  width: 343px;
  .centerbanner-swiper{
    .swiper-pagination-fraction{
      text-align: center;
      color: #fcfbfb;
      bottom:6px;
      width:40px;
      right:6px;
      left:auto;
      background: rgba(0,0,0,0.2);
      height:18px;
      line-height: 18px;
      border-radius: 4px;
      font-size: 12px;
    }
  }
  .centerbanner{
    width: 343px;
    height: 98px;
    margin:0px auto;
    border-radius: 8px;
    overflow: hidden;
    img{
      width: 100%;
      height: 100%;
    }
  }
}
.addletend_con{
  h2{
    font-size: 16px;
    color: #333333;
    line-height: 22px;
    margin: 0 12px 9px;
    font-weight: 600;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAAPCAYAAAA2yOUNAAAAAXNSR0IArs4c6QAAAEJJREFUKFNjZEACbP3/G/4zMtQz/mdo/FXI2ACTYkRWxDrh/38Y/3cBI1xuVBHDsAmCwwwMDDYMDAxHfhcw2sIiGwBaMEAQKkPwAgAAAABJRU5ErkJggg==) no-repeat left center;
    background-size: auto 15px;
    padding-left: 13px;
  }
  .addletend_con_goods{
    display: block;
    align-items: center;
    padding: 0 11px 8px;
    div{
      width: 109px;
      background: #ffffff;
      border-radius: 8px;
      padding: 10px 11px 13px;
      margin: 0 4px 10px;
      img{
        display: block;
        border-radius: 8px;
        width: 145px;
        height: 97px;
        overflow: hidden;
        margin: 0 auto 5px;
      }
      h6{
        height: 24px;
        font-size: 13px;
        line-height: 24px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      p{
        font-size: 12px;
        color: #666666;
        line-height: 17px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      a{
        height: 30px;
        line-height: 30px;
        border-radius: 100px;
        display: block;
        margin: 8px auto 0;
        text-align: center;
        font-size: 14px;
        color: #f84248;
        //padding: 0 9px;
        span{
          padding: 0 1px;
        }
        i{
          display: inline-block;
          width: 12px;
          height: 30px;
          background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAAXNSR0IArs4c6QAAAFpJREFUKFN90NEJgDAMhOH7Z3MMXUZcRXdyAbcQTvpQaJvUPIXcFwJBTdnegLOd1Z4BvpJ24BhxB0toO8UBznAKM/wHH0k3sJTF2ekOpdB2QAHOUAYvYM0e/gFtfzALeEcg4QAAAABJRU5ErkJggg==) no-repeat right center;
          background-size: auto 10px;
        }
        .hot_goods{
          width: 60px;
          font-size: 10px;
          color: #999;
        }
        .hot_goods_font{
          font-size: 13px;
        }
      }
    }
  }
  .addletend_flex{
    display: flex;
    overflow: hidden;
    div{
      width: auto;
      flex: 1;
      max-width: 49%;
    }
  }
  .padding_min{
    div{
      display: inline-block;
      padding: 6px 5px 8px;
      img{
        width: 98px;
        height: 62px;
      }
      a{
        height: 26px;
        line-height: 25px;
        background: #ffffff;
        border-radius: 14px;
        color: #f84248;
        font-weight: Medium;
        //padding: 0 8px;
        i{
          height: 25px;
          background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAAXNSR0IArs4c6QAAAEVJREFUKFNjZEACM2bMCM/IyFiJLAZjMyILTp8+/T8DA0NyZmbmPHTFKApBkrgUYyjEpRirQmyKKVOIzZ3keYbo4MEX4AAO+zALn0BbCQAAAABJRU5ErkJggg==) no-repeat right center;
          background-size: auto 10px;
        }
      }
    }
    .maxwidth{
      width: 168px;
      img{
        width: 151px;
        height: 71px;
      }
      .displ_flex{
        display: flex;
        width: auto;
        div{
          width: auto;
          flex: 1;
          padding: 0;
          margin: 0 1px 0 0;
          overflow: hidden;
        }
        .goods_center{
          text-align: center;
        }
        a{
          width: 61px;
          padding: 0 4px;
        }
      }
    }
  }
}
.footer {
  background: #ffffff;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 9;
  .van-tabbar--fixed{
    text-align: center;
    padding-top: 10px;
    height: auto;
    box-shadow: 0 2px 3.46667vw -2px #667e8c4d;
    .van-tabbar-item__icon{
      img{
        width: 36px;
        height: 36px;
        margin: 0 auto;
      }
      p{
        height: 27px;
        line-height: 27px;
        font-size: 12px;
        color: #333333;
      }
    }
  }
}
</style>
