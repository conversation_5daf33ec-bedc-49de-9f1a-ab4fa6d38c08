export default [
  {
    path: '/order/list.html',
    name: 'OrderList',
    component: resolve => require(['./orderlist.vue'], resolve),
    meta: {
      title: '号卡订单',
      keepAlive:true,
      login:true,
      role:[1,2]
    }
  },
  {
    path: '/order/details.html',
    name: 'OrderDetail',
    component: resolve => require(['./orderdetail.vue'], resolve),
    meta: {
      title: '订单详情',
      login:true,
      role:[1,2]
    }
  },
  {
    path: '/shoporder/statistics.html',
    name: 'ShopOrderStatistics',
    component: resolve => require(['./shoporder/orderstatistics.vue'], resolve),
    meta: {
      title: '店铺订单',
      keepAlive:true,
      login:true,
      role:[1,2]
    }
  },
  {
    path: '/shoporder/list.html',
    name: 'ShopOrderList',
    component: resolve => require(['./shoporder/orderlist.vue'], resolve),
    meta: {
      title: '订单列表',
      keepAlive:true,
      login:true,
      role:[1,2]
    }
  },
  {
    path: '/shoporder/details.html',
    name: 'shopOrderDetail',
    component: resolve => require(['./shoporder/orderdetail.vue'], resolve),
    meta: {
      title: '订单详情',
      login:true,
      role:[1,2]
    }
  },
  {
    path: '/addletend/index.html',
    name: 'AddletEnd',
    component: resolve => require(['./end/addletend.vue'], resolve),
    meta: {
      title: '',
      login:false,
      role:[1,2]
    }
  }
]
