<template>
  <div class="orderlist20220614Style">
    <Shopname
      v-if="data.showShopName"
      content="号卡订单"
      bg-color="#efefef"
      :show-back="true"
    />
    <div
      v-if="data.myuser"
      class="orderlist1"
      :class="{
        'padding-top-34':data.showShopName,
      }"
    >
      <section
        :class="{
          'top1': data.showShopName,
        }"
        class="shaixuan"
        @click="data.screeningBounced = true"
      >
        <div class="left">
          <img src="./images/shaixuantubiao1.png" alt="" />
          <span>筛选项</span>
        </div>

        <div class="right">
          <img src="./images/rightjian.png" alt="" />
        </div>
      </section>

      <!-- 订单转交弹出列表 -->
      <Popup v-model="data.transfer" position="bottom">
        <Picker
          title="选择店员"
          show-toolbar
          :columns="data.transferList"
          @cancel="data.transfer = false"
          @confirm="onConfirm"
        />
      </Popup>

      <!-- 筛选1 -->
      <Popup v-model="data.screeningBounced" position="bottom">
        <Popup v-model="data.startDateShowMaker" position="bottom">
          <DatetimePicker
            :value="data.startDateValue"
            type="date"
            @cancel="data.startDateShowMaker = false"
            @confirm="confirmTime1"
          />
        </Popup>
        <Popup v-model="data.endDateShowMaker" position="bottom">
          <DatetimePicker
            :value="data.endDateValue"
            type="date"
            @cancel="data.endDateShowMaker = false"
            @confirm="confirmTime2"
          />
        </Popup>

        <div class="screening">
          <div class="commontop">
            <span>筛选</span>
            <div class="rem" @click="data.screeningBounced = false">
              <img src="./images/remove.png" alt="" />
            </div>
          </div>

          <div class="selectDate">
            <div class="tit">
              选择日期
            </div>
            <div class="detailCon">
              <div class="ipt" @click="data.startDateShowMaker = true">
                <span>{{
                  data.startDate ? parseTime(data.startDate, "{y}-{m}-{d}") : "开始时间"
                }}</span>
                <img src="./images/asdjsajsd.png" alt="" />
              </div>
              <i>至</i>
              <div class="ipt" @click="data.endDateShowMaker = true">
                <span>{{
                  data.endDate ? parseTime(data.endDate, "{y}-{m}-{d}") : "结束时间"
                }}</span>
                <img src="./images/asdjsajsd.png" alt="" />
              </div>
              <div class="reset" @click="resetTime">
                <Icon name="replay" />
              </div>
            </div>
          </div>

          <div class="handleWay">
            <div class="tit">
              办理方式
            </div>
            <div class="detailCon">
              <ul>
                <li
                  v-for="(item, index) in data.handleWayList"
                  :key="index"
                  :class="data.handleWayIndex === item.value ? 'active' : ''"
                >
                  <span
                    @click="
                      () => {
                        data.handleWayIndex = item.value
                      }
                    "
                  >{{ item.title }}</span>
                </li>
              </ul>
            </div>
          </div>

          <div class="activeSection">
            <div class="tit">
              激活状态
            </div>
            <div class="detailCon">
              <ul>
                <li
                  v-for="(item, index) in data.activeList"
                  :key="index"
                  :class="data.activeIndex === item.value ? 'active' : ''"
                >
                  <span
                    @click="
                      () => {
                        data.activeIndex = item.value
                      }
                    "
                  >{{ item.title }}</span>
                </li>
              </ul>
            </div>
          </div>

          <div class="submitparent">
            <div class="submit" @click="handleScreeningBounced">
              确认
            </div>
          </div>
        </div>
      </Popup>

      <List
        v-model="data.loading"
        :finished="data.finished"
        class="vant-clearfix"
        :finished-text="data.orderlist && data.orderlist.length > 0 ? '没有更多啦……' : ''"
        @load="getList"
      >
        <template v-if="data.orderlist && data.orderlist.length > 0">
          <div v-for="(item, key) in data.orderlist" :key="key" class="listTtem">
            <div class="listItemTitle">
              <div class="titleleft">
                {{ item.numApplyWayName }}
              </div>
              <div
                class="titleright"
                :class="{
                  green: item.numActiveStatus === 'AD',
                  red: item.numActiveStatus === 'WA',
                  grey: item.numActiveStatus === 'OA',
                }"
              >
                {{ item.numActiveStatusName }}
              </div>
            </div>
            <div class="listItemContent">
              <div class="img">
                <img :src="item.imgId" width="100%" />
              </div>
              <div class="contentlist">
                <div class="contentItem">
                  <div class="label">
                    订单号：
                  </div>
                  <div class="value">
                    {{ item.orderId }}
                  </div>
                </div>
                <div class="contentItem">
                  <div class="label">
                    客户手机号：
                  </div>
                  <div class="value">
                    {{ judgePhone(item.mobile) }}
                  </div>
                </div>
                <div class="contentItem">
                  <div class="label">
                    下单时间：
                  </div>
                  <div class="value">
                    {{ item.createTime }}
                  </div>
                </div>
              </div>
              <a
                v-show="item.numActiveStatus == 'WA' && item.numApplyWay == 4"
                :href="'tel:' + item.mobile"
                class="telradio"
              >
                <Icon color="#fff" name="phone" />
              </a>
            </div>
            <div class="btngroup">
              <div class="btnblue btnItem" @click="gotodetail(item.orderId)">
                查看详情
              </div>
              <div
                v-show="item.numActiveStatus == 'WA' && item.numApplyWay == 4"
                class="btnbrey btnItem"
                @click="handleCanle(item)"
              >
                取消订单
              </div>
              <div
                v-show="
                  item.numActiveStatus == 'WA' &&
                    item.numApplyWay == 4 &&
                    data.myuser.RuleId == 2
                "
                class="btnbrey btnItem"
                @click="handletransfer(item)"
              >
                订单转交
              </div>
            </div>
          </div>
        </template>
        <div
          v-else-if="data.orderlist && data.orderlist.length == 0"
          class="noActivity1"
        >
          <img src="~@/assets/index_img/ice_tab_no.png" />
          <p>暂无数据</p>
        </div>
        <div v-else></div>
      </List>
    </div>
  </div>
</template>

<script setup>
import {reactive,ref,onMounted,getCurrentInstance} from "vue"
import { List, Popup, DatetimePicker, Picker, Dialog,Toast ,Icon } from "vant"
import orderApi from "@/api/order"
import UA from "@/utils/ua"
import { parseTime,getImgUrl } from "@/utils/utils"
import Shopname from "@/components/index/headercon.vue"
import loginApi from "@/api/login"
import loginUtils from "@/utils/login"

const data = reactive({
  orderlist: null,
  sendData: {
    pageNo: 1,
    pageSize: 15,
  },
  loading: false,
  finished: false,
  ua: null,
  myuser: null,

  /* 订单转交 */
  transfer: false,
  /* 转交店员列表 */
  transferList: [],

  screeningBounced: false,

  /* 办理方式数据列表 */
  handleWayList: [
    { title: "全部", value: null },
    { title: "极速办卡", value: 4 },
    { title: "包邮到家", value: 1 },
    { title: "京东到家", value: 2 },
    { title: "厅内自提", value: 3 },
  ],
  /* 办理方式索引 */
  handleWayIndex: null,

  /* 激活状态 */
  activeList: [
    { title: "全部", value: null },
    { title: "待激活", value: "WA" },
    { title: "已激活", value: "AD" },
    { title: "已取消", value: "OA" },
  ],
  /* 激活状态索引 */
  activeIndex: null,

  /* 开始时间 */
  startDate: null,
  /* 结束时间 */
  endDate: null,
  /* 开始时间显示 */
  startDateValue: new Date(),
  /* 结束时间显示 */
  endDateValue: new Date(),

  /* 开始时间选择器显示与隐藏 */
  startDateShowMaker: false,
  /* 结束时间选择器显示与隐藏 */
  endDateShowMaker: false,
  showShopName:!UA.isWechat &&!UA.isWechatWork &&!UA.isApp &&!UA.isIosQQ &&!UA.isAndroidQQ,
  proxy:null
})


const confirmTime1 = (val) => {
  data.startDateShowMaker = false
  data.startDate = parseTime(val, "{y}-{m}-{d}")
}

const confirmTime2 = (val) => {
  data.endDateShowMaker = false
  data.endDate = parseTime(val, "{y}-{m}-{d}")
}

/* 重置时间 */
const resetTime = () => {
  data.startDate = null
  data.endDate = null
  data.startDateValue = new Date()
  data.endDateValue = new Date()
}

/* 取消订单 */
const handleCanle = (item) => {
  Dialog.confirm({
    message: "您是否要取消极速办卡订单",
    cancelButtonText: "否",
    cancelButtonColor: "#0085D0",
    confirmButtonText: "是",
    confirmButtonColor: "#0085D0",
  }).then(() => {
    orderApi
      .canelOrder({
        orderId: item.orderId,
        staffId: data.myuser.staffId,
        shopId: data.myuser.shopId,
      })
      .then((res) => {
        if (res.code == 0) {
          Toast("操作成功")
          data.sendData.pageNo = 1
          getList()
        } else {
          Toast("操作失败")
          data.sendData.pageNo = 1
          getList()
        }
      })
      .catch(() => {
        Toast("操作失败")
        data.sendData.pageNo = 1
        getList()
      })
  })
}

/* 订单转交确定 */
const onConfirm = (item) => {
  orderApi
    .forwardOrder({
      orderId: item.currentOrderObj.orderId,
      staffId: item.staffId,
      shopId: data.myuser.shopId,
    })
    .then((res) => {
      if (res.code == 0) {
        Toast("操作成功")
        data.sendData.pageNo = 1
        getList()
      } else {
        Toast(res.message)
        data.sendData.pageNo = 1
        getList()
      }
      data.transfer = false
    })
    .catch(() => {
      Toast("操作失败")
      data.sendData.pageNo = 1
      getList()
    })
}

/* 订单转交点击 */
const handletransfer = (obj) => {
  getStaffs(obj)
  data.transfer = true
}

function getObj(list){
  let obj = {}
  list.forEach((item)=>{
    obj[item.value] = item.title
  })
  return obj
}

const handleWayObj = getObj(data.handleWayList)
const activeObj = getObj(data.activeList)
/* 筛选确认 */
const handleScreeningBounced = () => {
  if (data.startDate || data.endDate) {
    if (!data.startDate || !data.endDate) {
      Toast("日期为必填项")
      return
    }
  }

  // 范围是90天
  let time1 = new Date(data.startDate).getTime()
  let time2 = new Date(data.endDate).getTime()
  let jiushi = 86400000 * 90
  if (time1 > time2) {
    Toast("结束时间要晚于开始时间")
    return
  } else {
    if (time2 - time1 >= jiushi) {
      Toast("查看范围不得超过90天")
      return
    }
  }

  data.sendData.pageNo = 1
  getList()
  data.screeningBounced = false
}

const monishuju = (orderList) => {
  orderList.forEach((value) => {
    value.imgId = value.imgId
      ? getImgUrl(value.imgId)
      : "//img1.staff.ydsc.aplusunion.cn/file/tw66h3h59tqhhfyh.png"
    //激活方式  WA：待激活、AD：已激活 、OA：已取消
    value.numActiveStatusName =
      value.numActiveStatus === "WA"
        ? "待激活"
        : value.numActiveStatus === "AD"
          ? "已激活"
          : value.numActiveStatus === "OA"
            ? "已取消"
            : ""
    //办理方式 1：线上办理、2：上门办理、3：到厅自取、4：极速办卡
    value.numApplyWayName =
      value.numApplyWay == 1
        ? "线上办理"
        : value.numApplyWay == 2
          ? "上门办理"
          : value.numApplyWay == 3
            ? "到厅自取"
            : value.numApplyWay == 4
              ? "极速办卡"
              : ""
  })
  data.loading = false
  data.finished = true
}

const getList = () => {
  if (!data.myuser.staffId) {
    Toast("请查看自己店铺的数据")
    data.orderlist = []
    data.loading = false
    data.finished = true
    return false
  }
  data.sendData.shopId = data.myuser.shopId
  if (data.myuser.RuleId == 1) {
    data.sendData.shopAssistantId = data.myuser.staffId
  } else {
    data.sendData.shopAssistantId = null
  }

  // 日期
  if (data.startDate && data.endDate) {
    let a1 = new Date(data.startDate).getTime()
    let a2 = new Date(data.endDate).getTime()
    if (a1 > a2) {
      data.sendData.createTimeEnd = data.startDate
      data.sendData.createTimeStart = data.endDate
    } else {
      data.sendData.createTimeEnd = data.endDate
      data.sendData.createTimeStart = data.startDate
    }
    data.sendData.createTimeStart += " 00:00:00"
    data.sendData.createTimeEnd += " 23:59:59"
  } else {
    data.sendData.createTimeEnd = null
    data.sendData.createTimeStart = null
  }

  // 激活状态
  data.sendData.numactiveStatus = data.activeIndex

  // 办理方式
  data.sendData.numApplyWay = data.handleWayIndex

  orderApi.getOrderList(data.sendData).then((res) => {
    if (data.sendData.pageNo == 1) {
      data.orderlist = []
    }
    if (res.code) {
      Toast(res.message)
      data.loading = false
      data.finished = true
    } else {
      if (
        !(res.data && res.data.orderList && res.data.orderList.length > 0)
      ) {
        data.loading = false
        data.finished = true
        // console.log(data.orderlist)
        return false
      }
      res.data.orderList.forEach((value) => {
        value.imgId = value.imgId
          ? getImgUrl(value.imgId)
          : "//img1.staff.ydsc.liuliangjia.cn/file/tw66h3h59tqhhfyh.png"
        //激活方式  WA：待激活、AD：已激活 、OA：已取消
        value.numActiveStatusName = activeObj[value.numActiveStatus]
          ? activeObj[value.numActiveStatus]
          :""
        //办理方式 1：线上办理、2：上门办理、3：到厅自取、4：极速办卡
        value.numApplyWayName = handleWayObj[value.numApplyWay]
          ? handleWayObj[value.numApplyWay]
          : ""
        data.orderlist.push(value)
      })
      data.loading = false
      if (res.data.total > data.orderlist.length) {
        data.sendData.pageNo++
      } else {
        data.finished = true
      }
    }
  })
}

const gotodetail = (orderId) => {
  if(!data.proxy){
    return false
  }
  data.proxy.$router.push({
    path: "/order/details.html",
    query: {
      orderId: orderId,
      staffId: data.myuser.staffId,
    },
  })
}

/* 获取店员 */
const getStaffs = (obj) => {
  loginApi
    .getUserInfo(localStorage.getItem("yundianToken"), 0, 5)
    .then((res) => {
      orderApi
        .getStaffs({
          shopId: data.myuser.shopId,
        })
        .then((res) => {
          if (res.code == 0) {
            let resdata = res.data
            if (data) {
              const newData = resdata.map((item) => {
                return {
                  text: item.name,
                  ...item,
                  currentOrderObj: obj,
                }
              })
              data.transferList = newData
            }
          }
        })
    })
}

/* 脱敏 */
const judgePhone = (val) => {
  if (!val) return ""
  let reg = /^(.{3}).*(.{4})$/
  return val.replace(reg, "$1****$2")
}

const logined = (res) => {
  if (res) {
    data.myuser = res
    if(data.proxy){
      data.proxy.$store.commit("SET_SHOPID",data.myuser.shopId)
    }
  }
}

onMounted(()=>{
  let getVueInstance = getCurrentInstance()
  data.proxy = getVueInstance ? getVueInstance.proxy : null
  loginUtils.login(true,true,logined,false,false,logined,"0",5)
})
</script>

<style lang="scss" scoped>
  .orderlist20220614Style {
    .telradio {
      border-radius: 50%;
      width: 20px;
      margin-left: auto;
      display: inline-flex;
      height: 20px;
      background: #00ad298f;
      align-items: center;
      justify-content: center;
      transform: rotate(261deg);
    }
    .orderlist1 {
      font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
        "Source Sans Pro", "Trebuchet MS", Arial;
      font-size: 14px;
      color: #2c2c2c;
      font-weight: 400;
      background: rgb(247, 248, 249);
      padding: 10px;
      min-height: calc(100vh - 148px);
      .noActivity1 {
        padding-top: 242px;
        text-align: center;
        line-height: 30px;
        font-size: 16px;
        color: #757575;
        font-weight: 400;
      }
    }
    .font12 {
      font-size: 12px;
    }
    .textCenter {
      text-align: center;
    }
    .icon_download {
      background: url(~@/assets/index_normal/icon_download.png) 0 0 no-repeat;
      width: 24px;
      height: 24px;
      display: inline-block;
      background-size: contain;
      transform: translate(0, 6px);
    }
    .listTtem {
      width: 355px;
      height: 177px;
      background: #ffffff;
      border-radius: 8px;
      margin-bottom: 10px;
      &:last-child {
        margin-bottom: 0;
      }
      .listItemTitle {
        height: 40px;
        border-bottom: 1px solid #eeeeee;
        .titleleft,
        .titleright {
          margin: 0 17px;
          line-height: 40px;
          height: 40px;
          font-size: 14px;
          font-weight: 400;
          color: #333333;
        }
        .titleleft {
          float: left;
        }
        .titleright {
          float: right;
        }
      }
      .listItemContent {
        display: flex;
        align-items: center;
        border-bottom: 1px solid #eeeeee;
        padding: 10px 15px;
        .img {
          width: 70px;
          height: 70px;
        }
        .contentItem {
          width: 210px;
          height: 23px;
          font-size: 12px;
          font-weight: 400;
          text-align: left;
          color: #666666;
          line-height: 24px;
          display: flex;
          .label {
            width: 87px;
            text-align: right;
          }
          .value {
            flex: 1;
            text-align: left;
            color: #999999;
          }
        }
      }
      .btngroup {
        margin: 10px 15px;
        .btnItem {
          width: 73px;
          height: 24px;
          border: 1px solid #979797;
          border-radius: 12.5px;
          font-weight: 400;
          text-align: left;
          color: #979797;
          line-height: 21px;
          text-align: center;
          float: right;
          margin-left: 10px;
          &.btnblue {
            color: #5099fe;
            border-color: #5099fe;
          }
        }
      }
    }
    .padding-top-34 {
      padding-top: 34px; //34+15
    }
    .min-height-160 {
      min-height: calc(100vh - 80px);
    }
    .red {
      color: #ed2668 !important;
    }
    .green {
      color: #00ad29 !important;
    }
    .grey {
      color: #999999 !important;
    }

    .shaixuan {
      &.top1 {
        top: 34px; //34+15
      }
      position: sticky;
      top: 0;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #fff;
      padding: 10px 20px;
      margin-bottom: 10px;
      border-radius: 20px;

      .left {
        display: flex;
        align-items: center;
        img {
          width: 17px;
        }
        span {
          margin-left: 5px;
          font-size: 14px;
          color: #5099fe;
        }
      }

      .right {
        img {
          width: 15px;
        }
      }
    }

    .commontop {
      span {
        font-size: 18px;
        color: #333333;
        text-align: center;
        display: block;
        padding: 16px 0;
        padding-bottom: 0;
        font-weight: bold;
      }
      .rem {
        position: absolute;
        width: 20px;
        right: 20px;
        top: 16px;
        height: 20px;
        img {
          width: 100%;
          height: auto;
        }
      }
    }

    .screening {
      position: relative;
      padding-bottom: 30px;

      .tit {
        font-size: 14px;
        color: #333;
        font-weight: bold;
        margin-bottom: 20px;
        margin-top: 20px;
      }

      ul {
        padding: 0 9px;
        display: flex;
        flex-flow: wrap;
        li {
          padding: 0 9px;
          width: (100%/3);
          margin-bottom: 10px;
          &.active {
            span {
              background: rgba(80, 153, 254, 0.1);
              border: 1px solid #5099fe;
              color: #5099fe;
            }
          }
          span {
            display: block;
            text-align: center;
            padding: 11px 0;
            background: #f6f6f6;
            border: 1px solid #f6f6f6;
            border-radius: 18px;
            color: #333;
            font-size: 14px;
          }
        }
      }

      .handleWay,
      .activeSection {
        .tit {
          padding: 0 18px;
        }
      }

      .selectDate {
        padding: 0 18px;
        .detailCon {
          display: flex;
          align-items: center;

          i {
            font-style: normal;
            color: #333;
            font-size: 14px;
            padding: 0 10px;
          }

          .ipt {
            display: flex;
            flex: 1;
            min-width: 0;
            align-items: center;
            justify-content: space-between;
            background: #f6f6f6;
            padding: 12px 16px;
            border-radius: 18px;
            span {
              color: #a1a1a1;
              font-size: 14px;
              line-height: 1.8;
            }
            img {
              width: 16px;
              height: 16px;
              margin-left: 2px;
            }
          }
        }
      }

      .submitparent {
        padding: 0 18px;
      }
      .submit {
        height: 42px;
        background: #5099fe;
        border-radius: 21px;
        font-size: 14px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        margin-top: 20px;
      }
    }

    .zhuanjiaoul {
      padding: 20px 0;
      li {
        text-align: center;
        font-size: 16px;
        color: #666666;
        padding: 20px 0;
      }
    }

    .reset {
      padding: 5px 0;
    }
  }
</style>
