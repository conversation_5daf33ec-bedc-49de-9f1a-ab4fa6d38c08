<template>
  <div>
    <van-popup
      v-model="showGoodsDialog"
      round
      position="bottom"
      closeable
      close-icon="arrow-left"
      close-icon-position="top-left"
      :style="{ height: '65%' }"
    >
      <p class="goods-dialog-title">
        商品选择
      </p>
      <div class="list-title">
        <p class="radio"></p>
        <p class="goods-img">
          商品图片
        </p>
        <p class="goods-name">
          商品名称
        </p>
        <p class="goods-price">
          商品价格
        </p>
      </div>
      <van-list
        v-model="data.loading"
        :finished="data.finished"
        :finished-text="data.showNoYyData ? '' : '没有更多了'"
        class="vant-clearfix"
        :class="{ noYyData: data.showNoYyData }"
        @load="onLoad"
      >
        <van-checkbox-group v-model="data.results" class="radia-group">
          <van-cell v-for="item in data.list" :key="item.goodsId">
            <div class="list-item">
              <p class="radio">
                <van-checkbox
                  :name="item.goodsId"
                  @click="changeRadio(item)"
                ></van-checkbox>
              </p>
              <p class="goods-img">
                <img :src="item.imgUrl" />
              </p>
              <div class="goods-name overEli">
                <span>{{ item.goodsName }}</span>
              </div>
              <p class="goods-price">
                {{ (item.minPrice / 100).toFixed(2) }}
              </p>
            </div>
          </van-cell>
          <div v-if="data.showNoYyData" class="noYyDataTitle">
            搜索不到商品，请先登录平台添加商品
          </div>
        </van-checkbox-group>
        <div class="btn-area">
          <div class="btn-cancle" @click="cancle">
            取消
          </div>
          <div class="btn-confirm" @click="confirm1">
            确认
          </div>
        </div>
      </van-list>
    </van-popup>
  </div>
</template>
<script setup>
import * as api from '@/api/broad'
import { getImgUrl } from '@/utils/utils'
import Vue ,{reactive,ref,onMounted,getCurrentInstance,defineProps,defineExpose,watch} from 'vue'
import { Popup, List, Cell, CheckboxGroup, Checkbox, Toast } from 'vant'
Vue.use(Popup)
Vue.use(List)
Vue.use(Cell)
Vue.use(Checkbox)
Vue.use(CheckboxGroup)
const props = defineProps({
  confirm: {
    type: Function,
    default: (fn) => {
      return fn
    },
  }
})

const data = reactive({
  list: [],
  loading: false,
  finished: false,
  results: [],
  shopManageForm: {
    pageNum: 1,
    pageSize: 10,
  },
  currentGoods: [],
  showNoYyData: false,
})
const showGoodsDialog = ref(false)
watch(showGoodsDialog,(val,oldval)=>{
  if (val) {
    changeValue()
  }
})
const changeValue = ()=> {
  data.list = []
  data.results = []
  data.shopManageForm.pageNum = 1
  data.finished = false
  onLoad()
}
const changeRadio = (parameData)=> {
  if (
    data.currentGoods.filter((item) => item.goodsId === parameData.goodsId)
      .length > 0
  ) {
    data.currentGoods = data.currentGoods.filter(
      (item) => item.goodsId !== parameData.goodsId
    )
  } else {
    data.currentGoods.push(parameData)
  }
}

const onLoad = ()=> {
  data.loading = true
  api.goodsSekector(data.shopManageForm).then((r) => {
    if (r.code) {
      Toast(r.message)
    } else {
      if (data.shopManageForm.pageNum == 1) {
        data.list = []
      }
      if (r.data) {
        r.data.list.forEach((value, item) => {
          value.imgUrl = getImgUrl(value.imgId)
          data.list.push(value)
        })
      }
      data.loading = false
      if (data.list && data.list.length == 0) {
        data.showNoYyData = true
        data.finished = true
        return
      }
      // eslint-disable-next-line no-cond-assign
      if (r.data.pages > data.shopManageForm.pageNum) {
        data.shopManageForm.pageNum++
      } else {
        data.finished = true
      }
      // console.log(data.list)
    }
  })
}

const confirm1 = ()=> {
  showGoodsDialog.value = false
  props.confirm(data.currentGoods)
  data.currentGoods = []
}
const cancle = ()=> {
  showGoodsDialog.value = false
}


// 主动暴露childMethod方法
defineExpose({
  showGoodsDialog
})
    
</script>
<style lang="scss" scoped>
.goods-dialog-title {
  font-size: 18px;
  height: 50px;
  line-height: 50px;
  text-align: center;
}
.vant-clearfix {
  height: 300px;
  overflow: auto;
}
.list-title,
.list-item {
  display: flex;
  text-align: center;
  .goods-name {
    flex: 1;
  }
  .goods-img {
    width: 80px;
  }
}
.list-title {
  font-size: 14px;
  color: #fff;
  background: #64bbff;
  height: 40px;
  line-height: 40px;
  .radio {
    width: 60px;
  }
  .goods-price {
    width: 80px;
  }
}
.list-item {
  // border-bottom:1px solid #000;
  .radio {
    width: 40px;
    padding-top: 20px;
  }
  .goods-price {
    width: 62px;
  }
  p {
    font-size: 10px;
    height: 60px;
    line-height: 60px;

    // add
    display: flex;
    flex-flow: wrap;
    align-items: center;
    justify-content: center;
    span {
      &.text-line-through {
        margin-left: 3px;
      }
    }

    &.goods-img {
      img {
        width: 50px;
        height: 50px;
      }
    }
  }
}
.btn-area {
  // display: none;
  position: fixed;
  z-index: 999;
  bottom: 0;
  width: 100%;
  height: 48px;
  line-height: 48px;
  text-align: center;
  font-size: 13.999987500000001px;
  background: #fff;
  .btn-cancle {
    float: left;
    width: 49.5%;
    color: #393939;
    background: #d9d9d9;
  }

  .btn-confirm {
    float: right;
    width: 49.5%;
    color: #fff;
    background: #64bbff;
  }
}
.noYyData {
  background: url(~@/assets/index_img/empty.png) center center no-repeat;
  background-size: 28%;
}
.noYyDataTitle {
  font-size: 16px;
  margin-top: 240px;
  text-align: center;
}
.overEli {
  height: 60px;
  display: flex;
  max-width: 170px;
  span {
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    align-self: center;
    line-height: 19px;
    text-overflow: ellipsis;
    display: -webkit-box;
    text-align: center;
    width: 100%;
  }
}
</style>
