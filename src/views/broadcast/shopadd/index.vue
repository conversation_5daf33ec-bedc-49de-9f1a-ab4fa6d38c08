<template>
  <div class="add-shop">
    <div v-if="data.isApp" class="add-shop__header">
      <div class="add-shop__header-back" @click="back">
        <Icon name="arrow-left" />
      </div>
      <div class="add-shop__header-title">
        {{ data.title }}
      </div>
    </div>
    <Form class="add-shop__content">
      <Field
        v-model="data.form.title"
        name="标题*"
        label="标题*"
        placeholder="名称"
        :rules="[{ required: true, message: '请输入名称' }]"
      ></Field>
      <Field
        readonly
        clickable
        :value="data.form.startTime"
        label="开始时间*"
        placeholder="点击选择时间"
        @click="
          data.showPicker = true
          setScrollTop()
        "
      ></Field>
      <Popup v-model="data.showPicker" position="bottom" @confirm="onConfirm">
        <DatetimePicker
          v-model="data.currentDate"
          type="datetime"
          title="选择完整时间"
          :min-date="data.minDate"
          :max-date="data.maxDate"
          @confirm="onConfirm"
          @cancel="data.showPicker = false"
        />
      </Popup>
      <Field
        readonly
        clickable
        :value="data.form.endTime"
        label="结束时间*"
        placeholder="点击选择时间"
        @click="
          data.showPicker1 = true
          setScrollTop()
        "
      ></Field>
      <Popup v-model="data.showPicker1" position="bottom" @confirm="onConfirm1">
        <DatetimePicker
          v-model="data.currentDate"
          type="datetime"
          title="选择完整时间"
          :min-date="data.minDate"
          :max-date="data.maxDate"
          @confirm="onConfirm1"
          @cancel="data.showPicker1 = false"
        />
      </Popup>
      <Field
        v-model="data.form.url"
        name="直播地址*"
        label="直播地址*"
        placeholder="请输入地址"
        :rules="[{ required: true, message: '请输入地址' }]"
      ></Field>
    </Form>
    <div class="add-shop__content-item">
      <span>直播商品*</span>
      <button @click="openAddGoods">
        选择
      </button>
    </div>

    <div class="add-shop__content-goods">
      <div
        v-for="(item, index) in data.checkedGoods"
        :key="index"
        class="add-shop__content-goods-desc"
      >
        <div
          class="add-shop__content-goods-desc-icon"
          @click="deleteGoods(item)"
        >
          <span>×</span>
        </div>
        {{ item }}
      </div>
    </div>
    <div class="add-shop__foot" @click="submit">
      <button>确定</button>
    </div>
    <Goodselector ref="goodsList" :confirm="confirmCallback"></Goodselector>
  </div>
</template>

<script setup>
import {reactive,getCurrentInstance,onMounted,ref} from "vue"
import { Sticky, Form, Field, Toast, Icon, DatetimePicker, Popup } from 'vant'
import Goodselector from './components/goodselector'
import * as api from '@/api/broad'
import UA from '@/utils/ua'

const data = reactive({
  icon: '<',
  title: '新增商品入口配置',
  form: {
    title: '',
    startTime: '',
    endTime: '',
    url: '',
  },
  checkedGoods: [],
  showPicker: false,
  showPicker1: false,
  minDate: new Date(2020, 0, 1),
  maxDate: new Date(2025, 10, 1),
  currentDate: new Date(),
  isApp: false,
  proxy:null
})
const back = ()=> {
  history.go(-1)
}
const getBroadDetail = ()=> {
  const params = {
    liveId: data.liveId,
  }
  api
    .getBroadDetail(params)
    .then((res) => {
      const resdata = res.data
      resdata.startTime = handleDate(new Date(resdata.startTime))
      resdata.endTime = handleDate(new Date(resdata.endTime))
      data.checkedGoods = resdata.goodsId
      data.form = resdata
      // console.log(data.form)
    })
    .catch()
}
// 活动时间处理
const  handleDate = (time, showMinute)=> {
  let timeY = time.getFullYear()
  let timeMon = time.getMonth() + 1
  let timeD = time.getDate()
  let timeH = time.getHours()
  let timeM = time.getMinutes()
  let timeS = time.getSeconds()
  timeMon = timeMon < 10 ? '0' + timeMon : timeMon
  timeD = timeD < 10 ? '0' + timeD : timeD
  if (timeH < 10) {
    timeH = '0' + timeH
  }
  if (timeM < 10) {
    timeM = '0' + timeM
  }
  if (timeS < 10) {
    timeS = '0' + timeS
  }
  return (
    timeY +
    '-' +
    timeMon +
    '-' +
    timeD +
    '' +
    ' ' +
    timeH +
    ':' +
    timeM +
    ':' +
    timeS
  )
}
const onConfirm = (datetime)=> {
  data.form.startTime = handleDate(datetime)
  data.showPicker = false
}
const onConfirm1 = (datetime)=> {
  data.form.endTime = handleDate(datetime)
  data.showPicker1 = false
}

const submit = ()=> {
  if (!data.form.title) {
    Toast('请输入名称')
    return
  }
  if (!data.form.startTime || !data.form.endTime) {
    Toast('请选择时间')
    return
  }
  if (data.form.startTime >= data.form.endTime) {
    Toast('结束时间应在开始时间之后')
    return
  }
  // if (!data.form.url || data.form.url.indexOf('.10086.cn/') === -1) {
  if (!data.form.url) {
    Toast('请输入正确格式的地址')
    return
  }
  if (data.checkedGoods.length < 1) {
    Toast('请选择商品')
    return
  }
  const params = {
    ...data.form,
    goodsId: data.checkedGoods,
    type: 1,
  }
  // console.log(params)
  data.loading = true
  if (data.liveId) {
    params.liveId = data.liveId
  }
  const request = data.liveId ? api.updateBroad : api.createBroad
  request(params)
    .then((res) => {
      if (res.message === 'ok') {
        data.loading = false
        Toast('提交成功')
        if(data.proxy){
          data.proxy.$router.push({
            path: '/broadcast/index.html?zbtype=1',
          })
        }
      } else {
        Toast(res.message)
        data.loading = false
      }
    })
    .catch()
}

const deleteGoods = (paremeData)=> {
  data.checkedGoods = data.checkedGoods.filter((item) => item !== paremeData)
}

const setScrollTop = ()=> {
  var scrollTop =
    document.documentElement.scrollTop ||
    window.pageYOffset ||
    document.body.scrollTop
  if (scrollTop == 0) {
    window.scrollTo(0, 1)
  }
}
let goodsList = ref(null)
const openAddGoods = ()=> {
  goodsList.value.showGoodsDialog = true
  setScrollTop()
}

const confirmCallback = (parameData)=> {
  data.checkedGoods = []
  parameData.forEach((item) => {
    data.checkedGoods.push(item.goodsId)
  })
}

onMounted(() => {
  const getVueInstance = getCurrentInstance()
  data.proxy = getVueInstance ? getVueInstance.proxy : null
  data.liveId = data.proxy ? data.proxy.$route.query.liveId : ''
  data.isApp = UA.isApp || UA.isWechat ? false : true
  if (data.liveId) {
    data.title = '修改商品入口配置'
    getBroadDetail()
  }
})
</script>
<style lang="scss" scoped>
  .add-shop {
    background: #f6f6f6;
    height: 100vh;
    &__header {
      width: 100%;
      height: 46px;
      line-height: 46px;
      background: #fff;
      position: sticky;
      top: 0;
      z-index: 2000;
      &-back {
        padding-left: 16px;
        float: left;
        i {
          position: absolute;
          font-size: 20px;
          height: 20px;
          left: 15px;
          top: 14px;
        }
      }
      &-title {
        text-align: center;
        font-size: 18px;
        color: #111;
      }
    }
    &__content {
      padding-top: 10px;
      &-goods {
        width: 100%;
        padding-top: 12px;
        padding-left: 26px;
        display: flex;
        flex-wrap: wrap;
        position: relative;
        //background: #fff;
        &-desc {
          margin-top: 20px;
          margin-right: 10px;
          width: 100px;
          height: 24px;
          line-height: 24px;
          border-radius: 6px;
          border: 2px solid #5caae1;
          background: #e6f5ff;
          color: #5caae1;
          text-align: center;
          font-size: 14px;
          white-space: nowrap;
          position: relative;
          &-icon {
            width: 16px;
            height: 16px;
            line-height: 16px;
            border-radius: 8px;
            background: #adadad;
            color: #fff;
            position: absolute;
            top: -8px;
            right: -6px;
            cursor: pointer;
          }
        }
      }
      &-item {
        width: 100%;
        height: 44px;
        line-height: 24px;
        background: #fff;
        padding: 10px 16px;
        font-size: 14px;
        color: #646566;
        button {
          width: 76px;
          height: 30px;
          border-radius: 6px;
          background: linear-gradient(to right, #93da80, #4fb5b1);
          border: none;
          color: #fff;
          float: right;
        }
      }
    }
    &__foot {
      padding: 16px 16px 0px 16px;
      width: 100%;
      button {
        width: 100%;
        height: 44px;
        line-height: 44px;
        color: #fff;
        background: linear-gradient(to right, #5fc8ff, #8372fe);
        border: none;
        border-radius: 22px;
        font-size: 16px;
      }
    }
  }
  .Field__label {
    color: #646566;
  }
</style>
