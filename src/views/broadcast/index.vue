<template>
  <div class="broad-cast">
    <div v-if="data.isApp" class="broad-cast__header">
      <div class="broad-cast__header-back" @click="backmanage">
        <Icon name="arrow-left" />
      </div>
      <div class="broad-cast__header-title">
        {{ data.title }}
      </div>
    </div>
    <div v-if="data.list.length > 0">
      <List
        id="activity-detail__main"
        v-model="data.loading"
        :finished="data.scrollFinish"
        :immediate-check="false"
        finished-text="到底啦~"
        class="broad-cast__list"
        @load="getBroadList"
      >
        <div
          v-for="item of data.list"
          :key="item.liveId"
          class="broad-cast__list-item"
        >
          <div class="broad-cast__list-item-img">
            <img src="./img/goods.png" />
          </div>
          <div class="broad-cast__list-item-content">
            <div class="broad-cast__list-item-content-title">
              {{ item.title }}
            </div>
            <div class="broad-cast__list-item-content-position">
              位置：{{ item.position }}
            </div>
            <div class="broad-cast__list-item-content-time">
              {{ item.showTime }}
            </div>
          </div>
          <div class="broad-cast__list-item-status">
            <div v-if="item.status === 1">
              <img src="./img/going.png" />
            </div>
            <div v-if="item.status === 0">
              <img src="./img/nostart.png" />
            </div>
            <div v-if="item.status === -1">
              <img src="./img/haveend.png" />
            </div>
          </div>
          <div
            class="broad-cast__list-item-change"
            @click="update(item.liveId)"
          >
            <img src="./img/change.png" />
          </div>
          <div class="broad-cast__list-item-delete" @click="del(item.liveId)">
            <img src="./img/delete.png" />
          </div>
        </div>
      </List>
    </div>
    <div v-else class="broad-cast__empty">
      暂无信息
    </div>
    <div class="broad-cast__foot">
      <button @click="createNewBroad">
        + 新增配置
      </button>
    </div>
  </div>
</template>

<script setup>
import * as api from '@/api/broad'
import { Toast, Dialog, List, Icon } from 'vant'
import {ref,reactive,onMounted,getCurrentInstance} from 'vue'
import UA from '@/utils/ua'
import loginUtils from '@/utils/login'

const data = reactive({
  icon: '<',
  title: '',
  list: [],
  loading: false,
  scrollFinish: false,
  pageNum: 1,
  pageSize: 20,
  total: 0,
  type: 0, // 0首页1商品页
  isApp: false,
  shopId: null,
  proxy : null
})

const del = (liveId) => {
  Dialog.confirm({
    message: '删除后不可恢复，您确定要删除吗？',
  })
    .then(() => {
      api
        .deleteBroad({
          liveId: liveId,
        })
        .then((res) => {
          Toast('删除成功')
          data.list = []
          data.pageNum = 1
          getBroadList()
        })
    })
    .catch((err) => {})
}

const update = (liveId) => {
  if (data.type === '0'&& data.proxy) {
    data.proxy.$router.push({
      path: '/broadcast/homeadd.html',
      query: {
        liveId: liveId,
      },
    })
  }
  if (data.type === '1' && data.proxy) {
    data.proxy.$router.push({
      path: '/broadcast/shopadd.html',
      query: {
        liveId: liveId,
      },
    })
  }
}

const handleDate = (dataParam) => {
  let timeY = dataParam.getFullYear()
  let timeMon = dataParam.getMonth() + 1
  let timeD = dataParam.getDate()
  let timeH = dataParam.getHours()
  let timeM = dataParam.getMinutes()
  // let timeS = data.getSeconds()
  timeMon = timeMon < 10 ? '0' + timeMon : timeMon
  timeD = timeD < 10 ? '0' + timeD : timeD
  timeH = timeH < 10 ? '0' + timeH : timeH
  timeM = timeM < 10 ? '0' + timeM : timeM
  return timeY + '-' + timeMon + '-' + timeD + ' ' + timeH + ':' + timeM
}
const getBroadList = ()=> {
  const params = {
    pageNum: data.pageNum,
    pageSize: data.pageSize,
    type: Number(data.type),
  }
  data.loading = true
  api.getBroadList(params).then((res) => {
    data.loading = false
    if (res.message !== 'ok') {
      Toast(res.message)
      data.scrollFinish = true
    } else if (res.data && res.data.list) {
      const resdata = res.data.list
      resdata.forEach((item) => {
        let timeNow = Date.parse(new Date())
        if (timeNow < item.startTime) {
          item.status = 0
        } else if (timeNow > item.endTime) {
          item.status = -1
        } else {
          item.status = 1
        }
        if (item.position === 0 && data.type === '1')
          item.position = '详情页'
        if (item.position === 0) item.position = '首屏'
        if (item.position === 1) item.position = '席底'
        item.showTime =
          handleDate(new Date(item.startTime)) +
          ' 至 ' +
          handleDate(new Date(item.endTime))
      })
      data.list = [...data.list, ...resdata]
      if (res.data.isLastPage) {
        data.scrollFinish = true
      } else {
        data.pageNum = data.pageNum + 1
      }
    }
  })
}

const backmanage = () => {
  window.location.href = '/yundian/ydmanage/index.html?shopId=' + data.shopId
}

const createNewBroad = () => {
  if (data.type === '0' && data.proxy) {
    data.proxy.$router.push({
      path: '/broadcast/homeadd.html',
    })
  }
  if (data.type === '1' && data.proxy) {
    data.proxy.$router.push({
      path: '/broadcast/shopadd.html',
    })
  }
}

onMounted(()=> {
  data.isApp = (UA.isApp || UA.isWechat) ? false :true
  const getVueInstance = getCurrentInstance()
  data.proxy = getVueInstance ? getVueInstance.proxy : null
  const url = new URL(location.href)
  data.shopId = url.searchParams.get('shopId')
  if(data.proxy){
    data.proxy.$store.commit('SET_SHOPID', data.shopId)
    data.type = data.proxy.$route.query.zbtype || ''
  }
  if (data.type === '0') {
    data.title = '首页入口配置列表'
  }
  if (data.type === '1') {
    data.title = '商品页入口配置列表'
  }
  getBroadList()
  document.title = data.title
})

</script>
<style>
  .icpinfo {
    margin-bottom: 40px;
  }
  html {
    background: #f6f6f6;
  }
</style>
<style lang="scss" scoped>
  .broad-cast {
    background: #f6f6f6;
    min-height: 100vh;
    &__header {
      width: 100%;
      height: 46px;
      line-height: 46px;
      background: #fff;
      position: sticky;
      top: 0;
      z-index: 10000;
      &-back {
        padding-left: 16px;
        float: left;
        i {
          position: absolute;
          font-size: 20px;
          height: 20px;
          left: 15px;
          top: 14px;
        }
      }
      &-title {
        text-align: center;
        font-size: 18px;
        color: #111;
      }
    }
    &__list {
      padding-top: 10px;
      // padding-bottom: 46px;
      &-item {
        position: relative;
        height: 110px;
        background: #fff;
        border-bottom: 2px dotted #f0f0f0;
        &-img {
          width: 70px;
          height: 70px;
          margin-top: 20px;
          margin-left: 10px;
          float: left;
          border: 2px solid #f0f0f0;
          text-align: center;
          img {
            position: relative;
            top: 50%;
            transform: translateY(-50%);
          }
        }
        &-content {
          padding-left: 90px;
          &-title {
            padding-top: 16px;
            font-size: 18px;
            color: #333333;
          }
          &-position {
            padding-top: 13px;
            font-size: 16px;
            color: #52addd;
          }
          &-time {
            padding-top: 13px;
            font-size: 12px;
            color: #999999;
          }
        }
        &-status {
          position: absolute;
          top: 12px;
          right: 70px;
          width: 60px;
          height: 60px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        &-change {
          position: absolute;
          top: 20px;
          right: 14px;
          width: 26px;
          height: 26px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        &-delete {
          position: absolute;
          top: 60px;
          right: 14px;
          width: 26px;
          height: 26px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
    &__empty {
      text-align: center;
      position: absolute;
      top: 40%;
      transform: translateY(-50%);
      font-size: 18px;
      text-align: center;
      width: 100%;
    }
    &__foot {
      position: fixed;
      bottom: 0px;
      padding: 16px 16px 0px 16px;
      width: 100%;
      button {
        width: 100%;
        height: 44px;
        line-height: 44px;
        color: #fff;
        background: linear-gradient(to right, #5fc8ff, #8372fe);
        border: none;
        border-radius: 22px;
        font-size: 16px;
      }
    }
  }
</style>
