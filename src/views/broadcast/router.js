export default [
  // {
  //   path: "/broadcast/index.html",
  //   name: "broadcast",
  //   component: resolve => require(["./index.vue"], resolve),
  //   meta: {
  //     title: "首页入口配置列表"
  //   }
  // },
  // {
  //   path: '/broadcast/shopadd.html',
  //   name: 'broadcastShopAdd',
  //   component: resolve => require(['./shopadd/index.vue'], resolve),
  //   meta: {
  //     title: '商品页入口配置'
  //   }
  // },
  // {
  //   path: "/broadcast/homeadd.html",
  //   name: "broadcastHomeAdd",
  //   component: resolve => require(["./homeadd/index.vue"], resolve),
  //   meta: {
  //     title: "新增首页直播入口"
  //   }
  // }
]
