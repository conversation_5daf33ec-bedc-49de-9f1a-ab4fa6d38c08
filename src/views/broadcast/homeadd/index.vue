<template>
  <div class="add-broad-cast">
    <div v-if="data.isApp" class="add-broad-cast__header">
      <div class="add-broad-cast__header-back" @click="back">
        <Icon name="arrow-left" />
      </div>
      <div class="add-broad-cast__header-title">
        {{ data.title }}
      </div>
    </div>

    <Form class="add-broad-cast__content">
      <Field
        v-model="data.form.title"
        name="标题*"
        label="标题*"
        placeholder="名称"
        :rules="[{ required: true, message: '请输入名称' }]"
      ></Field>

      <Field name="radio" label="位置*">
        <template #input>
          <RadioGroup v-model="data.form.position" direction="horizontal">
            <Radio name="0" style="display: none">
              首屏
            </Radio>
            <Radio name="1">
              席底
            </Radio>
          </RadioGroup>
        </template>
      </Field>

      <Field
        readonly
        clickable
        :value="data.form.startTime"
        label="开始时间*"
        placeholder="点击选择时间"
        @click="
          data.showPicker = true
          setScrollTop()
        "
      ></Field>
      <Popup
        v-model="data.showPicker"
        position="bottom"
        @confirm="onConfirm"
      >
        <DatetimePicker
          v-model="data.currentDate"
          type="datetime"
          title="选择完整时间"
          :min-date="data.minDate"
          :max-date="data.maxDate"
          @confirm="onConfirm"
          @cancel="data.showPicker = false"
        />
      </Popup>
      <Field
        readonly
        clickable
        :value="data.form.endTime"
        label="结束时间*"
        placeholder="点击选择时间"
        @click="
          data.showPicker1 = true
          setScrollTop()
        "
      ></Field>
      <Popup
        v-model="data.showPicker1"
        position="bottom"
        @confirm="onConfirm1"
      >
        <DatetimePicker
          v-model="data.currentDate"
          type="datetime"
          title="选择完整时间"
          :min-date="data.minDate"
          :max-date="data.maxDate"
          @confirm="onConfirm1"
          @cancel="data.showPicker1 = false"
        />
      </Popup>
    </Form>
    <Field
      v-model="data.form.url"
      name="直播地址*"
      label="直播地址*"
      placeholder="请输入地址"
      :rules="[{ required: true, message: '请输入地址' }]"
    ></Field>
    <Field
      v-model="data.form.intro"
      rows="4"
      autosize
      label="直播介绍*"
      type="textarea"
      placeholder="请输入介绍"
    ></Field>
    <div class="add-broad-cast__foot" @click="confirm">
      <button>确定</button>
    </div>
  </div>
</template>

<script setup>
import Vue, { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import {
  Sticky,
  Form,
  Field,
  Radio,
  RadioGroup,
  DatetimePicker,
  Toast,
  Icon,
  Popup,
} from 'vant'
import * as broadApi from '@/api/broad'
import UA from '@/utils/ua'
let data = reactive({
  title: '新增首页直播入口',
  icon: '<',
  form: {
    title: '',
    position: '',
    startTime: '',
    endTime: '',
    url: '',
    intro: '',
  },
  showPicker: false,
  showPicker1: false,
  minDate: new Date(2020, 0, 1),
  maxDate: new Date(2025, 10, 1),
  currentDate: new Date(),
  liveId: '',
  loading: false,
  isApp: false,
  proxy:null
})
const getBroadDetail = () => {
  const params = {
    liveId: data.liveId,
  }
  broadApi
    .getBroadDetail(params)
    .then((res) => {
      const resdata = res.data
      resdata.startTime = handleDate(new Date(resdata.startTime))
      resdata.endTime = handleDate(new Date(resdata.endTime))
      resdata.position = String(resdata.position)
      data.form = resdata
      // console.log(data.form)
    })
    .catch()
}
const back = () => {
  history.go(-1)
}

const onConfirm = (datetime) => {
  data.form.startTime = handleDate(datetime)
  data.showPicker = false
}
const onConfirm1 = (datetime) => {
  data.form.endTime = handleDate(datetime)
  data.showPicker1 = false
}
// 活动时间处理
const handleDate = (time) => {
  let timeY = time.getFullYear()
  let timeMon = time.getMonth() + 1
  let timeD = time.getDate()
  let timeH = time.getHours()
  let timeM = time.getMinutes()
  let timeS = time.getSeconds()
  timeMon = timeMon < 10 ? '0' + timeMon : timeMon
  timeD = timeD < 10 ? '0' + timeD : timeD
  if (timeH < 10) {
    timeH = '0' + timeH
  }
  if (timeM < 10) {
    timeM = '0' + timeM
  }
  if (timeS < 10) {
    timeS = '0' + timeS
  }
  return (
    timeY +
    '-' +
    timeMon +
    '-' +
    timeD +
    '' +
    ' ' +
    timeH +
    ':' +
    timeM +
    ':' +
    timeS
  )
}
const confirm = () => {
  if (!data.form.title) {
    Toast('请输入名称')
    return
  }
  if (!data.form.position) {
    Toast('请勾选位置')
    return
  }
  if (!data.form.startTime || !data.form.endTime) {
    Toast('请选择时间')
    return
  }
  if (data.form.startTime >= data.form.endTime) {
    Toast('结束时间应在开始时间之后')
    return
  }
  // if (!data.form.url || data.form.url.indexOf('.10086.cn/') === -1) {
  if (!data.form.url) {
    Toast('请输入直播地址')
    return
  }
  if (!data.form.intro) {
    Toast('请输入介绍')
    return
  }
  const params = {
    ...data.form,
    type: 0,
  }
  data.loading = true
  if (data.liveId) {
    params.liveId = data.liveId
  }
  const request = data.liveId ? broadApi.updateBroad : broadApi.createBroad
  request(params)
    .then((res) => {
      if (res.message === 'ok') {
        data.loading = false
        Toast('提交成功')
        if (data.proxy) {
          data.proxy.$router.push({
            path: '/broadcast/index.html?zbtype=0',
          })
        }
      } else {
        Toast(res.message)
        data.loading = false
      }
    })
    .catch()
}
const setScrollTop = () => {
  var scrollTop =
    document.documentElement.scrollTop ||
    window.pageYOffset ||
    document.body.scrollTop
  if (scrollTop == 0) {
    window.scrollTo(0, 1)
  }
}
onMounted(() => {
  const getVueInstance = getCurrentInstance()
  data.proxy = getVueInstance ? getVueInstance.proxy : null
  data.liveId = data.proxy ? data.proxy.$route.query.liveId : ''
  data.isApp = UA.isApp || UA.isWechat ? false : true
  if (data.liveId) {
    data.title = '修改首页直播入口'
    getBroadDetail()
  }
})
</script>
<style lang="scss" scoped>
  .add-broad-cast {
    background: #f6f6f6;
    min-height: calc(100vh - 80px);
    &__header {
      width: 100%;
      height: 46px;
      line-height: 46px;
      background: #fff;
      position: sticky;
      top: 0;
      z-index: 10000;
      &-back {
        padding-left: 16px;
        float: left;
        i {
          position: absolute;
          font-size: 20px;
          height: 20px;
          left: 15px;
          top: 14px;
        }
      }
      &-title {
        text-align: center;
        font-size: 18px;
        color: #111;
      }
    }
    &__content {
      padding-top: 10px;
    }
    &__foot {
      padding: 16px 16px 0px 16px;
      width: 100%;
      button {
        width: 100%;
        height: 44px;
        line-height: 44px;
        color: #fff;
        background: linear-gradient(to right, #5fc8ff, #8372fe);
        border: none;
        border-radius: 22px;
        font-size: 16px;
      }
    }
  }
</style>
