export default [
  {
    path: "/my/index.html",
    name: "my",
    component: resolve => require(["./index.vue"], resolve),
    meta: {
      title: "个人中心",
      login:true,
      role:[0,1,2]
    }
  },
  {
    path: '/my/mynearby.html',
    name: 'myNearby',
    component: resolve => require(['./mynearby/index.vue'], resolve),
    meta: {
      title: '门店查询'
    }
  },
  // {
  //   path: "/my/coupon.html",
  //   name: "myCoupon",
  //   component: resolve => require(["./coupon/index.vue"], resolve),
  //   meta: {
  //     title: "我的卡券"
  //   }
  // },
  {
    path: '/my/question.html',
    name: 'myQuestion',
    component: resolve => require(['./question/index.vue'], resolve),
    meta: {
      title: '常见问题'
    }
  },
  {
    path: '/my/info.html',
    name: 'myInfo',
    component: resolve => require(['./info/index.vue'], resolve),
    meta: {
      title: '我的资料',
      login:true,
      role:[0,1,2]
    }
  },
  {
    path: '/my/broadband.html',
    name: 'myBroadband',
    component: resolve => require(['./broadband/index.vue'], resolve),
    meta: {
      title: '我的预约',
      keepAlive:true,
      login:true,
      role:[0,1,2]
    }
  },
  {
    path: '/login.html',
    name: 'login',
    component: resolve => require(['./login/login.vue'], resolve),
    meta: {
      title: '登录',
      keepAlive:true
    }
  }
]
