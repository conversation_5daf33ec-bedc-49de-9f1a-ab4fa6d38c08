<template>
  <div class="index nearBy">

    <!-- 企业微信弹框 -->
    <van-popup
      v-model="nearbyData.popup.show"
      round
    >
      <div class="wechpopContent">
        <!-- <div class="remove" @click="handlePopupFalse">
          <l-icon name="close" color="#333333" size="30"/>
        </div> -->
        <!-- <div class="pongetitle">有问题找店长</div> -->
        <div class="bgimg">
          <img mode="widthFix" src="https://img1.shop.10086.cn/fs/goods/fs_62d03471e4b0355791a71fa5.png" alt="">
        </div>
        <div class="text">
          <div class="dzimg">
            <img :src="nearbyData.popup.wxCode" alt="">
            <img :src="nearbyData.popup.avatar" class="positiondzimg" alt="">
          </div>
          <div class="left">
            <div class="tit">
              添加方式:
            </div>
            <div class="com">
              <p>1、请使用微信扫码添加店铺客服</p>
              <p>2、截图保存图片,使用微信扫一扫添加店长好友</p>
            </div>
          </div>

        </div>
      </div>
    </van-popup>

    <div class="nav">
      <van-search
        v-model="nearbyData.keywords"
        placeholder="请输入查询的店铺或相关标签"
        @search="getNearShop"
        @clear="getNearShop"
      />
    </div>
    <div class="cm-line">
      当前店铺
    </div>
    <template v-if="nearbyData.shopInfo">
      <ul class="shop-list">
        <li :class="['borderno','nearbyitem','noimg', nearbyData.shopInfo.expansion ? 'heightYihang' : '']">
          <aside class="con">
            <div class="itemLeft">
              <div class="title">
                <div>
                  {{ nearbyData.shopInfo.shopShortName }}
                </div>
                <dt>
                  <a :href="'tel:' + nearbyData.shopInfo.kefuPhone" class="telNumber">
                    <img
                      src="https://touch.10086.cn/yundian/static/img/jajsdj.b3434e62.png"
                      alt=""
                    /></a>
                </dt>
                <dt>
                  <a v-if="nearbyData.shopInfo.qrCode" href="javascript:;" class="telNumber" @click="handleQrcode(nearbyData.shopInfo)">
                    <img
                      src="https://img1.shop.10086.cn/fs/goods/fs_635642fae4b0fdd36b797c57.png"
                      alt=""
                    /></a>
                </dt>
              </div>
              <div class="businesscontent">
                <!-- 5营业中 6休息中 1不显示 2关厅 -->
                <img v-if="nearbyData.shopInfo.shopOpenStatus===5" src="~@/assets/index_img/shop_open.png" alt="">
                <img v-if="nearbyData.shopInfo.shopOpenStatus===6" src="~@/assets/index_img/shop_close.png" alt="">
                <img v-if="nearbyData.shopInfo.shopOpenStatus===2" src="~@/assets/index_img/shop_close_grey.png" alt="">
                <span>
                  {{ nearbyData.shopInfo.shopOpenStatus===5 ? "营业中" : nearbyData.shopInfo.shopOpenStatus===6 ? "休息中" : nearbyData.shopInfo.shopOpenStatus===2? "关厅" :"" }}
                </span>
                <span v-if="nearbyData.shopInfo.shopOpenStatus===6 && getCurrentHour(nearbyData.shopInfo.businessHours)!=='店铺休息'">营业时间</span>
                <span v-if="nearbyData.shopInfo.shopOpenStatus===5 || nearbyData.shopInfo.shopOpenStatus===6">
                  {{ getCurrentHour(nearbyData.shopInfo.businessHours,nearbyData.shopInfo.shopOpenStatus) }}
                </span>
              </div>
              <p class="address">
                <!-- <van-icon name="location-o" class="icon_adress" size="16" /> -->
                <img
                  src="@/assets/nearby/locationicon.png"
                  class="icon icon_adress"
                />
                <span v-if="nearbyData.shopInfo.distance" class="distance">
                  {{
                    nearbyData.shopInfo.distance > 1000
                      ? (nearbyData.shopInfo.distance / 1000).toFixed(2) + "KM"
                      : nearbyData.shopInfo.distance.toFixed() + "米"
                  }}
                  {{
                    nearbyData.shopInfo.address && nearbyData.shopInfo.address.replace(/[\n|\s]+/g, "") ? "|" : ""
                  }}
                </span>
                <span :class="['addressName','paddingRigth']" :style="{'white-space':nearbyData.shopInfo.isShowExpansionButton?'nowrap':'inherit'}">{{ nearbyData.shopInfo.address }}</span>
              </p>
            </div>
            <div
              class="itemRight"
            >
              <img v-if="nearbyData.shopInfo.nearbyShopPicture" :src="myGetImg(nearbyData.shopInfo.nearbyShopPicture)" />
              <img v-else src="~@/assets/nearby/default_img.png" alt="" />
            </div>
          </aside>

          <!-- 标签列表 -->
          <div v-if="nearbyData.shopInfo && nearbyData.shopInfo.tagList && nearbyData.shopInfo.tagList.length" :class="['tagList', !nearbyData.shopInfo.expansion ? 'heightYihang' : '']">
            <dl class="dl">
              <div class="one">
                <dt v-for="(tag,index) in nearbyData.shopInfo.tagList.slice(0,3)" :key="index">
                  {{ tag }}
                </dt>
              </div>
              <div class="other">
                <dt v-for="(tag,index) in nearbyData.shopInfo.tagList.slice(3)" :key="index">
                  {{ tag }}
                </dt>
              </div>
            </dl>

          </div>

          <div v-if="nearbyData.shopInfo.isShowExpansionButton" class="zhankai">
            <span v-if="!nearbyData.shopInfo.expansion" @click="nearbyData.shopInfo.expansion = true">
              <van-icon name="arrow-down" size="15" color="#333333" />
              <i>展开</i>
            </span>
            <span v-if="nearbyData.shopInfo.expansion" @click="nearbyData.shopInfo.expansion = false">
              <van-icon name="arrow-up" size="15" color="#333333" />
              <i>收起</i>
            </span>
          </div>

          <div class="btnGroup">
            <div v-if="nearbyData.shopInfo.show == 1" class="btnItem" @click="quhao(nearbyData.shopInfo.shopId)">
              <img src="~@/assets/nearby/quhao.png" alt="">
              立即取号
            </div>
            <div class="btnItem" @click="goToShop(nearbyData.shopInfo.shopId)">
              <img src="~@/assets/nearby/gotoshop.png" alt="">
              进店逛逛
            </div>
          </div>
          <div style="clear: both;"></div>
        </li>
      </ul>
    </template>
    <div class="cm-line">
      更多店铺
    </div>
    <div v-if="nearbyData.loading" class="loading-area">
      <van-loading />
    </div>
    <!-- 调试用 -->
    <template v-if="nearbyData.userLocation">
      <!-- <template v-if="!userLocation"> -->
      <ul v-if="nearbyData.shopList && nearbyData.shopList.length" class="shop-list">
        <li
          v-for="(item, key) in nearbyData.shopList"
          :key="key"
          :class="[shopId == item.shopId ? 'disNo noimg' : 'noimg', item.expansion ? 'heightYihang' : '']"
          class="nearbyitem"
        >
          <aside class="con">
            <div class="itemLeft">
              <div class="title">
                <div>
                  {{ item.shopShortName }}
                </div>
                <dt>
                  <a :href="'tel:' + item.kefuPhone" class="telNumber">
                    <img
                      src="https://touch.10086.cn/yundian/static/img/jajsdj.b3434e62.png"
                      alt=""
                    /></a>
                </dt>
                <dt>
                  <a v-if="item.qrCode" href="javascript:;" class="telNumber" @click="handleQrcode(item)">
                    <img
                      src="https://img1.shop.10086.cn/fs/goods/fs_635642fae4b0fdd36b797c57.png"
                      alt=""
                    /></a>
                </dt>
              </div>
              <div class="businesscontent">
                <!-- 5营业中 6休息中 1不显示 2关厅 -->
                <img v-if="item.shopOpenStatus===5" src="~@/assets/index_img/shop_open.png" alt="">
                <img v-if="item.shopOpenStatus===6" src="~@/assets/index_img/shop_close.png" alt="">
                <img v-if="item.shopOpenStatus===2" src="~@/assets/index_img/shop_close_grey.png" alt="">
                <span>
                  {{ item.shopOpenStatus===5 ? "营业中" : item.shopOpenStatus===6 ? "休息中" : item.shopOpenStatus===2? "关厅" :"" }}
                </span>
                <span v-if="item.shopOpenStatus===6 && getCurrentHour(item.businessHours)!=='店铺休息'">营业时间</span>
                <span v-if="item.shopOpenStatus===5 || item.shopOpenStatus===6">
                  {{ getCurrentHour(item.businessHours,item.shopOpenStatus) }}
                </span>
              </div>
              <p class="address">
                <!-- <van-icon name="location-o" class="icon_adress" size="16" /> -->
                <img
                  src="@/assets/nearby/locationicon.png"
                  class="icon icon_adress"
                />
                <span v-if="item.distance" class="distance">
                  {{
                    item.distance > 1000
                      ? (item.distance / 1000).toFixed(2) + "KM"
                      : item.distance.toFixed() + "米"
                  }}
                  {{
                    item.address && item.address.replace(/[\n|\s]+/g, "") ? "|" : ""
                  }}
                </span>
                <span :class="['addressName','paddingRigth']" :style="{'white-space':item.isShowExpansionButton?'nowrap':'inherit'}">{{ item.address }}</span>
              </p>
            </div>
            <div
              class="itemRight"
            >
              <img v-if="item.nearbyShopPicture" :src="myGetImg(item.nearbyShopPicture)" />
              <img v-else src="~@/assets/nearby/default_img.png" alt="" />
            </div>
          </aside>



          <!-- 标签列表 -->
          <div v-if="item.tagList && item.tagList.length" :class="['tagList', !item.expansion ? 'heightYihang' : '']">
            <dl class="dl">
              <div class="one">
                <dt v-for="(tag,index) in item.tagList.slice(0,3)" :key="index">
                  {{ tag }}
                </dt>
              </div>
              <div class="other">
                <dt v-for="(tag,index) in item.tagList.slice(3)" :key="index">
                  {{ tag }}
                </dt>
              </div>
            </dl>

          </div>

          <div v-if="item.isShowExpansionButton" class="zhankai">
            <span v-if="!item.expansion" @click="item.expansion = true">
              <van-icon name="arrow-down" size="15" color="#333333" />
              <i>展开</i>
            </span>
            <span v-if="item.expansion" @click="item.expansion = false">
              <van-icon name="arrow-up" size="15" color="#333333" />
              <i>收起</i>
            </span>
          </div>

          <div class="btnGroup">
            <div v-if="item.show == 1" class="btnItem" @click="quhao(item.shopId)">
              <img src="~@/assets/nearby/quhao.png" alt="">
              立即取号
            </div>
            <div class="btnItem" @click="goToShop(item.shopId)">
              <img src="~@/assets/nearby/gotoshop.png" alt="">
              进店逛逛
            </div>
          </div>
          <div style="clear: both;"></div>
        </li>
        <li class="noimg borderno">
          <p class="desc" style="text-align: center;">
            已经没有了
          </p>
        </li>
      </ul>
      <div v-else-if="nearbyData.shopList && nearbyData.shopList.length == 0" class="needlocation">
        <br /><br />
        您是到火星了么？<br />暂时没有营业中的店铺，请您稍后再试。
      </div>
    </template>
    <div v-else class="needlocation">
      <img src="@/assets/nearby/location.png" />
      此功能需要获取您的地理位置，<br />否则无法获取附近店铺信息。
    </div>
    <!-- 4G免登 -->
    <LoginDialog
      :islogin="autoLoginObj.is4gLogined"
      :is-force-login="true"
      :isloginfn="logined"
      :is-auto-login="autoLoginObj.autoLogin"
      :show-cancle="false"
    />
  </div>
</template>

<script setup>
import { Search, Icon, Toast, Loading, Button, Popup } from "vant"
import "vant/lib/index.css"
import Vue,{reactive,onMounted} from "vue"
Vue.use(Search)
  .use(Icon)
  .use(Toast)
  .use(Loading)
  .use(Button)
  .use(Popup)

import geoApi from "@/api/geo"
import lbsApi from "@/api/lbs"
import { getActData, getCustomerCode } from '@/api/shop'
import loginUtils from "@/utils/login"
import LoginDialog from '@/components/login/index'
import { toHome } from "@/utils/wxMiniProgram"
import { getImgUrl } from "@/utils/utils"
import _ from 'lodash'

const nearbyData = reactive({
  userLocation: {},
  keywords: "",
  cmParam: {
    province_id: 100,
    city_id: 100
  },
  shopList: null,
  loading: false,
  shopInfo: null,
  shopId: null,
  user: null,

  popup:{
    show:false,
    wxCode:null,
    avatar:null,
  }
})
// 4G免登
const autoLoginObj = reactive({
  is4gLogined: false,
  autoLogin: false,
})

const autoLoginCb = (res) => {
  autoLoginObj.is4gLogined = false
  autoLoginObj.autoLogin = true
}
const url = new URL(location.href)
const shopId = url.searchParams.get("shopId")
nearbyData.shopId = shopId
loginUtils.login(false, false, logined, false, false, autoLoginCb, "0")

async function handleQrcode(item){
  let sharingCode = item.sharingCode

  Toast.loading({
    message: '加载中...',
    forbidClick: true,
    duration:0
  })


  if(!sharingCode){
    let homedata = (await getActData({shopId:item.shopId})) || {}
    let actData = _.get(homedata,'data.actData',[])
    let sharing = actData.find(item => item.componentCode == "sharing")
    let contact = actData.find(item => item.componentCode == "contact")
    let floor = null

    if(contact && _.get(contact,'dataList.floor', null)){
      floor = _.get(contact,'dataList.floor', null)
    }

    if(sharing && _.get(sharing,'dataList.floor', null)){
      floor = _.get(sharing,'dataList.floor', null)
    }

    sharingCode = _.get(floor,'sharingCode', null)

    if(!sharingCode){
      Toast('店长企微暂未开通，您可以换个方式联系')
      return
    }
  }

  getCustomerCode({sharingCode}).then(res => {
    Toast.clear()

    if(res.code){
      Toast('店长企微暂未开通，您可以换个方式联系')
      return
    }

    const qrCode = res.data.contactQrcode || res.data.qrCode

    if(!qrCode){
      Toast('店长企微暂未开通，您可以换个方式联系')
      return
    }
    let popup = {
      avatar:res.data.avatar,
      wxCode:qrCode,
      show : true
    }
    nearbyData.popup = popup
  })
}

function init() {
  getUserLocation()
}
function logined(res) {
  nearbyData.user = res
  nearbyData.isLogined = res && res.UserName > ""
}
function getShopInfo() {
  //当前店铺信息
  if (nearbyData.userLocation == null) {
    lbsApi
      .getShopInfo({
        shopId: nearbyData.shopId
      })
      .then(res => {
        if (res.code == 0 && res.data) {
          /* 增加是否标签收起字段 */
          let shops = JSON.parse(JSON.stringify(res.data))
          let strLength = 0
          strLength = shops.tagList.length
          shops.tagLength = strLength
          if(strLength > 3){
            shops.expansion = false
          }else{
            shops.expansion = true
          }

          nearbyData.shopInfo = shops
        }
      })
  } else {
    const { latitude, longitude } = nearbyData.userLocation
    lbsApi
      .getShopInfo({
        latitude,
        longitude,
        shopId: nearbyData.shopId
      })
      .then(res => {
        if (res.code == 0 && res.data) {
          /* 增加是否标签收起字段 */
          let shops = JSON.parse(JSON.stringify(res.data))

          let strLength = 0
          strLength = shops.tagList.length
          shops.tagLength = strLength

          // 拿到地址length
          const returnStringLength = (message) => {
            let lens = 0
            for (let i = 0; i < message.length; i++) {
              if ((message.charCodeAt(i) >= 0) && (message.charCodeAt(i) <= 255))
              { lens = lens + 1}
              else{ lens = lens + 2 }
            }
            return lens
          }
          let addressLength = returnStringLength(shops.address)
          shops.addressLength = addressLength

          // 决定展开收起的变量
          if(strLength > 3 || addressLength > 29){
            shops.expansion = false

            // 代表当前是否需要显示展开按钮
            shops.isShowExpansionButton = true
          }else{
            shops.expansion = true

            // 代表当前是否需要显示展开按钮
            shops.isShowExpansionButton = false
          }

          nearbyData.shopInfo = shops
        }
      })
  }
}
function getNearShop() {
  //附件店铺
  const { latitude, longitude } = nearbyData.userLocation
  lbsApi
    .getNearShop({
      latitude,
      longitude,
      keywords: nearbyData.keywords
    })
    .then(res => {
      if (res.code == 0 && res.data && res.data.shops) {

        /* 增加是否标签收起字段 */
        let shops = JSON.parse(JSON.stringify(res.data.shops))
        shops.forEach(item => {
          // 拿到标签length
          let strLength = 0
          strLength = item.tagList.length
          item.tagLength = strLength

          // 拿到地址length
          const returnStringLength = (message) => {
            let lens = 0
            for (let i = 0; i < message.length; i++) {
              if ((message.charCodeAt(i) >= 0) && (message.charCodeAt(i) <= 255))
              { lens = lens + 1}
              else{ lens = lens + 2 }
            }
            return lens
          }
          let addressLength = returnStringLength(item.address)
          item.addressLength = addressLength

          // 决定展开收起的变量
          if(strLength > 3 || addressLength > 29){
            item.expansion = false

            // 代表当前是否需要显示展开按钮
            item.isShowExpansionButton = true
          }else{
            item.expansion = true

            // 代表当前是否需要显示展开按钮
            item.isShowExpansionButton = false
          }
        })

        nearbyData.shopList = shops
      }
    })
}
function myGetImg(src){
  return getImgUrl(src)
}
function getCurrentHour(businessHours,shopOpenStatus){
  let week = new Date().getDay(),str=""
  if(businessHours){
    if(week>0){
      str = businessHours[week-1]
    }else{
      str = businessHours[6]
    }
  }else{
    str = ""
  }
  return  str
}
function quhao(shopID) {
  if (nearbyData.isLogined) {
    lbsApi
      .getUrlReq({
        lat: nearbyData.userLocation ? nearbyData.userLocation.latitude : "",
        lon: nearbyData.userLocation ? nearbyData.userLocation.longitude : "",
        pageType: 2,
        shopId: shopID
      })
      .then(res => {
        if (res.code == 0) {
          location.href = res.data.url
        } else {
          Toast(res.message)
        }
      })
  } else {
    loginUtils.login(true, true, logined)
  }
}
function goToShop(shopId) {
  toHome(shopId)

  location.href = "/yundian/index.html?shopId=" + shopId
}
async function getUserLocation() {
  nearbyData.loading = true
  geoApi.getPosition().then(res => {
    nearbyData.loading = false
    if (res.code) {
      Toast("获取位置信息失败")
      nearbyData.userLocation = null
      getShopInfo()
    } else {
      if (res.data && res.data.latitude) {
        nearbyData.userLocation = res.data || {}
        getShopInfo()
        getNearShop()
      } else {
        Toast("获取位置信息失败")
        nearbyData.userLocation = null
        getShopInfo()
      }
    }
  })
  geoApi.getLocation().then(res => {
    const { province } = res.data
    nearbyData.cmParam.province_id = province || 100
    nearbyData.cmParam.city_id = province || 100 //使用省会
  })
}
onMounted(()=>{
  setTimeout(init, 100)
})
</script>
<style>
body {
  background: #fff !important;
  font-size: 12px;
}
</style>
<style lang="scss" scoped>
  .zhankai{
    display: flex;
    padding:0 15px;
    justify-content: flex-end;
    align-items: center;
    span{
      font-size: 10px;
      color:#333;
      display: flex;
      align-items: center;
      i{
        font-style:normal;
        margin-left: 3px;
      }
    }
  }
.tagList{
  padding:0 15px;
  display: flex;
  // dl{
  //   .one{
  //     dt{
  //     //   overflow: hidden;
  //     // text-overflow: ellipsis;
  //     // white-space: nowrap;
  //     }
  //   }
  // }
  &.heightYihang{
    height:36px;
    overflow: hidden;
  }
  dl{
    display: flex;
    flex-flow: wrap;
    flex:1;
    min-width:0;
    margin-top: 10px;
    .other{
      display: flex;
      flex-flow:wrap;
      width:100%;
    }
    .one{
      display: flex;
      width:100%;
    }
    dt{
      font-size: 10px;
      background: #dff1fb;
      border:2px solid #51a7ea;
      border-radius:10px;
      padding:1px 3px;
      color:#51a7ea;
      margin-right: 5px;
      margin-bottom: 5px;
    }
  }
}
.loading-area {
  width: 40%;
  text-align: center;
  margin: 40px auto;
}
.needlocation {
  padding: 20px 20px 0;
  font-size: 14px;
  line-height: 180%;
  text-align: center;
  color: #999;
  img {
    width: 60px;
    display: block;
    margin: 10px auto;
  }
}
.index {
  min-height: calc(100vh - 73px);
  color: #333333;
  a {
    color: #333333;
  }
  .nav {
    padding: 10px 0;
    .van-search {
      width: 355px;
      height: 36px;
      background: #ffffff;
      border: 1px solid #dedede;
      border-radius: 21px;
      margin: 0 auto;
      .van-search__content {
        background: none;
        padding-left: 0;
      }
    }
  }
  .banner {
    height: 160px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .cm-icon {
    width: 345px;
    margin: 0 auto;
    display: flex;
    flex-direction: row;
    height: 115px;
    .item {
      flex-grow: 1;
      padding-top: 28px;
      font-size: 13px;
      text-align: center;
      img {
        width: 42px;
        height: 42px;
        margin: 0 auto 9px auto;
        display: block;
      }
    }
  }
  .cm-line {
    width: 375px;
    line-height: 40px;
    font-size: 14px;
    background: #f3f3f3;
    padding-left: 15px;
  }
  .shop-list {
    background: #f3f3f3;
    li {
      list-style: none;
      // height: 90px;
      position: relative;
      font-size: 12px;
      padding: 15px 15px 15px 140px;
      line-height: 20px;
      border-bottom: 1px dashed #e9e9e9;
      &.noimg {
        padding-left: 15px;
      }
      &.borderno {
        border: none;
      }
      .title {
        font-size: 14px;
        color: #333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .desc {
        font-size: 12px;
        color: #999;
        display: -webkit-box;
        -webkit-line-clamp: 1; /*设置p元素最大4行，父元素需填写宽度才明显*/
        text-overflow: ellipsis;
        overflow: hidden;
        /* autoprefixer: ignore next */
        -webkit-box-orient: vertical;
        word-wrap: break-word;
        word-break: break-all;
        white-space: nowrap;
        margin-bottom: 3.75px;
      }
      label {
        color: #ed2668;
        float: right;
      }
      .address {
        color: #64bbff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        height: 25px;
        line-height: 25px;
        width: 280px;
      }
      .change {
        float: right;
        width: 56px;
        height: 25px;
        background: linear-gradient(90deg, #93da80, #4fb5b1);
        border-radius: 13px;
        font-size: 12px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: #ffffff;
        line-height: 17px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 2px 0 0px;
      }
    }
    .disNo {
      display: none;
    }
  }
}

.nearBy {
  .nearbyitem{
    .itemLeft{
      width:240px!important;
      .title{
        display: flex;
        align-items: center;
        &>div{
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}

.telNumber{
  width: 20px;
  height: 20px;
  display: block;
  margin-left:6px;
  img{
    width:100%;
    height:100%;
  }
}

.wechpopContent{
  width:280px;
  border-radius:12px;
  overflow: hidden;
  background: #fff;
  .bgimg{
    img{
      width:100%;
    }
  }
  .text{
    .dzimg{
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      margin:7.5px 0;
      img{
        width:140px;
        height:140px;
      }
      img.positiondzimg{
        position: absolute;
        width:30px;
        height:30px;
        left:0;
        right:0;
        top:0;
        bottom:0;
        margin:auto;
      }
    }

    .left{
      padding:0px 14.5px;
      margin:7.5px 0;
      padding-bottom: 7.5px;
      margin-top: 15px;
      .tit{
        font-size: 15px;
        color:#000;
        margin-bottom: 5px;
        margin-top: 2.5px;
      }
      .com{
        color:#000;
        font-size: 10px;
        p{
          padding:2.5px 0;
          line-height: 1.8;
        }
      }
    }
  }
}

/* 新增样式 */
.index .shop-list li{
  .con{
    display: flex;
    .itemLeft{
      flex:1;
      min-width:0;
    }
  }

  .businesscontent{
    padding-top: 5px;
    width:100%;
    height:auto;
  }

  &.heightYihang{
    .addressName{
      overflow: inherit;
      text-overflow: inherit;
      white-space: inherit !important;
      word-break: break-all;
    }
  }
  .address{
    display: flex;
    height:auto;
    width:100% !important;
    padding:5px 0 !important;
    overflow: inherit;
    text-overflow: inherit;
    white-space: inherit;
    span{
      display: block;
      line-height: inherit;
    }
  }
  .icon_adress{
    transform: none;
  }
  .addressName{
    flex:1;
    min-width:0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 3px;
    &.paddingRigth{
      padding-right: 15px;
    }
  }
}
</style>
