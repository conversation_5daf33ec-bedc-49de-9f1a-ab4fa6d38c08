<template>
  <div class="index">
    <header-nav v-if="!question.isWechat" :title="question.title"></header-nav>
    <div class="questionMain">
      <Collapse v-model="question.activeNames">
        <CollapseItem title="一、云店介绍" name="1">
          <h3>1、什么是云店？</h3>
          <P>答：云店是中国移动实体营业厅提供的包括移动自有产品和线下实体商户自营产品相结合的线上店铺。</p>
          <h3>2、通过哪些方式可以进入云店？</h3>
          <P>答：点击店主或者店员分享的链接或扫码进入店铺；在APP“我的”“我的服务”中附近店铺进入云店。</p>
          <h3>3、云店都有哪些商品类型？</h3>
          <P>答：在云店用户能充值话费、流量，办理套餐、流量包、宽带，购买终端配件等商品。（具体商品品类以云店店铺销售商品为准）</p>
          <h3>4、云店需要登录吗？</h3>
          <P>答：浏览云店不需要登录，购买商品需要登录。</p>
        </CollapseItem>
        <CollapseItem title="二、购物指南" name="2">
          <h3>1、如何登录云店？</h3>
          <P>答：支持所有开放地区的中国移动用户在同一终端通过手机号码+短信验证码登录、手机号码+服务密码登录+短信验证码登录（双重验证）；支持所有开放地区的非中国移动用户在同一终端通过手机号码+短信验证码登录。</p>
          <h3>2、如何在云店充值流量？</h3>
          <P>1）点击流量充值按钮，进入流量直充页面。</p>
          <P>2）在流量直充页面，浏览列表中的流量商品，您可以根据自身需要，选择需要购买的流量业务，选择完您所挑选的流量业务后，点击立即充值，进入到订单支付页面。</p>
          <P>3）核对您充值流量的信息，所有信息确认无误后，选择您的支付方式，点击确认支付，在选择的支付方式进行支付后，完成流量充值。</p>
          <div class="questionIce">
            <img src="@/assets/my/questionIce1.jpg" alt="">
            <img src="@/assets/my/questionIce2.jpg" alt="">
            <img src="@/assets/my/questionIce3.jpg" alt="">
          </div>
          <h3>3、如何在云店充值话费？</h3>
          <P>1）点击话费充值按钮，进入充值交费页面。</p>
          <P>2）在充值交费页面，您可以根据自身需要，选择需要购买的话费面值，或者手动输入需要充值的整数面值，信息确定后，点击立即充值，进入到订单支付页面。</p>
          <P>3）核对您充值话费的信息，确认所有信息确认无误后，点击确认支付，在选择的支付方式进行支付后，完成话费充值。</p>
          <div class="questionIce">
            <img src="@/assets/my/questionIce4.jpg" alt="">
            <img src="@/assets/my/questionIce5.jpg" alt="">
            <img src="@/assets/my/questionIce6.jpg" alt="">
          </div>
          <h3>4、如何在云店办理套餐和流量包？</h3>
          <P>办理流量</p>
          <P>1）在流量专区，浏览流量商品，选择您想办理的流量业务、</p>
          <P>2）购买商品，在商品的列表页，您可以根据自身需要，选择需要购买的流量业务和购买方式，选中您所挑选的流量业务后，点击立即办理，进入到业务办理确认页面。</p>
          <P>3）核对办理的流量业务信息，所有信息确认无误后，点击确认订购，完成流量业务办理。</p>
          <div class="questionIce">
            <img src="@/assets/my/questionIce7.jpg" alt="">
            <img src="@/assets/my/questionIce8.jpg" alt="">
            <img src="@/assets/my/questionIce9.jpg" alt="">
          </div>
          <P>办理套餐</p>
          <P>1）在套餐专区，浏览套餐商品，选择您想办理的套餐业务、</p>
          <P>2）您可以根据自身需要，选择需要办理的套餐业务后，点击立即办理，进入到业务办理确认页面。</p>
          <P>3）核对办理的套餐信息，所有信息确认无误后，点击确认订购，完成套餐业务办理。</p>
          <P>4）如遇到新办理套餐和您现有的业务有冲动，可以选择放弃办理，或者选择一键办理，完成业务退订和新办理套餐确认。</p>
          <div class="questionIce">
            <img src="@/assets/my/questionIce10.jpg" alt="">
            <img src="@/assets/my/questionIce11.jpg" alt="">
            <img src="@/assets/my/questionIce12.jpg" alt="">
          </div>
          <h3>5、如何在云店购买终端配件？</h3>
          <P>1）在终端专区和配件专区，浏览商品，选择您想购买的终端或配件</p>
          <P>2）购买商品，在商品的列表页，您可以根据自身需要，选择购买方式，喜欢的终端款式，选择完您所挑选的商品后，点击立即购买，进入到订单信息填写页面。</p>
          <P>3）核对购买的终端信息，添加收货地址信息，确认所有信息确认无误后，点击提交订单，进入订单确认支付页面。</p>
          <P>4）选择支付的方式进行支付。完成终端购买。</p>
          <div class="questionIce">
            <img src="@/assets/my/questionIce13.jpg" alt="">
            <img src="@/assets/my/questionIce14.jpg" alt="">
            <img src="@/assets/my/questionIce15.jpg" alt="">
          </div>
          <h3>6、如何在云店办理宽带？</h3>
          <P>1）在宽带专区，浏览宽带商品，选择您想办理的宽带业务。</p>
          <P>2）购买商品，在商品的列表页，您可以根据自身需要，选择需要购买的宽带业务，选中您所挑选的宽带业务后，点击立即办理，进入到业务办理确认页面。</p>
          <P>3）核对办理的宽带业务信息，确认所有信息确认无误后，点击确认订购，完成宽带业务办理。</p>
          <h3>7、如何在云店购买“店长推荐”商品？</h3>
          <P>1）在店长推荐专区，浏览商品，选择您想购买的商品、</p>
          <P>2）购买商品，在商品的列表页，可以根据自身需要，选择购买方式和自己喜欢的商品，选中您所挑选的商品后，点击立即购买，进入到订单信息填写页面。</p>
          <P>3）核对购买的商品信息，所有信息确认无误后，点击提交订单，进入订单确认支付页面。</p>
          <P>4）选择支付的方式进行支付，完成商品购买。</p>
        </CollapseItem>
        <CollapseItem title="三、支付退款" name="3">
          <h3>1、云店都有哪些付款方式？</h3>
          <P>答：云店目前只支持在线支付，在线支付包括微信支付、支付宝支付、银联支付、和包支付4种支付方式。</p>
          <h3>2、已提交订单，如何付款？</h3>
          <P>目前云店支持在线支付，点击“我的”，进入“我的”页面，在“我的订单”中找到待付款订单，点击“去支付”，选择支付方式，确认支付，即可付款成功；目前在线支付支持支付宝支付、微信支付、和包支付、银联支付等方式进行支付，可根据您的使用喜好进行选择。</p>
          <h3>3、如果订单支付失败怎么办？</h3>
          <P>您好，支付总是失败可以有以下原因：</p>
          <P>（1） 您的银行卡尚未开通网上银行支付功能，建议您到当地营业厅开通网上银行；</p>
          <P>（2） 所用银行卡超出该银行支持地域范围，请您更换银行卡试试；</p>
          <P>（3） 银行卡已过期、作废、挂失或者余额不足等，建议您咨询您的开户行；</p>
          <P>（4） 部分银行设置有支付金额限制，如限制支付金额不能操作1500，超过1500的订单则无法支付成功，您可以联系发卡银行详细了解。</p>
          <P>（5） 如遇活动高峰期导致付款不成功，建议您重新打开页面进行支付。</p>
          <h3>4、如果订单重复支付怎么办？</h3>
          <P>同一笔订单如果支付超过了一次，重复支付的款项会在48小时之内原路退还，如若48小时内未退款成功，请联系商家客服处理。</p>
          <h3>5、订单已经取消，但是未收到退款怎么办？</h3>
          <P>答：退款到账时间一般为15个工作日，如若15个工作日还未收到退款，请联系商家客服处理。</p>
        </CollapseItem>
        <CollapseItem title="四、订单问题" name="4">
          <h3>1、在云店里如何查询已提交的订单？</h3>
          <P>答：点击“我的”，进入“我的”页面，找到“我的订单”，在我的订单中查看已购买商品即可。</p>
          <h3>2、订单如何取消？</h3>
          <P>答：点击“我的”，进入“我的”页面，找到“我的订单”，在我的订单中找到需要取消的订单点击取消即可。</p>
          <h3>3、为什么我的订单状态一直没有变化？</h3>
          <P>答：可能是由于快递暂时未录入信息导致，若长时间没有更新，请直接联系商家客服帮您处理。</p>
          <h3>4、为什么订单上显示的快递单号查询不到配送信息？</h3>
          <P>答：快递单号查询不到配送信息一般是由于商家上传错误了快递单号或快递暂时未录入信息导致，若您的订单长时间未更新，可直接联系商家客服帮您处理。</p>
        </CollapseItem>
        <CollapseItem title="五、物流环节" name="5">
          <h3>1、云店支持用户自己选择快递吗？</h3>
          <P>答：云店暂不支持用户选择快递。</p>
          <h3>2、商品邮寄有快递费吗？</h3>
          <P>答：是否收取快递费用请详见商品介绍。</p>
          <h3>3、收到的商品少了怎么办？</h3>
          <P>答：同一笔订单购买多个商品可能分为1个以上包裹发出，可能不会同时送达，建议您耐心等待1-2天，如未收到请联系商家客服。</p>
          <h3>4、收到的商品发错了怎么办？</h3>
          <P>答：如您确认收到的商品错误，请联系商家客服。</p>
        </CollapseItem>
        <CollapseItem title="六、售后咨询" name="6">
          <h3>1、如何退换货？</h3>
          <P>答：a、对于完成签收7日内的订单，客户可在我的-个人中心-商品订单对该订单发起退货申请；客服人员24小时内会与客户进行核实退货信息；</p>
          <P>b、对于符合退货规则的向用户提供退货地址，用户所有商品寄回；对于不符合退货规则的则退货申请不通过；</p>
          <P>c、用户附单据填写订单编号 、收货人电话、姓名、退换货理由并邮寄全套商品；</p>
          <P>d、收到退货后，商家确认货物是否符合退换条件，确认后将在15个工作日内退款至用户账户，如不符合退货要求的将对产品予以退回。</p>
        </CollapseItem>
      </Collapse>
    </div>
    <Footer />
  </div>
</template>

<script setup>
import headerNav from "@/views/my/components/headerNav"
import { Collapse, CollapseItem} from "vant"
import "vant/lib/index.css"
import {reactive} from "vue"
import UA from '@/utils/ua'
const question = reactive({
  title: "常见问题",
  activeNames: ["1"],
  info: "",
  isWechat:UA.isWechat
})
</script>
<style lang="scss" scoped>
.index {
    min-height: calc(100vh - 73px);
  .questionMain {
    .van-collapse-item {
      margin-top: 10px;
      div.questionIce{
        display: flex;
        margin: 6px 0;
        img{
          display: inline-block;
          width: 30%;
          height: 230px;
          flex: 1;
        }
      }
      p{
       text-indent:1.5em;
      }
    }
  }
}
</style>
