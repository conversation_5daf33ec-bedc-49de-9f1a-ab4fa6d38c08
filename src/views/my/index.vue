<template>
  <div class="my">
    <LoginDialog
      :islogin="autoLoginObj.is4gLogined"
      :is-force-login="autoLoginObj.isForceLogin"
      :isloginfn="logined"
      :is-auto-login="autoLoginObj.autoLogin"
      :show-cancle="true"
    />
    <div class="my-content">
      <div class="top">
        <div class="top-left">
          <div class="left">
            <img src="@/assets/my/head.png" alt="" />
          </div>
          <div v-if="data.logUser" class="right">
            <p class="telphone">
              {{ data.logUser.UserName }}
            </p>
            <p v-if="data.logUser.userProvince" class="telphone-name">
              {{ data.provindeName }}
            </p>
          </div>
          <span v-else class="right">
            <p class="login-btn" @click="openlogin()">
              立即登录
            </p>
          </span>
        </div>
        <div v-if="data.showLogout && data.logUser" class="top-right">
          <img src="@/assets/my/change.png" alt="" @click="goOut()" />
        </div>
      </div>
      <div v-if="data.shopInfo" class="shop-message">
        <div class="shop-left">
          <img src="@/assets/my/shop.png" alt="" />
          <p>当前门店</p>
        </div>
        <div class="line"></div>
        <div class="center">
          <p class="shop-name">
            {{ data.shopInfo.shopShortName }}
          </p>
          <p class="shop-address">
            <img src="@/assets/my/address.png" alt="" /><span>{{
              data.shopInfo.address
            }}</span>
          </p>
        </div>
        <div class="change">
          <img src="@/assets/my/changeShop.png" alt="" @click="changeShop()" />
        </div>
      </div>
      <div class="shop-sign">
        <Signin :title="data.shopInfo ? data.shopInfo.shopShortName : ''" />
      </div>
      <div class="my-order" :class="{bg_gray:data.wcsbData.show}">
        <div class="order-title" :class="{bg_white:data.wcsbData.show,top_radius:data.wcsbData.show}">
          <p class="title">
            我的订单
          </p>
          <p class="all-order" @click="lookAllOrder()">
            查看全部订单<van-icon name="arrow" size="12px" />
          </p>
        </div>
        <div class="order-type" :class="{bg_white:data.wcsbData.show,bottom_radius:data.wcsbData.show}">
          <ul>
            <li
              v-for="(item, index) in list.orderList"
              :key="index"
              @click="clickOrder(item)"
            >
              <van-badge
                :content="item.number > 0 ? item.number : ''"
                max="99"
                color="#FF7B3E"
              >
                <img :src="item.url" alt="" />
                <p>{{ item.title }}</p>
              </van-badge>
            </li>
          </ul>
        </div>
        <div v-if="data.wcsbData.show" class="wcsb_img" @click="goLink()">
          <img :src="data.wcsbData.img" alt="" />
        </div>
        <div class="other-type">
          <ul>
            <li
              v-for="(item, index) in list.otherTypeList"
              :key="index"
              @click="clickLink(item)"
            >
              <div class="type-left">
                <img :src="item.url" alt="" />
                <p>{{ item.title }}</p>
              </div>
              <div class="type-right">
                <!--<p v-if="index == 0 && item.number > 0">
                  {{ item.number }}张优惠卷
                </p>--><img src="@/assets/my/right.png" alt="" />
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <van-popup v-model="data.show" :style="{ borderRadius: '9px' }">
      <div class="out">
        <div class="point">
          确认退出
        </div>
        <div class="select">
          <p class="cancle" @click="cancle()">
            取消
          </p>
          <p @click="sure()">
            确定
          </p>
        </div>
      </div>
    </van-popup>
    <Footers :user-info="data.logUser" :force-show="true" isactive="my" />
  </div>
</template>
<script setup>
import { Badge, Icon, Popup, Toast } from "vant"
import Vue,{ onMounted, reactive, getCurrentInstance } from 'vue'
import "vant/lib/index.css"
import loginUtils from "@/utils/login"
import login from "@/api/login.js"
import ua from "@/utils/ua.js"
import shopAPI from "@/api/shop.js"
import lbsApi from "@/api/lbs"
import myOrder from "@/api/order.js"
import encryption from "@/api/encryption.js"
import provinceJson from "@/utils/province.js"
import stqurl from "@/utils/stqurl.js"
import insertCode from "@/utils/insertCode.js"
import { setSearchParamsArray,getImgUrl,getUrl,copy,getCookie } from "@/utils/utils.js"
//组件
import Signin from "@/views/my/components/signin.vue"
import LoginDialog from "@/components/login/index.vue"
import Footers from "@/views/my/components/myfooter.vue"
import {ENV} from "@/utils/env.js"
Vue.use(Badge)
  .use(Icon)
  .use(Popup)
const CMDATA = {
  WCSB:{
    SHOW:"1" //展示运营位数据
  }
}
const data = reactive({
  logUser: null,
  preview: null,
  pageInfo: null,
  shopInfo: null,
  active: 2,
  showLogout: true,
  isa: "ident",
  proxy:null,
  provindeName: null,
  isLogined: false,
  moOrder: "https://touch.10086.cn/i/mobile/ordersList.html", //我的订单
  myThh: "https://touch.10086.cn/i/gray/mobile/returnorderqry.html", //退换货
  show: false,
  wcsbData:{
    img:require("@/assets/index_img/wcsb.png"),
    link:"",
    show:false,
    dcsid:"yd_h5_me_wcsb"
  },
})
const list = reactive({
  orderList: [
    {
      title: "待付款",
      url: require("@/assets/my/obligations.png"),
      number: 0,
      links: "",
    },
    {
      title: "待发货",
      url: require("@/assets/my/sendGoods.png"),
      number: 0,
      links: "",
    },
    {
      title: "待收货",
      url: require("@/assets/my/receiving.png"),
      number: 0,
      links: "",
    },
    {
      title: "退换货",
      url: require("@/assets/my/return.png"),
      number: 0,
      links: "",
    },
  ],
  otherTypeList: [
    {
      title: "优惠券",
      url: require("@/assets/my/conpou.png"),
      number: "",
      // links: "coupon.html"
      //links: "https://b2c.market.chinamobile.com/cnr-web/myCounpons"
      links: "",
      key:"coupons"
    },
    {
      title: "我的拼团",
      links:"ms.html",
      url: require("@/assets/my/fs_62da7484e4b05eda6758b8e7.png"),
      key:"spelling",
    },
    {
      title: "我的预约",
      url: require("@/assets/my/fs_62c2a251e4b0355791a71f8a.png"),
      links: "broadband.html?shopId=",
      key:"reservation"
    },
    {
      title: "我的资料",
      url: require("@/assets/my/myInfo.png"),
      links: "info.html?shopId=",
      key:"profile"
    },
    {
      title: "常见问题",
      url: require("@/assets/my/about.png"),
      links: "question.html",
      key:"problem"
    },
  ],
})
onMounted(() => {
  const url = new URL(location.href)
  const shopId = url.searchParams.get('shopId')||url.searchParams.get('shop_id')
  const actId = url.searchParams.get('actId')
  const preview = url.searchParams.get('preview')
  const getVueInstance = getCurrentInstance()
  data.proxy = getVueInstance? getVueInstance.proxy:null
  if ((!shopId) && data.proxy) {
    data.proxy.$router.push({
      name: "nearby"
    })
    return false
  }
  if(data.proxy){
    data.proxy.$store.commit('SET_SHOPID', shopId)
  }
  data.pageInfo = {
    shopId,
    actId,
    preview,
  }
  data.preview = preview
  let needShopid = ["reservation","profile"]
  needShopid.forEach((value)=>{
    const reservationIndex = list.otherTypeList.findIndex(item => item.key == value)
    list.otherTypeList[reservationIndex].links = list.otherTypeList[reservationIndex].links + shopId
  })
  //默认订单链接
  if (ua.isApp) {
    data.isa = "sign"
    data.showLogout = false
  }
  list.orderList.forEach((item, i) => {
    if (i == 3) {
      item.links = setSearchParamsArray(data.myThh, {
        "WT.ac_id": "201214_GRZXTYDD_YJYDWDCKDD",
        systemId: "08",
        shopId: shopId,
      })
    } else {
      item.links = setSearchParamsArray(data.moOrder, {
        "WT.ac_id": "201214_GRZXTYDD_YJYDWDCKDD",
        systemId: "08",
        orStatu: i - 0 + 1,
        shopId: shopId,
      })
    }
    item.links = item.links + "&" + data.isa + "=fffffff0"
  })
  //4G免登
  loginUtils.login(true, true, logined, false, false, autoLoginCb, "0")
})
function logined(res) {
  if(!res){
    return false
  }
  data.logUser = res
  data.isLogined = res && res.UserName > ""
  data.moOrder = stqurl.ordersList
  data.myThh = stqurl.returnorderqry
  let province
  if (res) {
    province = provinceJson.data[res.userProvince + "_" + res.userCity]
  }
  let number = res.UserName
  let pat = /(\d{3})\d*(\d{4})/
  let text1 = number.replace(pat, "$1****$2")
  data.logUser.UserName = text1
  if (province != undefined) {
    let provinceCity = province.split("_")
    data.provindeName = provinceCity[0]
  }
  getCmData()
  getShopInfo()
  getmyOrder()
  getEncryption()
}
function getCmData(){
  shopAPI.getCmData({
    shopId: data.pageInfo.shopId,
    cmCodeList:["wenchasuanbi"]
  }).then((res) => {
    if (res.code == 0 && res.data) {
      if(res.data.wenchasuanbi){
        let cmdata = res.data.wenchasuanbi.data[1].tabData[0]
        data.wcsbData.img = cmdata.imgSrc !== "-1" ? getImgUrl(cmdata.imgSrc,"material") : require("@/assets/my/wcsb.png")
        data.wcsbData.show = cmdata.description === CMDATA.WCSB.SHOW
        data.wcsbData.link = getUrl("/hd/qd.html?url=/hd/wcsb1/index.html&key="+res.data.key+"&WT.ac_id=yd_h5_me_wcsb&shopId="+data.pageInfo.shopId)
      }else{
        data.wcsbData.show = false
      }
    } else {
      Toast(res.message)
    }
  })
}
function goLink(){
  insertCode(data.wcsbData.dcsid,data.wcsbData.link)
}
const autoLoginObj = reactive({
  is4gLogined :false,
  autoLogin:false,
  isForceLogin:false
})
//开启4G免登查询
function autoLoginCb() {
  autoLoginObj.is4gLogined = false
  autoLoginObj.autoLogin = true
  autoLoginObj.isForceLogin = true
}
/** 拉起登录 */
function openlogin(callback) {
  callback = callback || logined
  let nalogin = getCookie('nalogin') == 1 ? false : true
  // 注：nalogin是false 不会进入autoLoginCb
  loginUtils.login(
    true,
    true,
    callback,
    false,
    nalogin,
    autoLoginCb,
    '0'
  )
}
// 退出登录
function goOut() {
  data.show = true
}
// 退出登录取消按钮
function cancle() {
  data.show = false
}
// 退出登录确认按钮
function sure() {
  data.show = false
  getLogout()
}
//退出登录
function getLogout() {
  login.logout({}).then((res) => {
    if (res.code == 0) {
      window.localStorage.removeItem("yundian")
      window.location.href =
        "/hd/xskd/shop.html?shop_id=" + data.pageInfo.shopId
    }
  })
}
/* 当前店铺信息 */
function getShopInfo() {
  lbsApi.getShopInfo({shopId: data.pageInfo.shopId})
    .then((res) => {
      if (res.code == 0 && res.data) {
        data.shopInfo = res.data
        /* 我的拼团链接设置 */
        const spellingIndex = list.otherTypeList.findIndex(item => item.key == "spelling")
        let spellingUrl = null
        let storeId = data.shopInfo.unifiedChannelId
        let otherStoreId = data.pageInfo.shopId
        spellingUrl = `${ENV.getB2bDomain()}/cnr-web/pinTuanMyList?shareParam=c3RvcmVJZD0yNDcxNDcxMDEyMTEwNzAwMDAz`
        list.otherTypeList[spellingIndex].links = `${spellingUrl}&storeId=${storeId}&otherStoreId=${otherStoreId}`
      } else {
        Toast(res.message)
      }
    })
}
// 切换店铺
function changeShop() {
  data.proxy.$router.push({
    name: "myNearby",
    query: { shopId: data.pageInfo.shopId },
  })
}
//跳转优惠卷，我的资料,收货地址，关于云店
function clickLink(item) {
  if(!data.logUser){
    openlogin(null)
    return false
  }
  window.location.href = item.links
}
//跳转优惠券加密参数
function getEncryption() {
  encryption
    .getEncryption({
      shopId: data.pageInfo.shopId,
      type: "type1",
    })
    .then((res) => {
      if (res.code == 0 && res.data) {
        list.otherTypeList[0].links ="https://touch.10086.cn/i/mobile/mycoupons.html?shareParam=" +
          res.data.shareParam
        list.otherTypeList = copy(list.otherTypeList)
      } else {
        Toast(res.message)
      }
    })
}
//订单接口
function getmyOrder() {
  //订单接口
  myOrder
    .getOrderStatuscount({
      shopId: data.pageInfo.shopId,
    })
    .then((res) => {
      if (res.code == 0 && res.data) {
        res.data.orderStatusCountInfos.map((item, key) => {
          if (item.count && item.count != 0) {
            list.orderList[item.orderStatusCode - 1].number = item.count
          }
        })
      }
    })
}
// 查看所有订单
function lookAllOrder() {
  if(!data.logUser){
    openlogin(null)
    return false
  }
  let myorder = setSearchParamsArray(data.moOrder, {
    "WT.ac_id": "201214_GRZXTYDD_YJYDWDCKDD",
    systemId: "08",
    orStatu: "",
    shopId: data.pageInfo.shopId,
  })
  if (ua.isApp) {
    window.location.href = myorder + "&sign=fffffff0"
  } else {
    window.location.href = myorder + "&ident=fffffff0"
  }
}
//查看不同状态订单
function clickOrder(item) {
  if(!data.logUser){
    openlogin(null)
    return false
  }
  window.location.href = item.links
}
</script>
<style>
.icpinfo {
  margin-bottom: 72px;
}
</style>
<style lang="scss" scoped>
.my {
  // min-height: calc(100vh - 72px);
  background: #f6f6f6;
  position: relative;
  .my-content {
    .top {
      width: 100%;
      height: 126px;
      padding: 24px 0 0 0;
      background: linear-gradient(23deg, #5fc8ff -33%, #8372fe 99%);
      box-shadow: 0px 2px 11px 0px rgba(109, 177, 255, 0.29);
      display: flex;
      justify-content: space-between;
      .top-left {
        display: flex;
        margin-left: 22px;
        .left {
          img {
            width: 59px;
            height: 59px;
          }
        }
        .right {
          margin-left: 18px;
          .telphone,.login-btn {
            font-size: 21px;
            font-family: SFPro, SFPro-Regular;
            font-weight: 400;
            text-align: left;
            color: #ffffff;
            line-height: 25px;
            margin: 8px 0 4px 0;
          }
          .telphone-name {
            font-size: 14px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            text-align: left;
            color: #ffffff;
            line-height: 20px;
          }
          .login-btn{
            margin-top:15px;
            height:26px;
            border:1px solid #fff;
            border-radius: 4px;
            font-size: 14px;
            padding:0 10px;
          }
        }
      }
      .top-right {
        margin: 4px 22px 0 0;
        img {
          width: 30px;
          height: 30px;
        }
      }
    }
    .shop-message {
      width: 335px;
      height: 70px;
      margin: -35px auto 0 auto;
      padding: 0 10px;
      background: #ffffff;
      border-radius: 8px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      .shop-left {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        img {
          width: 28px;
          height: 28px;
          margin-bottom: 5px;
        }
        p {
          font-size: 10px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          text-align: left;
          color: #aaaaaa;
          line-height: 14px;
        }
      }
      .line {
        width: 1px;
        height: 36px;
        border-left: 1px solid #e8e8e8;
      }
      .center {
        display: flex;
        flex-direction: column;
        .shop-name {
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          text-align: left;
          color: #333333;
          line-height: 20px;
          margin-bottom: 5px;
          width: 170px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .shop-address {
          width: 180px;
          font-size: 12px;
          height:24px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          text-align: left;
          color: #999999;
          line-height: 17px;
          display: flex;
          align-items: center;
          img {
            width: 15px;
            height: 15px;
            margin-right: 5px;
          }
          span {
            display: inline-block;
            width: 170px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
      .change {
        width: 56px;
        height: 25px;
        background: linear-gradient(90deg, #93da80, #4fb5b1);
        border-radius: 13px;
        font-size: 12px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: #ffffff;
        line-height: 17px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 2px 0 0px;
        img {
          width: 56px;
          height: 25px;
        }
      }
    }
    .shop-sign{
      width:355px;
      margin:10px auto;
      border-radius: 8px;
    }
    .my-order {
      width: 355px;
      min-height: 130px;
      background: #ffffff;
      border-radius: 8px;
      margin: 10px auto 0 auto;
      .order-title {
        width: 100%;
        height: 38px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #f4f4f4;
        .title {
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          text-align: left;
          color: #666666;
          line-height: 20px;
          margin-left: 15px;
        }
        .all-order {
          font-size: 12px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          text-align: left;
          color: #999999;
          line-height: 17px;
          margin-right: 15px;
          .van-icon {
            vertical-align: middle;
            margin-left: 6px;
            color: #999;
          }
        }
      }
      .order-type {
        width: 100%;
        height: 90px;
        ul {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: space-around;
          li {
            .van-badge__wrapper {
              display: flex;
              flex-direction: column;
              align-items: center;
            }
            img {
              width: 25px;
              height: 23px;
              margin-bottom: 8px;
            }
            p {
              font-size: 12px;
              font-family: PingFangSC, PingFangSC-Regular;
              font-weight: 400;
              text-align: left;
              color: #666666;
              line-height: 17px;
            }
          }
        }
      }
      .other-type {
        width: 355px;
        background: #ffffff;
        border-radius: 8px;
        margin: 10px auto 0 auto;
        ul {
          width: 100%;
          li {
            height: 60px;
            padding: 0 13px;
            border-bottom: 1px solid #f4f4f4;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .type-left {
              display: flex;
              font-size: 14px;
              font-family: PingFangSC, PingFangSC-Regular;
              font-weight: 400;
              text-align: left;
              color: #666666;
              line-height: 20px;
              img {
                width: 24px;
                height: 24px;
                margin: -2px 18px 0 0;
              }
            }
            .type-right {
              display: flex;
              img {
                width: 7px;
                height: 15px;
                margin: 1px 0 0 8px;
              }
              p {
                font-size: 11px;
                font-family: PingFangSC, PingFangSC-Regular;
                font-weight: 400;
                text-align: left;
                color: #ed2668;
                line-height: 16px;
              }
            }
          }
          li:last-child {
            border-bottom: none;
          }
        }
      }
    }
  }
  .out {
    width: 272px;
    height: 108px;
    background: #ffffff;
    .point {
      width: 100%;
      height: 66px;
      border-bottom: 1px solid #f2f2f2;
      text-align: center;
      line-height: 66px;
      font-size: 15px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #000000;
    }
    .select {
      width: 100%;
      height: 40px;
      display: flex;
      p {
        width: 135px;
        height: 40px;
        text-align: center;
        line-height: 40px;
        font-size: 13px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: #5b8edc;
      }
      .cancle {
        border-right: 1px solid #f2f2f2;
      }
    }
  }
}
.wcsb_img{
  width:355px;
  height:90px;
  margin:9px auto;
  img{
    width:100%;
  }
}
.bg_gray{
  background: #f6f6f6!important;
}
.bg_white{
  background: #fff!important;
}
.top_radius{
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
.bottom_radius{
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
</style>
