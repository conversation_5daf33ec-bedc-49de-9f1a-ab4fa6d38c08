<template>
  <div class="broadband">
    <header-nav v-if="!UA.isWechat" :title="arrayData.title"></header-nav>
    <div class="nav">
      <Search
        v-model="arrayData.keywords"
        placeholder="输入预约单号，搜索我的预约订单"
        @search="search"
        @clear="refresh"
      />
    </div>
    <div class="couponMain">
      <Tabs v-model="arrayData.bdName" :before-change="beforeChange">
        <BroadbandTab
          :floor="arrayData.tabTitle"
          :items="arrayData.tabMain"
          tpl-id="roleUser"
          :refresh="refresh"
          :onload="getBroadbandList"
          :loading="arrayData.loading"
          :finished="arrayData.finished"
          :nodata-message="arrayData.nodataMessage"
        />
      </Tabs>
    </div>
    <Footer />
  </div>
</template>

<script setup>
import headerNav from '@/views/my/components/headerNav'
import loginUtils from '@/utils/login'
import getBroadband from '@/api/broadband'
import { Search, Toast, Tab, Tabs } from 'vant'
import 'vant/lib/index.css'
import Vue,{reactive,onMounted,getCurrentInstance} from 'vue'
import BroadbandTab from '@/components/broadband/broadbandTab.vue'
import UA from '@/utils/ua'
Vue.use(Tab)
const arrayData = reactive({
  title: '我的预约',
  activeNames: ['1'],
  info: '',
  tabTitle: ['全部', '待确认', '预约成功', '预约失败', '已取消'],
  tabMain: [],
  bdName: 0,
  keywords: '',
  data: {
    shopId: null,
    state: null,
    orderId: null,
    pageNo: 1,
    pageSize: 20,
  },
  loading: false,
  finished: false,
  nodataMessage: '',
  UA: null,
  shopId:null
})
function getBroadbandList(value) {
  if (arrayData.keywords != '') {
    arrayData.data.orderId = arrayData.keywords
  } else {
    arrayData.data.orderId = null
  }
  arrayData.data.shopId = arrayData.shopId
  arrayData.loading = true
  getBroadband.getUserBAOrderList(arrayData.data).then((res) => {
    if (res.code == 0) {
      if (arrayData.data.pageNo == 1) {
        arrayData.tabMain = []
      }
      arrayData.tabMain = arrayData.tabMain.concat(res.data.orderList)
      arrayData.loading = false
      if (arrayData.tabMain && arrayData.tabMain.length == 0) {
        if (arrayData.data.state == null && value != 'search') {
          arrayData.nodataMessage = '暂无预约列表信息'
        } else {
          arrayData.nodataMessage = '没有搜索到您要的信息'
        }
        arrayData.finished = true
        return false
      }
      if (res.data.totalPages > arrayData.data.pageNo) {
        arrayData.data.pageNo++
      } else {
        arrayData.nodataMessage = '没有更多了'
        arrayData.finished = true
      }
    } else {
      Toast(res.message)
    }
  })
}
function logined(res) {
  getBroadbandList()
}
function search() {
  arrayData.data.pageNo = 1
  arrayData.finished = false
  getBroadbandList('search')
}
function refresh() {
  arrayData.data.pageNo = 1
  arrayData.finished = false
  getBroadbandList()
}
function beforeChange(index) {
  arrayData.tabMain = []
  //TOC 待确认 MAS 预约成功 MAF 预约失败 HBC 已取消
  arrayData.data.state = null
  if (index == 1) {
    arrayData.data.state = 'TOC'
  } else if (index == 2) {
    arrayData.data.state = 'MAS'
  } else if (index == 3) {
    arrayData.data.state = 'MAF'
  } else if (index == 4) {
    arrayData.data.state = 'HBC'
  }
  arrayData.data.pageNo = 1
  arrayData.finished = false
  getBroadbandList()
  // 返回 Promise 来执行异步逻辑
  return new Promise((resolve) => {
    // 在 resolve 函数中返回 true 或 false
    resolve(index + 1)
  })
}
onMounted(()=>{
  const url = new URL(location.href)
  arrayData.shopId = url.searchParams.get("shopId")
  const getVueInstance = getCurrentInstance()
  const proxy = getVueInstance ? getVueInstance.proxy : null
  if (proxy) {
    if(!arrayData.shopId){
      proxy.$router.push({
        path: "/nearby/index.html"
      })
    }
    proxy.$store.commit("SET_SHOPID",arrayData.shopId)
  }
  loginUtils.login(true, true, logined, false, false, "", '0')
})
</script>
<style>
.header-nav{
  height: 52.5px !important;
}
.header-nav__title{
  font-size: 18.75px !important;
}
</style>
<style lang="scss" scoped>
ul {
  li {
    margin: 0;
    padding: 0;
    list-style: none;
  }
}
.broadband {
  min-height: calc(100vh - 73px);
  .nav {
    padding: 0;
    background: #ffffff;
    .van-search {
      width: 355px;
      height: 36px;
      background: #f7f7f7;
      border-radius: 21px;
      margin: 0 auto;
      .van-search__content {
        background: none;
        padding-left: 0;
      }
    }
  }
  .couponMain {
    .tabTilte {
      width: 100%;
      display: flex;
      li {
        flex: 1;
        line-height: 40px;
        font-size: 16px;
        text-align: center;
        border-bottom: #fff 1px solid;
        color: #333;
      }
      .active {
        border-bottom-color: #ff7300;
        color: #ff7300;
      }
    }
    .tabTitCon {
      width: 100%;
      background: #e2e1e1;
      display: flex;
      li {
        flex: 1;
        line-height: 40px;
        font-size: 12px;
        text-align: center;
        color: #333;
      }
      .conactive {
        color: rgb(255, 115, 0);
      }
    }
  }
  .tabContentCon {
    text-align: center;
    font-size: 13.5px;
    line-height: 30px;
    color: #999;
    img {
      display: inline-block;
      width: 75px;
      padding-top: 37.5px;
    }
  }
}
</style>
