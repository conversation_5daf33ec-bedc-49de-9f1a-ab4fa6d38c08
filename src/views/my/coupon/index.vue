<template>
  <div class="index">
    <header-nav :title="couponData.title"></header-nav>
    <div class="couponMain">
      <ul class="tabTilte">
        <li v-for="(tit,index) in couponData.tabTitle" :key="index" :class="{active:couponData.cur==index}" @click="couponData.cur=index">
          {{ tit }}
        </li>
      </ul>
      <div class="tabContent">
        <div v-for="(m,ind) in couponData.tabMain" v-show="couponData.cur==ind" :key="ind">
          <ul class="tabTitCon">
            <li v-for="(titc,indc) in couponData.tabConTitle" :key="indc" :class="{conactive:couponData.curcon==indc}" @click="couponData.curcon=indc">
              {{ couponData.titc }}
            </li>
          </ul>
        </div>
        <div class="tabContentCon">
          <img src="@/assets/my/couponNoIce.png" alt="">
          <p>暂无可用卡券</p>
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>

<script setup>
import headerNav from "@/views/my/components/headerNav"
import "vant/lib/index.css"
import {reactive} from "vue"
import loginUtils from "@/utils/login"

const couponData = reactive({
  title: "我的卡券",
  activeNames: ["1"],
  info: "",
  tabTitle: ['全部','店铺券','商品券','门店券'],
  tabMain: ['1','2','3','4'],
  tabConTitle: ['待使用','已使用','已过期'],
  tabConMain: ['1','2','3'],
  curcon:0,
  cur:0
})
loginUtils.login(false, false, logined, false, false, null, "0")
function logined(){

}
</script>
<style lang="scss" scoped>
ul{
  li{
    margin: 0;
    padding: 0;
    list-style: none;
  }
}
.index {
  min-height: calc(100vh - 73px);
  .couponMain {
    .tabTilte{
      width: 100%;
      display:flex;
      li{
        flex:1;
        line-height: 40px;
        font-size: 16px;
        text-align: center;
        border-bottom:#fff 1px solid;
        color: #333;
      }
      .active{
        border-bottom-color: #ff7300;
        color:#ff7300;
      }
    }
    .tabTitCon{
      width: 100%;
      background: #e2e1e1;
      display:flex;
      li{
        flex:1;
        line-height: 40px;
        font-size: 12px;
        text-align: center;
        color: #333;
      }
      .conactive{
        color:rgb(255, 115, 0);
      }
    }
  }
  .tabContentCon{
    text-align: center;
    font-size: 13.5px;
    line-height: 30px;
    color: #999;
    img{
      display: inline-block;
      width: 75px;
      padding-top: 37.5px;
    }
  }
}
</style>
