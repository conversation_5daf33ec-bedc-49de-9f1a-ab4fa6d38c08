<template>
  <div v-if="signData.signStatus && signData.signStatus.enableSignIn" class="sign-con">
    <div class="calendar-head">
      <div class="calenar-tip">
        本活动已连续签到<span>{{ signData.signStatus.continueSignInDays }}</span>天
      </div>
      <div class="calenar-tip-icon" @click="openTips"></div>
    </div>
    <div v-show="!signData.showAll" class="sign-ul sign-title">
      <div v-for="(day, index) in calendarObj.days" :key="index" class="sign-li">
        {{ day }}
      </div>
    </div>
    <div v-show="!signData.showAll" class="sign-ul calendar-day">
      <div v-for="item in signData.currentDay" :key="item" class="sign-li">
        <div class="sign-li-img" :class="[getType(item)]">
          <div class="img"></div>
        </div>
        <div class="sign-li-text" :class="{'today':isToday(item)}">
          {{ getDateText(item) }}
        </div>
      </div>
    </div>
    <Calendar
      v-show="signData.showAll"
      ref="myCalendar"
      :masks="calendarObj.masks"
      :min-date="monthObj.minDate"
      :max-date="monthObj.maxDate"
      trim-weeks
      locale="zh-cn"
      @transition-start="monthChange"
    >
      <template #day-content="{ day }">
        <div :class="[getType(day.id)]" class="sign-li-img calendar-img">
          <div class="img"></div>
        </div>
        <div class="sign-li-text calendar-text" :class="{'today':isToday(day.id)}">
          {{ getDateText(day.id) }}
        </div>
      </template>
    </Calendar>
    <div v-show="!signData.showAll" class="sign-btn-ul">
      <div v-if="signData.signStatus.signInShareSwitch && UA.isApp" class="sign-btn-li btn-share" @click="directShare">
        {{ signData.signStatus.shareData ? signData.signStatus.shareData.shareBtnName : '邀请好友一起领' }}
      </div>
      <div class="sign-btn-li btn-sign" :class="{'signed':signData.signStatus.todaySignInStatus}" @click="signIn">
        {{ signData.signStatus.todaySignInStatus ? '已签到' : '立即签到' }}
      </div>
    </div>
    <div class="sign-btn-toogle" :class="{'up':signData.showAll}" @click="open">
      <van-icon name="arrow" />
    </div>
    <van-popup
      v-model="showLogout"
      class="rule-popup"
      position="bottom"
    >
      <div class="rule-tips-title">
        活动规则
        <div class="close" @click="closeRuleTips">
          ×
        </div>
      </div>
      <div class="rule-tips-content">
        {{ signData.ruleText }}
      </div>
    </van-popup>
    <van-popup
      v-model="showSuccessTips"
      class="success-tips-con"
      position="center"
    >
      <div class="rule-tips-success-img"></div>
      <div class="rule-tips-success-title">
        签到成功
      </div>
      <div v-if="signData.signedObj.hasGift" class="rule-tips-success-gift"></div>
      <div class="rule-tips-success-p">
        <div v-if="signData.signedObj.hasGift" class="rule-tips-success-p1">
          恭喜您获得{{ signData.signedObj.giftContent }}
        </div>
        <div v-if="signData.signedObj.newFans" class="rule-tips-success-p2">
          <div class="select-icon"></div>
          <div>本活动为粉丝活动，已为您关注店铺成为粉丝</div>
        </div>
      </div>
      <div class="rule-tips-success-btn" @click="closeSuccessTips">
        我知道了
      </div>
    </van-popup>
    <div v-if="signData.showShare" class="wx-guide" @click="hideShare">
      <img src="~@/assets/index_img/wx_guide.png" alt="" width="200" />
    </div>
  </div>
</template>
<script setup>
import { ref,onBeforeMount, reactive , onMounted} from 'vue'
import { parseTime , getAllSearchParamsArray} from '@/utils/utils.js'
import { Popup } from 'vant'
import Calendar  from 'v-calendar/src/components/Calendar.vue'
import signApi from '@/api/sign.js'
import UA from "@/utils/ua.js"
import shareUtilApi from "@/utils/share"
const props = defineProps({
  // 组件的标题
  title: {
    type: String,
    default: "店铺标题"
  },
})
const signData = reactive({
  signDays: 0,
  showAll:false,
  currentDay:[],
  signStatus:{},
  shopId:null,
  signedObj:{},
  showShare:false,
  ruleText:'',
  myCalendar:""
})
const monthObj = reactive({
  minDate:null,
  maxDate:null
})
const calendarObj = reactive(
  {
    masks: {
      // weekdays: 'WWW',
      title:'YYYY年M月',
    },
    days:[ '一', '二', '三', '四', '五', '六','日'],
    signedDate: [],
    gifDate:[]
  }
)
const showLogout = ref(false)
const myCalendar = ref(null)
// 打开规则弹框
function openTips() {
  showLogout.value = true
}
// 关闭规则弹框
function closeRuleTips() {
  showLogout.value = false
}
//签到成功弹窗
const showSuccessTips = ref(false)
function openSuccessTips() {
  showSuccessTips.value = true
}
function closeSuccessTips() {
  showSuccessTips.value = false
}
// 分享签到
async function directShare(){
  let shopShortName = props.title
  if(!shopShortName){
    console.log('没有店铺名称，不能分享')
    return false
  }
  let fansShareData = {
    url: location.href,
    imgUrl: 'https://img0.shop.10086.cn/favicon.png__100.png',
    dcsId: 'yd_sharestore_share',
    desc: "欢迎光临",
    title: shopShortName,
    unifiedChannelId:'1111111111',
  }
  await shareUtilApi.share(fansShareData,callbackfn)
  if (UA.isWechatWork) {
    shareUtilApi.getContext(function(entry) {
      if (entry == "single_chat_tools" || entry == "group_chat_tools") {
        shareUtilApi.sendChatMessage()
      } else {
        signData.showShare = true
        shareUtilApi.qwShare(function() {
          signData.showShare = false
        })
      }
    })
    return false
  }
  //遮罩层显示
  if (UA.isWechat) {
    signData.showShare = true
  }
  // signData.showShare = true //调试
}
function hideShare(){
  signData.showShare = false
}
function callbackfn(itm){
  if(itm=='success'){
    console.log('shareSuccesses')
  }else{
    console.log('shareFailures')
  }
}
//去签到
function signIn(){
  if(signData.signStatus.todaySignInStatus){
    return false
  }
  signApi.signIn({shopId:signData.shopId}).then(res=>{
    if(!res.code){
      signData.signedObj = res.data
      // signData.signedObj =  {
      //   "status": 1,//签到状态 0 未签到 1 已签到
      //   "hasGift": 0,//签到是否获得礼品 0 无礼品 1 有礼品
      //   "giftContent": "每日签到a",//签到礼品类容
      //   "newFans": 1//是否成为新粉丝 0 无行为 1 新关注成为粉丝
      // }
      // signData.signStatus.todaySignInStatus  = signData.signedObj.status
      querySignStatus()
      querySignInGiftRecord()
    }
    openSuccessTips()
  })
}
/**查询签到状态 */
function querySignStatus(){
  signApi.querySignStatus({shopId:signData.shopId}).then(res=>{
    if(!res.code){
      signData.signStatus = res.data
      // signData.signStatus = {
      //   continueSignInDays: 0, //当前连续签到天数
      //   enableSignIn: true, //是否开启签到功能
      //   requireFansUser: false, //是否需要关注店铺
      //   signInShareSwitch: true, //签到分享开关
      //   todaySignInStatus: false //今天是否签到
      // }
    }
  })
}
/**查询签到记录 */
function querySignInGiftRecord(month=null){
  signApi.querySignInGiftRecord({
    shopId:signData.shopId,month
  }).then(res=>{
    if(!res.code){
      calendarObj.signedDate = res.data.signInDateList
      calendarObj.gifDate = res.data.giftDateList
    }
  })
}
function querySignInRule(){
  signApi.querySignInRule({shopId:signData.shopId}).then(res=>{
    if(!res.code){
      signData.ruleText = res.data
    }
  })
}
function getMonth({month,year}){
  if(month>9){
    return year +'-'+ month
  }else{
    return year +'-0'+ month
  }
}
/**月份切换 */
function monthChange(e){
  if(myCalendar.value){
    let monthObj = myCalendar.value.pages[0]
    let month = getMonth(monthObj)
    querySignInGiftRecord(month)
  }
}
onMounted(()=>{
  let urlQuery = new URLSearchParams(window.location.search)
  signData.shopId = urlQuery.get('shopId')
  querySignStatus()
  querySignInGiftRecord()
  querySignInRule()
})
function isToday(date){
  let currentDateText = parseTime(new Date(),'{y}-{m}-{d}')
  let dateText = parseTime(new Date(date),'{y}-{m}-{d}')
  if(currentDateText === dateText){
    return true
  }else{
    return false
  }
}
/**日期展示 */
function getDateText(date){
  if(isToday(date)){
    return '今天'
  }else{
    let curDate = new Date(date)
    return (curDate.getMonth()+1)+'.'+curDate.getDate()
  }
}
/**签到类型 */
function getType(date){
  let currentDate = new Date().getTime()
  let dateText = new Date(date).getTime()
  let type = "",istoday = isToday(date)
  if(calendarObj.signedDate.includes(date)){
    type = "signed"
  }else{
    if(currentDate > dateText){
      // '今天前'
      type = 'miss'
    }
    if((currentDate < dateText) || istoday){
      // '今天后'
      type = 'not-sign'
    }
    if(calendarObj.gifDate.includes(date)){
      type += " gift"
    }
  }
  if(istoday){
    // '今天'
    type += ' active'
  }
  return type
}
/**日历最前和最后的日期 */
function getMinMaxDate(){
  const month = new Date().getMonth()
  const minDate = new Date()
  minDate.setMonth(month-2)
  minDate.setDate(1)
  const maxDate = new Date()
  maxDate.setMonth(month+2)
  maxDate.setDate(0)
  monthObj.minDate = minDate
  monthObj.maxDate = maxDate
}
/**当前一周的日期 */
function getCurrentDay(){
  const currentDay = new Date().getDay()
  let days = []
  for(let i=1; i<8 ;i++){
    let j = i-currentDay
    let day = new Date().setDate(new Date().getDate()+j)
    days.push(parseTime(day,'{y}-{m}-{d}'))
  }
  signData.currentDay = days
}
async function open(){
  signData.showAll = signData.showAll ? false : true
  if(signData.showAll){
    const current = new Date()
    const month = current.getMonth()
    const year = current.getFullYear()
    await myCalendar.value.move({ month: month+1, year: year })
  }
}
onBeforeMount(()=>{
  getMinMaxDate()
  getCurrentDay()
  // getType('2025-01-24')
})
</script>
<style lang="scss" scoped>
.vc-container{
  border:none;
  :deep(.vc-header .vc-title){
    color: #262A30;
    font-size: 16px;
  }
  :deep(.vc-arrows-container){
    padding-left:0;
    padding-right:0;
    .is-disabled{
      opacity: 0.25;
    }
  }
  :deep(.vc-weeks .vc-weekday){
    font-size: 12px;
    color: #47494B;
  }
  :deep(.is-not-in-month *){
    opacity: 1!important;
  }
}
.calendar-head{
  width: 100%;
  display: flex;
  align-items: center;
  .calenar-tip{
    font-size: 15px;
    font-weight: bold;
    line-height: 15px;
    margin-left:-7px;
    span{
      font-size: 15px;
      margin: 0 5px;
      color:#F37125;
    }
  }
  .calenar-tip-icon{
    width:12px;
    height:12px;
    background:url(~@/assets/sign_img/tips.png) no-repeat center center;
    background-size: contain;
    margin-left: 5px;
  }
}
.sign-con{
  background: #ffffff;
  border-radius: 8px;
  padding:12px 20px 6px;
  .sign-ul{
    display: flex;
    justify-content: space-between;
    &.sign-title{
      margin-top:14px;
    }
    .sign-li{
      width:41px;
      text-align:center;
      font-size: 12px;
      color: #47494B;
      font-weight:bold;
    }
  }
  .calendar-day{
    margin-top:5px
  }
  .sign-calendar{
    .title{
      display: flex;
      justify-content: center;
    }
  }
  .sign-btn-ul{
    display: flex;
    justify-content: center;
    margin:11px auto 10px;
    .sign-btn-li{
      width:150px;
      height:34px;
      line-height: 34px;
      border-radius: 27px;
      text-align: center;
      box-sizing: border-box;
      font-size:15px;
      &.btn-share{
        background: #449dff0f;
        border: 1px solid #449DFF;
        color: #0E78FF;
        margin-right:17px;
      }
      &.btn-sign{
        background: #F9734C;
        color: #FFFFFF;
        &.signed{
          background: #FFA88F;
          cursor: pointer;
        }
      }
    }
  }
  .sign-btn-toogle{
    color:#449DFF;
    border-width: 2px;
    text-align: center;
    font-size: 20px;
    .van-icon{
      transform:rotate(90deg);
    }
    &.up .van-icon{
      transform:rotate(-90deg);
    }
  }
}
.sign-li-img{
  width:41px;
  height:52px;
  border-radius:8px;
  display:flex;
  justify-content: center;
  align-items: center;
  &.calendar-img{
    height:41px;
  }
  .img{
    width:30px;
    height:30px;
  }
}
.sign-li-text{
  font-size: 12px;
  text-align: center;
  color: #999999;
  margin-top:5px;
  height:16px;
  line-height:16px;
  font-weight: normal;
  &.calendar-text{
    color: #959BA3;
    margin-bottom:5px;
  }
  &.today{
    font-size: 14px;
    color: #0E78FF;
    font-weight: bold;
  }
}
.sign-con :deep(.vc-pane){
  width:329px;
}
.day-content{
  display:block;
  height:52px;
  border-radius:8px;
}
.sign-li-img{
  height:52px;
  border-radius:8px;
}
.signed,.not-sign,.gift{
  background: #E7F2FE;
}
.miss{
  background: #F5F5F5;
  .img{
    background: url("~@/assets/sign_img/sign_miss.png") no-repeat center center; //~@/assets/sign_img/tips.png
    background-size: contain;
  }
}
.signed .img{
  background: url("~@/assets/sign_img/sign_signed.png") no-repeat center center;
  background-size: contain;
}
.not-sign .img{
  background: url("~@/assets/sign_img/sign_nosign.png") no-repeat center center;
  background-size: contain;
}
.gift .img{
  background: url("~@/assets/sign_img/sign_gift.png") no-repeat center center;
  background-size: contain;
}
.active{
  background-image: linear-gradient(0deg, #8372FE 0%, #5FC8FF 100%);
}
.success-tips-con{
  border-radius: 20px;
  width :230px;
  padding-top:62px;
  overflow: visible;
  box-shadow: 0px 5px 0px 0px rgb(248,175,43);
  .rule-tips-success-img{
    width:125px;
    height:95px;
    background:url(~@/assets/sign_img/sign_success.png) no-repeat center center;
    background-size: contain;
    position: absolute;
    top:-31px;
    left:48px;
  }
  .rule-tips-success-title{
    font-size: 22px;
    color: #262A30;
    font-weight: bold;
    text-align: center;
    margin-bottom: 18px;
    &.high{
      margin-bottom: 27px;
    }
  }
  .rule-tips-success-gift{
    width:55px;
    height:55px;
    background: url(~@/assets/sign_img/sign_gift.png) no-repeat center center;
    background-size: contain;
    margin:0 auto 13px;
  }
  .rule-tips-success-p{
    text-align: center;
    margin-bottom: 17px;
    padding:0 27px;
  }
  .rule-tips-success-p1{
    font-size: 14px;
    color: #262A30;
    line-height: 20px;
    margin-bottom: 2px;
  }
  .rule-tips-success-p2{
    font-size: 12px;
    color: #959BA3;
    line-height: 17px;
    text-align: left;
    display: flex;
    .select-icon{
      width:12px;
      height:12px;
      background: url(~@/assets/sign_img/select_icon.png) no-repeat center center;
      background-size: contain;
      margin:2px 5px 0 0;
    }
    div:last-child{
      flex:1
    }
  }
  .rule-tips-success-btn{
    width: 150px;
    height: 34px;
    line-height: 34px;
    background: #F9734C;
    border-radius: 27px;
    font-size: 15px;
    color: #FFFFFF;
    font-weight:bold;
    margin:16px auto 30px;
    text-align: center;
  }
}
.rule-popup{
  border-radius: 9px;
  height :400px;
}
.rule-tips-title{
  font-size: 16px;
  height: 50px;
  line-height: 50px;
  font-weight: bold;
  position: relative;
  text-align: center;
  .close{
    position: absolute;
    right:20px;
    top:0;
    font-size: 30px;
    font-weight: normal;
    color: #ccc;
  }
}
.rule-tips-content{
  font-size: 12px;
  padding:0 20px 20px;
  line-height: 20px;
  height:300px;
  overflow: auto;
}
.wx-guide {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 3000;
  background: rgba(0, 0, 0, 0.7);

  img {
    width: 200px;
    position: absolute;
    top: 100px;
    left: 88px;
    z-index: 3001;
  }
}
</style>
