<template>
  <!--公共头部-->
  <div
    v-if="headerisApp"
    class="header-nav"
    :style="{ 'background':props.bgColor,'height':getVm(props.height)}"
  >
    <div
      v-show="props.showGoBack"
      class="header-nav__icon"
      :style="{
        'font-size':getVm(props.iconSize),
        'padding':getPadding(props.height,props.iconSize)}"
      @click="goBack"
    >
      <Icon name="arrow-left" />
    </div>
    <div class="header-nav__title" :style="{ 'font-size':getVm(props.fontSize),'line-height':getVm(props.height)}">
      {{ props.title }}
    </div>
    <div class="header-nav-right__icon">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import UA from "@/utils/ua"
import { Icon } from "vant"
import {defineProps} from "vue"
const props = defineProps({
  title: {
    type: String,
    default: "页面标题"
  },
  showGoBack: {
    type: Boolean,
    default: true
  },
  // 标题栏高度
  height:{
    type:Number,
    default:55
  },
  // 标题字号
  fontSize:{
    type:Number,
    default:22
  },
  iconSize:{
    type:Number,
    default:22
  },
  bgColor:{
    type:String,
    default:'#fff'
  }
})
function getVm(px){
  let num = px*100/375
  return num+'vw'
}
function getPadding(height,iconSize){
  let num = (height-iconSize)/2
  return getVm(num)
}
const headerisApp = !UA.isWechat&&!UA.isWechatWork&&!UA.isApp&&!UA.isIosQQ&&!UA.isAndroidQQ
function goBack() {
  if (window.history.length <= 1) {
    window.location.href ="/my/index.html"
    return false
  } else {
    window.history.go(-1)
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/minix.scss";
.header-nav {
  position: relative;
  width: 100%;
  height: 58px;
  background: #fff;
  &__icon {
    position: absolute;
    padding: 15px 20px 0px;
    height: 50px;
    line-height: 40px;
    font-size: 22px;
    :deep(.van-icon) {
      display: block;
    }
  }
  &__title {
    width: 100%;
    font-size: 22px;
    line-height: 50px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: center;
    color: #000000;
  }
  &__right {
    position: absolute;
    padding: 15px 20px 0px;
    height: 20px;
    line-height: 16px;
    font-size: 22px;
    top: 0;
    right: 0px;
  }
}
</style>
