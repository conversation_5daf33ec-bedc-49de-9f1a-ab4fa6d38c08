<template>
  <div>
    <!-- 带有登录功能和iconlist的底部导航 -->
    <Footers v-if="myuser || props.forceShow" :list="iconList" :user="myuser" :isactive="props.isactive" />
  </div>
</template>
<script setup>
import Footers from '@/components/myfooter'
import loginUtils from "@/utils/login"
import {reactive,ref,onMounted,defineProps,getCurrentInstance,computed,watch} from "vue"

const iconList = reactive({
  shopindex:{
    inactive: require("@/assets/my/other.png"),
    active: require("@/assets/my/others-active.png"),
    title: "店铺首页",
    key:'shopindex',
    links: '',
    isactive:false,
    showLink:true
  },
  onlineshop: {
    inactive: require('@/assets/my/onlineShop.png'),
    active: require('@/assets/my/onlineShop-active.png'),
    title: '在线看店',
    key: 'onlineshop',
    links: '',
    isactive: false,
    showLink: false,
  },
  service:{
    inactive: require("@/assets/my/customerService.png"),
    active: require("@/assets/my/customerService-active.png"),
    title: "客服",
    key:'service',
    links: '',
    isactive:false,
    showLink:false
  },
  manage:{
    inactive: require("@/assets/my/management.png"),
    active: require("@/assets/my/management-active.png"),
    title: "店铺管理",
    key:'manage',
    links: '',
    isactive:false,
    showLink:false
  },
  live:{
    inactive: require("@/assets/my/live.png"),
    active: require("@/assets/my/live-active.png"),
    title: "直播",
    key:'live',
    links: '',
    isactive:false,
    showLink:false
  },
  my:{
    inactive: require("@/assets/my/my.png"),
    active: require("@/assets/my/my-active.png"),
    title: "我的",
    key:'my',
    links: '',
    isactive:false,
    showLink:true
  }
})

const props = defineProps({
  userInfo:{
    type:Object,
    default:()=>{
      return null
    }
  },
  isactive:{
    type:String,
    default:()=>{
      return null
    }
  },
  forceShow:{ //我的页面不强制登录，其他页面强制登录
    type:Boolean,
    default:()=>{
      return false
    }
  }
})

watch(()=>props.userInfo,(val)=>{
  myuser.value = props.userInfo
  if(myuser.value.shopId && shopId.value == myuser.value.shopId){
    iconList.manage.showLink = true
  }else{
    iconList.manage.showLink = false
  }
})
if(props.isactive){
  iconList[props.isactive].isactive = true
}
const myuser = ref(null)
const shopId = ref(null)
onMounted(() => {
  const url = new URL(location.href)
  shopId.value = url.searchParams.get('shopId')
  const getVueInstance = getCurrentInstance()
  if(getVueInstance && getVueInstance.proxy){
    getVueInstance.proxy.$store.commit("SET_SHOPID",shopId.value)
  }
  document.body.scrollTop = 0
})
</script>

<style>
.icpinfo{
  margin-bottom: 68px ;
}
</style>
