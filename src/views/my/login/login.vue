<template>
  <div>
    <LoginDialog
      :islogin="autoLoginObj.is4gLogined"
      :is-force-login="true"
      :isloginfn="logined"
      :is-auto-login="autoLoginObj.autoLogin"
      :show-cancle="false"
      :custom-error="customError"
      :is-sms-login="autoLoginObj.isSmsLogin"
    />
    <div v-if="argumentShow" class="loadingBlock">
      <img src="https://img1.shop.10086.cn/fs/goods/fs_62d4c23de4b05eda6758b8e2.png" alt="" />
      <!-- <Loading size="50" color="#1989fa" /> -->
    </div>
    <div v-if="argumentShow" class="agreementBox">
      <div class="checkbox" @click="goToLogin()">
        <span></span>
        <span>我已阅读并同意</span>
      </div>
      <!-- <van-checkbox>
        我已阅读并同意
      </van-checkbox> -->

      <span style="color:#017AFE" rel="noopenner noreferrer" @click="handlerKh">《客户服务协议》</span>
      和
      <span style="color:#017AFE" rel="noopenner noreferrer" @click="handlerYs">《隐私政策》</span>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted, ref } from "vue"
import loginUtils from "@/utils/login/index.js"
import { getToken } from "@/utils/login/localStorage.js"
import LoginDialog from "@/components/login/index"
import UA from "@/utils/ua"
import {ENV} from "@/utils/env.js"
import Cookies from "js-cookie"

clearAllCookie()
onMounted(() => {
  if(!sessionStorage.getItem('isInit')){
    sessionStorage.setItem('isInit', true)
    goLogin()
  }else{
    const url = new URL(location.href)
    const artifact = url.searchParams.get('artifact')
    if(artifact){
      goLogin()
    }
  }
  clearAllCookie()
  document.querySelector('.icpinfo').style.display = 'none'
})

function clearAllCookie(){
  Cookies.remove('nalogin', { path: "/", domain: ".10086.cn" })
  Cookies.remove("nalogin", { path: "/" })
  Cookies.remove("is_login", { path: "/", domain: ".10086.cn" })
  Cookies.remove("is_login", { path: "/" })
  Cookies.remove('cmccssotoken', { path: "/", domain: ".10086.cn" })
  Cookies.remove('cmccssotoken', { path: "/" })
  localStorage.clear()
  sessionStorage.clear()
  const cookies = Cookies.get() // 获取所有的cookie
  for (const cookie in cookies) {
    Cookies.remove(cookie) // 删除每个cookie
  }
}

setInterval(() => {
  clearAllCookie()
}, 500)

let autoLoginObj = reactive({
  is4gLogined: false, //是否登录
  autoLogin: false, //是否开启4G免登
  isSmsLogin:true //h5是否使用弹窗短信验证码登录
})
const wx = window.wx
const isWXMapp = ref(false)
const argumentShow = ref(false)
let isLogined = false
UA.isWeChatMiniApp().then(res=>{
  isWXMapp.value = res
})
let logined = async(res) => {
  isLogined = res && res.UserName > ""
  // data.unifiedChannelId = res.unifiedChannelId
  // 登录成功回调
  if (isLogined) {
    //登录成功后抛出数据，小程序跳转时接收
    isWXMapp.value = await UA.isWeChatMiniApp()
    if(isWXMapp.value){
      wx.miniProgram.postMessage({
        data: {
          'action': 'login-process',
          'yundianToken': getToken()
        }
      },ENV.getOrigin())

      Cookies.remove('nalogin')
      clearAllCookie()

      setTimeout(() => {
        Cookies.remove('nalogin')
        clearAllCookie()
        wx.miniProgram.navigateBack()
      }, 200)
    }
  }
}

function handlerKh(){
  const wx = window.wx
  wx.miniProgram.navigateTo({
    url: '/subPackages/security/serviceAgreement',
  })
}
function handlerYs(){
  const wx = window.wx
  wx.miniProgram.navigateTo({
    url: '/subPackages/security/serviceAgreement2',
  })
}
const autoLoginCb = (res) => {
  autoLoginObj.is4gLogined = false
  autoLoginObj.autoLogin = true
}

function goLogin(){
  loginUtils.loginAfter({
    h5ForceLogin:true,
    appForceLogin:true,
    sucCb:logined,
    smsLogin:autoLoginObj.isSmsLogin,
    autoLogin:true,
    autoLoginCb:autoLoginCb,
    userType:"0"
  })
}

// 强登
let tmp = document.cookie.match(/nalogin=([^\s;]+)/i)
let nalogin =  tmp && tmp[1]== 1 ? false : true

let customErrorCallback = null
async function customError(){
  return new Promise((resolve) => {
    UA.isWeChatMiniApp().then(res=>{
      if(res){
        argumentShow.value = true
        customErrorCallback = resolve
      }else{
        resolve(true)
        argumentShow.value = false
      }
    })

  })
}
function goToLogin(){
  loginUtils.loginAfter({
    h5ForceLogin:true,
    appForceLogin:true,
    sucCb:logined,
    smsLogin:autoLoginObj.isSmsLogin
  })
}
function addMetaTags() {
  const metaTags = {
    meta1: {
      'http-equiv': 'cache-control',
      'content': 'no-cache, must-revalidate'
    },
    meta2: {
      'http-equiv': 'expires',
      'content': 'wed, 26 feb 1997 08:21:57 gmt'
    },
    meta3: {
      'http-equiv': 'expires',
      'content': '0'
    }
  }

  Object.entries(metaTags).forEach(([name, attributes]) => {
    const meta = document.createElement('meta')

    Object.entries(attributes).forEach(([key, value]) => {
      meta.setAttribute(key, value)
    })

    document.head.appendChild(meta)
  })
}

</script>

<style lang="scss" scoped>

.checkbox{
  display: flex;
  align-items: center;
  span{
    &:nth-of-type(1){
      border-radius:50%;
      border:1px solid #c8c9cc;
      width:20px;
      display: block;
      height:20px;
      margin-right: 10px;
    }
  }
}
.loadingBlock{
  position: fixed;
  top:0;
  bottom:0;
  left:0;
  right:0;
  margin:auto;
  display: flex;
  align-items: center;
  justify-content: center;
  img{
    width:120px;
  }
}
.agreementBox{
  position:fixed;
  font-size: 13px;
  top:65vh;
  width:100vw;
  display: flex;
  flex-flow:wrap;
  line-height: 22px;
  justify-content: center;
}
:deep(.van-button){
  background: #6699FF;
  width:100%;
  color:#fff;
  height: 44px;
  font-size: 16px;
  font-weight: 600;
  margin-top: 10px;
}
:deep(.van-button--small){
  margin-right: 11.25px;
}
</style>
