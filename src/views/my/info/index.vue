<template>
  <div class="index">
    <header-nav v-if="!isWechat" :title="title"></header-nav>
    <div class="Vip_info">
      <p>编辑资料</p>
    </div>
    <div class="">
      <van-form>
        <van-field
          v-model="infoData.userN.UserName"
          name="账号"
          label="账号"
          placeholder="账号"
          :readonly="infoData.status ? false : 'readonly'"
        />
        <van-field
          v-model="formData.nickname"
          name="昵称"
          label="昵称"
          maxlength="12"
          placeholder="昵称"
        />
        <van-field
          readonly
          clickable
          name="picker"
          :value="formData.gendername"
          label="性别"
          placeholder="选择性别"
          @click="infoData.showPicker = true"
        />
        <van-field
          :value="formData.birthday"
          readonly
          clickable
          label="生日"
          placeholder="选择你的生日"
          @click="infoData.chooseDate = true"
        />
        <van-popup v-model="infoData.showPicker" position="bottom">
          <van-picker
            title="性别"
            show-toolbar
            :columns="columns"
            @confirm="onConfirm"
            @cancel="infoData.showPicker = false"
          />
        </van-popup>
        <van-popup v-model="infoData.chooseDate" label="选择日期" position="bottom" :overlay="true">
          <van-datetime-picker
            v-model="infoData.currentDate"
            type="date"
            title="选择年月日"
            :min-date="infoData.minDate"
            :max-date="infoData.maxDate"
            @confirm="onConfirmDate"
            @cancel="infoData.chooseDate = false"
          />
        </van-popup>
      </van-form>
    </div>
    <div class="saveBottom" @click="saveInfo">
      <span>保存</span>
    </div>
  </div>
</template>

<script setup>
import headerNav from '@/views/my/components/headerNav'
import { Form, Field, Popup, Icon, Picker, DatetimePicker } from "vant"
import "vant/lib/index.css"
import Vue,{reactive,onMounted,getCurrentInstance} from "vue"
import loginUtils from "@/utils/login"
import shopUser from "@/api/user"
import {mapGetters} from "vuex"
import { Toast } from 'vant'
import UA from '@/utils/ua'

Vue.use(Form).use(Field).use(Popup).use(Icon).use(Picker).use(DatetimePicker)
const title = '我的资料'
const isWechat=UA.isWechat
const formData = reactive({
  nickname: '',
  gendername: '保密',
  gender: 0,
  birthday: ''
})
const columns = ['男', '女', '保密']
const infoData = reactive({
  showPicker: false,
  chooseDate: false,
  status:null,
  userN:'',
  currentDate: new Date(1990,0,1),
  maxDate: new Date(),
  minDate: new Date(1960,0, 1),
  info:''
})
function logined(res) {
  infoData.userN = res
  formData.nickname='yundian'+res.UserName.substr(7)
  getShopUserInfo()
}
function getShopUserInfo() {
  //查询我的资料
  shopUser.getShopUserInfo({
    tonken: infoData.userN.token
  }).then( res => {
    if(res.code == 0 && res.data ) {
      if(res.data.nickname&&res.data.nickname!=''){
        formData.nickname = res.data.nickname
      }
      if(res.data.gender&&res.data.gender!=null){
        formData.gender = res.data.gender
        if(res.data.gender==1){
          formData.gendername = '男'
        }else if(res.data.gender==2){
          formData.gendername = '女'
        }else{
          formData.gendername = '保密'
        }
      }
      if(res.data.birthday&&res.data.birthday!=null){
        formData.birthday = formatDate(res.data.birthday)
      }
    }
  })
}
function formatDate(value) {
  let date = new Date(value)
  let y = date.getFullYear()
  let MM = date.getMonth() + 1
  MM = MM < 10 ? "0" + MM : MM
  let d = date.getDate()
  d = d < 10 ? "0" + d : d
  return y + "-" + MM + "-" + d
}
function saveInfo() {
  //保存我的资料
  const {birthday,gender,nickname} = formData
  let str = proxy.$encryption(JSON.stringify({birthday,gender,nickname})) 
  shopUser.getShopUserCreate({str}).then( res => {
    if(res.code == 0) {
      Toast("保存成功")
    }else{
      Toast(res.message)
    }
  })
}
function onConfirm(value,index) {
  formData.gendername = value
  if(index+1!=3){
    formData.gender = index+1
  }else{
    formData.gender = 0
  }
  infoData.showPicker = false
}
function onConfirmDate(date) {
  let dategm,dategd
  if(((date.getMonth() + 1)+'').length==1){
    dategm='0'+(date.getMonth() + 1)
  }else{
    dategm=date.getMonth() + 1
  }
  if((date.getDate()+'').length==1){
    dategd='0'+date.getDate()
  }else{
    dategd=date.getDate()
  }
  formData.birthday = `${date.getFullYear()}-${dategm}-${dategd}`
  infoData.chooseDate = false
}
let proxy = null
onMounted(()=>{
  loginUtils.login(true,true,logined)
  let getCurrentVue = getCurrentInstance()
  proxy = getCurrentVue ? getCurrentVue.proxy : null
})

</script>
<style lang="scss" scoped>
  .index {
    height: calc(100vh - 73px);
    .Vip_info {
      height: 44px;
      line-height: 44px;
      background: #f6f6f6;
      p {
        font-size: 15px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        text-align: left;
        color: #666666;
        padding-left: 20px;
      }
    }
    .saveBottom {
      height: 46px;
      text-align: center;
      padding-top: 20px;
      span {
        display: block;
        width: 80%;
        line-height: 44px;
        color: #fff;
        font-size: 16px;
        background: linear-gradient(to right, #7BC6FA , #8073F6);
        border-radius: 22px;
        margin: 0 auto;
      }
    }
  }
  .van-field__control {
    text-align: right;
  }
</style>
