<template>
  <div class="index channel">
    <!-- 企业微信弹框 -->
    <div v-if="showLocationBtn" class="get-location" @click="getuserLocation">
      获取位置信息
    </div>
    <div v-if="data.loading" class="loading-area">
      <Loading />
    </div>
    <LoginDialog
      :islogin="autoLoginObj.is4gLogined"
      :is-force-login="true"
      :isloginfn="logined"
      :is-auto-login="autoLoginObj.autoLogin"
      :show-cancle="false"
    />
  </div>
</template>

<script setup>
import { Loading , Dialog } from "vant"
import { reactive, onMounted,nextTick,ref } from "vue"
import LoginDialog from "@/components/login/index"
import UA from "@/utils/ua"
import geoApi from "@/api/geo"
import lbsApi from "@/api/lbs"
import loginUtils from "@/utils/login"
import leadeonLoader from "@/utils/leadeonloader"
import { getAllSearchParamsArray } from "@/utils/utils"

const context = window

let data = reactive({
  userLocation: {},
  loading: false,
})
let parame = {}
// 商品渠道引流查找按：1归属地引流，2距离引流
const PUBINCOMESTATUS = {
  BELONGPLACE:1,
  DISTANCE:2,
}
let incomeStatus = PUBINCOMESTATUS.DISTANCE
onMounted(async()=> {
  parame = getAllSearchParamsArray(location.href)
  // 链接中pubIncomeStatus字段，1归属地引流，2距离引流
  let {pubIncomeStatus} = parame
  pubIncomeStatus= pubIncomeStatus ? pubIncomeStatus.replace(/\s/g,'+') : ""
  const res = await lbsApi.decrypt({pubIncomeStatus})
  if(res && res.data && res.data.incomeStatus){
    incomeStatus = res.data.incomeStatus
  }
  if(incomeStatus==PUBINCOMESTATUS.BELONGPLACE){
    loginUtils.login(true,true,logined,false,true,autoLoginCb,"0")
  }else{
    setTimeout(getuserLocation, 100)
  }

})
let autoLoginObj = reactive({
  is4gLogined: null, //是否登录
  autoLogin: false, //是否开启4G免登
})
const autoLoginCb = (res) => {
  autoLoginObj.is4gLogined = false
  autoLoginObj.autoLogin = true
}
function logined(res){
  parame.userProvId = res.userProvince
  parame.userCity = res.userCity
  if(parame.userProvId==parame.provId){
    //店铺对应省份的移动用户
    getuserLocation()
  }else{
    getGoods()
  }
}
const showLocationBtn = ref(false)
function getuserLocation() {
  data.loading = true
  geoApi.getPosition().then(res => {
    // res = {//调试页面专用，解除注释即可看到页面
    //   code:1,
    //   data:{
    //     locProvince:"北京",
    //     latitude: 39.822288,
    //     longitude: 116.294807
    //   }
    // }
    if (res.data && res.data.latitude) {
      getGoods(res.data.latitude,res.data.longitude)

    } else {
      getGoods()
    }
    nextTick(() => {
      data.loading = false
    })
  })
}

function dialogFn(confirmtext,callbackfn){
  Dialog.confirm({
    confirmButtonColor: "#06acea",
    confirmButtonText:'确定',
    showCancelButton:false,
    message: confirmtext,
  }).then(res=>{
    callbackfn &&  callbackfn()
  })
}

function getGoods(latitude,longitude){
  let sendData = {...parame,latitude,longitude}
  sendData.pubIncomeStatus = incomeStatus
  let {checkId}= parame
  checkId = checkId ? checkId.replace(/\s/g, "+") : ""
  Promise.all([
    lbsApi.incomeflowverify({checkId}),
    lbsApi.getNearShopByGoodsId(sendData),
  ]).then(([verifyRes, res]) => {
    if(verifyRes.code){
      //code值 0：成功
      // 80002：链接解析失败
      // 80003：链接已失效
      dialogFn('链接已失效，请关闭当前页，',()=>{
        close()
      })
      return false
    }
    if(res.code || !res.data){
      if(res.code==60001) {//商品已下架，请查看其它商品
        dialogFn('商品已下架，请关闭当前页，查看其他商品',()=>{
          close()
        })
      }
      if(res.code==60002) {//查询不到附近店铺信息
        dialogFn('附近店铺未上架该商品，请关闭当前页，查看其他商品',()=>{
          close()
        })
      }
    }else{
      let goodsLink = res.data.goodsLink
      window.location.href = goodsLink
      data.loading = true
      setTimeout(()=>{
        data.loading = false
      },2000)
    }
  })
}

function close(){
  if (UA.isWechat) {
    window.wx.closeWindow()
    return false
  }
  if(UA.isApp){//中国移动app
    leadeonLoader().then((leadeon)=>{
      leadeon.closeCurrentWebView({
        debug: false,
        success: function(res) {},
        error: function(res) {}
      })
    })
    return false
  }
  if(UA.isYDBG){//移动办公
    const context = window
    if(UA.isIos) {
      context.webkit.messageHandlers.closePage.postMessage({})
    }else{
      window.WebContainer.closePage()
    }
    return false
  }
}



</script>
<style lang="scss" scoped>
.channel{
  min-height: 100vh;
}
.get-location{
  position: absolute;
  top:40vh;
  text-align: center;
  left:50%;
  transform: translate(-50%,0);
  width:100px;
  border:1px solid#06acea;
  color:#06acea;
  height:20px;
  line-height: 20px;
  border-radius: 20px;
}
.loading-area {
  width: 40%;
  text-align: center;
  margin: 40px auto;
}
</style>
<style lang="scss">
.icpinfo{
  display: none;
}
</style>
