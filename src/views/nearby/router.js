export default [
  {
    path: '/nearby/(index.html)?',
    name: 'nearby',
    component: resolve => require(['./index.vue'], resolve),
    meta: {
      title: '中国移动附近店铺',
      metas:[
        {
          content:'一键搜索帮您快速找店！',
          name:'description',
          itemprop:'description'
        }
      ]
    }
  },{
    path: '/appnearby/(index.html)?',
    name: 'appnearby',
    component: resolve => require(['./appnearbyshop/index.vue'], resolve),
    meta: {
      title: '附近店铺'
    }
  },{
    path: '/appnearby/hotproduct.html',
    name: 'hotproduct',
    component: resolve => require(['./appnearbyshop/hotproduct.vue'], resolve),
    meta: {
      title: '热销商品',
      login:true,
      role:[2]
    }
  },
  {
    path: '/nearby/channeldrainage.html',
    name: 'channeldrainage',
    component: resolve => require(['./channeldrainage/index.vue'], resolve),
    meta: {
      title: '云店'
    }
  }
]
