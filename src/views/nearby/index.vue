<template>
  <div class="index nearBy">
    <!-- 企业微信弹框 -->
    <van-popup
      v-model="popup.show"
      round
    >
      <div class="wechpopContent">
        <div class="bgimg">
          <img mode="widthFix" src="https://img1.shop.10086.cn/fs/goods/fs_62d03471e4b0355791a71fa5.png" alt="">
        </div>
        <div class="text">
          <div class="dzimg">
            <img :src="popup.wxCode" alt="">
            <img :src="popup.avatar" class="positiondzimg" alt="">
          </div>
          <div class="left">
            <div class="tit">
              添加方式:
            </div>
            <div class="com">
              <p>1、请使用微信扫码添加店铺客服</p>
              <p>2、截图保存图片,使用微信扫一扫添加店长好友</p>
            </div>
          </div>

        </div>
      </div>
    </van-popup>

    <img
      style="position:absolute;top:-100%;"
      src="@/assets/nearby/share_img.png"
      alt=""
    />
    <div class="nav">
      <switch-location :user-location="data.userLocation" @confirm="switchConfirm" />
      <van-search
        v-model="data.keywords"
        placeholder="请输入查询的店铺或相关标签"
        :disabled="!data.userLocation || (data.userLocation.latitude&&!data.userChooseLocation.provinceId) || !data.userLocation.latitude"
        @search="getNearShop"
        @clear="getNearShop"
      />
    </div>
    <van-swipe
      v-if="data.from != 'home' && data.cmBanner && data.cmBanner.length"
      class="banner"
      :autoplay="3000"
      indicator-color="white"
    >
      <van-swipe-item v-for="(item, key) in data.cmBanner" :key="key">
        <a :href="item.link_skuid"><img :src="item.imgsrc" /></a>
      </van-swipe-item>
    </van-swipe>
    <div v-if="data.from != 'home' && data.cmIcon && data.cmIcon.length" class="cm-icon">
      <div v-for="(item, key) in data.cmIcon" :key="key" class="item">
        <a v-if="item.type" @click="quhao(item.shopId)">
          <img :src="item.imgsrc" />
          {{ item.title }}
        </a>
        <a v-else :href="item.link_skuid">
          <img :src="item.imgsrc" />
          {{ item.title }}
        </a>
      </div>
    </div>
    <div class="cm-line"></div>
    <div v-if="data.loading" class="loading-area">
      <van-loading />
    </div>
    <template v-if="data.userLocation || data.userChooseLocation.provinceId">
      <!-- <template v-if="true"> -->
      <ul v-if="data.shopList && data.shopList.length" class="shop-list">
        <li
          v-for="(item, key) in data.shopList"
          :key="key"
          :class="[!item.imgUrl ? 'noimg' : '', item.expansion ? 'heightYihang' : '']"
          class="nearbyitem"
        >
          <aside class="con">
            <div class="itemLeft">
              <div class="title">
                <div>
                  {{ item.shopShortName }}
                </div>
                <dt>
                  <a :href="'tel:' + item.kefuPhone" class="telNumber">
                    <img
                      src="https://touch.10086.cn/yundian/static/img/jajsdj.b3434e62.png"
                      alt=""
                    /></a>
                </dt>
                <dt>
                  <a v-if="item.qrCode" href="javascript:;" class="telNumber" @click="handleQrcode(item)">
                    <img
                      src="https://img1.shop.10086.cn/fs/goods/fs_635642fae4b0fdd36b797c57.png"
                      alt=""
                    /></a>
                </dt>
              </div>
              <div v-if="item.shopOpenStatus != 1" class="businesscontent">
                <!-- 5营业中 6休息中 1不显示 2关厅 -->
                <img v-if="item.shopOpenStatus===5" src="~@/assets/index_img/shop_open.png" alt="">
                <img v-if="item.shopOpenStatus===6" src="~@/assets/index_img/shop_close.png" alt="">
                <img v-if="item.shopOpenStatus===2" src="~@/assets/index_img/shop_close_grey.png" alt="">
                <span>
                  {{ item.shopOpenStatus===5 ? "营业中" : item.shopOpenStatus===6 ? "休息中" : item.shopOpenStatus===2? "关厅" :"" }}
                </span>
                <span v-if="item.shopOpenStatus === 6 && getCurrentHour(item.businessHours)!=='店铺休息'">营业时间</span>
                <span v-if="item.shopOpenStatus===5 || item.shopOpenStatus===6">
                  {{ getCurrentHour(item.businessHours,item.shopOpenStatus) }}
                </span>
              </div>
              <p class="address">
                <img
                  src="@/assets/nearby/locationicon.png"
                  class="icon icon_adress"
                />
                <span v-if="item.distance" class="distance">
                  {{
                    item.distance > 1000
                      ? (item.distance / 1000).toFixed(2) + "KM"
                      : item.distance.toFixed() + "米"
                  }}
                  {{
                    item.address && item.address.replace(/[\n|\s]+/g, "") ? "|" : ""
                  }}
                </span>
                <span :class="['addressName','paddingRigth']" :style="{'white-space':item.isShowExpansionButton?'nowrap':'inherit'}">{{ item.address }}</span>
              </p>

            </div>

            <div
              class="itemRight"
            >
              <img v-if="item.nearbyShopPicture" :src="myGetImg(item.nearbyShopPicture)" />
              <img v-else src="~@/assets/nearby/default_img.png" alt="" />
            </div>
          </aside>

          <!-- 标签列表 -->
          <div v-if="item.tagList && item.tagList.length" :class="['tagList', !item.expansion ? 'heightYihang' : '']">
            <dl class="dl">
              <div class="one">
                <dt v-for="(tag,index) in item.tagList.slice(0,3)" :key="index">
                  {{ tag }}
                </dt>
              </div>
              <div class="other">
                <dt v-for="(tag,index) in item.tagList.slice(3)" :key="index">
                  {{ tag }}
                </dt>
              </div>
            </dl>

          </div>

          <div v-if="item.isShowExpansionButton" class="zhankai">
            <span v-if="!item.expansion" @click="item.expansion = true">
              <van-icon name="arrow-down" size="15" color="#333333" />
              <i>展开</i>
            </span>
            <span v-if="item.expansion" @click="item.expansion = false">
              <van-icon name="arrow-up" size="15" color="#333333" />
              <i>收起</i>
            </span>
          </div>

          <div class="btnGroup">
            <div v-if="item.show == 1" class="btnItem" @click="quhao(item.shopId)">
              <img src="~@/assets/nearby/quhao.png" alt="">
              立即取号
            </div>
            <div class="btnItem" @click="goToShop(item.shopId)">
              <img src="~@/assets/nearby/gotoshop.png" alt="">
              进店逛逛
            </div>
          </div>
        </li>
      </ul>
    </template>
    <div v-if="isArrayEmpty(data.shopList) && isObjectEmpty(data.userLocation) && isObjectEmpty(data.userChooseLocation) && !data.loading" class="needlocation">
      <img src="@/assets/nearby/location.png" />
      您可以打开位置权限，查看附近的线上营业厅，或者在页面左上角选择查看指定省市的线上营业厅。
    </div>
    <div v-else-if="isArrayEmpty(data.shopList) && !isObjectEmpty(data.userChooseLocation) && !data.loading" class="needlocation">
      <img src="@/assets/nearby/location.png" />
      当前地区尚未开通线上店，您可以换个地区试试，如上海，内蒙古等
    </div>
    <div v-else-if="isArrayEmpty(data.shopList) && !data.loading" class="needlocation">
      <img src="@/assets/nearby/location.png" />
      暂未查到满足条件的店铺, 您可以换个关键词试试
    </div>
  </div>
</template>

<script setup>
import { Search, Swipe, SwipeItem, Icon , Toast, Loading , Button , Popup,Dialog } from "vant"
import "vant/lib/index.css"
import Vue ,{ reactive, onMounted,nextTick } from "vue"
Vue.use(Search)
  .use(Swipe)
  .use(SwipeItem)
  .use(Icon)
  .use(Toast)
  .use(Loading)
  .use(Button)
  .use(Popup)
  .use(Dialog)

import UA from "@/utils/ua"
import cmApi from "@/api/cm"
import geoApi from "@/api/geo"
import lbsApi from "@/api/lbs"
import loginUtils from "@/utils/login"
import { getActData, getCustomerCode } from '@/api/shop'
import shareUtilApi from "@/utils/share"
import { getImgUrl, isObjectEmpty, isArrayEmpty } from "@/utils/utils"
import { getQueryString } from "@/utils/utils"
import _ from 'lodash'
import SwitchLocation from '@/components/lbs/switch-location/switch-location.vue'

const context = window
const iconList = []
let data = reactive({
  isObjectEmpty,
  isArrayEmpty,
  from: null,
  userLocation: {},
  keywords: "",
  cmParam: {
    cm_code: ["fjdp_hdtj", "Fjdp_gntj"],
    province_id: 100,
    city_id: 100
  },
  cmBanner: [],
  cmIcon: [],
  shopList: null,
  loading: false,
  isLogined: null,
  user: null,
  shareConfig: {
    title: "中国移动附近店铺",
    url: window.location.href,
    desc: "一键搜索帮您快速找店！",
    imgUrl: location.origin + require("@/assets/nearby/share_img.png")
  },
  businessHours:null,
  // 用户选择的位置
  userChooseLocation:{}
})
let popup = reactive({
  show:false,
  wxCode:null,
  avatar:null,
})

onMounted(()=> {
  data.from = getQueryString("from")
  //微信分享配置
  if (UA.isApp) {
    shareUtilApi.appShare(data.shareConfig).then(()=>{
      loginUtils.login(false, false, logined)
    })
  } else {
    shareUtilApi.changeWxShareConfig(data.shareConfig)
    loginUtils.login(false, false, logined)
  }
  setTimeout(init, 100)
})
function switchConfirm(parame){
  data.userChooseLocation = parame

  getNearShop()
}
function homeData(item){
  return new Promise(resolve => {
    getActData({
      shopId:item.shopId
    }).then(res => {
      resolve(res)
    }).catch(() => {
      resolve({})
    })
  })
}
async function handleQrcode(item){
  let sharingCode = item.sharingCode

  Toast.loading({
    message: '加载中...',
    forbidClick: true,
    duration:0
  })
  if(!sharingCode){
    let homedata = await homeData(item)
    let actData = _.get(homedata,'data.actData',[])
    let sharing = actData.find(item => item.componentCode == "sharing")
    let contact = actData.find(item => item.componentCode == "contact")
    let floor = null

    if(contact && _.get(contact,'dataList.floor', null)){
      floor = _.get(contact,'dataList.floor', null)
    }

    if(sharing && _.get(sharing,'dataList.floor', null)){
      floor = _.get(sharing,'dataList.floor', null)
    }

    sharingCode = _.get(floor,'sharingCode', null)

    if(!sharingCode){
      Toast('店长企微暂未开通，您可以换个方式联系')
      return
    }
  }

  getCustomerCode({sharingCode}).then(res => {
    Toast.clear()

    if(res.code){
      Toast('店长企微暂未开通，您可以换个方式联系')
      return
    }

    const qrCode = res.data.contactQrcode || res.data.qrCode

    if(!qrCode){
      Toast('店长企微暂未开通，您可以换个方式联系')
      return
    }

    popup.avatar = res.data.avatar
    popup.wxCode = qrCode
    popup.show = true
  })
}

function init() {
  getuserLocation()
}
function logined(res) {
  data.user = res
  data.isLogined = res && res.UserName > ""
}
function myGetImg(src){
  return getImgUrl(src)
}
function getCmsData() {
  cmApi.getCmData(data.cmParam).then(res => {
    if (res.code == 0 && res.data && res.data.cmData) {
      const { fjdp_hdtj, Fjdp_gntj } = res.data.cmData
      data.cmBanner = fjdp_hdtj || []
      let icons = iconList.concat(Fjdp_gntj || [])
      if (icons.length > 4) icons.length = 4
      data.cmIcon = icons
    }
  })
}
function getNearShop() {
  const { latitude, longitude } = data.userLocation || {}
  const {provinceId,cityId,regionCode} = data.userChooseLocation
  const options = {
    keywords: data.keywords,
    province:provinceId,
    city:cityId,
    county:regionCode
  }

  let userLocationToStore = sessionStorage.getItem('userLocationToStore')
  if(userLocationToStore){
    userLocationToStore = JSON.parse(userLocationToStore)
  }else{
    userLocationToStore = {}
  }

  // 用户所在省与选择省不同
  if(provinceId == userLocationToStore.provinceId){
    options.latitude = latitude
    options.longitude = longitude
  }

  lbsApi
    .getNearShop(options)
    .then(res => {
      if (res.code == 0 && res.data && res.data.shops) {

        /* 增加是否标签收起字段 */
        let shops = JSON.parse(JSON.stringify(res.data.shops))
        shops.forEach(item => {
          // 拿到标签length
          let strLength = 0
          strLength = item.tagList.length
          item.tagLength = strLength

          // 拿到地址length
          const returnStringLength = (message) => {
            let lens = 0
            for (let i = 0; i < message.length; i++) {
              if ((message.charCodeAt(i) >= 0) && (message.charCodeAt(i) <= 255))
              { lens = lens + 1}
              else{ lens = lens + 2 }
            }
            return lens
          }
          let addressLength = returnStringLength(item.address)
          item.addressLength = addressLength

          // 决定展开收起的变量
          if(strLength > 3 || addressLength > 29){
            item.expansion = false

            // 代表当前是否需要显示展开按钮
            item.isShowExpansionButton = true
          }else{
            item.expansion = true

            // 代表当前是否需要显示展开按钮
            item.isShowExpansionButton = false
          }
        })

        data.shopList = shops
      }
    })
}
function yuyue(shopID) {
  if (data.isLogined) {
    lbsApi
      .getUrlReq({
        lat: data.userLocation ? data.userLocation.latitude : "",
        lon: data.userLocation ? data.userLocation.longitude : "",
        pageType: 2,
        shopId: shopID
      })
      .then(res => {
        if (res.code == 0) {
          location.href = res.data.url
        } else {
          Toast(res.message)
        }
      })
  } else {
    loginUtils.login(true, true, logined)
  }
}
function quhao(shopID) {
  if (data.isLogined) {
    lbsApi
      .getUrlReq({
        lat: data.userLocation ? data.userLocation.latitude : "",
        lon: data.userLocation ? data.userLocation.longitude : "",
        pageType: 2,
        shopId: shopID
        //provinceId:data.user.Province
      })
      .then(res => {
        if (res.code == 0) {
          location.href = res.data.url
        } else {
          Toast(res.message)
        }
      })
  } else {
    loginUtils.login(true, true, logined)
  }
}
async function goToShop(shopId) {
  const isWXMapp = await UA.isWeChatMiniApp()
  if (isWXMapp) {
    context.wx.miniProgram.switchTab({ url: "/pages/home/<USER>" })
    context.wx.miniProgram.postMessage({ data: { wxtoHomeShopId: shopId } })
  } else {
    if (data.from == "home") {
      location.href = "/yundian/my/index.html?shopId=" + shopId
    } else {
      location.href = "/yundian/index.html?shopId=" + shopId
    }
  }
}
async function getuserLocation() {
  data.loading = true
  geoApi.getPosition().then(res => {
    // res = {//调试页面专用，解除注释即可看到页面
    //   code:0,
    //   data:{
    //     locProvince:"北京",
    //     latitude: 39.822288,
    //     longitude: 116.294807
    //   }
    // }
    data.userLocation = null
    if (res.code) {
      Dialog.alert({
        title: '温馨提示',
        confirmButtonColor: "#06acea",
        confirmButtonText:'查看指定区域店铺',
        message: '您可以打开位置权限，查看附近的线上营业厅，或者在页面左上角选择查看指定省市的线上营业厅。',
      })

      setTimeout(() => {
        Dialog.close()
      }, 5000)
      data.userLocation = null //调试用
    } else {
      if (res.data && res.data.latitude) {
        data.userLocation = {
          latitude:res.data.latitude,
          longitude:res.data.longitude,
        } || {}
      } else {
        Dialog.alert({
          title: '温馨提示',
          confirmButtonColor: "#06acea",
          confirmButtonText:'查看指定区域店铺',
          message: '您可以打开位置权限，查看附近的线上营业厅，或者在页面左上角选择查看指定省市的线上营业厅。',
        })

        setTimeout(() => {
          Dialog.close()
        }, 5000)
        data.userLocation = null
      }
    }
    nextTick(() => {
      data.loading = false
    })
  })
  geoApi.getLocation().then(res => {
    const { province } = res.data
    data.cmParam.province_id = province || 100
    data.cmParam.city_id = province || 100 //使用省会
    getCmsData()
  })
}
function getCurrentHour(businessHours,shopOpenStatus){
  let week = new Date().getDay(),str=""
  if(businessHours){
    if(week>0){
      str = businessHours[week-1]
    }else{
      str = businessHours[6]
    }
  }else{
    str = ""
  }
  return  str
}
</script>
<style>
body {
  background: #fff !important;
  font-size: 12px;
}
</style>
<style lang="scss" scoped>
.loading-area {
  width: 40%;
  text-align: center;
  margin: 40px auto;
}
.needlocation {
  padding: 20px 20px 0;
  font-size: 14px;
  line-height: 180%;
  text-align: center;
  color: #999;
  img {
    width: 60px;
    display: block;
    margin: 10px auto;
  }
}
.index {
  min-height: calc(100vh - 73px);
  color: #333333;
  a {
    color: #333333;
  }
  .nav {
    padding: 10px 0;
    .van-search {
      width: 355px;
      height: 36px;
      background: #ffffff;
      border: 1px solid #dedede;
      border-radius: 21px;
      margin: 0 auto;
      .van-search__content {
        background: none;
        padding-left: 0;
      }
    }
  }
  .banner {
    height: 160px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .cm-icon {
    width: 345px;
    margin: 0 auto;
    display: flex;
    flex-direction: row;
    height: 115px;
    .item {
      flex-grow: 1;
      padding-top: 28px;
      font-size: 13px;
      text-align: center;
      img {
        width: 42px;
        height: 42px;
        margin: 0 auto 9px auto;
        display: block;
      }
    }
  }
  .cm-line {
    width: 375px;
    height: 10px;
    background: #f3f3f3;
  }
  .shop-list {
    li {
      list-style: none;
      // height: 90px;
      position: relative;
      font-size: 12px;
      padding: 15px 15px 15px 140px;
      line-height: 20px;
      border-bottom: 1px dashed #e9e9e9;
      &.noimg {
        padding-left: 15px;
      }
      > img {
        position: absolute;
        width: 120px;
        height: 90px;
        left: 16px;
      }
      .desc {
        font-size: 12px;
        color: #999;
        height: 40px;
        display: -webkit-box;
        -webkit-line-clamp: 2; /*设置p元素最大4行，父元素需填写宽度才明显*/
        text-overflow: ellipsis;
        overflow: hidden;
        /* autoprefixer: ignore next */
        -webkit-box-orient: vertical;
        word-wrap: break-word;
        word-break: break-all;
      }
      label {
        color: #ed2668;
        float: right;
      }
      .address {
        padding-top: 5px;
        color: #64bbff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        height: 25px;
        width: 260px;
      }
    }
  }
}

.zhankai{
      display: flex;
      padding:0 15px;
      justify-content: flex-end;
      align-items: center;
      span{
        font-size: 10px;
        color:#333;
        display: flex;
          align-items: center;
          i{
            font-style:normal;
            margin-left: 3px;
          }
      }
    }
.tagList{
  padding:0 15px;
  display: flex;
  &.heightYihang{
    height:36px;
    overflow: hidden;
  }

  dl{
    display: flex;
    flex-flow: wrap;
    margin-top: 10px;
    flex:1;
    min-width:0;
    .other{
      display: flex;
      flex-flow:wrap;
      width:100%;
    }
    .one{
      display: flex;
      width:100%;
    }
    dt{
      font-size: 10px;
      background: #dff1fb;
      border:2px solid #51a7ea;
      border-radius:10px;
      padding:1px 3px;
      color:#51a7ea;
      margin-right: 5px;
      margin-bottom: 5px;
    }
  }
}

 .telNumber{
    width: 20px;
    height: 20px;
    display: block;
    margin-left:6px;
    img{
      width:100%;
      height:100%;
    }
  }

  .nearBy {
    .nearbyitem{
      .itemLeft{
        width:240px!important;
        .title{
          display: flex;
          align-items: center;
          &>div{
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

.wechpopContent{
  width:280px;
    border-radius:12px;
    overflow: hidden;
    background: #fff;
    .bgimg{
      img{
        width:100%;
      }
    }
    .text{
      .dzimg{
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        margin:7.5px 0;
        img{
          width:140px;
          height:140px;
        }
        img.positiondzimg{
          position: absolute;
          width:30px;
          height:30px;
          left:0;
          right:0;
          top:0;
          bottom:0;
          margin:auto;
        }
      }

      .left{
        padding:0px 14.5px;
        margin:7.5px 0;
        padding-bottom: 7.5px;
        margin-top: 15px;
        .tit{
          font-size: 15px;
          color:#000;
          margin-bottom: 5px;
          margin-top: 2.5px;
        }
        .com{
          color:#000;
          font-size: 10px;
          p{
            padding:2.5px 0;
            line-height: 1.8;
          }
        }
      }
    }
  }

  /* 新增样式 */
  .index .shop-list li{

    .con{
      display: flex;
      .itemLeft{
        flex:1;
        min-width:0;
      }
    }

    .businesscontent{
      padding-top: 5px;
      width:100%;
      height:auto;
    }

    &.heightYihang{
      .addressName{
        overflow: inherit;
        text-overflow: inherit;
        white-space: inherit !important;
        word-break: break-all;
      }
    }
    .address{
      display: flex;
      height:auto;
      width:100% !important;
      padding:5px 0 !important;
      overflow: inherit;
      text-overflow: inherit;
      white-space: inherit;
      span{
        display: block;
        line-height: inherit
      }
    }
    .icon_adress{
      transform: none;
    }
    .addressName{
      flex:1;
      min-width:0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-left: 3px;
      &.paddingRigth{
        padding-right: 15px;
      }
    }
  }

  .index .nav{
    display: flex;
    align-items: center;
    padding:10px;

    .van-search{
      width:auto;
      flex:1;
      min-width:0;
      box-sizing: border-box;
    }
  }

</style>
