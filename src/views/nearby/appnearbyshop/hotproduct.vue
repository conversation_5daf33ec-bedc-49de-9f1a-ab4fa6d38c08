<template>
  <div class="storemanagement hot_product">
    <Shopname
      v-if="showShopName"
      bg-color="#efefef"
      :content="data.title"
      :show-back="true"
    />
    <div
      v-for="(item, key) in goodsItem"
      :key="item ? item.cmId : key"
      class="hot_product_con"
      :class="{ 'padding-top-34': showShopName && key === 0 }"
    >
      <h3>热销商品{{ key + 1 }}号推荐位</h3>
      <div v-if="item" class="hot_product_card">
        <div class="goodsItem" @click="setShowLink(key)">
          <img
            v-if="item.imgUrl != ''"
            :src="item.imgUrl"
            class="image image1"
          />
          <img v-else :src="item.picture" class="image image1" />
          <div>
            <p class="title1 overEll">
              {{ item.goodsName }}
            </p>
            <p class="title2 overEll">
              {{ item.title }}
            </p>
            <PriceCom
              v-if="item.priceFrom"
              :price-from="item.priceFrom"
              :shop-price="item.shopPrice"
              :price-section="item.priceSection"
              price-class="numColor"
            />
            <p v-else-if="item.sku && item.sku[0]" class="numColor">
              <small>￥</small><b>{{ (item.sku[0].price / 100).toFixed(2) }}</b>
            </p>
            <p v-else class="numColor">
              <small>￥</small><b>{{ (item.price / 100).toFixed(2) }}</b>
            </p>
          </div>
        </div>
        <a
          href="javascript:void(0);"
          class="item-close ac_remove_goods"
          @click="shopDelete(key, item.cmId)"
        ></a>
      </div>
      <div v-else class="hot_product_xzsp" @click="openAddGoods(key)"></div>
    </div>
    <Goodselector
      ref="selectorDialog"
      tpl-id="1"
      :selecttype="data.selecttype"
      :confirm="confirmCallback"
      :goods-used-id="goodsUsedId"
      :goods-type="data.goodsType"
      yiye-shop="yiyeShop"
      :reqsource="5"
    />
  </div>
</template>
<script setup>
import Vue, {
  reactive,
  ref,
  onMounted,
  watch,
  computed,
  getCurrentInstance,
} from 'vue'
import { Toast, Icon } from 'vant'
import loginUtils from '@/utils/login'
import lbsApi from '@/api/lbs'
import UA from '@/utils/ua'
import { getImgUrl } from '@/utils/utils'
//组件
import Shopname from '@/components/index/headercon'
import Goodselector from '@/components/index/goodselector'
import PriceCom from '@/components/index/price.vue'
Vue.use(Toast).use(Icon)
onMounted(() => {
  const url = new URL(location.href)
  const shopId =
    url.searchParams.get('shopId') || url.searchParams.get('shop_id')
  const actId = url.searchParams.get('actId')
  const preview = url.searchParams.get('preview')
  data.pageInfo = {
    shopId,
    actId,
    preview,
  }
  // console.log(user)
  //强登
  const getVueInstance = getCurrentInstance()
  const proxy = getVueInstance ? getVueInstance.proxy : null
  if (proxy) {
    proxy.$store.commit('SET_SHOPID', shopId)
  }
  loginUtils.login(true, true, logined, false, false, '', null, 5)
})
//1、o2oGoodsStatus 2 下线 3上线 运营位下线，店铺首页不展示商品
const O2OGOODSSTATUS = {
  ONLINE: 3,
  OFFLINE: 2,
}
//2、goodsStatus 51是上架 52商品下架，店铺首页应该展示商品，可以进入详情页
const GOODSSTATUS = {
  ONLINE: 51,
  OFFLINE: 52,
}
//3、goodsSourde 1是商城自有 2异业商品
const GOODSSOURCE = {
  ZIYOU: 1,
  YIYE: 2,
}
const data = reactive({
  title: '热销商品',
  ua: null,
  isLogined: false,
  insertUpdate: false,
  goodsType: null,
  users: null,
  index: null,
  items: [],
  adpCode: 'hotGoods',
  cmId: null,
  goodsItemVal: null,
  goodsItem: null,
  selecttype: 'radio',
  dataList: {
    ad: [],
    floor: {
      design: '1',
    },
  },
  adLinkType: '',
})
const goodsItem = ref(null)
const goodslist = computed(() => {
  let goodslist = []
  if (data.items && data.items.length > 0) {
    data.items.forEach((element) => {
      if (
        element.o2oGoodsStatus == O2OGOODSSTATUS.OFFLINE &&
        data.configure != 1
      ) {
        return
      }
      goodslist.push(element)
    })
  }
  return goodslist
})
const goodsUsedId = computed(() => {
  let goodsIdInUse = []
  if (data.items && data.items.length > 0) {
    data.items.forEach((element) => {
      goodsIdInUse.push(element.goodsId)
    })
  }
  return goodsIdInUse
})
let showShopName = ref(false)
showShopName.value =
  !UA.isWechat &&
  !UA.isWechatWork &&
  !UA.isApp &&
  !UA.isIosQQ &&
  !UA.isAndroidQQ
const logined = (res) => {
  data.users = res
  data.isLogined = res && res.UserName > ''
  shopQueryOnLine()
}
const confirmCallback = (value) => {
  data.goodsItemVal = value
  if (value.goodsSource && value.goodsSource == GOODSSOURCE.YIYE) {
    value.price = value.minPrice
  }
  if (data.insertUpdate == true) {
    shopInsert(data.index, value)
  } else {
    shopUpdate(data.index, value)
  }
}
let selectorDialog = ref(null)
const openAddGoods = (index) => {
  data.index = index
  data.insertUpdate = true
  selectorDialog.value.openDialog()
}
const setShowLink = (index) => {
  data.index = index
  data.insertUpdate = false
  selectorDialog.value.openDialog()
}
//查询热点商品
const shopQueryOnLine = () => {
  lbsApi
    .shopQueryOnLine(
      {
        adpCode: data.adpCode,
        shopId: data.pageInfo.shopId,
      },
      5
    )
    .then((res) => {
      if (res.code == 0) {
        //data.shopInfo = res.data
        res.data.forEach((value, item) => {
          if (value != null && value.picture && value.picture != '') {
            if (value.goodsSource && value.goodsSource == GOODSSOURCE.YIYE) {
              value.imgUrl = value.picture
            } else {
              value.imgUrl = getImgUrl(value.picture)
            }
          }
        })
        goodsItem.value = res.data
      } else {
        Toast(res.message)
      }
    })
}
//新增热点商品
const shopInsert = (index, value) => {
  console.log(value)
  let gSourc = 1
  if (value.goodsSource && value.goodsSource == GOODSSOURCE.YIYE) {
    gSourc = value.goodsSource
  }
  lbsApi
    .shopInsert(
      {
        cmCode: index + 1,
        goods: data.goodsItemVal,
        adpCode: data.adpCode,
        status: 2,
        goodsSource: gSourc,
        shopId: data.pageInfo.shopId,
      },
      5
    )
    .then((res) => {
      if (res.code == 0) {
        shopQueryOnLine()
      } else {
        Toast(res.message)
      }
    })
}
//更新热点商品
const shopUpdate = (index, value) => {
  let gSourc = 1
  if (value.goodsSource && value.goodsSource == GOODSSOURCE.YIYE) {
    gSourc = value.goodsSource
  }
  lbsApi
    .shopUpdate(
      {
        cmCode: index + 1,
        goods: data.goodsItemVal,
        adpCode: data.adpCode,
        status: 2,
        goodsSource: gSourc,
        shopId: data.pageInfo.shopId,
      },
      5
    )
    .then((res) => {
      if (res.code == 0) {
        shopQueryOnLine()
      } else {
        Toast(res.message)
      }
    })
}
//删除热点商品
const shopDelete = (index, cmId) => {
  lbsApi
    .shopDelete(
      {
        cmId: cmId,
        shopId: data.pageInfo.shopId,
      },
      5
    )
    .then((res) => {
      if (res.code == 0) {
        let array = goodsItem.value
        array[index] = null
        goodsItem.value = [].concat(array)
      } else {
        Toast(res.message)
      }
    })
}
</script>
<style lang="scss" scoped>
.add-shop {
  background: #f6f6f6;
  height: 100vh;
  &__header {
    width: 100%;
    height: 46px;
    line-height: 46px;
    background: #fff;
    position: sticky;
    top: 0;
    z-index: 2000;
    &-back {
      padding-left: 16px;
      float: left;
      i {
        position: absolute;
        font-size: 20px;
        height: 20px;
        left: 15px;
        top: 14px;
      }
    }
    &-title {
      text-align: center;
      font-size: 18px;
      color: #111;
      font-family: PingFangSC;
    }
  }
}
.storemanagement {
  min-height: 101vh;
  .hot_product_con {
    &:nth-of-type(2) h3 {
      background: url(~@/assets/nearby/appnearby/icon_01.png) 15px 12px
        no-repeat #fff;
      background-size: 20px 20px;
    }
    &:nth-of-type(3) h3 {
      background: url(~@/assets/nearby/appnearby/icon_02.png) 15px 12px
        no-repeat #fff;
      background-size: 20px 20px;
    }
    &:nth-of-type(4) h3 {
      background: url(~@/assets/nearby/appnearby/icon_03.png) 15px 12px
        no-repeat #fff;
      background-size: 20px 20px;
    }
    h3 {
      height: 45px;
      line-height: 45px;
      font-size: 16px;
      color: #333;
      padding-left: 45px;
      margin-bottom: 1px;
      margin-top: 19px;
    }
    .hot_product_xzsp {
      text-align: center;
      width: 124px;
      height: 136px;
      background: #e6f0f4 url(~@/assets/index_img/icon_add.png) no-repeat center;
      margin-left: 49px;
      background-size: 26%;
    }
    .hot_product_card {
      text-align: center;
      width: 124px;
      margin-left: 49px;
      position: relative;
      .goodsItem {
        position: relative;
        .image {
          display: block;
          margin: 0 auto 4px;
          width: 75px;
          height: 75px;
        }
        p {
          font-size: 13px;
          height: 24px;
          line-height: 24px;
        }
        .title1 {
          color: #333;
        }
        .title2 {
          color: rgb(153, 153, 153);
        }
        .numColor {
          font-weight: 700;
          color: #ed2668;
          white-space: nowrap;
        }
        .overEll {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .item-close {
        position: absolute;
        width: 37.5px;
        height: 37.5px;
        border-radius: 50%;
        right: 0;
        top: 0;
        background: rgb(237, 38, 104) url(~@/assets/index_img/icon_close_w.png)
          no-repeat center center;
        background-size: 50%;
      }
    }
  }
}
.padding-top-34 {
  padding-top: 34px;
}
</style>
