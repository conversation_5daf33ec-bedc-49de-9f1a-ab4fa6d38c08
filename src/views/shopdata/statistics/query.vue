<template>
  <div class="statics">
    <h1>
      <Icon name="arrow-left" @click="backmanage()" />全员营销推荐数据查询
    </h1>
    <div class="statics-query">
      <h4>时间筛选</h4>
      <div class="row" @click="data.showOrderDataPicker = true">
        <Cell :value="data.startTime">
          <template #right-icon>
            <Icon name="notes-o" class="calendar-icon" />
          </template>
        </Cell>
        <div class="midline">
          -
        </div>
        <Cell :value="data.endTime">
          <template #right-icon>
            <Icon name="notes-o" class="calendar-icon" />
          </template>
        </Cell>
      </div>
      <h4 v-if="data.user && data.user.RuleId == 2">
        推荐人筛选
      </h4>
      <div v-if="data.user && data.user.RuleId == 2" class="statics-query__menu">
        <DropdownMenu>
          <DropdownItem v-model="data.valuemenu" :options="data.option" />
        </DropdownMenu>
        <!--<select id="group" value="0">
          <option value="0">全体成员</option>
        </select>-->
      </div>
      <div class="statics-query__btn" @click="statisticNeimeng()">
        统计查询
      </div>
    </div>
    <div v-if="data.channelName" class="statics-result">
      <img class="img-lj" src="~@/assets/statistics/icon-link.png" />
      <div class="statics-result__cont">
        <div class="statics-result__top clearfix">
          <img class="h2-tit" src="~@/assets/statistics/icon-tit.png" />
          <img src="~@/assets/statistics/icon-dashed.png" />
          <dl>
            <dt>推荐人</dt>
            <dd>{{ data.shareName }}</dd>
          </dl>
          <dl>
            <dt>归属渠道</dt>
            <dd>{{ data.channelName }}</dd>
          </dl>
        </div>
        <div class="statics-result__exa clearfix">
          <div>
            <p class="thead">
              <span>业务名称</span><span>推荐笔数</span><span>办理笔数</span>
            </p>
            <template v-if="data.busList && data.busList.length > 0">
              <p v-for="(item, key) in busList" :key="key" class="tbody">
                <span>{{ item.packageName }}</span><span>{{ item.shareNum }}</span><span>{{ item.busNum }}</span>
              </p>
              <p class="tbott">
                <span>合计</span><span>{{ data.zsShareNum }}</span><span>{{ data.zsBusNum }}</span>
              </p>
            </template>
            <template v-else>
              <p class="exa-none">
                没有统计数据，换一下统计维度试试
              </p>
            </template>
          </div>
        </div>
      </div>
    </div>
    <!--
      :min-date="minDate"
      :max-date="maxDate"
    -->
    <Calendar
      v-model="data.showOrderDataPicker"
      type="range"
      :min-date="data.minDate"
      :max-date="data.maxDate"
      :allow-same-day="true"
      :default-date="data.defaultDate"
      @confirm="onConfirm"
    />
    <Popup v-model="data.showstaffnum" round position="bottom">
      <Picker
        show-toolbar
        :columns="data.staffnumcolumns"
        :default-index="0"
        @cancel="data.showstaffnum = false"
        @confirm="getOrderData"
      />
    </Popup>
  </div>
</template>

<script setup>
import {
  Icon,
  Toast,
  Cell,
  Calendar,
  Picker,
  Popup,
  DropdownMenu,
  DropdownItem,
} from 'vant'
import {reactive,onMounted,getCurrentInstance} from 'vue'
import shopdataApi from '@/api/shopdata'
import loginUtils from '@/utils/login'

let data = reactive({
  title: '统计查询',
  user: null,
  showOrderDataPicker: false,
  minDate: new Date(),
  maxDate: new Date(),
  defaultDate: new Date(),
  startTime: '',
  endTime: '',
  showstaffnum: false,
  staffnumcolumns: [],
  isLogined: null,
  valuemenu: 0,
  option: [{ text: '全体成员', value: 0 }],
  shareName: '',
  channelName: '',
  zsShareNum: 0,
  zsBusNum: 0,
  busList: null,
})

const formatDate = (date) => {
  return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
}
let pastMonth = data.minDate.getMonth() - 12
data.minDate.setMonth(pastMonth)
let daybefore = new Date(new Date().getTime() - 24 * 60 * 60 * 1000)
data.maxDate = daybefore
data.startTime = formatDate(daybefore)
data.endTime = formatDate(daybefore)
data.defaultDate = daybefore

const logined = (res) => {
  data.user = res
  data.isLogined = res && res.UserName > ''
  staffInfo()
  if (data.user.RuleId == 2) {
    statisticNeimeng()
  }
}
const backmanage = () => {
  window.location.href = '/hd/xskd/index.html'
}

const onConfirm = (date) => {
  const [start, end] = date
  data.showOrderDataPicker = false
  //data.date = `${formatDate(start)} - ${formatDate(end)}`
  data.startTime = formatDate(start)
  data.endTime = formatDate(end)
}
function staffInfo() {
  shopdataApi.staffInfo().then((res) => {
    if (res.code == 0 && res.data) {
      res.data.forEach((item) => {
        if (data.user.RuleId != 2) {
          data.option = []
          data.valuemenu = item.staffId
          statisticNeimeng()
        }
        data.option.push({
          text: item.staffName,
          value: item.staffId,
        })
      })
    }
  })
}
const statisticNeimeng = () => {
  let staffid = null,
    startdate = null,
    enddate = null
  if (data.valuemenu != 0) {
    staffid = data.valuemenu
  }
  if (data.startTime != '起始时间') {
    startdate = data.startTime + ' 00:00:00'
    enddate = data.endTime + ' 00:00:00'
  }
  shopdataApi
    .statisticNeimeng({
      staffId: staffid,
      startDate: startdate,
      endDate: enddate,
    })
    .then((res) => {
      if (res.code == 0 && res) {
        data.shareName = res.data.shareName
        data.channelName = res.data.channelName
        data.busList = res.data.busList
        data.zsShareNum = 0
        data.zsBusNum = 0
        res.data.busList.forEach((value, item) => {
          data.zsShareNum = data.zsShareNum + value.shareNum
          data.zsBusNum = data.zsBusNum + value.busNum
        })
      } else {
        //data.memberdata = {}
        Toast(res.message)
      }
    })
}

function getOrderData(){
  console.log(222)
}

onMounted(()=>{
  const url = new URL(location.href)
  data.shopId = url.searchParams.get('shopId')
  let getCurrentVue = getCurrentInstance()
  data.proxy = getCurrentVue ? getCurrentVue.proxy : null
  data.proxy.$store.commit('SET_SHOPID', data.shopId)
  loginUtils.login(true, true, logined, false, false, '', null, 5)
})
</script>
<style>
.statics-query__menu .van-dropdown-menu__bar{
  box-shadow: none;
  border: 1px solid #CCCCCC;
  margin: 9.75px 0 22.5px;
  height: 37.5px;
  border-radius: 17.25px;
  background: #F4F4F4;
  font-size: 13.5px;
  padding-left: 9.75px;
}
.statics-query__menu .van-dropdown-item{
  width: 90%;
  margin: 0 auto;
}
.statics-query__menu .van-overlay{
  background-color: rgba(0,0,0,0);
  position: fixed !important;
}
.statics-query__menu .van-dropdown-item__content{
  border: 1px solid rgb(204, 204, 204);
  border-radius: 15px;
}
.statics-query__menu .van-dropdown-menu__title{
  width: 100%;
  font-size: 14.25px;
}
.statics-query__menu .van-dropdown-menu__title::after{
  right: 18.75px;
  margin-top: -7.5px;
  width: 7.5px;
  height: 7.5px;
  border: 1px solid;
  border-color: transparent transparent #999999 #999999;
}
.statics-query__menu .van-dropdown-menu__title--down::after{
  margin-top: -2.25px;
  border-color: transparent transparent #ee0a24 #ee0a24;
}
</style>
<style lang="scss" scoped>
.statics {
  font-size: 12px;
  min-height: 100vh;
  background: #f6f7f9 url(~@/assets/statistics/bg.png) no-repeat top center;
  background-size:cover;
  padding-bottom: 22.5px;
  h1{
    color:#fff;
    height:80px;
    line-height: 80px;
    font-size:20px;
    text-align: center;
    position: relative;
    .van-icon{
      position: absolute;
      left: 7.5px;
      top: 27.75px;
      font-size: 22.5px;
    }
  }
  .statics-query__menu .van-dropdown-item {
    width: 90%;
    margin: 0 auto;
  }
  &-query{
    width: 355px;
    padding:13.5px 10.500000000000002px;
    background: #FFFFFF;
    box-shadow: 0px 8px 8px 0px rgba(111,143,243,1);
    border-radius: 8px;
    margin:0 auto;
    h4{
      font-size: 14px;
    }
    .row{
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding:10.125px 0 18.75px;
      .van-cell{
        width: 155px;
        height: 34px;
        background: #F4F4F4;
        border-radius: 15px;
        border: 1px solid #CCCCCC;
        border-radius: 15px;
        padding: 6.375000000000001px 16.125px;
        align-items: center;
        justify-content: center;
        &__title{
          color: #AAAAAA;
        }
      }
      &__btn {
        width: 255px;
        height: 40px;
        color: #fff;
        font-size: 18px;
        line-height: 40px;
        background: linear-gradient(360deg, #8372fe 0%, #5fc8ff 100%);
        box-shadow: 0px 2px 11px 0px rgba(109, 177, 255, 0.29);
        border-radius: 20px;
        text-align: center;
        margin: 0 auto 0.3rem;
      }
    }
    &__btn{
      width: 255px;
      height: 40px;
      color:#fff;
      font-size:18px;
      line-height: 40px;
      background: linear-gradient(360deg, #8372FE 0%, #5FC8FF 100%);
      box-shadow: 0px 2px 11px 0px rgba(109,177,255,0.29);
      border-radius: 20px;
      text-align: center;
      margin: 0 auto 11.25px;
    }

  }
  &-result{
    position: relative;
    .img-lj{
      position: absolute;
      top: -30px;
      left: 8%;
      display: block;
      width: 84%;
      height: auto;
    }
    &__cont{
      width: 355px;
      padding:10.500000000000002px 0;
      background: #FFFFFF;
      box-shadow: 0px 8px 8px 0px rgba(111,143,243,1);
      border-radius: 8px;
      margin:11.25px auto 0;
    }
    &__top{
      margin-bottom: 11.25px;
      img{
        display: block;
        width: 84%;
        height: auto;
      }
      .h2-tit{
        width: 206.25px;
        margin: 7.5px auto;
      }
      dl{
        padding: 7.5px 10.500000000000002px 7.5px 30px;
        clear: both;
        line-height: 15px;
        dt{
          float: left;
          width: 20%;
          font-size: 12px;
        }
        .h2-tit {
          width: 5.5rem;
          margin: 0.2rem auto;
        }
      }
    }
    &__exa{
      clear: both;
      margin: 0 10.500000000000002px;
      padding: 10.500000000000002px 0;
      border-top: 1px dashed #999999;
      p{
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 30px;
        margin-bottom: 6px;
        padding: 5.250000000000001px;
        border-radius: 5.250000000000001px;
        span{
          //flex: 1;
          min-width: 75px;
          padding: 0 5.250000000000001px;
          text-align: center;
          &:nth-of-type(1){
            //flex: 3;
            flex: 1;
            text-align: left;
            padding-left: 13.5px;
            line-height: 17.25px;
          }
        }
      }
      .thead{
        font-size: 14px;
        font-weight: 600;
      }
      .tbody{
        font-size: 12px;
        background: #F8F8F8;
      }
      .exa-none{
        text-align: center;
        padding: 15px 0 22.5px;
        font-size: 15px;
        color: #AAAAAA;
      }
      .tbott{
        font-size: 14px;
        color: #fff;
        background-image: linear-gradient(to right, #8372FE , #5FC8FF);
      }
    }
  }
}
</style>