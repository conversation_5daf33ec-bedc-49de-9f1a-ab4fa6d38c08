
const staffnumcolumns = [{ key: -1, text: "店员工号" }]
const goodstypecolumns = [
  { key: -1, text: "全部商品" },
  { key: 1011, text: "话费充值" },
  { key: 1021, text: "流量充值" },
  { key: 1031, text: "套卡" },
  { key: 1032, text: "终端" },
  { key: 1033, text: "配件" },
  { key: 1034, text: "套餐" },
  { key: 1035, text: "增值业务" },
  { key: 1036, text: "宽带" },
  { key: 1041, text: "异业商品" }
]
const popTextMap = {
  1: "<h4>浏览量PV</h4><p>统计周期内，访问对应店铺产生的PV总和。PV指人工触发的页面的访问次数。</p><hr>",
  2: "<h4>访客UV</h4><p>统计周期内，访问对应店铺所在页面的访客数。</p><hr>",
  3: "<h4>访次VV</h4><p>统计周期内，访问对应店铺所在页面的访问次数。</p><hr>",
  4: "<h4>累计粉丝</h4><p>截止到选择日期，店铺的所有粉丝数。</p><hr>",
  5: "<h4>当日新增关注</h4><p>所选日期对应的新增关注店铺的粉丝数。</p><hr>",
  6: "<h4>当日取消关注</h4><p>所选日期对应的取消关注店铺的粉丝数。</p><hr>",
  7: "<h4>预约数</h4><p>统计周期内，宽带商品的下单总数。</p><hr>",
  8: "<h4>总订单数</h4><p>统计周期内，店铺商品（包括：话费充值、流量充值、套卡、终端、配件、套餐、增值业务、宽带、异业商品等）产生的总订单数量。</p><hr>",
  9: "<h4>总订单数</h4><p>统计周期内，话费/流量充值的总订单数。</p><hr>",
  10: "<h4>办理总数</h4><p>统计周期内，套餐/增值业务的办理总数。</p><hr>",
  11: "<h4>总订单数</h4><p>统计周期内，套卡/终端/配件的总订单数。</p><hr>",
  12: "<h4>总订单数</h4><p>统计周期内，异业商品的总订单数。</p><hr>",
  13: "<h4>支付成功订单数</h4><p>统计周期内，异业商品支付成功订单数。</p><hr>",
  14: "<h4>支付成功订单数</h4><p>统计周期内，店铺商品（包括：话费充值、流量充值、套卡、终端、配件、异业商品）支付成功的订单量与店铺商品（包括：套餐、增值业务、宽带）办理成功的订单量之和。</p><hr>",
  15: "<h4>支付成功订单数</h4><p>统计周期内，话费/流量充值的支付成功订单数。</p><hr>",
  16: "<h4>办理成功数</h4><p>统计周期内，套餐/增值业务的办理成功数。</p><hr>",
  17: "<h4>支付订单数</h4><p>统计周期内，套卡/终端/配件的支付成功订单数。</p><hr>",
  18: "<h4>支付总金额</h4><p>统计周期内，异业商品的支付总金额。</p><hr>",
  19: "<h4>办理用户总数</h4><p>统计周期内，套餐/增值业务的办理用户总数。</p><hr>",
  20: "<h4>支付成功订单金额</h4><p>统计周期内，店铺商品（包括：话费充值、流量充值、套卡、终端、配件、异业商品）支付成功金额与店铺商品（包括：套餐、增值业务、宽带等）办理成功交易额之和。</p><hr>",
  21: "<h4>支付成功金额</h4><p>统计周期内，话费/流量充值的支付成功订单金额。</p><hr>",
  22: "<h4>支付订单金额</h4><p>统计周期内，套卡/终端/配件的支付订单成功金额。</p><hr>",
  23: "<h4>异业退款订单数</h4><p>统计周期内，异业商品退款订单数。</p><hr>",
  24: "<h4>退款订单数</h4><p>统计周期内，异业商品退款订单数。</p><hr>",
  25: "<h4>充值成功订单数</h4><p>统计周期内，话费/流量支付成功并且充值成功的订单数。</p><hr>",
  26: "<h4>人均办理数</h4><p>统计周期内，套餐/增值业务的人均办理数。</p><hr>",
  27: "<h4>货到付款订单数</h4><p>统计周期内，套卡/终端/配件的货到付款订单数。</p><hr>",
  28: "<h4>异业退款订单金额</h4><p>统计周期内，异业商品的退款的订单金额。</p><hr>",
  29: "<h4>退款订单金额</h4><p>统计周期内，异业商品的退款订单金额。</p><hr>",
  30: "<h4>充值成功金额</h4><p>统计周期内，话费/流量支付成功并且充值成功的总金额。</p><hr>",
  31: "<h4>交易额</h4><p>统计周期内，办理套餐/增值业务的交易额。</p><hr>",
  32: "<h4>货到付款订单金额</h4><p>统计周期内，套卡/终端/配件的货到付款订单金额。</p><hr>",
  33: "<h4>优惠总金额</h4><p>统计周期内，话费/流量充值成功后订单优惠的总金额。</p><hr>",
  34: "<h4>客单价</h4><p>统计周期内，办理套餐/增值业务的客单价。</p><hr>",
  35: "<h4>客单价</h4><p>统计周期内，办理套卡/终端/配件的客单价。</p><hr>"
}

const getFontSize = (num) => {
  if (!num) return ".64rem"
  const text = String(num)
  if (text.length > 8) return ".4rem"
  else if (text.length >= 6 && text.length <= 8) return ".5rem"
  else return ".64rem"
}



export default{
  getFontSize,
  popTextMap,
  goodstypecolumns,
  staffnumcolumns
}