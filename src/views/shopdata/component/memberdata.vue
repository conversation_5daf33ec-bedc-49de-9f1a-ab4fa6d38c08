<template>
  <div class="data">
    <h3>粉丝数据</h3>
    <h4>时间筛选</h4>
    <div class="con">
      <Cell
        :value="props.memberdate"
        @click="showpickker(props.memberdate)"
      >
        <template #right-icon>
          <Icon
            name="notes-o"
            class="calendar-icon"
          />
        </template>
      </Cell>
      <div class="midline">
        &nbsp;
      </div>
      <Cell class="nb"></Cell>
    </div>
    <div class="con mt15">
      <div class="datanum newMembers">
        <h6>
          <span
            :style="{
              fontSize: getFontSize(memberData.totalMembers)
            }"
          >
            {{
              memberData.totalMembers == 0
                ? "0"
                : memberData.totalMembers || "0"
            }}</span>
        </h6>
        <p>累计粉丝</p>
        <a
          @click="showPopup(4)"
        ><Icon
          name="info-o"
          color="#9B9B9B"
          size="16px"
        /></a>
      </div>
      <div class="bgwhite">
        &nbsp;
      </div>
      <div class="datanum quitMembers">
        <h6>
          <span
            :style="{
              fontSize: getFontSize(memberData.newMembers)
            }"
          >
            {{
              memberData.newMembers == 0
                ? "0"
                : memberData.newMembers || "0"
            }}</span>
        </h6>
        <p>当日新增关注</p>
        <a
          @click="showPopup(5)"
        ><Icon
          name="info-o"
          color="#9B9B9B"
          size="16px"
        /></a>
      </div>
      <div class="bgwhite">
        &nbsp;
      </div>
      <div class="datanum totalMembers">
        <h6>
          <span
            :style="{
              fontSize: getFontSize(memberData.quitMembers)
            }"
          >
            {{
              memberData.quitMembers == 0
                ? "0"
                : memberData.quitMembers || "0"
            }}</span>
        </h6>
        <p>当日取消关注</p>
        <a
          @click="showPopup(6)"
        ><Icon
          name="info-o"
          color="#9B9B9B"
          size="16px"
        /></a>
      </div>
    </div>
  </div>
</template>
<script setup>
import shopdataApi from "@/api/shopdata"
import shopdatamin from './shopdatamin'
import {defineProps,defineEmits, toRef,reactive,computed} from "vue"
import { Icon, Cell } from "vant"

const {getFontSize,popTextMap,goodstypecolumns} = shopdatamin
let staffnumcolumns = shopdatamin.staffnumcolumns
const emit = defineEmits(["showPopupFn","showpickkerFn"])
const props = defineProps({
  memberdate:{
    type:String,
    default:null
  },
  memberdata:{
    type:Object,
    default:()=>{}
  }
})
let memberData = computed(()=>{
  return props.memberdata
})
const showPopup = (prams)=>{
  emit("showPopupFn",prams)
}
const showpickker = (prams)=>{
  emit("showpickkerFn",prams)
}
</script>
<style lang="scss" scoped>
  .data {
    width: 345px;
    min-height: 245px;
    opacity: 1;
    background: #ffffff;
    border-radius: 5px;
    box-shadow: 0px 5px 20px -2px rgba(0, 0, 0, 0.1);
    margin: 10px auto 5px;
    padding: 20px 15px;
    h3 {
      font-size: 17px;
      color: #000000;
      font-weight: bold;
    }
    h4 {
      display: inline-block;
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      margin: 15px 0 5px 0;
      &.left {
        width: 163px;
      }
    }
    .con {
      display: flex;
      color: rgba(0, 0, 0, 0.4);
      &.mt15 {
        margin-top: 15px;
      }
      .van-cell {
        padding: 0 5px;
        height: 28px;
        line-height: 28px;
        background: #ffffff;
        border: 1px solid #eeeeee;
        border-radius: 5px;
        color: rgba(0, 0, 0, 0.4);
        div {
          width: 80%;
        }
        &.nb {
          border: none;
        }
      }
      .midline {
        width: 40px;
        text-align: center;
        height: .75rem;
        line-height: .6rem;
      }
      .datanum {
        background: #f8f8f7;
        width: 102px;
        height: 102px;
        margin-bottom: 5px;
        text-align: center;
        h6 {
          margin-top: 8px;
          em {
            font-size: 18px;
            color: #000000;
            +span {
            text-indent: 0rem;
            }
          }
          span {
            display: inline-block;
            font-size: 24px;
            color: #000000;
            line-height: 28px;
            font-weight: bold;
            opacity: 0.8;
          }
        }
        p {
          height: 16px;
          line-height: 16px;
          font-size: 11px;
          color: #000000;
          opacity: 0.5;
          margin-top: 5px;
        }
        a {
          display: block;
          width: 14px;
          height: 14px;
          margin: 5px auto 0;
          padding: 0;
          position: relative;
          i {
            position: absolute;
            top: 0;
            left: 0;
          }
        }
      }
      .bgwhite {
        width: 5px;
        background: #fff;
      }
    }
    .calendar-icon {
      font-size: 24px;
      line-height: inherit;
    }
  }
</style>
