<template>
  <div class="data">
    <h3>交易数据</h3>
    <h4>时间筛选</h4>
    <div
      class="con"
      @click="showpickker(props.orderdate)"
    >
      <Cell
        :title="props.datestart"
      >
        <template #right-icon>
          <Icon
            name="notes-o"
            class="calendar-icon"
          />
        </template>
      </Cell>
      <div class="midline">
        -
      </div>
      <Cell
        :title="props.dateend"
      >
        <template #right-icon>
          <Icon
            name="notes-o"
            class="calendar-icon"
          />
        </template>
      </Cell>
    </div>

    <h4 class="left">
      店员工号
    </h4>
    <h4>商品筛选</h4>
    <div class="con">
      <Cell
        :title="props.staffnum.text"
        @click="showpickker(props.staffnum.text)"
      >
      </Cell>
      <div class="midline">
          &nbsp;
      </div>
      <Cell
        :title="props.goodstype.text"
        @click="showpickker(props.goodstype.key)"
      >
      </Cell>
    </div>
    <div
      v-if="goodsType.key == 1036"
      class="con mt15"
    >
      <div class="datanum">
        <h6>
          <span
            :style="{
              fontSize: getFontSize(orderData.broadbandOrder)
            }"
          >
            {{
              orderData.broadbandOrder == 0
                ? "0"
                : orderData.broadbandOrder
                  || "- -"
            }}</span>
        </h6>
        <p>预约数</p>
        <a
          @click="showPopup(7)"
        ><Icon
          name="info-o"
          color="#9B9B9B"
          size="16px"
        /></a>
      </div>
    </div>

    <div v-else class="con mt15">
      <div class="datanum">
        <h6>
          <span
            :style="{
              fontSize: getFontSize(orderData.totalOrder)
            }"
          >
            {{
              orderData.totalOrder == 0
                ? "0"
                : orderData.totalOrder
                  || "- -"
            }}</span>
        </h6>
        <p v-if="goodsType.key == 1034 || goodsType.key == 1035">
          办理总数
        </p>
        <p v-else>
          总订单数
        </p>
        <a v-if="goodsType.key == -1" @click="showPopup(8)">
          <Icon
            name="info-o"
            color="#9B9B9B"
            size="16px"
          /></a>
        <a
          v-else-if="goodsType.key == 1011 || goodsType.key == 1021"
          @click="showPopup(9)"
        >
          <Icon
            name="info-o"
            color="#9B9B9B"
            size="16px"
          /></a>
        <a
          v-else-if="goodsType.key == 1034 || goodsType.key == 1035"
          @click="showPopup(10)"
        >
          <Icon
            name="info-o"
            color="#9B9B9B"
            size="16px"
          /></a>
        <a
          v-else-if="
            goodsType.key == 1031 ||
              goodsType.key == 1032 ||
              goodsType.key == 1033
          "
          @click="showPopup(11)"
        >
          <Icon
            name="info-o"
            color="#9B9B9B"
            size="16px"
          /></a>
        <a v-else-if="goodsType.key == 1041" @click="showPopup(12)">
          <Icon
            name="info-o"
            color="#9B9B9B"
            size="16px"
          /></a>
      </div>
      <div class="bgwhite">
          &nbsp;
      </div>

      <div v-if="goodsType.key == 1041" class="datanum">
        <h6>
          <span
            :style="{
              fontSize: getFontSize(orderData.apolloSucOrder)
            }"
          >
            {{
              orderData.apolloSucOrder == 0
                ? "0"
                : orderData.apolloSucOrder
                  || "- -"
            }}
          </span>
        </h6>
        <p>支付成功订单数</p>
        <a
          @click="showPopup(13)"
        ><Icon
          name="info-o"
          color="#9B9B9B"
          size="16px"
        /></a>
      </div>

      <div
        v-else-if="goodsType.key == 1034 || goodsType.key == 1035"
        class="datanum"
      >
        <h6>
          <span
            :style="{
              fontSize: getFontSize(orderData.userSucBus)
            }"
          >
            {{
              orderData.userSucBus == 0
                ? "0"
                : orderData.userSucBus
                  || "- -"
            }}</span>
        </h6>
        <p>办理成功数</p>
        <a
          @click="showPopup(16)"
        ><Icon
          name="info-o"
          color="#9B9B9B"
          size="16px"
        /></a>
      </div>

      <div v-else class="datanum">
        <h6>
          <span
            :style="{
              fontSize: getFontSize(orderData.sucPayOrder)
            }"
          >
            {{
              orderData.sucPayOrder == 0
                ? "0"
                : orderData.sucPayOrder || "- -"
            }}</span>
        </h6>
        <p v-if="goodsType.key == 1034 || goodsType.key == 1035">
          办理成功数
        </p>
        <p
          v-else-if="
            goodsType.key == 1031 ||
              goodsType.key == 1032 ||
              goodsType.key == 1033
          "
        >
          支付订单数
        </p>
        <p v-else>
          支付成功订单数
        </p>
        <a v-if="goodsType.key == -1" @click="showPopup(14)">
          <Icon
            name="info-o"
            color="#9B9B9B"
            size="16px"
          /></a>
        <a
          v-else-if="goodsType.key == 1011 || goodsType.key == 1021"
          @click="showPopup(15)"
        >
          <Icon
            name="info-o"
            color="#9B9B9B"
            size="16px"
          /></a>
        <a
          v-else-if="
            goodsType.key == 1031 ||
              goodsType.key == 1032 ||
              goodsType.key == 1033
          "
          @click="showPopup(17)"
        >
          <Icon
            name="info-o"
            color="#9B9B9B"
            size="16px"
          /></a>
      </div>
      <div class="bgwhite">
          &nbsp;
      </div>

      <div v-if="goodsType.key == 1041" class="datanum">
        <h6>
          <em
            v-if="orderData.apolloSucAmount || orderData.apolloSucAmount == 0"
            :style="{
              fontSize: getFontSizeIcon(orderData.apolloSucAmount)
            }"
          >
            ￥</em><span
            :style="{
              fontSize: getFontSize(orderData.apolloSucAmount)
            }"
          >
            {{
              orderData.apolloSucAmount == 0
                ? "0"
                : orderData.apolloSucAmount || "- -"
            }}
          </span>
        </h6>
        <p>支付总金额</p>
        <a
          @click="showPopup(18)"
        ><Icon
          name="info-o"
          color="#9B9B9B"
          size="16px"
        /></a>
      </div>
      <div
        v-else-if="goodsType.key == 1034 || goodsType.key == 1035"
        class="datanum"
      >
        <h6>
          <span
            :style="{
              fontSize: getFontSize(orderData.userBus)
            }"
          >
            {{
              orderData.userBus == 0
                ? "0"
                : orderData.userBus
                  || "- -"
            }}</span>
        </h6>
        <p>办理用户总数</p>
        <a
          @click="showPopup(19)"
        ><Icon
          name="info-o"
          color="#9B9B9B"
          size="16px"
        /></a>
      </div>
      <div v-else class="datanum">
        <h6>
          <em
            v-if="
              orderData.sucPayOrderAmount || orderData.sucPayOrderAmount == 0
            "
            :style="{
              fontSize: getFontSizeIcon(orderData.sucPayOrderAmount)
            }"
          >
            ￥</em><span
            :style="{
              fontSize: getFontSize(orderData.sucPayOrderAmount)
            }"
          >
            {{
              orderData.sucPayOrderAmount == 0
                ? "0"
                : orderData.sucPayOrderAmount
                  || "- -"
            }}</span>
        </h6>
        <p v-if="goodsType.key == -1">
          支付成功订单金额
        </p>
        <p v-else-if="goodsType.key == 1011 || goodsType.key == 1021">
          支付成功金额
        </p>
        <p
          v-else-if="
            goodsType.key == 1031 ||
              goodsType.key == 1032 ||
              goodsType.key == 1033
          "
        >
          支付订单金额
        </p>
        <a v-if="goodsType.key == -1" @click="showPopup(20)">
          <Icon
            name="info-o"
            color="#9B9B9B"
            size="16px"
          /></a>
        <a
          v-else-if="goodsType.key == 1011 || goodsType.key == 1021"
          @click="showPopup(21)"
        >
          <Icon
            name="info-o"
            color="#9B9B9B"
            size="16px"
          /></a>
        <a
          v-else-if="
            goodsType.key == 1031 ||
              goodsType.key == 1032 ||
              goodsType.key == 1033
          "
          @click="showPopup(22)"
        >
          <Icon
            name="info-o"
            color="#9B9B9B"
            size="16px"
          /></a>
      </div>
    </div>

    <div v-if="goodsType.key != 1036" class="con ">
      <div
        v-if="goodsType.key == -1 || goodsType.key == 1041"
        class="datanum"
      >
        <h6>
          <span
            :style="{
              fontSize: getFontSize(orderData.apolloReturnOrder)
            }"
          >
            {{
              orderData.apolloReturnOrder == 0
                ? "0"
                : orderData.apolloReturnOrder || "- -"
            }}</span>
        </h6>
        <p v-if="goodsType.key == -1">
          异业退款订单数
        </p>
        <p v-else>
          退款订单数
        </p>
        <a v-if="goodsType.key == -1" @click="showPopup(23)">
          <Icon
            name="info-o"
            color="#9B9B9B"
            size="16px"
          /></a>
        <a v-else @click="showPopup(24)">
          <Icon
            name="info-o"
            color="#9B9B9B"
            size="16px"
          /></a>
      </div>
      <div
        v-else-if="goodsType.key == 1011 || goodsType.key == 1021"
        class="datanum"
      >
        <h6>
          <span
            :style="{
              fontSize: getFontSize(orderData.rechargeSucOrder)
            }"
          >
            {{
              orderData.rechargeSucOrder == 0
                ? "0"
                : orderData.rechargeSucOrder || "- -"
            }}</span>
        </h6>
        <p>充值成功订单数</p>
        <a
          @click="showPopup(25)"
        ><Icon
          name="info-o"
          color="#9B9B9B"
          size="16px"
        /></a>
      </div>
      <div
        v-else-if="goodsType.key == 1034 || goodsType.key == 1035"
        class="datanum"
      >
        <h6>
          <span
            :style="{
              fontSize: getFontSize(orderData.avgBus)
            }"
          >
            {{
              orderData.avgBus == 0
                ? "0"
                : orderData.avgBus
                  || "- -"
            }}</span>
        </h6>
        <p>人均办理数</p>
        <a
          @click="showPopup(26)"
        ><Icon
          name="info-o"
          color="#9B9B9B"
          size="16px"
        /></a>
      </div>
      <div
        v-else-if="
          goodsType.key == 1031 ||
            goodsType.key == 1032 ||
            goodsType.key == 1033
        "
        class="datanum"
      >
        <h6>
          <span
            :style="{
              fontSize: getFontSize(orderData.cdOrder)
            }"
          >
            {{
              orderData.cdOrder == 0
                ? "0"
                : orderData.cdOrder
                  || "- -"
            }}</span>
        </h6>
        <p>货到付款订单数</p>
        <a
          @click="showPopup(27)"
        ><Icon
          name="info-o"
          color="#9B9B9B"
          size="16px"
        /></a>
      </div>
      <div class="bgwhite">
          &nbsp;
      </div>

      <div
        v-if="goodsType.key == -1 || goodsType.key == 1041"
        class="datanum"
      >
        <h6>
          <em
            v-if="
              orderData.apolloReturnOrderAmount ||
                orderData.apolloReturnOrderAmount == 0
            "
            :style="{
              fontSize: getFontSizeIcon(orderData.apolloReturnOrderAmount)
            }"
          >
            ￥</em><span
            :style="{
              fontSize: getFontSize(orderData.apolloReturnOrderAmount)
            }"
          >
            {{
              orderData.apolloReturnOrderAmount == 0
                ? "0"
                : orderData.apolloReturnOrderAmount || "- -"
            }}</span>
        </h6>
        <p v-if="goodsType.key == -1">
          异业退款订单金额
        </p>
        <p v-else>
          退款订单金额
        </p>
        <a v-if="goodsType.key == -1" @click="showPopup(28)">
          <Icon
            name="info-o"
            color="#9B9B9B"
            size="16px"
          /></a>
        <a v-else @click="showPopup(29)">
          <Icon
            name="info-o"
            color="#9B9B9B"
            size="16px"
          /></a>
      </div>
      <div
        v-else-if="goodsType.key == 1011 || goodsType.key == 1021"
        class="datanum"
      >
        <h6>
          <em
            v-if="
              orderData.rechargeSucAmount || orderData.rechargeSucAmount == 0
            "
            :style="{
              fontSize: getFontSizeIcon(orderData.rechargeSucAmount)
            }"
          >
            ￥</em><span
            :style="{
              fontSize: getFontSize(orderData.rechargeSucAmount)
            }"
          >
            {{
              orderData.rechargeSucAmount == 0
                ? "0"
                : orderData.rechargeSucAmount || "- -"
            }}</span>
        </h6>
        <p>充值成功金额</p>
        <a
          @click="showPopup(30)"
        ><Icon
          name="info-o"
          color="#9B9B9B"
          size="16px"
        /></a>
      </div>
      <div
        v-else-if="goodsType.key == 1034 || goodsType.key == 1035"
        class="datanum"
      >
        <h6>
          <em
            v-if="
              orderData.sucPayOrderAmount || orderData.sucPayOrderAmount == 0
            "
            :style="{
              fontSize: getFontSizeIcon(orderData.sucPayOrderAmount)
            }"
          >
            ￥</em><span
            :style="{
              fontSize: getFontSize(orderData.sucPayOrderAmount)
            }"
          >
            {{
              orderData.sucPayOrderAmount == 0
                ? "0"
                : orderData.sucPayOrderAmount || "- -"
            }}</span>
        </h6>
        <p>交易额</p>
        <a
          @click="showPopup(31)"
        ><Icon
          name="info-o"
          color="#9B9B9B"
          size="16px"
        /></a>
      </div>
      <div
        v-else-if="
          goodsType.key == 1031 ||
            goodsType.key == 1032 ||
            goodsType.key == 1033
        "
        class="datanum"
      >
        <h6>
          <em
            v-if="orderData.cdOrderAmount || orderData.cdOrderAmount == 0"
            :style="{
              fontSize: getFontSizeIcon(orderData.cdOrderAmount)
            }"
          >
            ￥</em><span
            :style="{
              fontSize: getFontSize(orderData.cdOrderAmount)
            }"
          >
            {{
              orderData.cdOrderAmount == 0
                ? "0"
                : orderData.cdOrderAmount || "- -"
            }}</span>
        </h6>
        <p>货到付款订单金额</p>
        <a
          @click="showPopup(32)"
        ><Icon
          name="info-o"
          color="#9B9B9B"
          size="16px"
        /></a>
      </div>
      <div class="bgwhite">
          &nbsp;
      </div>

      <div
        v-if="goodsType.key == 1011 || goodsType.key == 1021"
        class="datanum"
      >
        <h6>
          <em
            v-if="orderData.discountAmount || orderData.discountAmount == 0"
            :style="{
              fontSize: getFontSizeIcon(orderData.discountAmount)
            }"
          >
            ￥</em><span
            :style="{
              fontSize: getFontSize(orderData.discountAmount)
            }"
          >
            {{
              orderData.discountAmount == 0
                ? "0"
                : orderData.discountAmount || "- -"
            }}</span>
        </h6>
        <p>优惠总金额</p>
        <a
          @click="showPopup(33)"
        ><Icon
          name="info-o"
          color="#9B9B9B"
          size="16px"
        /></a>
      </div>
      <div
        v-else-if="
          goodsType.key == 1034 ||
            goodsType.key == 1035 ||
            goodsType.key == 1031 ||
            goodsType.key == 1032 ||
            goodsType.key == 1033
        "
        class="datanum"
      >
        <h6>
          <em
            v-if="orderData.price || orderData.price == 0"
            :style="{
              fontSize: getFontSizeIcon(orderData.price)
            }"
          >
            ￥</em><span
            :style="{
              fontSize: getFontSize(orderData.price)
            }"
          >
            {{ orderData.price == 0
              ? "0"
              : orderData.price
                || "- -" }}</span>
        </h6>
        <p>客单价</p>
        <a
          v-if="
            goodsType.key == 1031 ||
              goodsType.key == 1032 ||
              goodsType.key == 1033
          "
          @click="showPopup(35)"
        >
          <Icon
            name="info-o"
            color="#9B9B9B"
            size="16px"
          /></a>
        <a v-else @click="showPopup(34)">
          <Icon
            name="info-o"
            color="#9B9B9B"
            size="16px"
          /></a>
      </div>
      <div v-else>
          &nbsp;
      </div>
    </div>
  </div>
</template>
<script setup>
import shopdataApi from "@/api/shopdata"
import shopdatamin from './shopdatamin'
import {defineProps,defineEmits, toRef,toRefs,reactive, computed} from "vue"
import { Icon, Cell } from "vant"
const {getFontSize,popTextMap,goodsTypecolumns} = shopdatamin
let staffnumcolumns = shopdatamin.staffnumcolumns

const emit = defineEmits(["showPopupFn","showpickkerFn"])
const props = defineProps({
  datestart:{
    type:String,
    default:null
  },
  dateend:{
    type:String,
    default:null
  },
  orderdata:{
    type:Object,
    default:()=>{}
  },
  orderdate:{
    type:String,
    default:null
  },
  goodstype:{
    type:Object,
    default:()=>{}
  },
  staffnum:{
    type:Object,
    default:()=>{}
  }
})
let orderData = computed(()=>{
  return props.orderdata
})
let goodsType = computed(()=>{
  return props.goodstype
})
const showPopup = (prams)=>{
  emit("showPopupFn",prams)
}
const showpickker = (prams)=>{
  emit("showpickkerFn",prams)
}
const getFontSizeIcon = (num) => {
  const text = String(num)
  if (text.length > 8) return ".3rem"
  else if (text.length >= 6 && text.length <= 8) return ".4rem"
  else return ".48rem"
}
</script>
<style lang="scss" scoped>
  .data {
    width: 345px;
    min-height: 245px;
    opacity: 1;
    background: #ffffff;
    border-radius: 5px;
    box-shadow: 0px 5px 20px -2px rgba(0, 0, 0, 0.1);
    margin: 10px auto 5px;
    padding: 20px 15px;
    h3 {
      font-size: 17px;
      color: #000000;
      font-weight: bold;
    }
    h4 {
      display: inline-block;
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      margin: 15px 0 5px 0;
      &.left {
        width: 163px;
      }
    }
    .con {
      display: flex;
      color: rgba(0, 0, 0, 0.4);
      &.mt15 {
        margin-top: 15px;
      }
      .van-cell {
        padding: 0 5px;
        height: 28px;
        line-height: 28px;
        background: #ffffff;
        border: 1px solid #eeeeee;
        border-radius: 5px;
        color: rgba(0, 0, 0, 0.4);
        div {
          width: 80%;
        }
        &.nb {
          border: none;
        }
      }
      .midline {
        width: 40px;
        text-align: center;
        height: .75rem;
        line-height: .6rem;
      }
      .datanum {
        background: #f8f8f7;
        width: 102px;
        height: 102px;
        margin-bottom: 5px;
        text-align: center;
        h6 {
          margin-top: 8px;
          em {
            font-size: 18px;
            color: #000000;
            +span {
            text-indent: 0rem;
            }
          }
          span {
            display: inline-block;
            font-size: 24px;
            color: #000000;
            line-height: 28px;
            font-weight: bold;
            opacity: 0.8;
          }
        }
        p {
          height: 16px;
          line-height: 16px;
          font-size: 11px;
          color: #000000;
          opacity: 0.5;
          margin-top: 5px;
        }
        a {
          display: block;
          width: 14px;
          height: 14px;
          margin: 5px auto 0;
          padding: 0;
          position: relative;
          i {
            position: absolute;
            top: 0;
            left: 0;
          }
        }
      }
      .bgwhite {
        width: 5px;
        background: #fff;
      }
    }
    .calendar-icon {
      font-size: 24px;
      line-height: inherit;
    }
  }
</style>
