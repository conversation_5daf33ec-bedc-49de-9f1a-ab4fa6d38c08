<template>
  <div class="data">
    <h3>店铺数据</h3>
    <h4>时间筛选</h4>
    <div
      class="con"
      @click="openPicker"
    >
      <Cell
        :title="props.shopdatestart"
      >
        <template #right-icon>
          <Icon
            name="notes-o"
            class="calendar-icon"
          />
        </template>
      </Cell>
      <div class="midline">
        -
      </div>
      <Cell
        :title="props.shopdateend"
      >
        <template #right-icon>
          <Icon name="notes-o" class="calendar-icon" />
        </template>
      </Cell>
    </div>
    <div class="con mt15">
      <div class="datanum">
        <h6>
          <span
            :style="{
              fontSize: getFontSize(shopData.pv)
            }"
          >
            {{ shopData.pv == 0
              ? "0"
              : shopData.pv
                || "0" }}
          </span>
        </h6>
        <p>浏览量PV</p>
        <a
          @click="showPopup(1)"
        ><Icon
          name="info-o"
          color="#9B9B9B"
          size="16px"
        /></a>
      </div>
      <div class="bgwhite">
        &nbsp;
      </div>
      <div class="datanum">
        <h6>
          <span
            :style="{
              fontSize: getFontSize(shopData.uv)
            }"
          >
            {{ shopData.uv == 0
              ? "0"
              : shopData.uv
                || "0" }}</span>
        </h6>
        <p>访客UV</p>
        <a
          @click="showPopup(2)"
        ><Icon
          name="info-o"
          color="#9B9B9B"
          size="16px"
        /></a>
      </div>
      <div class="bgwhite">
        &nbsp;
      </div>
      <!-- <div class="datanum">
        <h6>
          <span
            :style="{
              fontSize: getFontSize(shopData.vv)
            }"
          >
            {{ shopData.vv == 0
              ? "0"
              : shopData.vv
                || "0" }}</span>
        </h6>
        <p>访次VV</p>
        <a
          @click="showPopup(3)"
        ><Icon
          name="info-o"
          color="#9B9B9B"
          size="16px"
        /></a>
      </div> -->
    </div>
  </div>
</template>
<script setup>
import {defineProps,defineEmits, toRef,reactive,computed} from "vue"
import shopdataApi from "@/api/shopdata"
import shopdatamin from './shopdatamin'
import { Icon, Cell } from "vant"
const {getFontSize,popTextMap,goodstypecolumns} = shopdatamin
let staffnumcolumns = shopdatamin.staffnumcolumns
const emit = defineEmits(["showPopupFn","openPickerFn"])
const props = defineProps({
  shopdatestart:{
    type:String,
    default:null
  },
  shopdateend:{
    type:String,
    default:null
  },
  shopdata:{
    type:Object,
    default:function(data){
      return data || {}
    }
  }
})
let shopData = computed(()=>{
  return props.shopdata
})
const showPopup = (prams)=>{
  emit("showPopupFn",prams)
}
const openPicker = ()=>{
  emit("openPickerFn")
}
const getFontSizeIcon = (num) => {
  const text = String(num)
  if (text.length > 8) return ".3rem"
  else if (text.length >= 6 && text.length <= 8) return ".4rem"
  else return ".48rem"
}

</script>
<style lang="scss" scoped>
  .data {
    width: 345px;
    min-height: 245px;
    opacity: 1;
    background: #ffffff;
    border-radius: 5px;
    box-shadow: 0px 5px 20px -2px rgba(0, 0, 0, 0.1);
    margin: 10px auto 5px;
    padding: 20px 15px;
    h3 {
      font-size: 17px;
      color: #000000;
      font-weight: bold;
    }
    h4 {
      display: inline-block;
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      margin: 15px 0 5px 0;
      &.left {
        width: 163px;
      }
    }
    .con {
      display: flex;
      justify-content: center;
      color: rgba(0, 0, 0, 0.4);
      &.mt15 {
        margin-top: 15px;
      }
      .van-cell {
        padding: 0 5px;
        height: 28px;
        line-height: 28px;
        background: #ffffff;
        border: 1px solid #eeeeee;
        border-radius: 5px;
        color: rgba(0, 0, 0, 0.4);
        div {
          width: 80%;
        }
        &.nb {
          border: none;
        }
      }
      .midline {
        width: 40px;
        text-align: center;
        height: .75rem;
        line-height: .6rem;
      }
      .datanum {
        background: #f8f8f7;
        width: 102px;
        height: 102px;
        margin-bottom: 5px;
        text-align: center;
        h6 {
          margin-top: 8px;
          em {
            font-size: 18px;
            color: #000000;
            +span {
            text-indent: 0rem;
            }
          }
          span {
            display: inline-block;
            font-size: 24px;
            color: #000000;
            line-height: 28px;
            font-weight: bold;
            opacity: 0.8;
          }
        }
        p {
          height: 16px;
          line-height: 16px;
          font-size: 11px;
          color: #000000;
          opacity: 0.5;
          margin-top: 5px;
        }
        a {
          display: block;
          width: 14px;
          height: 14px;
          margin: 5px auto 0;
          padding: 0;
          position: relative;
          i {
            position: absolute;
            top: 0;
            left: 0;
          }
        }
      }
      .bgwhite {
        width: 5px;
        background: #fff;
      }
    }
    .calendar-icon {
      font-size: 24px;
      line-height: inherit;
    }
  }
</style>
