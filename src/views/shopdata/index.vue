<template>
  <div class="shopdata">
    <div v-if="data.isApp" class="header">
      <Icon name="arrow-left" class="back" @click="back" />
      {{ data.title }}
    </div>
    <!-- <Transactiondata
      :datestart="timeData.datestart"
      :dateend="timeData.dateend"
      :orderdata="data.orderdata"
      :goodstype="data.goodstype"
      :staffnum="data.staffnum"
      @showPopupFn="showPopup"
      @showpickkerFn="showpickker"
    /> -->
    <Memberdata
      :memberdate="timeData.memberdate"
      :memberdata="data.memberdata"
      @showPopupFn="showPopup"
      @showpickkerFn="showpickker"
    />
    <Shopdata
      :shopdatestart="timeData.shopdatestart"
      :shopdateend="timeData.shopdateend"
      :shopdata="data.shopdata"
      @showPopupFn="showPopup"
      @openPickerFn="openShopPicker"
    />
    <Calendar
      v-model="data.showOrderDataPicker"
      type="range"
      :allow-same-day="true"
      :min-date="timeData.minDate"
      :max-date="timeData.maxDate"
      :default-date="timeData.defaultDate"
      @confirm="orderData"
    />
    <Calendar
      v-model="data.showShopDataPicker"
      type="range"
      :allow-same-day="true"
      :min-date="timeData.minDate"
      :max-date="timeData.maxDate"
      :default-date="timeData.defaultDate"
      @confirm="shopData"
    />
    <Calendar
      v-model="data.showStaffdataPicker"
      :min-date="timeData.minDate"
      :max-date="data.StaffmaxDate"
      @confirm="staffData"
    />
    <Popup v-model="data.showpop" round closeable close-icon="close" class="poptip">
      <div v-myhtml="data.popcon" class="poptext"></div></Popup>
    <Popup v-model="data.showstaffnum" round position="bottom">
      <Picker
        show-toolbar
        :columns="staffnumcolumns"
        :default-index="0"
        @cancel="data.showstaffnum = false"
        @confirm="staffnumconfirm"
      />
    </Popup>
    <Popup v-model="data.showgoodstype" round position="bottom">
      <Picker
        show-toolbar
        :columns="goodstypecolumns"
        :default-index="0"
        @cancel="data.showgoodstype = false"
        @confirm="goodstypeconfirm"
      />
    </Popup>
  </div>
</template>

<script setup>
import shopdatamin from './component/shopdatamin'
import Shopdata from './component/shopdata.vue'
import Memberdata from './component/memberdata.vue'
import Transactiondata from './component/transactiondata.vue'
import { Icon, Calendar, Popup, Picker } from 'vant'
import { getCurrentInstance, onMounted, reactive,ref } from 'vue'
import shopdataApi from '@/api/shopdata'
import { getToken, removeLocalStorage } from "@/utils/login/localStorage.js"
import loginApi from "@/api/login"
import { dateFormat } from '@/filters'
import UA from '@/utils/ua'
// const nineTime ='0900'

const { getFontSize, popTextMap } = shopdatamin
let staffnumcolumns = ref(shopdatamin.staffnumcolumns)
let goodstypecolumns = ref(shopdatamin.goodstypecolumns)
const getFontSizeIcon = (num) => {
  const text = String(num)
  if (text.length > 8) return '.3rem'
  else if (text.length >= 6 && text.length <= 8) return '.4rem'
  else return '.48rem'
}
const timeData = reactive({
  datestart: '开始时间',
  dateend: '结束时间',
  memberdate: '选择时间',
  shopdatestart: '开始时间',
  shopdateend: '结束时间',
  minDate: new Date(2010, 0, 1),
  maxDate: new Date(new Date().setDate(new Date().getDate() - 1)),
  defaultDate: [
    new Date(new Date().setDate(new Date().getDate() - 1)),
    new Date(new Date().setDate(new Date().getDate() - 1)),
  ],
})

const data = reactive({
  title: '店铺数据',
  StaffmaxDate: new Date(),
  staffnum: {},
  goodstype: {},
  showstaffnum: false,
  showgoodstype: false,
  memberdata: {},
  shopdata: {},
  orderdata: {},
  showShopDataPicker: false,
  showOrderDataPicker: false,
  showStaffdataPicker: false,
  showpop: false,
  popcon: '',
  isApp: false,
  shopId: null,
  user: null,
})
data.goodstype = goodstypecolumns.value[0]
data.staffnum = staffnumcolumns.value[0]
const staffInfo = () => {
  shopdataApi
    .staffInfo()
    .then((res) => {
      if (res.code == 0 && res.data) {
        let staffnumcolumninner = []
        if (data.user.RuleId == 2) {
          staffnumcolumninner.push({ key: -1, text: '店员工号' })
        }
        res.data.forEach((item) => {
          staffnumcolumninner.push({
            key: item.staffNumber,
            text: item.staffNumber,
          })
        })
        staffnumcolumns.value = staffnumcolumninner
        data.staffnum = staffnumcolumninner[0]
        getorderdata()
      }
    })
}

const logined = (res) => {
  data.user = res
  if ( res && res.RuleId > 0) {
    staffInfo()
    getstaffData()
    getshopdata()
  }
}

const openShopPicker = () => {
  data.showShopDataPicker = true
}
const getstaffData = () => {
  shopdataApi
    .staffData({
      statTime: timeData.memberdate,
      reqsource: 5,
    })
    .then((res) => {
      if (res.code == 0 && res) {
        data.memberdata = res.data
      } else {
        data.memberdata = {}
      }
    })
}

const staffData = (statTime) => {
  timeData.memberdate = formatDate(statTime)
  getstaffData()
  data.showStaffdataPicker = false
}

const getshopdata = () => {
  shopdataApi
    .shopData({
      startDate: timeData.shopdatestart,
      endDate: timeData.shopdateend,
      productId: data.goodstype.key,
      staffNumber: data.staffnum.key,
      reqsource: 5,
    })
    .then((res) => {
      if (res.code == 0 && res.data) {
        data.shopdata = res.data
      } else {
        data.shopdata = {}
      }
    })
}

const shopData = (dateRange) => {
  let [startDate, endDate] = dateRange
  timeData.shopdatestart = formatDate(startDate)
  timeData.shopdateend = formatDate(endDate)
  getshopdata()
  data.showShopDataPicker = false
}

const getorderdata = () => {
  shopdataApi
    .shopData({
      startDate: timeData.datestart,
      endDate: timeData.dateend,
      productId: data.goodstype.key,
      staffNumber: data.staffnum.key,
      reqsource: 5,
    })
    .then((res) => {
      data.showOrderDataPicker = false
      if (res.code == 0 && res.data) {
        data.orderdata = res.data
      } else {
        data.orderdata = {}
      }
    })
}

const orderData = (dateRange) => {
  let [startDate, endDate] = dateRange
  timeData.datestart = formatDate(startDate)
  timeData.dateend = formatDate(endDate)
  getorderdata()
  data.showOrderDataPicker = false
}

const showpickker = (type) => {
  if (type == data.staffnum.text) {
    data.showstaffnum = true
  }
  if (type == data.goodstype.key) {
    data.showgoodstype = true
  }
  if (type == timeData.orderdate) {
    data.showOrderDataPicker = true
  }
  if (type == timeData.memberdate) {
    data.showStaffdataPicker = true
  }
  setScrollTop()
}

const staffnumconfirm = (value) => {
  data.staffnum = value
  getorderdata()
  data.showstaffnum = false
}

const goodstypeconfirm = (value) => {
  data.goodstype = value
  getorderdata()
  data.showgoodstype = false
}

const formatDate = (date) => {
  return dateFormat(date, 'yyyy-MM-dd')
}

const back = () => {
  history.go(-1)
  // window.location.href = '/hd/xskd/index.html?shopId=' + data.shopId
}

const showPopup = (index) => {
  data.popcon = popTextMap[index]
  data.showpop = true
}

const setScrollTop = () => {
  var scrollTop =
    document.documentElement.scrollTop ||
    window.pageYOffset ||
    document.body.scrollTop
  if (scrollTop == 0) {
    window.scrollTo(0, 1)
  }
}

onMounted(() => {
  const url = new URL(location.href)
  data.shopId = url.searchParams.get('shopId')
  let getCurrentVue = getCurrentInstance()
  data.proxy = getCurrentVue ? getCurrentVue.proxy : null
  data.proxy.$store.commit('SET_SHOPID', data.shopId)
  loginApi.getUserInfo(getToken(),null,5).then((res)=>{
    if (res.code == 0) {
      logined(res.data)
    } else if (res.code == 1) {
      removeLocalStorage()
      return null
    }
  })
  data.isApp = UA.isApp || UA.isWechat ? false : true
  let now = new Date()
  // data.maxDate = new Date(now.setDate(now.getDate()-1))
  timeData.datestart = formatDate(
    now.getHours() >= 9
      ? now.setDate(now.getDate() - 1)
      : now.setDate(now.getDate() - 2)
  )
  timeData.dateend = timeData.datestart
  timeData.shopdatestart = timeData.datestart
  timeData.shopdateend = timeData.datestart
  timeData.memberdate = formatDate(new Date())
})
</script>

<style>
body {
  background: #f6f7f9 !important;
  font-size: 12px;
}
</style>
<style lang="scss" scoped>
.shopdata {
  .header {
    width: 100%;
    height: 44px;
    line-height: 44px;
    background: #fff;
    text-align: center;
    font-size: 18px;
    position: relative;
    .back {
      position: absolute;
      left: 10px;
      bottom: 13px;
    }
  }
  .data {
    width: 345px;
    min-height: 245px;
    opacity: 1;
    background: #ffffff;
    border-radius: 5px;
    box-shadow: 0px 5px 20px -2px rgba(0, 0, 0, 0.1);
    margin: 10px auto 5px;
    padding: 20px 15px;
    h3 {
      font-size: 17px;
      color: #000000;
      font-weight: bold;
    }
    h4 {
      display: inline-block;
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      margin: 15px 0 5px 0;
      &.left {
        width: 163px;
      }
    }
    .con {
      display: flex;
      color: rgba(0, 0, 0, 0.4);
      &.mt15 {
        margin-top: 15px;
      }
      .van-cell {
        padding: 0 5px;
        height: 28px;
        line-height: 28px;
        background: #ffffff;
        border: 1px solid #eeeeee;
        border-radius: 5px;
        color: rgba(0, 0, 0, 0.4);
        div {
          width: 80%;
        }
        &.nb {
          border: none;
        }
      }
      .midline {
        width: 40px;
        text-align: center;
        height: 28.125px;
        line-height: 22.5px;
      }
      .datanum {
        background: #f8f8f7;
        width: 102px;
        height: 102px;
        margin-bottom: 5px;
        text-align: center;
        h6 {
          margin-top: 8px;
          em {
            font-size: 18px;
            color: #000000;
            +span {
            text-indent: 0px;
            }
          }
          span {
            display: inline-block;
            font-size: 24px;
            color: #000000;
            line-height: 28px;
            font-weight: bold;
            opacity: 0.8;
          }
        }
        p {
          height: 16px;
          line-height: 16px;
          font-size: 11px;
          color: #000000;
          opacity: 0.5;
          margin-top: 5px;
        }
        a {
          display: block;
          width: 14px;
          height: 14px;
          margin: 5px auto 0;
          padding: 0;
          position: relative;
          i {
            position: absolute;
            top: 0;
            left: 0;
          }
        }
      }
      .bgwhite {
        width: 5px;
        background: #fff;
      }
    }
    .calendar-icon {
      font-size: 24px;
      line-height: inherit;
    }
  }
  .poptext {
    text-align: center;
    color: #000000;
    width: 285px;
    min-height: 140px;
    padding-bottom: 65px;
  }
  :deep(.poptext) {
    h4 {
      font-size: 18px;
      font-weight: bold;
      height: 25px;
      line-height: 25px;
      margin: 18px auto;
    }
    p {
      font-size: 14px;
      line-height: 20px;
      opacity: 0.8;
      width: 253px;
      margin: 0 auto;
    }
    hr {
      width: 285px;
      height: 1px;
      margin: 0;
      position: absolute;
      bottom: 52px;
      border: 1px solid #eeeeee;
    }
  }
  :deep(.poptip) .van-icon-close::before {
    content: '关闭';
    font-size: 18px;
  }
  :deep(.poptip) .van-popup__close-icon {
    bottom: 20px;
    top: unset;
    right: 0;
    left: 0;
    text-align: center;
    color: rgba(66, 124, 246, 1);
    font-size: 18px;
  }
  :deep(.van-cell__value--alone) {
    color: rgba(0, 0, 0, 0.4);
  }
  :deep(.van-ellipsis) {
    line-height: 44px;
  }
}
</style>
