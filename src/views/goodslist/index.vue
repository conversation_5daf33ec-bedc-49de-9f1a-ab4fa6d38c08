<template>
  <div>
    <Shopname
      v-if="showShopName"
      :title="floorData.floorName"
      :height="44"
      :font-size="18"
      :icon-size="24"
    />
    <div
      class="searchlist"
      :class="{'padding-top-44':showShopName}"
    >
      <div v-if="floorData.imgUrl" class="banner" @click="goImgLink(floorData.adLink)">
        <img :src="getImgUrl(floorData.imgUrl)" />
      </div>
      <div v-if="hotGoodslist && hotGoodslist.length" class="index-home-recommend">
        <ul
          class="recommend-goods-list"
        >
          <li v-for="item in hotGoodslist" :key="item.goodsId">
            <a href="javascript:void(0)" @click="insertCodeGoods(key+1,item.goodsId,item.goodsSource,item.goodsLink)">
              <img :src="getImgUrl(item.imgUrl)" />
              <p class="title">
                {{ item.goodsTitle }}
              </p>
              <PriceCom
                v-if="item.priceFrom"
                :price-from="item.priceFrom"
                :shop-price="item.shopPrice"
                :price-section="item.priceSection"
              />
              <p class="price">
                <em>¥</em>
                {{ (item.price/100).toFixed(2) }}
              </p>
            </a>
            <span v-if="showShare" class="share-goods" @click="openShare(item)"></span>
          </li>
        </ul>
      </div>
      <div class="goodslist-container">
        <div class="goodslist-title">
          {{ secondData.floorName }}
        </div>
        <div v-if="goodslist && goodslist.length" class="goods-container">
          <List
            v-model="goodsOption.loading"
            :finished="goodsOption.finished"
            finished-text="没有更多了"
            class="goods-list"
            @load="getGoods"
          >
            <Cell v-for="(item, key) in goodslist" :key="item.goodsId">
              <li>
                <a href="javascript:void(0)" @click="insertCodeGoods(key+1,item.goodsId,item.goodsSource,item.goodsLink)">
                  <img :src="getImgUrl(item.imgUrl)" />
                  <p class="title">
                    {{ item.goodsTitle }}
                  </p>
                  <p class="subtitle">
                    {{ item.goodsSubTitle }}
                  </p>
                  <PriceCom
                    v-if="item.priceFrom"
                    :price-from="item.priceFrom"
                    :shop-price="item.shopPrice"
                    :price-section="item.priceSection"
                  />
                  <p class="price">
                    <em>¥</em>
                    {{ (item.price/100).toFixed(2) }}
                  </p>
                </a>
                <span v-if="showShare" class="share-goods" @click="openShare(item)"></span>
              </li>
            </Cell>
          </List>
        </div>
        <ul
          v-else-if="notForst"
          class="goods-tab-no"
          :class="{'is-app':!showShopName}"
        >
          <li class="tab-no-con">
            <div>
              <img src="~@/assets/index_img/ice_tab_no.png" />
              <p>抱歉，楼层没有商品</p>
            </div>
          </li>
        </ul>
        <ul
          v-else
          class="goods-tab-no"
          :class="{'is-app':!showShopName}"
        >
          <li class="tab-no-con">
          </li>
        </ul>
      </div>
    </div>
    <!-- 分享组件 -->
    <Share ref="shareRef" :shopshare="shopShare" />
  </div>
</template>
<script setup>
import { onMounted,ref,reactive,computed,getCurrentInstance } from "vue"
import router from '@/router/index.js'
import Shopname from "@/views/my/components/headerNav.vue"
import Share from "@/components/index/share.vue"
import shopApi from "@/api/shop.js"
import insertCode from "@/utils/insertCode.js"
import PriceCom from "@/components/index/price.vue"
import UA from "@/utils/ua.js"
import { Loading,Toast,List,Cell} from 'vant'
import EventBus from '@/api/eventbus.js'
import {getImgUrl} from "@/utils/utils.js"
import loginUtils from "@/utils/login/index.js"
onMounted(()=>{
  const url = new URL(location.href)
  searchData.shopId = url.searchParams.get('shopId')
  searchData.floorId = url.searchParams.get('floorId')
  if(!searchData.shopId){
    router.push({
      name: "nearby"
    })
  }
  document.querySelector('body').setAttribute('class', 'secondary-list')
  loginUtils.login(
    false,
    false,
    logined,
    false,
    false,
    "",
    '0'
  ) //用户也可查到信息

})
window.scrollTo(0, 0)
function logined(){
  getHotGoods()
  getGoods()
  queryShopStaTus()
}
let showShopName = ref(false)
showShopName.value = !UA.isWechat&&!UA.isWechatWork&&!UA.isApp&&!UA.isIosQQ&&!UA.isAndroidQQ
let searchData = {
  shopId:null,
  floorId:'',
  pageSize:10,
  pageNum:1
}
const showLoading= ref(false)
/** 页面标题和banner数据 */
const floorData = ref({})
/** 热销商品 */
const hotGoodslist = ref([])
/** 获取热销商品和banner数据 */
function getHotGoods(){
  showLoading.value = true
  shopApi.getSecondaryRecommendGoods(searchData).then(res=>{
    showLoading.value = false
    if(res.code){
      Toast(res.message)
    }else{
      if( res.data && res.data.goodsList ){
        hotGoodslist.value = res.data.goodsList
      }
      floorData.value = res.data
    }
  })
}
const goodslist= ref([])
const secondData = ref({})
const  notForst= ref(false)
const goodsOption = reactive({
  loading:false,
  finished:false
})
/** 获取商城商品 */
function getGoods(){
  showLoading.value = true
  shopApi.getSecondaryGoods(searchData).then(res=>{
    notForst.value = true
    showLoading.value = false
    if(res.code){
      Toast(res.message)
    }else{
      secondData.value = res.data
      if (searchData.pageNum == 1) {
        goodslist.value = []
      }
      goodslist.value = goodslist.value.concat(res.data.goodsList)
      goodsOption.loading = false
      if (goodslist.value && goodslist.value.length == 0) {
        goodsOption.finished = true
        return false
      }
      if (res.data.total > searchData.pageNum*searchData.pageSize) {
        searchData.pageNum++
      } else {
        goodsOption.finished = true
      }
    }
  })
}
function goImgLink(adLink){
  location.href = adLink
}
function insertCodeGoods(key,goodsId,source,url){
  let dcs_id = key+'_'+goodsId+'_'+source
  insertCode("goods_"+dcs_id,url)
}
let shopData = {}
const RESULTCODE={
  SUCCESS:0
}
const shopShare = ref({})
/** 获取店铺信息 */
function queryShopStaTus() {
  shopApi.getShopStatus({ shopId:searchData.shopId }).then((r) => {
    if(r.code==RESULTCODE.SUCCESS && r.data){
      shopData = r.data
      shopShare.value = {
        url: location.href,
        imgUrl: 'https://img0.shop.10086.cn/favicon.png__175.png',
        dcsId: 'yd_sharestore_' + searchData.shopId + '_share',
        desc: shopData.propaganda,
        title: shopData.shopShortName,
        unifiedChannelId:shopData.uniChannelId,
        address:shopData.address,
        shortName:shopData.shopShortName,
        provinceId:shopData.province
      }
    }
  })
}
/** 获取用户信息 */
let getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null
const user = ref(null)
if(proxy){
  user.value = proxy.$store.getters.user
}
const STAFFRULE = {
  CLERK:1,
  SHOPKEEPER:2
}
/** 是否展示分享按钮 */
const showShare = computed(()=>{
  if (user.value && user.value.userInfo) {
    return (
      user.value.userInfo.shopId == searchData.shopId &&
      (user.value.userInfo.RuleId == STAFFRULE.CLERK || user.value.userInfo.RuleId == STAFFRULE.SHOPKEEPER)
    )
  } else {
    return false
  }
})

/** 分享 */
function openShare(item){
  let share={
    ...item,
    title:item.goodsTitle,
    url:item.goodsLink,
    desc:item.goodsSubTitle || item.goodsLink,
    imgUrl:item.goodsSource==2?item.imgUrl :getImgUrl(item.imgUrl),
    price:item.price,
    isDoMeal:item.iopGoods
  }
  if(share.isDoMeal){ //iop数据
    share.url = shopShare.value.url
  }
  insertCode(getDcsId("yd_index", item) + "_share")
  share.dcsId = getDcsId("yd_sharegoods", item) + "_share"
  EventBus.$emit('openShareDialog' ,'goods' ,share)
  if (UA.isWechatWork) {
    EventBus.$emit('share')
  }
}
function getDcsId(dcsId, item) {
  let dcs_id = dcsId + "_" + searchData.shopId+ "_" + searchData.floorId
  if (item.goodsSource == 2) {
    dcs_id += "_yysp"
  } else if (item.goodsSource == 1) {
    dcs_id += "_zysp"
  } else {
    dcs_id += "_szsp"
  }
  return dcs_id + "_" + item.goodsId
}
</script>
<style lang="scss">
.secondary-list {
  .icpinfo {
    margin-bottom: 0 !important;
  }
}
</style>
<style lang="scss" scoped>
$bgcolor:#fff;
$commonpadding:15px;
.searchlist{
  background: $bgcolor;
  min-height: 100vh;
}
.padding-top-44{
  padding-top: 44px;
}

.header-nav{
  position: fixed;
  z-index: 1111111111;
}

.banner{
  width: 375px;
  height: 180px;
  background: url("https://img1.zz.ydsc.liuliangjia.cn/fs/goods/fs_65e03f8de4b07c253215c7cc.png") no-repeat;
  margin-bottom: 6px;
  img{
    width: 100%;
    height: 100%;
  }
}
// 推荐商品
$mo2: 2;
$red:#FA2C19;
// url("https://img1.shop.10086.cn/fs/goods/fs_65e98ce2e4b07358cc471e7a.hot_goods_bg.png") no-repeat top center

.index-home-recommend {
  padding:  51.5px 0 20px 0;
  background:
    url('~@/assets/home/<USER>') no-repeat center top ,
    url('~@/assets/home/<USER>') no-repeat center bottom ,
    url('~@/assets/home/<USER>') no-repeat center 60px ,
    ;
  background-size: 365px 60px, 365px 92px, 365px calc(100% - 152px);
  width:365px;
  margin:0 auto;
  .recommend-goods-list {
    display: flex;
    flex-flow: row wrap;
    padding: 0 9px 0;
    border-radius: 5px;
    position: relative;
    width:345px;
    margin:0 auto;
    box-sizing: border-box;
    li{
      width:159px;
      height:72px;
      padding:17px 10.5px;
      background: #EAF6FE;
      border-radius: 6px;
      position: relative;
      &:nth-child(2n+1){
        margin-right:9px;
        margin-bottom: 9px;
        background: #FFF3EF;
      }
      img{
        width:50px;
        height:38px;
        float:left;
        margin-right:7.5px;
        border-radius: 4px;
      }
      .title{
        font-weight: bold;
        font-size: 12px;
        color: #333333;
        overflow: hidden;
        text-overflow:ellipsis;
        white-space: nowrap;
        line-height: 16.5px;
        margin-bottom: 5px;
      }
      .price{
        font-weight: bold;
        font-size: 14px;
        line-height: 20px;
        color:$red;
      }
      .share-goods{
        bottom:15px;
        right: 12.5px;
        width: 18px;
        height: 18px;
      }
    }
  }
}
.goodslist-container{
  padding-bottom: 20px;
}
.goodslist-title{
  height: 45px;
  line-height: 45px;
  font-weight: bolder;
  font-size: 18px;
  color: #3C3C3C;
  margin:0 $commonpadding 8px;
}
.goods-container{
  background: #fff;
  width: 345px;
  padding:4px $commonpadding;
  background: #FFFFFF;
  box-shadow: 0 2px 13px -2px #667e8c4d;
  border-radius: 6px;
  margin:0 auto;
}
.goods-list{
  display: flex;
  // flex-direction: row;
  flex-flow:row wrap;
  .van-cell {
    padding:0;
    &::after{
      border:none;
    }
  }

  li{
    list-style: none;
    position: relative;
    font-size: 13px;
    line-height: 20px;
    background:#fff;
    padding:12.75px 0 7.75px;
    width:315px;
    border-bottom: 1px solid #EEEEEE;
    position: relative;
    img{
      width: 80px;
      height: 58.5px;
      float:left;
      margin-right:10px;
      border-radius: 4px;
    }
    p{
      overflow: hidden;
      text-overflow:ellipsis;
      white-space: nowrap;
    }
    p.title{
      height: 21px;
      line-height: 22px;
      font-size: 16px;
      font-weight: bold;
      font-size: 15px;
      color: #333333;
    }
    p.subtitle{
      height: 18px;
      line-height: 18px;
      font-size: 13px;
      font-weight: 400;
      text-align: left;
      color: #666;
    }
    p.price{
      font-size: 16px;
      color:$red;
      font-weight: bold;
      line-height: 22px;
      margin-top:4px;
      em{
        font-size:12px;
        margin-right:-2px;
      }
    }
    .share-goods{
      bottom:7.75px;
      right: 15px;
      width: 22px;
      height: 22px;
    }
  }
  :deep(.van-list__finished-text){
    width:315px;
  }
}
.goods-tab-no{
  width: 100%;
  padding: 100px 0 0;
  height:calc(100vh - 166px);
  text-align: center;
  &.is-app{
    height:calc(100vh - 131px);
  }
  .tab-no-con {
    img{
      display: block;
      height: 67.5px;
      width: auto;
      margin: 0 auto 7.5px;
    }
    p{
      display: inline-block;
      line-height: 22.5px;
      font-size: 13.5px;
      font-weight: 400;
      color:#757575;
    }
  }
}
.hr-style{
  width:110px;
  hr{
    margin-top:12px;
  }
}
.searchBottom{
  display:flex;
  padding:10px 15px;
  box-sizing: border-box;
}
.padding-top-50{
  padding-top:50px;
}

.share-goods{
  position: absolute;
  display: block;
  background: url(~@/assets/home/<USER>
  background-size: 100% auto;
  z-index: 2;
}

</style>
