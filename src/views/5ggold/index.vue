<template>
  <div>
    <!-- 优惠购机专用-已弃用 -->
    <Shopname
      v-if="goldData.showShop"
      content="【一级云店】优惠购机"
      :show-back="true"
    />
    <div
      :class="{
        'padding-top-34': goldData.showShop,
      }"
    >
      <div v-if="!goldData.showContract">
        <div v-if="goldData.notContract" class="contractContainer">
          <div class="notContractMsgImg">
            <img
              v-if="goldData.showErrImgType === 2"
              src="~@/assets/preferentialpurchase/404.png"
            />
            <img
              v-if="goldData.showErrImgType === 1"
              src="~@/assets/preferentialpurchase/noContract.png"
            />
          </div>
          <div class="notContractMsg">
            {{ goldData.notContractMsg }}
          </div>
          <div
            v-if="goldData.shareParam"
            class="btn goBackBtn"
            @click="goDetail"
          >
            返回
          </div>
        </div>
      </div>
      <div v-else class="contractContainer">
        <div class="item">
          <dl class="label">
            办理合约的手机号码
          </dl>
          <dd v-if="goldData.userName" class="activity">
            <p>{{ getPassPhone(goldData.userName) }}</p>
            <p>
              {{ goldData.provinceName }}
            </p>
          </dd>
          <dl class="label">
            购买方式
          </dl>
          <dd class="activity">
            <p>优惠购机</p>
          </dd>
          <dl class="label">
            活动方案
          </dl>
          <dd
            v-for="(item, index) in goldData.contractList"
            :key="index"
            :class="{ activity: index === activetyIndex }"
            @click="getCurrent(item, index)"
          >
            <p>{{ item.name }}</p>
            <p>在网{{ item.countMonth }}个月</p>
          </dd>
        </div>
        <div class="item">
          <dl>活动详情</dl>
          <dl v-if="goldData.currentContract" class="itemlabel">
            优惠档位名称：<span style="color: #333333">
              {{ goldData.currentContract.name }}
            </span>
          </dl>
          <dl v-if="goldData.currentContract" class="itemlabel">
            承诺在网时间：<span style="color: #333333">
              {{ goldData.currentContract.countMonth }}个月
            </span>
          </dl>
        </div>
        <div class="btn buyBtn" @click="purchaseQuliCheck">
          立即购买
        </div>
      </div>
    </div>
    <van-dialog
      v-model="goldData.showdeleteOrder"
      confirm-button-color="#5fade8"
      confirm-button-text="返回"
      show-cancel-button
      @confirm="goDetail"
    >
      <div class="dialog_title">
        抱歉~
      </div>
      <div class="dialog_content">
        您不具备办理该合约资格
      </div>
    </van-dialog>
  </div>
</template>

<script setup>
import {ENV} from "@/utils/env.js"
import Shopname from '@/components/index/headercon.vue'
import loginUtils from '@/utils/login/index.js'
import UA from '@/utils/ua.js'
import { getPassPhone } from "@/utils/utils.js"
import PreferentialpurchaseApi from '@/api/preferentialpurchase.js'
import provinceJson from '@/utils/province.js'
import Vue, { reactive, onMounted, getCurrentInstance } from 'vue'
import { Dialog } from 'vant'
Vue.use(Dialog)

let goldData = reactive({
  contractList: [],
  currentContract: null,
  userName: null,
  provinceName: null,
  activetyIndex: 0,
  provinceGoodsId: null,
  shareParam: null,
  showContract: false,
  notContract: false,
  showdeleteOrder: false,
  notContractMsg: '',
  showErrImgType: 2,
  proxy: null,
  showShop:
    !UA.isWechat &&
    !UA.isWechatWork &&
    !UA.isApp &&
    !UA.isIosQQ &&
    !UA.isAndroidQQ,
})
function logined(res) {
  goldData.userName = res ? res.UserName : null
  if (!goldData.userName) {
    showError('哎呦喂~ 走神了~')
    goldData.showErrImgType = 2
  } else {
    let province = provinceJson.data[res.userProvince + '_' + res.userCity]
    goldData.provinceName = province ? province.split('_')[0] : null
    getContract()
  }
}
function showError(message) {
  goldData.notContractMsg = message
  goldData.contractList = null
  goldData.notContract = true
}
function goDetail() {
  if (location.origin.indexOf('grey') !== -1) {
    location.href =
      'https://apollo.asiainfo.com/cnr-web/goodsDetail?shareParam=' +
      goldData.shareParam
  } else {
    location.href = `${ENV.getB2bDomain()}/cnr-web/goodsDetail?shareParam=` +
      goldData.shareParam

  }
}
function getContract() {
  PreferentialpurchaseApi.queryByProGoodsCode({
    provinceGoodsId: goldData.provinceGoodsId,
    // provinceGoodsId: "7788",
    type: 1, //优惠购机类型
  }).then((res) => {
    goldData.showContract = false
    if (res.code === 0 && res.data && res.data.length > 0) {
      goldData.contractList = res.data
      goldData.currentContract = res.data[0]
      goldData.activetyIndex = 0
      goldData.showContract = true
      document.getElementsByClassName('icpinfo')[0].style.marginBottom = '48px'
    } else {
      showError('啊哦~合约跑路了~~')
      goldData.showErrImgType = 1
    }
  })
}
function getCurrent(item, index) {
  goldData.currentContract = item
  goldData.activetyIndex = index
}
function purchaseQuliCheck() {
  PreferentialpurchaseApi.purchaseQuliCheck({
    contractId: goldData.currentContract.contractId,
    serialNo: goldData.serialNo,
  }).then((res) => {
    if (res.code) {
      goldData.showdeleteOrder = true
    } else {
      if (location.origin.indexOf('grey') !== -1) {
        location.href =
          'https://apollo.asiainfo.com/cnr-web/channelFillOrder?requestParam=' +
          res.data
      } else {
        location.href = `${ENV.getB2bDomain()}/cnr-web/channelFillOrder?requestParam=` +
              res.data
      }
    }
  })
}
onMounted(() => {
  let myUrl = new URL(location.href)
  goldData.provinceGoodsId = myUrl.searchParams.get('requestParam')
  goldData.shareParam = myUrl.searchParams.get('shareParam')
  goldData.serialNo = myUrl.searchParams.get('serialNo')
  if (!goldData.provinceGoodsId || !goldData.shareParam || !goldData.serialNo) {
    showError('哎呦喂~ 走神了~')
    goldData.showErrImgType = 2
    return false
  }
  let getVueInstance = getCurrentInstance()
  goldData.proxy = getVueInstance ? getVueInstance.proxy : null
  goldData.provinceGoodsId = goldData.provinceGoodsId.replace(/\s/g, '+')
  loginUtils.login(false, false, logined, false, false, logined, '0')
})
</script>

<style lang="scss" scoped>
.padding-top-34 {
  padding-top: 34px;
  background: #f9f9f9;
}
.contractContainer {
  min-height: calc(100vh - 160px);
  padding: 10px 0;
}
.item {
  padding: 15px 0 10px 0;
  background: #fff;
  margin-bottom: 10px;
  &:not(:first-child) {
    padding-top: 0;
    dl:first-child {
      margin-left: 0;
      height: 43px;
      padding-left: 20px;
      line-height: 43px;
      border-bottom: 1px solid#f7f7f7;
    }
  }
}
dl {
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  text-align: left;
  color: #999999;
  line-height: 20px;
  margin: 0 0 0px 20px;
  &:not(:first-child) {
    margin-top: 15px;
  }
}
dd {
  border: 1px solid #999999;
  border-radius: 13px;
  padding: 6px 17px;
  display: inline-block;
  margin: 10px 0 0px 20px;
  p {
    font-size: 13px;
    font-weight: 400;
    text-align: left;
    color: #999999;
    line-height: 18px;
    text-align: center;
  }
  &.activity {
    border: 1px solid #4187f9;
    p {
      color: #4389fa;
    }
  }
}
.itemlabel {
  font-size: 13px;
}
.btn {
  width: 375px;
  height: 48px;
  background: linear-gradient(270deg, #7368ff, #3fbeff);
  color: #fff;
  text-align: center;
  line-height: 48px;
  font-size: 14px;
  font-weight: 400;
}
.goBackBtn {
  width: 200px;
  border-radius: 20px;
  margin: 0 auto;
  height: 40px;
  line-height: 40px;
  font-size: 16px;
}
.buyBtn {
  position: fixed;
  bottom: 0;
}
.notContractMsg {
  margin: 20px auto 60px;
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #353535;
}
.notContractMsgImg {
  text-align: center;
  margin-top: 170px;
  img {
    width: 150px;
  }
}
@mixin dialog_font {
  text-align: center;
  font-weight: 600;
}
.dialog_title {
  font-size: 16px;
  color: #353535;
  padding: 37px 0 10px;
  @include dialog_font;
}
.dialog_content {
  font-size: 15px;
  color: #353535;
  padding: 5px 0 25px;
  color: #434343;
  @include dialog_font;
}
</style>
