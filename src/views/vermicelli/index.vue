<template>
  <main class="fensi">
    <!-- app下显示头部 -->
    <div v-if="arrayData.isApp" class="header">
      <Icon name="arrow-left" class="back" @click="back" />
      <span>粉丝专区</span>
    </div>
    <div class="shop-btn" @click="goShop">
      进店逛逛
    </div>
    <aside v-if="arrayData.isMember==1" class="wrapper">
      <!-- 名片 -->
      <div class="infocard">
        <div class="left">
          <div class="pic">
            <img
              class="shoplogo"
              src="@/assets/index_img/vipicon_infohead.png"
            />
          </div>
          <p>店铺粉丝</p>
        </div>
        <div class="right">
          <!-- 手机号 -->
          <p class="mobiletitle">
            <span>{{ arrayData.fanCardData.fansMobile }}</span>
            <span class="iconshare" @click="openShare"></span>
          </p>

          <!-- 编号 -->
          <p>NO.{{ arrayData.fanCardData.fansNumber }}</p>
          <div class="text">
            <!-- 店铺图标 -->
            <img
              class="shopIcon"
              src="@/assets/index_img/vipicon_add.png"
              alt=""
            />

            <!-- 店铺名称 -->
            <span class="shopName">{{ arrayData.fanCardData.shopName }}</span>

            <!-- 专属客服标识 -->
            <span
              v-if="arrayData.tplId == neimengTemplate"
              class="customer"
              @click="getCustomerCode"
            >
              <span class="radio">
                <img src="@/assets/index_normal/kefu.png" alt="" />
              </span>
            </span>

            <!-- 关注 -->
            <span class="attention" @click="cancelDialog">已关注</span>
          </div>
        </div>
      </div>

      <!-- 内容列表 -->
      <article class="list">
        <ul v-if="arrayData.cmDataList.length">
          <li
            v-for="item in arrayData.cmDataList"
            :key="item.id"
            @click="handlerBanner(item)"
          >
            <div class="img">
              <img :src="item.img" alt="" />
            </div>
          </li>
        </ul>

        <!-- 敬请期待提示 -->
        <div v-else class="empty">
          <img src="@/assets/index_normal/shoucang.png" alt="" />
          <span>精彩活动, 敬请期待</span>
        </div>
      </article>
    </aside>
    <van-dialog
      v-model="arrayData.showFollow"
      :show-confirm-button="false"
      :before-close="confirmShare"
      :close-on-click-overlay="true"
    >
      <div class="icon-container">
        <span class="iconfont icon-follow"></span>
        关注店铺
        <div class="close" @click="closeFollowDialog">
          ×
        </div>
      </div>
      <!-- 名片 -->
      <div class="infocard scaled">
        <div class="left">
          <div class="pic">
            <img
              class="shoplogo"
              src="@/assets/index_img/vipicon_infohead.png"
            />
          </div>
          <p>店铺粉丝</p>
        </div>
        <div class="right">
          <!-- 手机号 -->
          <p>{{ arrayData.user ? arrayData.user.UserName : "" }}</p>

          <div class="text">
            <!-- 店铺图标 -->
            <img
              class="shopIcon"
              src="@/assets/index_img/vipicon_add.png"
              alt=""
            />

            <!-- 店铺名称 -->
            <span class="shopName">{{ arrayData.shopName }}</span>
          </div>
        </div>
      </div>
      <div class="btn-container" @click="addMember">
        关注店铺，查看粉丝专区
      </div>
    </van-dialog>
    <!-- 分享组件 -->
    <Share v-if="arrayData.showShare" :shopshare="arrayData.share" />
  </main>
</template>

<script setup>
import Vue, { reactive, onMounted, getCurrentInstance } from 'vue'
import {
  Icon,
  Toast,
  Field,
  Cell,
  Form,
  Button,
  Dialog,
  Picker,
  Popup,
  Image as VanImage,
  NoticeBar,
} from 'vant'
import { getToken, removeLocalStorage } from "@/utils/login/localStorage.js"
import loginApi from "@/api/login"
import UA from '@/utils/ua'
import { getFansCmDataByCode } from '@/api/vermicelli'
import VipApi from '@/api/vip'
import { getImgUrl,getUrl } from '@/utils/utils'
import shopAPI from '@/api/shop'
import EventBus from '@/api/eventbus'
import insertCode from '@/utils/insertCode'
import Share from '@/components/index/share'
import shareUtilApi from "@/utils/share"
import _ from 'lodash'

const neimengTemplate = 3

Vue.use(Toast)
  .use(Field)
  .use(Cell)
  .use(Form)
  .use(Button)
  .use(Dialog)
  .use(Picker)
  .use(Popup)
  .use(VanImage)
  .use(NoticeBar)

const arrayData = reactive({
  /* 是否app内打开 */
  isApp: false,

  /* 粉丝卡片数据 */
  fanCardData: {},

  /* 内容列表数组 */
  cmDataList: [],

  /* 模板Id */
  tplId: 4,

  /* 内蒙模板Id */
  neimengTemplate,

  /* 分享数据 */
  share: {
    url: location.href,
    imgUrl: 'https://img0.shop.10086.cn/favicon.png__175.png',
    dcsId: 'yd_sharevermicelli_share',
    desc: "粉丝专区",
    title: "店铺名称",
    shortTitle: "店铺名称",
    isPoster:true
  },
  shopId: null,
  proxy:null,
  showShare:false,
  /* 展示关注店铺弹窗 */
  showFollow:false,
  isMember:false,
  shopName:""
})

/* 获取数据 */
async function getData() {
  // 查询是否是会员, 不是会员就跳转到首页
  let memnerRes = await VipApi.IsMember({
    shopId: arrayData.shopId,
  })
  arrayData.isMember = _.get(memnerRes, 'data.member', 0)

  if (arrayData.isMember != 1) {
    arrayData.showFollow = true
    getShopInfo()
  }else{
    showFollow()
  }
}

function getShopInfo(){
  shopAPI.getShopStatus({ shopId: arrayData.shopId }).then((statusRes) => {
    shopInfoData(statusRes)
  })
}

function showFollow(){
  Promise.all([
    shopAPI.getShopStatus({ shopId: arrayData.shopId }),
    getFansCmDataByCode({ shopId: arrayData.shopId })
  ]).then(([statusRes,FansCmData]) => {

    shopInfoData(statusRes)
    getFollowData(FansCmData)
  })
}

function shopInfoData(statusRes){
  if (!statusRes) {
    statusRes = {}
  }
  const {
    tplId,
    shopId,
    province,
    unifiedChannelId,
    address,
    shopShortName
  } = statusRes.data

  if (arrayData.proxy) {
    arrayData.proxy.$store.commit('SET_PAGEINFO', {
      tplId,
      shopId,
      address,
      provinceId:province,
      unifiedChannelId,
      shortName: shopShortName,
    })
  }
  arrayData.tplId = tplId
  arrayData.showShare = true
  arrayData.shopName = shopShortName
}

async function getFollowData(res){
  if (!(res.code == 0 && res.data)) {
    return
  }

  let {
    fansNumber,
    fansMobile,
    shopName,
    nickname,
    cmDataList,
    sharingCode,
    sharingQrCode,
    key
  } = res.data

  arrayData.fanCardData = {
    fansNumber,
    fansMobile,
    shopName,
    nickname,
  }
  arrayData.sharingCode = sharingCode
  arrayData.sharingQrCode = sharingQrCode
  arrayData.key = key
  arrayData.cmDataList =
    (cmDataList &&
      cmDataList.map &&
      cmDataList.map((item) => {
        return {
          ...item,
          img: getImgUrl(item.imgSrc),
        }
      })) ||
    []
}

function closeFollowDialog(){
  back({ home: true })
}

function confirmShare(action, done) {
  done(false)
}

function addMember(){
  VipApi.AddMemberInfo({
    shopId: arrayData.shopId,
    staffId: arrayData.key
  }).then(res => {
    if (res.code == 0) {
      getData()
      arrayData.showFollow = false
    } else{
      Toast(res.message)
      // this.closevip()
    }
  })
}


/* 返回按钮 */
async function back({ home }) {
  const isWXMapp = await UA.isWeChatMiniApp()
  if (isWXMapp) {
    window.wx.miniProgram.switchTab({ url: '/pages/home/<USER>' })
  } else {
    if (home) {
      window.location.href = '/yundian/index.html?shopId=' + arrayData.shopId
    } else {
      history.go(-1)
    }
  }
}


function logined(res) {
  arrayData.user = res
  let isLogined = res && res.UserName > ''

  // 登录成功回调
  if (isLogined) {
    getData()
  }
}

/* 初始化 */
function init() {
  document.querySelector('body').setAttribute('class', 'vermicelli')
  let keyArray = document.cookie.match(/ChannelControl=([^\s;]+)/i)
  const url = new URL(location.href)
  arrayData.key = keyArray ? keyArray[1] : null
  arrayData.shopId = url.searchParams.get('shopId')

  let tplId = url.searchParams.get('tplId')
  if (tplId) {
    arrayData.tplId = tplId
  }

  // 强登
  loginApi.getUserInfo(getToken(),"0",1).then((res)=>{
    if (res.code == 0) {
      logined(res.data)
    } else if (res.code == 1) {
      removeLocalStorage()
      return null
    }
  })
  // 页头隐藏
  if (UA.isApp || UA.isWechat) {
    arrayData.isApp = false
    let share = configShare()
    shareUtilApi.appShare(share)
  } else {
    arrayData.isApp = true
  }
}

/* 取消关注 */
function cancelDialog() {
  Dialog.confirm({
    title: '温馨提示',
    message: '确定取消关注店铺吗?',
    confirmButtonColor: '#5099fe',
    confirmButtonText: '确认取消',
    cancelButtonText: '我再想想',
  }).then(() => {
    VipApi.withdeaw({
      shopId: arrayData.shopId,
    }).then((res) => {
      if (res.code) {
        Toast(res.message)
      } else {
        back({ home: true })
      }
    })
  })
}

/* banner跳转 */
function handlerBanner(item) {
  if (item.goodsLink) {
    location.href = item.goodsLink
    return
  }

  location.href = item.link
}

function goShop(){
  back({ home: true })
}

/* 插码 */
function insertCodeGoods(key) {
  let dcs_id = key
  insertCode('yd_index_' + arrayData.shopID + '_' + dcs_id)
}

/* 弹出客服弹框 */
function getCustomerCode() {
  insertCodeGoods('managerwecom')
  if (!arrayData.sharingQrCode || !arrayData.sharingCode) {
    Toast('店铺暂未开通企微，您可以试试电话联系店长')
    return
  }

  shopAPI.getCustomerCode({ sharingCode: arrayData.sharingCode }).then((r) => {
    if (r.code) {
      Toast(r.message)
    } else {
      if (r.data.contactQrcode) {
        EventBus.$emit('openShareDialog', 'service', r.data.contactQrcode)
      } else if (
        location.origin.indexOf('grey.touch.10086.cn') &&
        r.data.qrCode
      ) {
        EventBus.$emit('openShareDialog', 'service', r.data.qrCode)
      } else {
        Toast('店铺暂未开通企微，您可以试试电话联系店长')
      }
    }
  })
}

function configShare(){
  let share = arrayData.share
  share.url = getUrl(`/hd/qd.html?url=${location.pathname}&key=${arrayData.key}&shopId=${arrayData.shopId}`)
  insertCode("yd_vermicelli_share")
  share.title = arrayData.shopName
  return share
}
//分享页面
function openShare() {
  let share = configShare()
  let isExecuteNow = UA.isWechatWork
  EventBus.$emit("openShareDialog", "shop", share, isExecuteNow)
}
const getVueInstance = getCurrentInstance()
arrayData.proxy = getVueInstance ? getVueInstance.proxy : null
onMounted(()=> {
  init()
  if (arrayData.proxy) {
    arrayData.proxy.$store.commit("SET_SHOPID", arrayData.shopId)
  }

})
</script>

<style lang="scss">
body {
  background: #f7f8f9;
  min-height: 100vh;

  &.vermicelli {
    display: flex;
    flex-direction: column;

    #app {
      flex: 1;
      min-height: 0;
    }

    .icpinfo {
      margin-bottom: 0 !important;
    }
  }
}
</style>
<style lang="scss" scoped>
.header {
  width: 100%;
  height: 44px;
  line-height: 44px;
  background: #fff;
  text-align: center;
  font-size: 18px;
  position: relative;

  .back {
    position: absolute;
    left: 10px;
    bottom: 13px;
  }

  span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    text-align: center;
    padding: 0 60px;
  }
  .iconfont{
    position: absolute;
    right:10px;
    top:0;
    padding:0;
  }
}

.infocard {
  width: 345px;
  height: 122px;
  background: url(~@/assets/index_img/vipinfocardbg.png) 0 0 no-repeat;
  background-size: 100% 100%;
  margin: 0 auto;
  margin-top: 15px;

  .left {
    width: 108px;
    height: 122px;
    float: left;

    .pic {
      width: 73px;
      margin: 0 0 0 22px;
      padding-top: 8px;
      text-align: center;

      img {
        width: 73px;
        height: 83px;
      }
    }

    p {
      margin: 2px auto 0;
      text-align: center;
      font-size: 13px;
      color: #e7b65a;
    }
  }

  .right {
    width: 237px;
    float: left;
    position:relative;
    p:nth-child(1) {
      padding-top: 18px;
      height: 48px;
      line-height: 30px;
      font-size: 22px;
      color: #a98f48;
    }

    p:nth-child(2) {
      padding-top: 9px;
      height: 29px;
      line-height: 20px;
      font-size: 14px;
      color: #e1be6d;
    }

    .text {
      display: flex;
      align-items: center;
      padding-top: 12px;
      padding-right: 11px;

      .shopIcon {
        width: 16px;
        height: 15px;
      }

      .shopName {
        font-size: 14px;
        color: #a38b53;
        flex: 1;
        min-width: 0;
        padding-left: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .customer {
        padding-right: 6px;
        margin-right: 6px;
        padding-left: 3px;

        .radio {
          background: #ffa622;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          padding: 3px;
        }

        img {
          width: 12px;
        }
      }

      .attention {
        border: 1px solid #e7b65a;
        border-radius: 11px;
        background: rgba(255, 255, 255, 0.71);
        font-size: 12px;
        color: #e1be6d;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 3px 10px;
      }
    }
  }
}

.list {
  ul {
    padding: 0 15px;
    margin-bottom: 40px;

    li {
      margin-top: 10px;
      border-radius: 8px;
      overflow: hidden;

      .img {
        height: 150px;

        img {
          width: 100%;
          height: 100%;
          display: block;
        }
      }
    }
  }
}

.fensi {
  display: flex;
  flex-direction: column;

  .wrapper {
    flex: 1;
    min-width: 0;
  }
}

.empty {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 132px;

  img {
    width: 38px;
  }

  span {
    color: #5e5e5e;
    font-size: 14px;
    margin-left: 10px;
  }
}
.scaled{
  transform: scale(0.8);
  transform-origin: 32% 50%;
}
$blue:#5b8be6;
.icon-container{
  background: $blue;
  color:#fff;
  height:60px;
  line-height: 60px;
  padding-left: 15px;
  position: relative;
  font-size:16px;
  .close{
    position:absolute;
    right:10px;
    top:0;
    font-size: 24px;
  }
}
.btn-container{
  width:300px;
  height:40px;
  line-height: 40px;
  font-size: 12px;
  background: $blue;
  color:#fff;
  text-align: center;
  margin:60px auto 10px;
  border-radius: 20px;
}
@keyframes btn_animation{
    from {right:-100%}
    to {right:0}
}
.shop-btn{
  position:fixed;
  right:0;
  top:220px;
  background: #659aff url("https://img1.zz.ydsc.liuliangjia.cn/fs/goods/fs_6532021be4b07c253215c76f.shopicon.png") no-repeat left;
  color:#fff;
  font-size:14px;
  padding:0 20px 0 50px;
  height:40px;
  line-height: 40px;
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
  background-size: contain;
  animation: btn_animation 0.5s linear;
}
.mobiletitle{
  display: flex;
  align-items: center;
  .iconshare{
    width:20px;
    height:20px;
    margin-left: 4px;
    display: block;
    background: url(https://img1.shop.10086.cn/fs/goods/fs_62d4c68de4b0b77767875c4a.share.png);
    background-size:contain;
  }
}
</style>
