export default [
  {
    path: '/broadband/configure.html',
    name: 'BroadbandConfigure',
    component: resolve => require(['./configure.vue'], resolve),
    meta: {
      title: '宽带预约配置',
      login:true,
      role:[1,2]
    }
  },
  {
    path: '/broadband/index.html',
    name: 'BroadbandIndex',
    component: resolve => require(['./index.vue'], resolve),
    meta: {
      title: '宽带预约',
      login:true,
      role:[0,1,2]
    }
  },
  {
    path: '/broadband/usersidelist.html',
    name: 'UsersideList',
    component: resolve => require(['./usersidelist.vue'], resolve),
    meta: {
      title: '预约专区',
      login:true,
      role:[0,1,2]
    }
  },
  {
    path: '/broadband/usersidedetail.html',
    name: 'UsersideDetail',
    component: resolve => require(['./usersidedetail.vue'], resolve),
    meta: {
      title: '预约',
      login:true,
      role:[0,1,2]
    }
  },
  {
    path: '/broadband/list.html',
    name: 'BroadbandList',
    component: resolve => require(['./broadbandlist.vue'], resolve),
    meta: {
      title: '预约单列表',
      keepAlive:true,
      login:true,
      role:[1,2]
    }
  },
  {
    path: '/broadband/details.html',
    name: 'BroadbandDetail',
    component: resolve => require(['./details.vue'], resolve),
    meta: {
      title: '预约详情',
      login:true,
      role:[0,1,2] //游客自己的宽带预约详情和店长看的预约详情共用一个页面，页面里有权限限制
    }
  }
]
