<template>
  <div class="body">
    <div v-if="data.isShow" class="header">
      <van-icon name="arrow-left" class="back" @click="back" />
      {{ data.title }}
    </div>
    <div v-if="data.hasShop">
      <div v-if="data.booking" class="mt10 booking">
        <van-form :label-width="100" @submit="onSubmit">
          <van-field
            v-model="data.username"
            name="appointmentName"
            label="预约人姓名"
            placeholder="请输入预约人姓名"
            :maxlength="32"
            :error-message="data.ErrorMessage1"
            required
            colon
            label-width="100px"
          />
          <van-field
            v-model="data.tel"
            name="appointmentMobile"
            label="预约手机号"
            type="tel"
            required
            placeholder="请填写预约手机号"
            :error-message="data.ErrorMessage2"
            colon
            label-width="100px"
          />
          <van-field
            readonly
            clickable
            name="areaText"
            :value="data.areaText"
            label="预约地址:"
            required
            :error-message="data.ErrorMessage3"
            placeholder="点击选择省市区"
            @click.prevent="onClickArea"
          />
          <van-field
            v-model="data.appointmentAddress"
            name="appointmentAddress"
            type="text"
            label=" "
            placeholder="请填写安装地址"
            :error-message="data.ErrorMessage4"
            :maxlength="60"
            label-width="100px"
          />
          <div v-if="data.tplContent" v-myhtml="data.tplContent" class="tplContent rich"></div>
          <!-- <div v-html="tplContent" class="tplContent rich"></div> -->
          <div style="padding: 16px;position:fixed;width:100%;bottom:0;background:#fff">
            <van-button round block type="info" native-type="submit" color="linear-gradient(to right, #60C7FF, #8374FE)">
              立即预约
            </van-button>

          </div>
        </van-form>
      </div>
      <div v-if="data.booked" class="mt10 Booked">
        <h1>预约成功</h1>
        <p>您的预约单号：{{ data.OrderID }}
          <br>请保持电话畅通，我们会尽快与您联系</p>
        <div class="bts">
          <van-button round plain hairline type="info" @click="gotohome">
            返回店铺首页
          </van-button>
          <van-button round plain hairline type="info" @click="gotoorder">
            查看预约信息
          </van-button>
        </div>
      </div>
      <popup-area
        v-if="data.province"
        ref="area"
        :province="data.province"
        :city="data.city"
        :is-open="isOpen"
        @close="close"
        @confirm="onAreaConfirm"
      />
    </div>
    <div v-else class="no-shop">
      找不到该页面
    </div>
  </div>
</template>

<script setup>
import { Icon, Toast ,Field ,Cell ,Form,Button ,Dialog} from "vant"
import Vue,{ref,reactive,onMounted,getCurrentInstance} from "vue"
import loginUtils from "@/utils/login/index.js"
import UA from "@/utils/ua.js"
import {setScrollTop} from "@/utils/utils.js"
import popupArea from "@/components/popup-area.vue"
import broadbandApi from "@/api/broadband.js"
import shopApi from "@/api/shop.js"
Vue.use(Icon)
  .use(Toast)
  .use(Field)
  .use(Cell)
  .use(Form)
  .use(Button)
  .use(Dialog)
let data = reactive({
  title: "宽带预约",
  isShow:false,
  tel: '',
  username: '',
  appointmentAddress:'',
  pattern:/^1\d{10}$/,
  ErrorMessage1:'',
  ErrorMessage2:'',
  ErrorMessage3:'',
  ErrorMessage4:'',
  value: '',
  showArea: false,
  areaList: {}, // 数据格式见 Area 组件文档
  province:null,
  city: null,
  addressInfo:{},
  areaText:null,
  referrerSecretStr:null,
  tplInfo:null,
  shopId:null,
  OrderID:null,
  booking:true,
  booked:false,
  disabled:true,
  sourceUrl:null,//链接来源
  showBroadband:false,//是否显示宽带模块
  hasShop:true,
  tplContent: null,
  flag:false
})
let logined = (res)=> {
  data.user = res
  data.isLogined = res && res.UserName > ""
  if (data.isLogined) {
    getShopDetail()
  }
}
let validator = (val)=> {
  return /^[^\n\s]+$/.test(val)
}
let back = async()=> {
  const isWXMapp = await UA.isWeChatMiniApp()
  if(isWXMapp){
    window.wx.miniProgram.switchTab({url: '/pages/home/<USER>'})
  }else{
    history.go(-1)
  }
}
let area = ref(null)
let onClickArea = ()=> {
  setScrollTop()
  open()
}
const isOpen = ref(false)
function open() {
  isOpen.value = true
}
function close(){
  isOpen.value = false
}

let onSubmit = (values)=> {
  if (data.flag) {
    Toast("您点的太快啦~")
    return false
  }
  if(!values.appointmentName){
    data.ErrorMessage1 = "请填写预约人姓名"
    return false
  }else{
    data.ErrorMessage1 = ""
  }
  if(!values.appointmentMobile){
    data.ErrorMessage2 = "请填写预约手机号"
    return false
  }else if(!data.pattern.test(values.appointmentMobile)){
    data.ErrorMessage2 = "请填写正确的手机号"
    return false
  }else{
    data.ErrorMessage2=""
  }
  if(!values.areaText){
    data.ErrorMessage3 = "点击选择省市区"
    return false
  }else{
    data.ErrorMessage3 = ""
  }
  if(!values.appointmentAddress){
    data.ErrorMessage4 = "请填写安装地址"
    return false
  }else{
    data.ErrorMessage4 = ""
  }
  values.appointmentAddress=values.appointmentAddress.replace(/[\n|\s]+/g,'')
  values.referrerSecretStr = data.referrerSecretStr ? data.referrerSecretStr.replace(/\s/g,'+') : ''
  values.templateId = data.tplInfo && data.tplInfo.templateId
  values.shopId = data.shopId
  values.district = data.addressInfo.regionName
  data.flag = true
  broadbandApi.broadbandSubmit(values).then(res=>{
    data.flag = false
    if(res.code){
      Dialog({
        title:"提示",
        message:res.message,
        confirmButtonColor:"#0085D0",
        confirmButtonText:"我知道了",
      })
    }else{
      data.OrderID = res.data.orderId
      data.booking = false
      data.booked = true
    }
  })

}
let onAreaConfirm = (param)=> {
  data.addressInfo.provinceId = data.province.provinceId
  data.addressInfo.province = data.province.provinceName
  data.addressInfo.cityId = data.city.cityId
  data.addressInfo.city = data.city.cityName
  data.addressInfo.regionCode = param.regionCode
  data.addressInfo.regionName = param.regionName
  data.areaText = data.province.provinceName + " " +data.city.cityName+ " " +param.regionName
}
let getShopDetail = ()=>{
  shopApi.queryShopInfo({shopId:data.shopId}).then(res=>{
    if(res.code){
      data.hasShop = false
    }else{
      data.province = {
        provinceId: res.data.province,
        provinceName: res.data.provinceName
      }
      data.city = {
        cityId: res.data.city,
        cityName: res.data.cityName
      }

      getDataYhb()
    }

  })
}
let getDataYhb = ()=> {
  Promise.all([
    broadbandApi.queryShopTmpl({shopId:data.shopId,type:1}),
    broadbandApi.getBroadbandStatus({shopId:data.shopId}),
  ]).then(([bdShopTmpl, bdStatus]) => {
    if(bdShopTmpl.data) {
      data.tplInfo = bdShopTmpl.data
      data.tplContent = bdShopTmpl.data.content

      if(bdShopTmpl.data.status){
        data.disabled = bdShopTmpl.data.status!==1
      }else{
        data.disabled=true
      }
    }
    if(bdStatus.code){
      Toast(bdStatus.message)
    }else{
      data.showBroadband = bdStatus.data.broadbandStatus==1?true:false
    }
  })
}
let gotohome = async()=> {
  const isWXMapp = await UA.isWeChatMiniApp()
  if(isWXMapp){
    window.wx.miniProgram.switchTab({url: '/pages/home/<USER>'})
  }else{
    window.location.href = "/yundian/index.html?shopId="+data.shopId
  }
}
let gotoorder = ()=> {
  //window.location.href = "/yundian/my/broadband.html?shopId="+data.shopId
  window.location.href = "/yundian/broadband/details.html?tplId=roleUser&orderId="+data.OrderID
}
onMounted(()=>{
  const url = new URL(location.href)
  const key = url.searchParams.get('key')
  data.shopId = url.searchParams.get('shopId')
  data.sourceUrl = url.searchParams.get('sourceUrl')
  data.referrerSecretStr = key
  const getVueInstance = getCurrentInstance()
  const proxy = getVueInstance ? getVueInstance.proxy : null
  if (proxy) {
    proxy.$store.commit("SET_SHOPID",data.shopId)
  }
  loginUtils.login(true,true,logined,false,false,'',"0")
  data.isShow = UA.isApp || UA.isWechat ? false :true
})

</script>
<style>
body {
  background: #f6f7f9 !important;
  font-size: 12px;
  min-height: 101vh;
}
.van-overlay{
  min-height: 102vh;
}
.van-dialog__header{
  font-size:18px;
  font-weight: bold;
}
.van-dialog__message{
  font-size:14px;
}
.icpinfo{
  margin-bottom:80px;
}
</style>
<style lang="scss" scoped>
.body{
  min-height: calc(100vh - 73px);
  .header {
    width: 100%;
    height: 44px;
    line-height: 44px;
    background: #fff;
    text-align: center;
    font-size: 18px;
    position: relative;
    .back {
      position: absolute;
      left: 10px;
      bottom: 13px;
    }
  }
  .mt10{
    margin-top:10px;
  }
  .Booked{
    padding:22px 0;
    background:#fff;
    h1{
      height: 53px;
      line-height: 53px;
      background:url(~@/assets/index_img/icon_booked.png) 99px 0 no-repeat #fff;
      background-size:53px 53px;
      text-indent: 170px;
      font-size: 26px;
      color:#0FC81B
    }
    p{
      line-height: 37.5px;
      font-size: 14.25px;
      text-align: center;
      color: #a2a2a2;
      padding-top: 10.875px;
    }
    .bts{
      text-align: center;
      margin-top:17px;
      .van-button{
        margin:0 5px;
      }
    }

  }
}
.tplContent{
  padding:18.75px;
  font-size:12px;
  background: #fff;
  padding-bottom:75px;
}
.no-shop{
  margin:150px 0;text-align:center
}
</style>
