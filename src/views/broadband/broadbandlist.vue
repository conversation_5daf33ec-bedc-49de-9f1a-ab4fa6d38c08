<template>
  <div class="broadband">
    <header-nav :title="data.title"></header-nav>
    <div class="couponMain">
      <van-icon
        v-if="data.hasrole"
        name="filter-o"
        class="searchbt"
        size="18"
        @click="data.showSearch = true"
      />
      <van-tabs v-model="data.bdName" :before-change="beforeChange">
        <template>
          <BroadbandTab
            :floor="data.tabTitle"
            :items="data.tabMain"
            tpl-id="shop"
            :ruleid="data.ruleId"
            :refresh="refresh1111"
            :nodata-message="data.nodataMessage"
            :loading="data.loading"
            :finished="data.finished"
            :onload="getBroadbandList"
          />
          <!-- {{ item }} -->
        </template>
      </van-tabs>
    </div>
    <van-action-sheet v-model="data.showSearch" closeable :round="false">
      <div class="nav">
        <van-form
          ref="searchForm"
          :label-width="100"
          @submit="search('search')"
        >
          <div style="display: flex">
            <van-button
              plain
              block
              type="info"
              style="margin-right: 187.5px"
              native-type="button"
              color="linear-gradient(to right, #60C7FF, #8374FE)"
              @click="reset"
            >
              重置
            </van-button>
            <van-button
              plain
              block
              type="info"
              native-type="submit"
              color="linear-gradient(to right, #60C7FF, #8374FE)"
            >
              确定
            </van-button>
          </div>
          <van-field
            v-model="data.sendData.orderId"
            name="orderId"
            type="tel"
            label="预约单号"
            placeholder="请填写预约单号"
            colon
            label-width="100px"
          />
          <van-field
            v-model="data.sendData.appointmentMobile"
            name="appointmentMobile"
            label="预约手机号"
            type="tel"
            placeholder="请填写预约手机号"
            colon
            label-width="100px"
          />
          <van-field
            v-if="data.ruleId == 2"
            v-model="data.sendData.staffNumber"
            name="staffNumber"
            label="推荐人工号"
            type="text"
            placeholder="请填写推荐人工号"
            colon
            label-width="100px"
          />
          <van-field
            v-if="data.ruleId == 2"
            v-model="data.sendData.staffMobile"
            name="staffMobile"
            label="推荐人手机号"
            type="tel"
            colon
            placeholder="请填写推荐人手机号"
            label-width="100px"
          />
          <van-field
            readonly
            clickable
            name="createTimeStart"
            :value="data.createTimeStartName"
            label="起始时间"
            colon
            placeholder="点击选择时间"
            @click.prevent="openSetTime('start')"
          />
          <van-field
            readonly
            clickable
            name="createTimeEnd"
            :value="data.createTimeEndName"
            label="截止时间"
            colon
            placeholder="点击选择时间"
            @click.prevent="openSetTime('end')"
          />
        </van-form>
      </div>
    </van-action-sheet>
    <van-action-sheet v-model="data.showpicker" closeable :round="false">
      <div class="nav">
        <van-datetime-picker
          v-if="data.showPickerStart"
          v-model="data.createTimeStart"
          type="date"
          title="选择起始时间"
          :min-date="data.minDate"
          :max-date="data.maxDate"
          @confirm="confirmstart"
          @cancel="
            data.showPickerStart = false
            data.showpicker = false
          "
        />
        <van-datetime-picker
          v-if="data.showPickerEnd"
          v-model="data.createTimeEnd"
          type="date"
          title="选择截止时间"
          :min-date="data.minDate"
          :max-date="data.maxDate"
          @confirm="confirmEnd"
          @cancel="
            data.showPickerEnd = false
            data.showpicker = false
          "
        />
      </div>
    </van-action-sheet>
    <Footer />
  </div>
</template>

<script setup>
import headerNav from "@/views/my/components/headerNav"
import loginUtils from "@/utils/login"
import getBroadband from "@/api/broadband"
import {
  Toast,
  Tab,
  Tabs,
  Field,
  Form,
  Button,
  DatetimePicker,
  ActionSheet,
  Icon,
} from "vant"
import "vant/lib/index.css"
import Vue,{onMounted, reactive,getCurrentInstance} from "vue"
import BroadbandTab from "@/components/broadband/broadbandTab.vue"
import {setScrollTop,parseTime} from "@/utils/utils"
Vue.use(Field)
  .use(Toast)
  .use(Tab)
  .use(Tabs)
  .use(Form)
  .use(Button)
  .use(DatetimePicker)
  .use(ActionSheet)
  .use(Icon)
const STATETEXT = [
  {code:null,text:"全部"},
  {code:"TOC",text:"待确认"},
  {code:"MAS",text:"预约成功"},
  {code:"MAF",text:"预约失败"},
  {code:"HBC",text:"已取消"}
]
//TOC 待确认 MAS 预约成功 MAF 预约失败 HBC 已取消
let data = reactive({
  title: "预约单列表",
  showSearch: false,
  showpicker: false,
  activeNames: ["1"],
  user: null,
  info: "",
  tabTitle: ["全部", "待确认", "预约成功", "预约失败", "已取消"],
  tabMain: [],
  bdName: 0,
  minDate: new Date(2020, 0, 1),
  maxDate: new Date(2120, 0, 1),
  createTimeStart: new Date(),
  createTimeStartName: "",
  createTimeEnd: new Date(),
  createTimeEndName: "",
  showPickerStart: false,
  showPickerEnd: false,
  sendData: {
    shopId: null,
    state: null,
    createTimeStart: null,
    createTimeEnd: null,
    staffId: null,
    pageNo: 1,
    pageSize: 20,
  },
  nodataMessage: "",
  ruleId: null,
  hasrole: true,
  loading: false,
  finished: false,
  shopId: null,
})
let logined = (res) => {
  data.user = res
  // console.log(res)
  if (data.user && res.shopId) {
    data.sendData.shopId = res.shopId
    data.ruleId = res.RuleId
    if (data.ruleId == 1) {
      data.sendData.staffId = res.staffId
      data.sendData.stafferId = res.stafferId
    }
    getBroadbandList()
    data.hasrole = true
  } else {
    data.hasrole = false //游客看不了
    data.tabMain = []
    data.nodataMessage = "无权查看，请去自己的店铺"
  }
}
let beforeChange = (index) => {
  data.tabMain = []
  //TOC 待确认 MAS 预约成功 MAF 预约失败 HBC 已取消
  data.sendData.state = null
  // 返回 false 表示阻止此次切换
  if (index === 5) {
    return false
  }
  data.sendData.state = STATETEXT[index].code
  data.sendData.pageNo = 1
  data.finished = false
  getBroadbandList()
  // 返回 Promise 来执行异步逻辑
  return new Promise((resolve) => {
    // 在 resolve 函数中返回 true 或 false
    resolve(index + 1)
  })
}
let search = (value) => {
  if(data.createTimeStart>data.createTimeEnd){
    Toast("截止日期不能早于起始日期")
    return false
  }
  data.showSearch = false
  data.sendData.pageNo = 1
  data.finished = false
  getBroadbandList(value)
}
let reset = () => {
  data.sendData = {
    shopId: data.sendData.shopId,
    staffId: data.sendData.staffId,
    pageSize: data.sendData.pageSize,
    state: null,
  }
  data.createTimeStartName = ""
  data.createTimeEndName = ""
  data.createTimeStart = new Date()
  data.createTimeEnd = new Date()
  // data.search()
}
let refresh1111 = ()=> {
  data.sendData.pageNo = 1
  data.finished = false
  getBroadbandList()
}
let getBroadbandList = (value) => {
  if (!data.hasrole) {
    return false
  }
  data.loading = true
  getBroadband.getShopBAOrderList(data.sendData).then((res) => {
    if (res.code == 0) {
      if (data.sendData.pageNo == 1) {
        data.tabMain = []
      }
      data.tabMain = data.tabMain.concat(res.data.orderList)
      data.loading = false
      if (data.tabMain && data.tabMain.length == 0) {
        if (data.sendData.state == null && value != "search") {
          data.nodataMessage = "暂无预约列表信息"
        } else {
          data.nodataMessage = "没有搜索到您要的信息"
        }
        data.finished = true
        return false
      }
      if (res.data.totalPages > res.data.pageNo) {
        data.sendData.pageNo++
      } else {
        data.nodataMessage = "没有更多了"
        data.finished = true
      }
    } else {
      Toast(res.message)
    }
  })
}
let confirmstart = (time) => {
  data.createTimeStart = time
  data.createTimeStartName = parseTime(time, "{y}/{m}/{d}")
  data.sendData.createTimeStart = parseTime(
    time,
    "{y}/{m}/{d} {h}:{i}:{s}"
  )
  data.showPickerStart = false
  data.showpicker = false
}
let confirmEnd = (time)=> {
  data.createTimeEnd = time
  let setData = new Date(new Date(time).getTime() + 24 * 60 * 60 * 1000 - 1)
  data.createTimeEndName = parseTime(setData, "{y}/{m}/{d}")
  data.sendData.createTimeEnd = parseTime(
    setData,
    "{y}/{m}/{d} {h}:{i}:{s}"
  )
  data.showPickerEnd = false
  data.showpicker = false
}
let  openSetTime = (type)=> {
  if (type == "start") {
    data.showPickerStart = true
    data.showPickerEnd = false
  } else {
    data.showPickerStart = false
    data.showPickerEnd = true
  }
  data.showpicker = true
  setScrollTop()
}
onMounted(()=>{
  const url = new URL(location.href)
  data.shopId = url.searchParams.get("shopId")
  const getVueInstance = getCurrentInstance()
  const proxy = getVueInstance ? getVueInstance.proxy : null
  if (proxy) {
    proxy.$store.commit("SET_SHOPID",data.shopId)
  }
  loginUtils.login(true, true, logined, false, false, "", null, 5)
})

</script>
<style>
.header-nav {
  height: 52.5px !important;
}
.header-nav__title {
  font-size: 18.75px !important;
}
.broadband .van-tabs__wrap {
  background: #fff;
}
.broadband .van-tabs__nav--line {
  width: 90%;
}
</style>
<style lang="scss" scoped>
ul {
  li {
    margin: 0;
    padding: 0;
    list-style: none;
  }
}
.broadband {
  // min-height: 101vh;
  min-height: calc(101vh - 73px);

  .nav {
    padding: 0;
    background: #ffffff;
    .van-search {
      width: 355px;
      height: 36px;
      background: #f7f7f7;
      border-radius: 21px;
      margin: 0 auto;
      .van-search__content {
        background: none;
        padding-left: 0;
      }
    }
  }
  .searchbt {
    position: absolute;
    right: 10px;
    top: 12px;
    z-index: 1;
    border-left: 2px solid #979797;
    padding-left: 5px;
  }
  .couponMain {
    position: relative;
    .tabTilte {
      width: 100%;
      display: flex;
      li {
        flex: 1;
        line-height: 40px;
        font-size: 16px;
        text-align: center;
        border-bottom: #fff 1px solid;
        color: #333;
      }
      .active {
        border-bottom-color: #ff7300;
        color: #ff7300;
      }
    }
    .tabTitCon {
      width: 100%;
      background: #e2e1e1;
      display: flex;
      li {
        flex: 1;
        line-height: 40px;
        font-size: 12px;
        text-align: center;
        color: #333;
      }
      .conactive {
        color: rgb(255, 115, 0);
      }
    }
  }
  .tabContentCon {
    text-align: center;
    font-size: 13.5px;
    line-height: 30px;
    color: #999;
    img {
      display: inline-block;
      width: 75px;
      padding-top: 37.5px;
    }
  }
}
</style>
