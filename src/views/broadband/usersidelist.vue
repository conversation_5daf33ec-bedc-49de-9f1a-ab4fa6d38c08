<template>
  <main class="usersidelist">
    <!-- app下显示头部 -->
    <div v-if="data.isApp" class="header">
      <van-icon name="arrow-left" class="back" @click="back" />
      {{ data.pongeTitle }}
    </div>

    <!-- banner -->
    <section class="banner">
      <a href="javascript:;">
        <van-image
          width="100%"
          height="100%"
          fit="cover"
          :src="data.sectionImgSrcLink"
        />
      </a>
    </section>

    <!-- 列表 -->
    <section class="list">
      <ul>
        <li
          v-for="item in data.dataList"
          :key="item.templateId"
          @click="handleDetail(item)"
        >
          <a href="javascript:;">
            <div class="img">
              <van-image
                width="100%"
                height="100%"
                fit="cover"
                :src="item.optionImgSrcLink"
              />
            </div>
            <div class="text">
              <div class="title">{{ item.optionName }}</div>
              <div :class="['btn', !item.isSuccess ? 'hui' : '']">
                {{ item.buttonName }}
              </div>
            </div>
          </a>
        </li>
      </ul>
    </section>
  </main>
</template>

<script setup>
import broadbandApi from '@/api/broadband'
import loginUtils from '@/utils/login'
import UA from '@/utils/ua'
import lodash from 'lodash'
import { Image as VanImage, Icon } from 'vant'
import Vue , {ref,reactive,onMounted,getCurrentInstance} from 'vue'
import {setAcId , getSearchParams} from "@/utils/utils"
Vue.use(Icon)
let data = reactive({
  sectionImgSrcLink: null,
  dataList: [],

  /* 是否app内打开 */
  isApp: false,

  pongeTitle: '',
})
// type：1 宽带 2终端 3云店聚合页
const TYPECONF = {
  BROADBAND:1,
  TERMINAL:2,
  AGGREGATION:3
}
const getVueInstance = getCurrentInstance()
const proxy = getVueInstance ? getVueInstance.proxy : null
/* 初始化 */
const init = async()=> {
  const url = new URL(location.href)
  data.referrerSecretStr = url.searchParams.get('key')
  data.shopId = url.searchParams.get('shopId')

  if (!data.shopId) {
    data.shopId = url.searchParams.get('goods_shop_id')
  }

  data.sourceUrl = url.searchParams.get('sourceUrl')
  if (proxy) {
    proxy.$store.commit("SET_SHOPID",data.shopId)
    //判断是小程序
    const isWXMapp = await UA.isWeChatMiniApp()
    if (isWXMapp) {
      //修改ac_id
      const newUrl = new URL(window.location.href)
      newUrl.searchParams.set('WT.ac_id', 'SHOP_APPLET_TRANSACT')
      window.history.replaceState({}, '', newUrl.toString())
      proxy.$cookies.set('ac_id', 'SHOP_APPLET_TRANSACT' , { 'path': '/' })
    }
  }
  // 强登
  loginUtils.login(
    true,
    true,
    (res) => {
      data.user = res
      data.isLogined = res && res.UserName > ''

      // 登录成功回调
      if (data.isLogined) {
        getData()
      }
    },
    false,
    false,
    '',
    '0'
  )

  // 页头隐藏
  data.isApp = (UA.isApp || UA.isWechat) ? false : true
}

const getData = ()=> {
  broadbandApi
    .getOnlineSection({
      shopId: data.shopId,
    })
    .then((res) => {
      data.sectionImgSrcLink = lodash.get(
        res,
        'data.sectionImgSrcLink',
        null
      )

      data.pongeTitle = lodash.get(res, 'data.title', '预约专区')
      document.title = data.pongeTitle

      data.key = lodash.get(res, 'data.key', '')

      /* 返回的列表里面的type
        type：1 宽带 2终端 3云店聚合页
    */
      let dataList = lodash.get(res, 'data.optionList', [])

      let promiseAll = dataList.map((item) => {
        if(item.type == TYPECONF.AGGREGATION){
          item.isSuccess = true
          return null
        }
        return broadbandApi.queryShopTmplGoods({
          templateId: item.templateId,
        })
      })
      promiseAll = promiseAll.filter((item)=>{
        return item !== null
      })
      Promise.all(promiseAll).then((sucArr) => {
        sucArr.forEach((item, index) => {
          if (item.data && item.data.status == 1) {
            dataList[index].isSuccess = true
          } else {
            dataList[index].isSuccess = false
          }
        })
        data.dataList = dataList
      })
    })
}

const handleDetail = (item)=> {
  if (!item.isSuccess) {
    return false
  }
  if(item.type == TYPECONF.AGGREGATION){
    var a = document.createElement('a')
    a.href = item.aggregatePageUrl
    a.click()
    return false
  }
  let detailUrl = `${location.origin}/yundian/broadband/usersidedetail.html?templateId=${item.templateId}&appointmentType=${item.type}&shopId=${data.shopId}&key=${data.key}`
  detailUrl = setAcId(detailUrl)
  if (proxy) {
    proxy.$router.push({
      path: "/broadband/usersidedetail.html"+getSearchParams(detailUrl),
    })
  }
}

const back = async({ home })=> {
  const isWXMapp = await UA.isWeChatMiniApp()
  if (isWXMapp) {
    window.wx.miniProgram.switchTab({ url: '/pages/home/<USER>' })
  } else {
    if (home) {
      window.location.href = '/yundian/index.html?shopId=' + data.shopId
    } else {
      history.go(-1)
    }
  }
}
onMounted(()=>{
  init()
})

</script>

<style lang="scss">
body {
  background: #f7f8f9;
  min-height: 101vh;
}
</style>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 44px;
  line-height: 44px;
  background: #fff;
  text-align: center;
  font-size: 18px;
  position: relative;

  .back {
    position: absolute;
    left: 10px;
    bottom: 13px;
  }
}

.usersidelist {
  .banner {
    margin-bottom: 20px;
    a {
      height: 160px;
      display: block;
      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }
  }

  .list {
    padding: 0 10px;
    ul {
      li {
        margin-bottom: 20px;
        a {
          display: block;
          border-radius: 9px;
          overflow: hidden;
          box-shadow: 0px 3px 4px 0px rgba(227, 227, 227, 0.5);
          .img {
            height: 109px;
            width: 100%;
            img {
              width: 100%;
              height: 100%;
              display: block;
            }
          }

          .text {
            display: flex;
            align-items: center;
            padding: 13px;
            .title {
              font-size: 16px;
              color: #333;
              flex: 1;
              min-width: 0;
              padding-right: 20px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .btn {
              width: 100px;
              height: 32px;
              background: linear-gradient(90deg, #2892ff, #007eff);
              border-radius: 16px;
              color: #fff;
              font-size: 14px;
              display: flex;
              align-items: center;
              justify-content: center;
              &.hui {
                background: #ccc;
              }
            }
          }
        }
      }
    }
  }
}
</style>
