<template>
  <div class="broadband">
    <header-nav :title="data.title"></header-nav>
    <div v-if="data.orderDetail" class="couponMain-details">
      <h2>预约信息</h2>
      <div class="bd-con clearfix">
        <div class="bd-con-ner clearfix">
          <p class="clearfix">
            <span class="bd-let">预约类型：</span>{{ data.orderDetail.appointmentType == data.appointmentType.terminal ? '终端预约' : '宽带预约' }}
          </p>
          <p v-if="data.orderDetail.appointmentName" class="clearfix">
            <span class="bd-let">预约手机号：</span>
            <span class="bd-phone">
              {{ getPassPhone(data.orderDetail.appointmentMobile) }}
            </span>
            <a
              v-if="data.orderDetail.appointmentType == data.appointmentType.terminal || data.showPhone.indexOf(data.orderDetail.state)!==-1"
              :href="'tel:'+data.orderDetail.appointmentMobile"
              class="icon-phone-img"
            >
              <img src="~@/assets/index_normal/phone.png" alt="" />
            </a>
          </p>
          <p v-if="data.orderDetail.appointmentName" class="bd-hs-ys clearfix">
            <span class="bd-let">预约人姓名：</span>
            <span class="w80 breakAll">{{ data.orderDetail.appointmentName }}</span>
          </p>
          <p
            v-if="
              data.orderDetail.province &&
                data.orderDetail.city &&
                data.orderDetail.district &&
                data.orderDetail.appointmentAddress
            "
            class="bd-hs-ys clearfix"
          >
            <span class="bd-let">预约地址明细：</span>
            <span class="w80 breakAll">{{
              data.orderDetail.province +
                ' ' +
                data.orderDetail.city +
                ' ' +
                data.orderDetail.district +
                ' ' +
                data.orderDetail.appointmentAddress
            }}</span>
          </p>
          <!-- <p v-if="data.orderDetail.credentials && data.orderDetail.credentialsNo" class="clearfix">
            <span class="bd-let">证件类型：</span>{{ data.orderDetail.credentials == '01'?'身份证':'护照' }}
          </p>
          <p v-if="data.orderDetail.credentialsNo && data.orderDetail.credentials" class="clearfix">
            <span class="bd-let">证件号码：</span>{{ data.orderDetail.credentialsNo }}
          </p> -->
          <p v-if="data.orderDetail.memo" class="bd-hs-ys clearfix">
            <span class="bd-let">备注：</span>
            <span class="w80 breakAll">{{ data.orderDetail.memo }}</span>
          </p>
          <p class="clearfix">
            <span class="bd-let">预约状态：</span>
            <span :class="data.colorConfig[data.orderDetail.state]">
              {{
                data.shopState.indexOf(data.orderDetail.state) !== -1
                  ? data.stateText[data.orderDetail.state]
                  : ''
              }}
            </span>
            <a
              v-if="
                ['HUD', 'TOC', 'AUF', 'AUS'].indexOf(data.orderDetail.state) !== -1 && data.ruleId == 2
              "
              class="ljclbt"
              @click="openChangeOrderDialog(data.orderDetail.orderId)"
            >立即处理</a>
            <a
              v-if="
                ['HUD', 'TOC', 'AUF'].indexOf(data.orderDetail.state) !== -1 &&
                  data.tplId != 'roleUser'
              "
              class="ljclbt"
              @click="handlerDealWith(data.orderDetail)"
            >修改信息</a>
            <a
              v-if="data.orderDetail.state == 'TOA' && data.tplId != 'roleUser'"
              class="ljclbt"
              @click="handlerUndo(data.orderDetail)"
            >撤消修改</a>
          </p>
          <p
            v-if="
              data.orderDetail.state == 'MAS' && data.orderDetail.appointmentType == data.appointmentType.broadband
            "
            class="clearfix"
          >
            <span class="bd-let">宽带账号：</span>
            <span class="w80 breakAll">{{ data.orderDetail.productCode }}</span>
          </p>
          <p v-if="data.orderDetail.state == 'MAF'" class="clearfix">
            <span class="bd-let">失败原因：</span>
            <span class="w80 breakAll">{{ data.orderDetail.reason }}</span>
          </p>
          <p
            v-if="data.orderDetail.state == 'HBC' && data.tplId == 'shop'"
            class="clearfix"
          >
            <span class="bd-let">取消原因：</span>
            <span class="w80 breakAll">{{ data.orderDetail.reason }}</span>
          </p>
          <p
            v-if="data.orderDetail.state == 'HBC' && data.tplId == 'roleUser'"
            class="clearfix"
          >
            <span class="bd-let">取消原因：</span>
            <span
              class="w80 breakAll"
            >用户自主取消，{{ data.orderDetail.reason }}</span>
          </p>
          <p v-if="data.tplId == 'shop'" class="clearfix">
            <span class="bd-let">操作人：</span><span>{{ data.orderDetail.operator }}</span>
          </p>
          <p v-if="data.orderDetail.appointmentType == data.appointmentType.terminal" class="clearfix">
            <span class="bd-let">商品名称：</span>
            <span class="w80 breakAll">{{ data.orderDetail.goodsName }}</span>
          </p>
          <p v-if="data.orderDetail.appointmentType == data.appointmentType.terminal" class="clearfix">
            <span class="bd-let">商品SKU：</span>
            <span class="w80 breakAll">{{ data.orderDetail.skuName }}</span>
          </p>
        </div>
        <p class="hs clearfix">
          <span class="bd-let">预约单号：</span>{{ data.orderDetail.orderId }}
        </p>
        <p class="hs clearfix">
          <span class="bd-let">创建时间：</span>{{ data.orderDetail.createTime }}
        </p>
        <p class="hs clearfix">
          <span class="bd-let">状态更新时间：</span>{{ data.orderDetail.modifyTime }}
        </p>
      </div>
    </div>
    <!-- 弹框 -->
    <van-dialog
      v-model="data.showChangeOrderStatus"
      title="预约处理"
      show-cancel-button
      confirm-button-color="#5fade8"
      :before-close="changeOrderStatus"
    >
      <van-radio-group
        v-model="data.sendData1.state"
        direction="horizontal"
        class="dialogContent"
      >
        <van-radio name="MAS">
          预约成功
        </van-radio>
        <van-radio name="MAF">
          预约失败
        </van-radio>
      </van-radio-group>
      <van-field
        v-if="data.sendData1.state == 'MAS'"
        v-model="data.sendData1.productCode"
        class="boradbandInput"
        type="text"
        placeholder="请输入BOSS侧宽带账号"
        label-width="100px"
        clearable
      />
      <van-field
        v-if="data.sendData1.state == 'MAF'"
        v-model="data.sendData1.reason"
        class="boradbandInput"
        type="textarea"
        :autosize="{ maxHeight: 100 }"
        :maxlength="256"
        placeholder="请填写预约失败原因，例：用户主动取消"
        label-width="100px"
        clearable
      />
    </van-dialog>

    <!-- 立即处理 -->
    <modifythe
      :modifythe-data="data.modifytheData"
      :modify-dialog="data.modifyDialog"
      @updateOrder="refresh"
      @closeModifyDialog="(e) => (data.modifyDialog = e)"
    />
    <Footer />
  </div>
</template>

<script setup>
import headerNav from '@/views/my/components/headerNav'
import loginUtils from '@/utils/login'
import broadbandApi from '@/api/broadband'
import { Toast, Dialog, RadioGroup, Radio, Field, Form } from 'vant'
import { mapGetters } from 'vuex'
import 'vant/lib/index.css'
import modifythe from '@/components/broadband/modifythe'
import {getPassPhone} from "@/utils/utils"
import Vue ,{reactive,onMounted,getCurrentInstance,computed} from 'vue'
Vue.use(Toast).use(Dialog).use(RadioGroup).use(Radio).use(Field).use(Form)
let data = reactive({
  title: '预约详情',
  activeNames: ['1'],
  info: '',
  orderDetail: null,
  orderId: null,
  tplId: null,
  showChangeOrderStatus: false,
  showCancleReservation: false,
  sendData1: {
    state: 'MAS',
    productCode: '',
    reason: '',
  },
  ruleId: null,

  modifyDialog: false,
  modifytheData: {},
  stateText: {
    TOC: '待确认',
    MAS: '预约成功',
    MAF: '预约失败',
    HBC: '已取消',
    TOA: '待审核',
    AUS: '审核成功',
    AUF: '审核失败',
    HUD: '已撤消',
  },
  // 商户看到的状态
  shopState: ['TOC', 'MAS', 'MAF', 'HBC', 'TOA', 'AUS', 'AUF', 'HUD'],
  // 用户看到的状态
  roleUserState: ['TOC', 'MAS', 'MAF', 'HBC'],
  //在宽带预约单状态是“待确认”、“待审核”、“已撤消”、“审核成功”、“审核失败”展示电话图标
  showPhone:["TOC","TOA","HUD","AUS","AUF"],
  colorConfig: {
    TOC: 'yll',
    MAS: 'ls',
    MAF: 'red',
    HBC: 'hs',
    HUD: 'red',
    TOA: 'yll',
  },
  appointmentType:{
    broadband:10,
    terminal:20
  }
})
/* 撤销修改 */
let handlerUndo = (item)=> {
  broadbandApi.undoModifyBAOrder(item).then((res) => {
    if (res.code) {
      Toast(res.message)
      return
    }

    refresh()
  })
}

/* 点击修改信息 */
let handlerDealWith = (item)=> {
  data.modifytheData = item
  data.modifyDialog = true
}

let logined = (res)=> {
  if (res) {
    getBroadbandDetail()
  }
}
let getBroadbandDetail = ()=> {
  if (data.tplId == 'shop') {
    broadbandApi
      .getShopBAOrderInfo({ orderId: data.orderId })
      .then((res) => {
        data.orderDetail = res.data
      })
  } else {
    broadbandApi
      .getUserBAOrderInfo({ orderId: data.orderId })
      .then((res) => {
        data.orderDetail = res.data
      })
  }
}
let openChangeOrderDialog = (orderId)=> {
  data.currentOrderId = orderId
  data.showChangeOrderStatus = true
}
let changeOrderStatus = (action, done)=> {
  if (action == 'cancel') {
    done()
    return false
  }
  if (data.sendData1.state == 'MAS') {
    //预约成功
    if (!data.sendData1.productCode) {
      Toast('请输入BOSS侧宽带账号')
      done(false)
      return false
    } else if (!/^[\w\d]+$/.test(data.sendData1.productCode)) {
      Toast('请输入字母或数字')
      done(false)
      return false
    }
    delete data.sendData1.reason
  }
  if (data.sendData1.state == 'MAF') {
    //预约失败
    let myreason = data.sendData1.reason
      ? data.sendData1.reason.replace(/[\n|\s]+/g, '')
      : null
    if (!myreason) {
      Toast('请输入失败原因')
      data.sendData1.reason = ''
      done(false)
      return false
    }
    delete data.sendData1.productCode
  }
  data.sendData1.orderId = data.currentOrderId
  broadbandApi.updateBAOrderState(data.sendData1, 5).then((res) => {
    if (res.code) {
      Toast(res.message)
      done(false)
    } else {
      //刷新页面
      done()
      refresh()
    }
  })
}
let refresh  = ()=> {
  getBroadbandDetail()
}
onMounted(()=>{
  const url = new URL(location.href)
  data.tplId = url.searchParams.get('tplId')
  data.orderId = url.searchParams.get('orderId')
  data.ruleId = url.searchParams.get('ruleid')
  const getVueInstance = getCurrentInstance()
  const proxy = getVueInstance ? getVueInstance.proxy : null
  let myuser = proxy ? proxy.$store.getters.user : null
  if (myuser.userInfo == null) {
    //强登
    loginUtils.login(true, true, logined, false, false, '', '0')
  } else {
    getBroadbandDetail()
  }
})
</script>
<style>
body {
  background: #f9fafc;
}
.header-nav {
  height: 52.5px !important;
}
.header-nav__title {
  font-size: 18.75px !important;
}
</style>
<style lang="scss" scoped>
.broadband {
  min-height: calc(100vh - 73px);
}
.breakAll {
  white-space: normal;
  word-break: break-all;
  word-wrap: break-word;
}
.couponMain-details {
  width: 100%;
  height: 100%;
  overflow: hidden;
  color: #333;
  background: #f9fafc;
  font-size: 13.5px;
  .clearfix:after {
    content: '.';
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
  h2 {
    line-height: 37.5px;
    padding: 0 15px;
  }
  .bd-con {
    background: #fff;
    padding-right: 7.5px;
    padding-bottom: 7.5px;
    line-height: 30px;
    .bd-con-ner {
      padding: 7.5px 0;
      border-bottom: 1px solid #f9fafc;
      margin-bottom: 7.5px;
    }
    .ljclbt {
      display: inline-block;
      border: 1px solid #0085d0;
      border-radius: 6px;
      width: 75px;
      height: 26.25px;
      line-height: 26.25px;
      text-align: center;
      color: #0085d0;
      margin-left: 10px;
    }
    .bd-let {
      float: left;
      width: 105px;
      text-align: right;
    }
    .w80 {
      display: inline-block;
      width: 262.5px;
      line-height: 22.5px;
      padding: 3.75px 0;
    }
    .hs {
      color: #999;
    }
    .ls {
      color: #00ad29;
    }
    .red {
      color: #ed2668;
    }
    .yll {
      color: #e97a00;
    }
  }
}
.dialogContent {
  font-size: 14px;
  margin: 18.75px 56.25px 11.25px;
}
.boradbandInput {
  :deep(.van-field__body) {
    width: 94%;
    margin: 0 auto;
    border: 1px solid #ccc;
    padding: 3.75px 7.5px;
    border-radius: 7.5px;
  }
}
.icon-phone-img{
  width:20px;
  height:20px;
  margin:5px 0 0 10px;
  img{
    width:100%;
  }
}
.bd-phone,.icon-phone-img{
  float: left;
}
</style>
