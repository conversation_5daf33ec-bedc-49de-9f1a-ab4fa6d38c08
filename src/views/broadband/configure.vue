<template>
  <div class="body">
    <div v-if="data.isApp" class="header">
      <van-icon name="arrow-left" class="back" @click="back" />
      {{ data.title }}
    </div>
    <h3>宽带预约配置</h3>
    <van-cell v-if="data.disabled" center :title="data.cellTitle">
      <template #right-icon>
        <van-switch v-model="data.checked" size="24" disabled />
      </template>
    </van-cell>
    <van-cell v-else center :title="data.cellTitle">
      <template #right-icon>
        <van-switch v-model="data.checked" size="24" @change="changeShow" />
      </template>
    </van-cell>
    <div class="tip">
      备注：开启表示在店铺上架带预约功能，店主需具有在省侧给用户下单的能力。</div>
  </div>
</template>

<script setup>
import { Icon, Toast ,Switch,Cell } from "vant"
import Vue,{reactive,onMounted,getCurrentInstance}from "vue"
import loginUtils from "@/utils/login"
import UA from "@/utils/ua"
import broadbandApi from "@/api/broadband"
Vue.use(Icon)
  .use(Toast)
  .use(Switch)
  .use(Cell)
const BROADBANDONLINE = 1
let data = reactive({
  title: "宽带预约配置",
  isApp:false,
  checked: false,
  disabled:true,
  user:null,
  shopId:null,
  floorId:null,
  cellTitle:'宽带预约',
  proxy: null
})
let logined = (res)=> {
  data.user = res
  data.isLogined = res && res.UserName > ""
  if (data.isLogined) {
    if(res.shopId) {//内部员工才能配置
      data.shopId = res.shopId
      getBroadbrandTpl(res.shopId)
      getChecked()
    }else{
      data.proxy.$router.push({
        name: "nearby"
      })
      //location.href = "nearby/index.html"
    }
  }
}
let back = ()=> {
  history.go(-1)
  // window.location.href = "/hd/xskd/index.html?shopId="+ data.shopId
}
let getBroadbrandTpl = (shopId)=>{
  broadbandApi.queryShopTmpl({
    shopId:shopId,
    type:1
  },5).then(res=>{

    if(res.code){
      Toast(res.message)
    }else{
      if(res.data&&res.data.status){
        data.disabled = res.data.status!== BROADBANDONLINE
      }else{
        data.disabled=true
      }
      if(data.disabled){
        data.cellTitle = "宽带预约（已下线，不可用）"
      }else{
        data.cellTitle = "宽带预约"
      }
    }

  })
}
let getChecked = ()=>{
  broadbandApi.getBroadbandStatus({
    shopId:data.shopId
  },5).then(res=>{
    if(res.code){
      Toast(res.message)
    }else{
      data.checked = res.data.broadbandStatus==1?true:false
    }

  })
}
let changeShow = ()=>{
  let sendData = {
    broadbandStatus:data.checked?1:0,
    shopId:data.shopId
  }
  broadbandApi.updateBroadbandStatus(sendData,5).then(res=>{
    if(res.code){
      Toast(res.message)
    }
  })
}
onMounted(()=> {
  const url = new URL(location.href)
  data.shopId = url.searchParams.get("shopId")
  const getVueInstance = getCurrentInstance()
  const proxy = getVueInstance ? getVueInstance.proxy : null
  if (proxy) {
    proxy.$store.commit("SET_SHOPID",data.shopId)
    data.proxy = proxy
  }
  //强登
  loginUtils.login(true,true,logined,false,false,"",null,5)
  //页头隐藏
  if(UA.isApp || UA.isWechat){
    data.isApp = false
  }else{
    data.isApp = true
  }
})
</script>
<style>
body {
  background: #f6f7f9 !important;
  font-size: 12px;
}
</style>
<style lang="scss" scoped>
.body{
  min-height: calc(100vh - 73px);
  .header {
    width: 100%;
    height: 44px;
    line-height: 44px;
    background: #fff;
    text-align: center;
    font-size: 18px;
    position: relative;
    .back {
      position: absolute;
      left: 10px;
      bottom: 13px;
    }
  }
  h3{
    height: 52px;
    line-height: 52px;
    font-size: 15px;
    text-align: left;
    color: #999999;
    text-indent: 26px;
  }
  .van-cell{
    width: 355px;
    height: 57px;
    background: #ffffff;
    border-radius: 15px;
    margin: 0 auto;
    span{
    font-size:14px;
    }
  }
  .tip{
    width: 335px;
    font-size: 13px;
    text-align: left;
    color: #999999;
    line-height: 24px;
    margin: 12px auto;
  }
}
</style>
