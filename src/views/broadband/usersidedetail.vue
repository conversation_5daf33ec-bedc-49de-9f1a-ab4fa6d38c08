<template>
  <main class="usersidedetail">
    <!-- app下显示头部 -->
    <div v-if="data.isApp" class="header">
      <van-icon name="arrow-left" class="back" @click="back" />
      <span>{{ data.tplTitle }}</span>
    </div>

    <!-- 推荐人信息 -->
    <!-- <referees :tel="data.telkey" /> -->

    <!-- banner -->
    <section v-if="data.broadbandImage && !data.orderID" class="banner">
      <a href="javascript:;">
        <img :src="data.broadbandImage" alt="" />
      </a>
    </section>

    <!-- 商品轮播 -->
    <!-- <section v-if="!orderID && appointmentType == 2" class="goods"> -->
    <section v-if="!data.orderID" class="goods">
      <swiper
        v-if="data.goodsTemplateSkuList && data.goodsTemplateSkuList.length"
        ref="swipebanner"
        class="swipebanner"
        :options="data.swiperOptions"
      >
        <swiper-slide
          v-for="(item, index) in data.goodsTemplateSkuList"
          :key="item.id"
        >
          <div class="child">
            <div class="img">
              <van-image
                width="100%"
                height="100%"
                fit="contain"
                :src="item.goodsImage"
              />
              <!-- <img class="curImg" :src="item.img" /> -->
              <!-- <img class="bgImg" :src="item.img" /> -->
            </div>
            <div class="text">
              <div class="title">
                {{ item.goodsName }}
              </div>
              <div :class="['sku', item.skuCurrentName ? 'active' : '']">
                <van-cell
                  is-link
                  :title="
                    item.skuCurrentName
                      ? item.skuCurrentName
                      : '请选择预约商品颜色/型号等信息'
                  "
                  @click="handleClick(index, item.skuNames)"
                />
              </div>
            </div>
          </div>
        </swiper-slide>
      </swiper>
      <div
        v-if="data.hasGoodsTemplateSkuList"
        slot="button-prev"
        class="swiper-button-prev"
      ></div>
      <div
        v-if="data.hasGoodsTemplateSkuList"
        slot="button-next"
        class="swiper-button-next"
      ></div>
    </section>

    <!-- 商品sku选择弹出框 -->
    <van-popup v-model="data.goodsSku.popup" position="bottom">
      <van-picker
        show-toolbar
        :columns="data.goodsSku.data"
        @confirm="
          (val, index) => {
            ;(data.goodsTemplateSkuList[data.goodsSku.index].skuCurrentName =
              val),
              (data.goodsTemplateSkuList[data.goodsSku.index].skuId = index),
              (data.goodsSku.popup = false)
          }
        "
        @cancel="data.goodsSku.popup = false"
      />
    </van-popup>

    <!-- 身份填写 -->
    <section v-if="!data.orderID" class="identity">
      <div class="children">
        <van-form
          ref="form"
          :label-width="100"
          label-align="right"
          @submit="onSubmit"
        >
          <van-field
            v-show="data.formInfoArr.includes('1')"
            v-model="data.userName"
            name="userName"
            label="预约人姓名 "
            placeholder="请填写预约人姓名"
            :maxlength="32"
            :error-message="data.userNameError"
            required
            colon
            label-width="100px"
          />
          <van-field
            v-show="data.formInfoArr.includes('1')"
            v-model="data.tel"
            name="tel"
            label="预约手机号 "
            type="tel"
            required
            placeholder="请填写预约手机号"
            :error-message="data.telError"
            colon
            label-width="100px"
          />
          <van-field
            v-show="data.formInfoArr.includes('2')"
            readonly
            clickable
            name="typeDocument"
            :value="data.typeDocument"
            label="证件类型 :"
            placeholder="请选择证件类型"
            right-icon="arrow"
            @click="data.typeDocumentPopup = true"
          />
          <van-popup v-model="data.typeDocumentPopup" position="bottom">
            <van-picker
              show-toolbar
              :columns="['身份证', '护照']"
              @confirm="
                (val) => {
                  ;(data.typeDocument = val), (data.typeDocumentPopup = false)
                }
              "
              @cancel="data.typeDocumentPopup = false"
            />
          </van-popup>
          <van-field
            v-show="data.formInfoArr.includes('2')"
            v-model="data.credentialsNo"
            name="credentialsNo"
            label="证件号码 "
            required
            placeholder="请填写证件号码"
            :error-message="data.documentNumberError"
            colon
            label-width="100px"
          />
          <van-field
            v-show="data.formInfoArr.includes('1')"
            readonly
            clickable
            name="provinceCity"
            :value="
              data.province && data.city
                ? `${data.province.provinceName}  ${data.city.cityName}`
                : ''
            "
            label="预约地址 :"
            required
            placeholder=""
          />
          <van-field
            v-show="data.formInfoArr.includes('1')"
            readonly
            clickable
            name="areaText"
            :value="data.areaText"
            :error-message="data.areaTextError"
            label=" "
            placeholder="点击选择区县"
            @click.prevent="onClickArea"
          />
          <van-field
            v-show="data.formInfoArr.includes('1')"
            v-model="data.appointmentAddress"
            name="appointmentAddress"
            type="text"
            label=" "
            placeholder="请填写详细地址"
            :error-message="data.appointmentAddressError"
            :maxlength="60"
            label-width="100px"
          />
          <van-field
            v-show="data.formInfoArr.includes('3')"
            v-model="data.remarks"
            name="remarks"
            label="备注 "
            placeholder="如有备注，请填写"
            :error-message="data.remarksError"
            colon
            label-width="100px"
          />
        </van-form>
      </div>
    </section>

    <!-- 产品介绍 -->
    <section v-if="data.tplContent && !data.orderID" class="introduce">
      <div v-myhtml="data.tplContent" class="tplContent rich"></div>
    </section>

    <!-- 立即预约 -->
    <section v-if="!data.orderID" :class="['submit']">
      <van-button
        :loading="data.submitBtnLoading"
        round
        block
        type="info"
        color="linear-gradient(90deg,#2892ff, #007eff)"
        @click="appointmentSubmit"
      >
        立即预约
      </van-button>
    </section>

    <!-- 省选择 -->
    <popupArea
      ref="area"
      :is-district="true"
      :province="data.province"
      :city="data.city"
      :is-open="isOpen"
      @close="close"
      @confirm="onAreaConfirm"
    />

    <!-- 预约成功弹出框 -->
    <div v-if="data.orderID" class="Booked">
      <div class="title">
        <van-icon size="60" name="passed" color="#06BC7B" />
        <h1>预约成功</h1>
      </div>
      <p>
        您的预约单号：{{ data.orderID }}
        <br />请保持电话畅通，我们会尽快与您联系~
      </p>
      <div class="bts">
        <van-button
          round
          plain
          hairline
          type="info"
          @click="back({ home: true })"
        >
          返回店铺首页
        </van-button>
        <van-button round plain hairline type="info" @click="handleToDetail">
          查看预约信息
        </van-button>
      </div>
    </div>
    <template v-if="data.isWeChatMiniApp || UA.isApp">
      <van-dialog
        v-model="subMsg.show"
        title=""
        :show-cancel-button="false"
        :show-confirm-button="false"
        confirm-button-color="#5fade8"
        class="dialog-container"
      >
        <div class="dialog">
          <div class="dialog-sub-check-img" @click="goSubMsg">
            <div class="dialog-sub-check-btn">
            </div>
          </div>
          <a href="javascript:void(0)" class="dialog-btn-exit" @click="subMsg.show = false">
          </a>
        </div>
      </van-dialog>
    </template>
    <LoginDialog
      :islogin="autoLoginObj.is4gLogined"
      :is-force-login="true"
      :isloginfn="logined"
      :is-auto-login="autoLoginObj.autoLogin"
      :show-cancle="false"
    />
  </main>
</template>

<script setup>
import Vue, {
  ref,
  reactive,
  onMounted,
  computed,
  getCurrentInstance,
} from 'vue'
import popupArea from '@/components/popup-area'
import {
  Icon,
  Toast,
  Field,
  Cell,
  Form,
  Button,
  Dialog,
  Picker,
  Popup,
  Image as VanImage,
  NoticeBar,
} from 'vant'
import shopApi from '@/api/shop'
import UA from '@/utils/ua'
import broadbandApi from '@/api/broadband'
import loginUtils from '@/utils/login'
import 'swiper/dist/css/swiper.css'
import VueAwesomeSwiper from 'vue-awesome-swiper'
import lodash from 'lodash'
import { getImgUrl, getAllSearchParamsArray, getCookie } from '@/utils/utils'
import shareUtilApi from '@/utils/share'
import LoginDialog from '@/components/login/index'
import leadeonLoader from "@/utils/leadeonloader"

Vue.use(Icon)
  .use(Toast)
  .use(Field)
  .use(Cell)
  .use(Form)
  .use(Button)
  .use(Dialog)
  .use(Picker)
  .use(Popup)
  .use(VueAwesomeSwiper)
  .use(VanImage)
  .use(NoticeBar)
let data = reactive({
  /* 按钮loading */
  submitBtnLoading: false,

  /* 产品介绍文本 */
  tplContent: '',

  /* 预约人姓名 */
  userName: '',
  /* 预约人姓名错误信息 */
  userNameError: '',

  /* 预约手机号 */
  tel: '',
  /* 预约手机号错误信息 */
  telError: '',

  /* 证件类型 */
  typeDocument: '身份证',
  /* 证件选择器弹出框 */
  typeDocumentPopup: false,

  /* 证件号码 */
  credentialsNo: '',
  /* 证件号码错误信息 */
  documentNumberError: '',

  /* 备注 */
  remarks: '',
  /* 备注错误信息 */
  remarksError: '',

  /* 安装地址 */
  appointmentAddress: '',
  /* 安装地址错误信息 */
  appointmentAddressError: '',

  /* 预约地址 */
  areaText: null,
  /* 预约地址报错信息 */
  areaTextError: '',

  /* 省市区对象数据 */
  province: null,
  city: null,
  region: null,

  /* 商品数据 */
  goodsTemplateSkuList: [],

  /* 商品sku选择弹出框数据 */
  goodsSku: {
    /* 弹出框是否显示 */
    popup: false,

    /* 列表数据 */
    data: [],

    /* 要改变的列表的index */
    index: 0,
  },

  /* 页面是否显示错误信息, true为不显示 */
  hasShop: true,

  /* 是否app内打开 */
  isApp: false,

  /* 是否小程序内打开 */
  isWeChatMiniApp: false,

  /* 是否显示头图banner */
  broadbandImage: false,

  /* swiper配置*/
  swiperOptions: {
    loop: false,
    spaceBetween: 10,
    navigation: {
      nextEl: '.swiper-button-next',
      prevEl: '.swiper-button-prev',
    },
  },

  /* 预约成功后的单号 */
  orderID: null,

  /* 可显示的表单模块
      1：收件信息 2：证件信息 3：备注
  */
  formInfoArr: [],

  /* 预约类型 1 宽带 2终端 */
  // appointmentType:1,

  /* 分享信息 */
  shareConfig: {
    title: '聚合页',
    url: window.location.href,
    desc: window.location.href,
    imgUrl: 'https://img1.shop.10086.cn/goods/tc2txqenygq3nkry_940x7200',
  },

  /* 推荐人信息 */
  telkey: null,

  /* 模板标题 */
  tplTitle: '',
})

const autoLoginObj = reactive({
  autoLogin: false,
  is4gLogined: null,
})

const autoLoginCb = (res) => {
  autoLoginObj.is4gLogined = false
  autoLoginObj.autoLogin = true
}

const logined = (res) => {
  data.user = res
  data.isLogined = res && res.UserName > ''
  data.unifiedChannelId = res.unifiedChannelId

  // 登录成功回调
  if (data.isLogined) {
    getShopDetail()
  }
}

const getVueInstance = getCurrentInstance()
const proxy = getVueInstance ? getVueInstance.proxy : null

const handleClick = (index, param) => {
  data.goodsSku.popup = true
  data.goodsSku.index = index
  data.goodsSku.data = param
}
let form = ref(null)
const appointmentSubmit = () => {
  form.value.submit()
}

/* 初始化 */
async function init(){
  const url = new URL(location.href)

  data.referrerSecretStr = url.searchParams.get('key')
  if (data.referrerSecretStr) {
    // data.telkey = desDeclassified(data.referrerSecretStr)
  }

  data.shopId = url.searchParams.get('shopId')
  data.templateId = url.searchParams.get('templateId')
  data.orderID = url.searchParams.get('orderId')
  // type：1 宽带 2终端
  // data.appointmentType = url.searchParams.get('appointmentType') || 1
  data.sourceUrl = url.searchParams.get('sourceUrl')
  if (proxy) {
    proxy.$store.commit('SET_SHOPID', data.shopId)
  }
  let tmp = document.cookie.match(/nalogin=([^\s;]+)/i)
  let nalogin =
    url.searchParams.get('nalogin') == 1 || (tmp && tmp[1] == 1) ? false : true
  // 强登
  loginUtils.login(true, true, logined, false, nalogin, autoLoginCb, '0')

  // 页头隐藏
  if (UA.isApp || UA.isWechat) {
    data.isApp = false
  } else {
    data.isApp = true
  }
  data.isWeChatMiniApp = await UA.isWeChatMiniApp()
}

/* 获取宽带预约模板数据 */
const getData = async() => {
  // 获取店铺状态弹框提示
  function getShopStatus() {
    return new Promise((resolve) => {
      if (data.shopId) {
        broadbandApi
          .getBroadbandStatus({ shopId: data.shopId })
          .then((bdStatus) => {
            // 错误提示
            if (bdStatus.code) {
              Toast(bdStatus.message)
            }

            resolve()
          })
          .catch(() => {
            resolve()
          })
        return
      }
      resolve()
    })
  }

  await getShopStatus.call(this)

  Promise.all([
    broadbandApi.queryShopTmplGoods({ templateId: data.templateId }),
  ]).then(([goodsList]) => {
    // 模板详情
    // data.tplInfo = lodash.get(bdShopTmpl, 'data',{})

    // 模板标题
    data.tplTitle = lodash.get(goodsList, 'data.templateName', '')
    document.title = data.tplTitle

    // 模板内容
    data.tplContent = lodash.get(goodsList, 'data.content', '')

    // 是否显示宽带预约头图
    let broadbandImage = lodash.get(goodsList, 'data.broadbandImage')
    data.broadbandImage = getImgUrl(broadbandImage)

    // 表单需要显示的模块
    let formInfo = lodash.get(goodsList, 'data.formInfo')
    if (formInfo && formInfo.split) {
      data.formInfoArr = formInfo.split(',')
    }

    // 预约商品
    data.goodsTemplateSkuList = lodash
      .get(goodsList, 'data.goodsTemplateSkuList', [])
      .map((item) => {
        return {
          ...item,
          goodsImage: item.goodsImage
            ? getImgUrl(item.goodsImage)
            : item.goodsImage,
          skuNames:
            item.skuNames && typeof item.skuNames === 'string'
              ? JSON.parse(item.skuNames)
              : item.skuNames,
        }
      })

    // 分享当前页面
    let params = getQueryParam()
    params.key = data.referrerSecretStr
    let filterurl = createURL(location.origin + location.pathname, params)
    data.shareConfig.url = filterurl
    data.shareConfig.desc = filterurl
    data.shareConfig.unifiedChannelId = data.unifiedChannelId
    share()

    // // 错误提示
    // if (bdStatus.code) {
    //   Toast(bdStatus.message)
    // }
  })
}
let swipebanner = ref(null)
//预约结果消息订阅弹窗
const subMsg = reactive({
  show:false
})
async function goSubMsg(){
  let currentUrl = location.href+'&orderId='+data.orderID
  let pageUrl = `/subPackages/submsg/submsg?orderId=${data.orderID}&shopId=${data.shopId}&tmplId=YUYUE&type=2&backUrl=${encodeURIComponent(currentUrl)}`
  if(UA.isApp){
    const leadeon = await leadeonLoader()
    leadeon.openMiniProgram({
      debug: false,
      wx:{//微信小程序
        "userName": 'gh_74df7f2509b1', //小程序的username
        "path": pageUrl, //打开小程序指定页面路径
        "miniProgramType": '0', //0 正式版 1 开发板 2体验版
      },
      success: function(res) {
      },
      error: function(res) {
      }
    })
  }
  if(data.isWeChatMiniApp){
    window.wx.miniProgram.navigateTo({url: pageUrl})
  }
}
/* 立即预约 */
const onSubmit = (parame) => {
  /* 处理商品名称 */
  // const {} = data.$refs
  let swiper = swipebanner.value
  if (swiper && swiper.swiper) {
    let realIndex = swiper.swiper.realIndex
    let currentGoods = data.goodsTemplateSkuList[realIndex]

    if (currentGoods) {
      if (!currentGoods.skuCurrentName) {
        Toast('请选择当前终端商品的sku')
        return
      }
      // parame.goodsName = currentGoods.goodsName
      // /* 处理商品sku名称 */
      // parame.skuName = currentGoods.skuCurrentName
      parame.goodsId = currentGoods.id
      parame.skuId = currentGoods.skuId
    }
  }

  /* 验证预约人姓名 */
  if (data.formInfoArr.includes('1')) {
    if (!parame.userName) {
      data.userNameError = '请填写预约人姓名'
      return false
    } else if (parame.userName.length > 60) {
      data.userNameError = '长度不能超过60个字符'
      return false
    } else {
      data.userNameError = ''
    }
  }
  parame.appointmentName = parame.userName

  /* 验证手机号 */
  if (data.formInfoArr.includes('1')) {
    if (!parame.tel) {
      data.telError = '请填写预约手机号'
      return false
    } else if (!/^1\d{10}$/.test(parame.tel)) {
      data.telError = '请填写正确的手机号'
      return false
    } else {
      data.telError = ''
    }
  }
  parame.appointmentMobile = parame.tel

  /* 验证证件号码 */
  const reg = new RegExp(
    '(^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{2}$)'
  )
  const city = {
    11: '北京',
    12: '天津',
    13: '河北',
    14: '山西',
    15: '内蒙古',
    21: '辽宁',
    22: '吉林',
    23: '黑龙江 ',
    31: '上海',
    32: '江苏',
    33: '浙江',
    34: '安徽',
    35: '福建',
    36: '江西',
    37: '山东',
    41: '河南',
    42: '湖北 ',
    43: '湖南',
    44: '广东',
    45: '广西',
    46: '海南',
    50: '重庆',
    51: '四川',
    52: '贵州',
    53: '云南',
    54: '西藏 ',
    61: '陕西',
    62: '甘肃',
    63: '青海',
    64: '宁夏',
    65: '新疆',
    71: '台湾',
    81: '香港',
    82: '澳门',
    91: '国外',
  }
  if (data.formInfoArr.includes('2')) {
    if (!parame.credentialsNo) {
      data.documentNumberError = '请填写证件号码'
      return false
    } else if (!reg.test(parame.credentialsNo)) {
      data.documentNumberError = '证件号格式错误'
      return false
    } else if (!city[parame.credentialsNo.substr(0, 2)]) {
      data.documentNumberError = '地址编码错误'
      return false
    }
    let ero = isIdCard(parame.credentialsNo)
    if (ero) {
      data.documentNumberError = ero
      return false
    } else {
      data.documentNumberError = ''
    }
  }

  /* 验证选择区县 */
  if (data.formInfoArr.includes('1')) {
    if (!parame.areaText) {
      data.areaTextError = '点击选择区县'
      return false
    } else {
      data.areaTextError = ''
    }
  }

  /* 验证安装地址 */
  if (data.formInfoArr.includes('1')) {
    if (!parame.appointmentAddress) {
      data.appointmentAddressError = '请填写详细地址'
      return false
    } else if (parame.appointmentAddress.length > 60) {
      data.appointmentAddressError = '长度不能超过60个汉字'
      return false
    } else {
      data.appointmentAddressError = ''
    }
  }

  /* 处理证件类型 */
  switch (parame.typeDocument) {
  case '身份证':
    parame.credentials = '01'
    break
  case '护照':
    parame.credentials = '02'
    break
  default:
    break
  }

  /* 处理备注 */
  if (data.formInfoArr.includes('3')) {
    if (data.remarks.length > 100) {
      data.remarksError = '长度不能超过100个汉字'
      return false
    }
  }

  /* 处理其他 */
  parame.appointmentAddress = parame.appointmentAddress.replace(/[\n|\s]+/g, '')
  parame.referrerSecretStr = data.referrerSecretStr
    ? data.referrerSecretStr.replace(/\s/g, '+')
    : ''
  // parame.templateId = data.tplInfo && data.tplInfo.templateId
  parame.templateId = data.templateId
  parame.shopId = data.shopId
  parame.district = parame.areaText
  parame.memo = data.remarks

  /* 身份证加密 */
  if (parame.credentialsNo && proxy) {
    parame.credentialsNo = proxy.$encryption(parame.credentialsNo)
  }

  /* 手机号加密 */
  if (parame.appointmentMobile && proxy) {
    parame.appointmentMobile = proxy.$encryption(parame.appointmentMobile)
  }

  if (data.submitBtnLoading) {
    return false
  }
  let allParams = getAllSearchParamsArray(location.href)
  let ac_id = allParams["WT.ac_id"] || getCookie("ac_id")
  if (ac_id) {
    parame.wtacId = ac_id
  }
  data.submitBtnLoading = true
  broadbandApi
    .broadbandSubmit(parame)
    .then((res) => {
      data.submitBtnLoading = false

      if (res.code == 10005) {
        // 预约模板是下线状态
        Dialog.alert({
          title: '提示',
          message: '该预约业务已下线, 您可以逛逛首页',
          theme: 'round-button',
          confirmButtonText: '返回店铺首页',
          confirmButtonColor: '#0A83FF',
        }).then(() => {
          back({ home: true })
        })

        return
      }

      if (res.code) {
        Dialog({
          title: '提示',
          message: res.message,
          confirmButtonColor: '#0085D0',
          confirmButtonText: '我知道了',
        })
        return
      }

      // 预约成功要弹框
      data.orderID = res.data.orderId
      // 小程序里订阅弹框
      if(data.isWeChatMiniApp || UA.isApp){
        subMsg.show = true
      }
    })
    .catch((err) => {
      data.submitBtnLoading = false
      // console.log(err)
    })
}

/* 获取店铺详情 */
const getShopDetail = async() => {
  function getShopDetail(shopId) {
    return new Promise((resolve) => {
      if (shopId) {
        shopApi
          .queryShopInfo({ shopId })
          .then((res) => {
            if (res.code) {
              data.hasShop = false
            }
            resolve(res)
          })
          .catch(() => {
            resolve({})
          })
      } else {
        resolve({})
      }
    })
  }

  const res = await getShopDetail(data.shopId)

  data.province = {
    provinceId: lodash.get(res, 'data.province', '100'),
    provinceName: lodash.get(res, 'data.provinceName', '北京'),
  }
  data.city = {
    cityId: lodash.get(res, 'data.city', '100'),
    cityName: lodash.get(res, 'data.cityName', '北京'),
  }

  getData()
}

/* 返回按钮 */
const back = async({ home }) => {
  const isWXMapp = await UA.isWeChatMiniApp()
  if (isWXMapp) {
    // window.wx.miniProgram.switchTab({ url: '/pages/home/<USER>' })
    let miniProgram = window.wx.miniProgram
    miniProgram.switchTab({url: '/pages/home/<USER>'})
    if(data.shopId){
    // 如果有店铺id,需要把店铺id回传给小程序
      miniProgram.postMessage({ data: {wxtoHomeShopId:data.shopId} })
    }
  } else {
    if (home) {
      window.location.href = '/yundian/index.html?shopId=' + data.shopId
    } else {
      history.go(-1)
    }
  }
}
/* 跳转预约信息 */
const handleToDetail = () => {
  location.href = `/yundian/broadband/details.html?tplId=roleUser&orderId=${data.orderID}&shopId=${data.shopId}`
}
const isOpen = ref(false)
function open() {
  isOpen.value = true
}
function close(){
  isOpen.value = false
}
/* 点击预约地址弹出框 */
const onClickArea = () => {
  let scrollTop =
    document.documentElement.scrollTop ||
    window.pageYOffset ||
    document.body.scrollTop
  if (scrollTop == 0) {
    window.scrollTo(0, 1)
  }
  if(data.province){
    open()
  }
}

/* 区县选择确定回调 */
const onAreaConfirm = (regionData) => {
  data.region = {
    regionId: regionData.regionCode,
    regionName: regionData.regionName,
  }

  data.areaText = regionData.regionName
}

/* 18位身份证需要验证最后一位校验位 */
const isIdCard = (value) => {
  if (value.length == 18) {
    value = value.split('')
    //∑(ai×Wi)(mod 11)
    //加权因子
    var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    //校验位
    var parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2]
    var sum = 0
    var ai = 0
    var wi = 0
    for (var i = 0; i < 17; i++) {
      ai = value[i]
      wi = factor[i]
      sum += ai * wi
    }
    if (parity[sum % 11] != value[17]) {
      return '证件格式错误'
    } else {
      return false
    }
  } else {
    return '证件格式错误'
  }
}

/* 分享当前页面 */
const share = () => {
  if (UA.isApp) {
    shareUtilApi.appShare(data.shareConfig)
  } else {
    shareUtilApi.changeWxShareConfig(data.shareConfig)
  }
}

/* 转换url */
const getQueryParam = (url) => {
  url = url == null ? window.location.href : url
  let search = url.substring(url.lastIndexOf('?') + 1)
  let query = {}
  let reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    let name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    query[name] = val
    return rs
  })
  return query
}
const createURL = (url, param) => {
  let Url
  let queryStr = ''
  for (let key in param) {
    let link = '&' + key + '=' + param[key]
    queryStr += link
  }
  Url = url + '?' + queryStr.substr(1)
  return Url
}
let hasGoodsTemplateSkuList = computed(() => {
  return data.goodsTemplateSkuList && data.goodsTemplateSkuList.length
})
onMounted(() => {
  init()
})
</script>

<style lang="scss">
body {
  background: #f7f8f9;
  min-height: 101vh;
}
.tplContent {
  padding: 18.75px;
  font-size: 12px;
  background: #fff;
  padding-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0px 0px 6px 0px rgba(222, 222, 222, 0.8);

  p,
  span {
    font-size: 12px !important;
  }
}
</style>
<style lang="scss" scoped>
.header {
  width: 100%;
  height: 44px;
  line-height: 44px;
  background: #fff;
  text-align: center;
  font-size: 18px;
  position: relative;

  .back {
    position: absolute;
    left: 10px;
    bottom: 13px;
  }

  span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    text-align: center;
    padding: 0 60px;
  }
}

.usersidedetail {
  background: #fff;
  min-height: calc(100vh - 73px);

  .Booked {
    padding: 22px 0;
    padding-top: 70px;
    background: #fff;
    .title {
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
    }
    h1 {
      font-size: 18px;
      color: #333;
      margin-top: 15px;
      font-weight: bold;
      margin-bottom: 20px;
    }
    p {
      line-height: 1.8;
      font-size: 14.25px;
      text-align: center;
      color: #a2a2a2;
      padding-top: 10.875px;
    }
    .bts {
      text-align: center;
      margin-top: 17px;

      :deep(.van-button) {
        height: auto;
        padding: 8px 25px;
      }
      .van-button {
        margin: 0 5px;
      }
    }
  }

  .swiper-button-prev,
  .swiper-button-next {
    width: 38px;
    height: 38px;
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: 50%;
    background-size: 8px 16px;
    top: auto;
    margin-top: 0;
    bottom: 120px;
  }
  .swiper-button-prev {
    left: 5px;
    background-image: url("data:image/svg+xml;charset=utf-8,<svg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'><path%20d%3D'M0%2C22L22%2C0l2.1%2C2.1L4.2%2C22l19.9%2C19.9L22%2C44L0%2C22L0%2C22L0%2C22z'%20fill%3D'%23ffffff'%2F><%2Fsvg>");
  }
  .swiper-button-next {
    right: 5px;
    background-image: url("data:image/svg+xml;charset=utf-8,<svg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'><path%20d%3D'M27%2C22L27%2C22L5%2C44l-2.1-2.1L22.8%2C22L2.9%2C2.1L5%2C0L27%2C22L27%2C22z'%20fill%3D'%23ffffff'%2F><%2Fsvg>");
  }

  .goods {
    margin: 10px 0;
    padding: 0 10px;
    position: relative;
  }

  .swipebanner,
  .banner a,
  .identity .children {
    box-shadow: 0px 0px 10px 1px rgba(0, 0, 0, 0.15);
    border-radius: 12px;
  }

  .swipebanner {
    width: 100%;
    .child {
      background: #fff;
      .img {
        width: 100%;
        height: 355px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #ececec;
        position: relative;
        // overflow: hidden;
        .curImg {
          max-width: 100%;
          max-height: 100%;
          position: relative;
          z-index: 2;
        }
        // .bgImg{
        //   z-index:1;
        //   position: absolute;
        //   width:100%;
        //   height:100%;
        //   left:0;
        //   top:0;
        //   -webkit-filter: blur(15px);
        //       filter: blur(15px);
        // }
      }
      .text {
        padding: 0 10px;
        .title {
          padding: 15px 10px;
          font-size: 18px;
          color: #333;
          font-weight: bold;
          border-bottom: 1px solid #e1dbdb;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .sku {
          &.active {
            :deep(.van-cell) {
              .van-cell__title {
                span {
                  color: #333;
                }
              }
            }
          }
          :deep(.van-cell) {
            padding: 15px 10px;
            .van-cell__title {
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              span {
                font-size: 18px;
                color: #ddd;
              }
            }
          }
        }
      }
    }
  }

  ::v-deep .van-cell--required {
    &:before {
      content: '';
    }

    .van-cell__title.van-field__label {
      span {
        position: relative;

        &:before {
          position: absolute;
          right: -8px;
          color: #ee0a24;
          font-size: 14px;
          content: '';
        }
      }
    }
  }

  .banner {
    padding: 10px;

    a {
      display: block;
      overflow: hidden;

      img {
        width: 100%;
        display: block;
      }
    }
  }

  .identity {
    padding: 10px;
    background: #fff;

    .children {
      overflow: hidden;
      padding: 20px 0;
    }
  }

  .introduce {
    padding: 10px;
  }

  .submit {
    padding: 0 10px;
    padding: 16px;
    position: fixed;
    width: 100%;
    bottom: 0;
    background: #fff;
  }
}
.dialog-container{
  background-color: transparent;
}
.dialog-sub-check-img{
  background: url("~@/assets/home/<USER>") no-repeat 0 0;
  background-size: 100% auto;
  width:285px;
  height:365px;
  position:relative;
  margin:0 auto;
}
.dialog-sub-check-btn{
  background: url("~@/assets/home/<USER>") no-repeat 0 0;
  background-size: 100% auto;
  width:170px;
  height:39px;
  position:relative;
  text-align: center;
  position:absolute;
  bottom:23px;
  left:58px
}
.dialog-btn-exit{
  display: block;
  margin: 0 auto;
  width: 30px;
  height: 44px;
  background: url("~@/assets/home/<USER>") no-repeat 0 0;
  background-size: 100% auto;
}
</style>
