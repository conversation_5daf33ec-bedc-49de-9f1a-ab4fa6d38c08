export default [
  {
    path: "/marketingtemplate/index.html",
    name: "marketingTemplate",
    component: resolve => require(["./index.vue"], resolve),
    meta: {
      title: "商品推广",
      login:false,
      role:[1,2]
    }
  },
  {
    path: '/marketingtemplate/config/index.html',
    name: 'marketingConfig',
    component: resolve => require(['./config/index.vue'], resolve),
    meta: {
      title: '商品推广',
      login:true,
      role:[1,2]
    }
  },
  {
    path: '/marketingtemplate/config/goods.html',
    name: 'marketingConfigGoods',
    component: resolve => require(['./config/goods.vue'], resolve),
    meta: {
      title: '商品推广',
      login:true,
      role:[1,2]
    }
  },
  {
    path: '/marketingtemplate/config/poster.html',
    name: 'marketingConfigPoster',
    component: resolve => require(['./config/poster.vue'], resolve),
    meta: {
      title: '商品推广',
      login:false,
      role:[1,2]
    }
  }
]
