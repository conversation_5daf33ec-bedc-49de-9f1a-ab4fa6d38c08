<template>
  <div>
    <Shopname
      v-if="showShopName"
      bg-color="#fff"
      :content="data.title"
      :show-back="false"
    />
    <div class="cloud-poster" :class="{ 'padding-top-34': showShopName }">
      <div class="poster-con">
        <div ref="qrCodeContainer" class="posterContainer">
          <img v-if="data.time" :src="data.qwUrl" alt="" width="288">
          <div v-else>
            <span v-if="data.message">
              {{ data.message }}
            </span>
            <span v-else>加载中……</span>
          </div>
        </div>
      </div>
      <p v-if="!UA.isApp">
        请长按图片选择“转发给朋友”，或保存图片
      </p>
      <div v-else>
        <div v-if="data.time" class="app_share">
          <span class="btn_download" @click="saveImg()">下载宣传图</span>
          <span class="btn_copyclip" @click="share(data.url,data.qwUrl,data.shopShortName)">分享给好友</span>
        </div>
      </div>
      <div class="viewPage" @click="viewPage(data.url)">
        点击这里查看推广页
      </div>
      <div class="copyQRCode">
        <div ref="qrCodeContainer1" class="qrCodeContainer clearfix">
          <img v-if="data.template" :src="getImgUrl(data.template.posterPicture)">
          <div class="shop_desc_content">
            <div class="shop_info">
              <p class="short_name_con">
                {{ data.shopShortName }}
              </p>
              <p class="lxdh_phone">
                <span>{{ data.staffName }}</span> {{ data.phone }}
              </p>
            </div>
            <div class="qrcode_info">
              <div class="qrcode_bg">
                <div id="qrCodeUrlCopy" ref="qrCodeUrlCopy" class="copyQRCodeImg"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import Shopname from '@/components/index/headercon'
import shareUtilApi from "@/utils/share"
import { reactive, ref, onBeforeMount,nextTick,onMounted,getCurrentInstance } from "vue"
import { getImgUrl } from "@/utils/utils.js"
import { getToken } from "@/utils/login/localStorage.js"
import { Toast, List } from "vant"
import MarketTmp from "@/api/marketingtemplate"
import shareApi from "@/api/share"
import loginApi from "@/api/login"
import UA from '@/utils/ua'
import QRCode from "qrcodejs2"

const showShopName =
  !UA.isWechat &&
  !UA.isWechatWork &&
  !UA.isApp &&
  !UA.isIosQQ &&
  !UA.isAndroidQQ
const data = reactive({
  title: '商品推广',
  logUser: null,
  isLogined: false,
  shopId: null,
  show: false,
  template:null,
  shopShortName:null,
  staffName:null,
  phone:null,
  qwUrl:'',
  qrcodeCopy: null,
  qrcodeurl: null,
  url:null,
  proxy:null,
  time:false,
  message:null,
  fansShareData:null
})
const RESULTCODE={
  SUCCESS:0
}
const recordId = ref(null)
onBeforeMount(async()=>{
  const url = new URL(location.href)
  data.shopId = url.searchParams.get('shopId') || url.searchParams.get('shop_id')
  recordId.value = url.searchParams.get('recordId')
  data.url='/marketingtemplate/index.html?shopId='+data.shopId+'&recordId='+recordId.value
  await getMarketingPageInfo()
  loginApi.getUserInfo(getToken(),"0",5).then((res)=>{
    if (res.code == RESULTCODE.SUCCESS) {
      logined(res.data)
    }
  })
})
async function logined(res) {
  if(!res){
    return false
  }
  data.logUser = res
  data.isLogined = res && res.UserName > ""
  data.shopId = res.shopId
}
function getMarketingPageInfo(){
  MarketTmp.getMarketingPageInfo({
    shopId:data.shopId,
    recordId:recordId.value
  }).then((r) => {
    if(r.code==RESULTCODE.SUCCESS && r.data){
      data.template=r.data.template
      data.shopShortName=r.data.shopShortName
      data.staffName=r.data.staffName
      data.phone=r.data.phone
    }else{
      data.message=r.message
      if(r.message){
        Toast(r.message)
      }else{
        Toast('系统异常，请稍后重试')
      }
    }
  })
}
const qrCodeUrlCopy = ref(null)
async function creatQrCode(){
  if (data.isQW) {
    return false
  }
  if (data.qrcodeCopy) {
    data.qrcodeCopy.clear()
  }
  data.qrcodeurl= await getShortUrl(window.location.origin+"/yundian"+data.url)
  //防止dialog首次加载不出二维码 在dialog上加@opened 回调
  data.qrcodeCopy = new QRCode(document.querySelector('#qrCodeUrlCopy'), {
    text: data.qrcodeurl,
    width: 129,
    height: 129,
    colorDark: "#000000",
    colorLight: "#ffffff",
    correctLevel: QRCode.CorrectLevel.H,
  })
  nextTick(() => {
    setTimeout(()=>{
      if(!data.message){
        getPoster()
      }
    },1000)
  })
}
let qrCodeContainer1 = ref(null)
function getPoster() {
  data.qwUrl = ""
  shareUtilApi.getImg(qrCodeContainer1.value).then((imgUrl) => {
    data.qwUrl = imgUrl
    data.time=true
    showQrCode()
  })
}
// 下载宣传图
function saveImg() {
  const targetDom = qrCodeContainer1.value
  shareUtilApi.saveImg(targetDom, () => {
    Toast("图片已保存")
    data.show = false
  })
}
/**分享事件 */
function share(url,qwUrl,shopShortName) {
  data.fansShareData = {
    url : window.location.origin+"/yundian"+url,
    imgUrl : 'https://img0.shop.10086.cn/favicon.png__175.png',
    desc : '商品推广',
    title : shopShortName
  }
  //修改微信分享配置
  shareUtilApi.appH5Share(data.fansShareData)
}
const emit = defineEmits(["close"])
function showQrCode() {
  data.show = true
  emit('close')
}
async function getShortUrl(url) {
  const res=await shareApi.getShortUrl({ url: url })
  if(res.data&&res.data.url) {
    return res.data.url
  } else {
    return url
  }
}
onMounted(() => {
  let getCurrentVue = getCurrentInstance()
  data.proxy = getCurrentVue ? getCurrentVue.proxy : null
  creatQrCode()
})
const viewPage = (url)=>{
  if(data.proxy){
    data.proxy.$router.push({path:url})
  }
}
</script>
<style>
  #app {
    background: #E5E5E5;
  }
  .head{
    font-size: 16px;
    font-weight: bold;
  }
  .icpinfo{
    margin-bottom: 86px;
  }
</style>
<style lang="scss" scoped>
@import "@/styles/minix.scss";
$px: 3Px;
  .clearfix:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
  .cloud-poster{
    text-align: center;
    line-height: 30px;
    font-size: 14px;
    p{
      font-size: 14px;
      color: #666;
      line-height: 20px;
    }
    .viewPage{
      display: inline-block;
      width: 315px;
      height: 48px;
      line-height: 48px;
      font-size: 16px;
      color: #3774fd;
      border: 1px solid #367ffb;
      border-radius: 27px;
      margin: 40px auto;
    }
    .poster-con{
      width: 288px;
      margin: 0 auto;
      padding: 30px 0 16px;
      .posterContainer{
        width: 100%;
        img{
          width: 100%;
        }
      }
    }
    .shop_desc_content{
      display: flex;
      width: 288 * $px;
      height: 68 * $px;
      background: url("~@/assets/marketingtemplate/poster_bot.png") no-repeat 0 0;
      background-color: #0687F4;
      background-size: 100% auto;
      padding: 0;
      .shop_info{
        padding: 10 * $px 0 0 14 * $px;
        text-align: left;
        width: 218 * $px;
        p{
          color: #fff;
          font-size: 12 * $px;
        }
        .short_name_con{
          color: #fff;
          line-height: 14 * $px;
          height: 14 * $px;
          font-size: 12 * $px;
        }
        .lxdh_phone{
          font-size: 9 * $px;
          line-height: 17 * $px;
          height: 17 * $px;
          padding-left: 14 * $px;
          span{
            margin-right: 2 * $px;
          }
        }
      }
      .qrcode_info{
        width: 70 * $px;
        padding: 13*$px 16*$px 0 11*$px;
      }
    }
    .copyQRCode {
      position: fixed;
      top: -10000 * $px;
      background: #E5E5E5;
      img{
        display: block;
        width: 288 * $px;
      }
    }
    .qrCodeContainer{
      width: 288 * $px;
    }
    .app_share{
      display: flex;
      align-items: center;
      width: 310px;
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      color: #fff;
      margin: 0 auto;
      span{
        display: inline-block;
        border-radius: 18px;
        flex: 1;
      }
      .btn_download{
        background: linear-gradient(90deg, #5fc8ff, #8372fe);
        margin-right: 10px;
      }
      .btn_copyclip{
        background: linear-gradient(90deg, #93da80, #4fb5b1);
        margin-left: 10px;
      }
    }
  }
  .padding-top-34 {
    padding-top: 34px !important;
  }
</style>
