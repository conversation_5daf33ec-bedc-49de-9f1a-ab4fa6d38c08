<template>
  <div class="content marketing-template">
    <Shopname
      v-if="showShopName"
      bg-color="#fff"
      :content="data.title"
      :show-back="false"
    />
    <h6 :class="{ 'padding-top-34': showShopName }">
      选择营销模板
    </h6>
    <div class="template-content">
      <List
        v-if="data.templatelist && data.templatelist.length > 0"
        id="activity-detail__main"
        v-model="data.loading"
        :finished="data.finished"
        :finished-text="data.showNoData ? '' : '没有更多了'"
        class="broad-cast__list"
      >
        <SelectTemplate :items="data.templatelist" :temcurrent="data.templateCurrent" :templatetype="1" @emitSelectTemplate="emitSelectTemplate" />
      </List>
      <div v-else class="tab-no-con">
        <img src="~@/assets/index_img/ice_tab_no.png" />
        <p>模板正在准备中，敬请期待 …</p>
      </div>
    </div>
    <div class="but">
      <button :class="{'selecttem': data.temCur}" @click="nextStep()">
        下一步
      </button>
    </div>
  </div>
</template>
<script setup>
import Shopname from '@/components/index/headercon'
import SelectTemplate from "@/components/marketingtemplate/selectTemplate.vue"
import Vue, { reactive, ref, getCurrentInstance, onBeforeMount } from "vue"
import { getToken } from "@/utils/login/localStorage.js"
import { Toast, List } from "vant"
import MarketTmp from "@/api/marketingtemplate"
import loginApi from "@/api/login"
import UA from '@/utils/ua'
const data = reactive({
  title: '商品推广',
  logUser: null,
  isLogined: false,
  templatelist: [],
  loading:false,
  finished:false,
  showNoData:false,
  temCur: false,
  templateCurrent: []
})
const showShopName =
  !UA.isWechat &&
  !UA.isWechatWork &&
  !UA.isApp &&
  !UA.isIosQQ &&
  !UA.isAndroidQQ
const RESULTCODE={
  SUCCESS:0
}
const shopId = ref(null)
const temCurId = ref(null)
onBeforeMount(async()=>{
  const url = new URL(location.href)
  shopId.value = url.searchParams.get('shopId') || url.searchParams.get('shop_id')
  temCurId.value = url.searchParams.get('temCurId')
  loginApi.getUserInfo(getToken(),"0",5).then((res)=>{
    if (res.code == RESULTCODE.SUCCESS) {
      logined(res.data)
    }
  })
})
async function logined(res) {
  if(!res){
    return false
  }
  data.logUser = res
  data.isLogined = res && res.UserName > ""
  shopId.value = res.shopId
  getTemplatelist()
}
// 获取模板列表
function getTemplatelist() {
  data.loading = true
  MarketTmp.getMarketingTemplate({ shopId:shopId.value }).then((r) => {
    data.loading = false
    if(r.code==RESULTCODE.SUCCESS && r.data){
      data.templatelist=r.data.marketingTemplateList
      if(temCurId.value){
        for(let i in data.templatelist){
          if(data.templatelist[i].templateId == temCurId.value){
            data.templateCurrent.push(data.templatelist[i])
            data.temCur = true
          }
        }
      }
    }else{
      if(r.message){
        Toast(r.message)
      }else{
        Toast('系统异常，请稍后重试')
      }
    }
    data.showNoData = false
    data.finished = true
  })
}
// 选择模板回调
function emitSelectTemplate(item) {
  data.templateCurrent = item
  data.temCur = true
}
// 选完模板下一步
const getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null
function nextStep() {
  if(data.temCur){
    //链接跳转
    proxy.$router.push({path:'/marketingtemplate/config/goods.html?shopId='+shopId.value+'&temCurId='+data.templateCurrent[0].templateId})
  }
}
</script>
<style>
  #app {
    background: #E5E5E5;
  }
  .head{
    font-size: 16px;
    font-weight: bold;
  }
  .icpinfo{
    margin-bottom: 86px;
  }
</style>
<style lang="scss" scoped>
  h6{
    font-weight: bold;
    color: #050505;
    font-size: 16px;
    line-height: 22px;
    padding: 20px 11px 4px;
  }
  .but{
    text-align: center;
    width: 100%;
    height: 86px;
    box-shadow: 0px 0px 1px 0px rgba(0,0,0,0.22);
    background: #fff;
    position: fixed;
    left: 0;
    bottom: 0;
    p{
      display: inline-block;
    }
    button{
      width: 334px;
      height: 48px;
      background-color: #D7D8D7;
      border-radius: 27px;
      line-height: 48px;
      color: #fff;
      margin: 13px auto 0;
      font-size: 16px;
      border: none;
    }
    button.selecttem{
      background: linear-gradient(135deg,#3759ff, #34caf4);
    }
  }
  .tab-no-con{
    text-align: center;
    color: #333;
    padding: 50px 0 75px;
    line-height: 37.5px;
    font-size: 13.5px;
  }
  .padding-top-34 {
    padding-top: 44px !important;
  }
</style>
