<template>
  <div>
    <Shopname
      v-if="showShopName"
      bg-color="#fff"
      :content="data.title"
      :show-back="false"
    />
    <div class="content marketing-goods" :class="{ 'padding-top-34': showShopName }">
      <h6>
        选择推广商品
      </h6>
      <p class="return-back" @click="goBack(data.shopId)">
        返回上一步
      </p>
      <div class="goods-content">
        <div class="goods-left">
          <div v-for="(item, key) in data.guideNav" :key="key">
            <p v-if="data.goodsdatalists[item.goodsType]&&data.goodsdatalists[item.goodsType].length!=0" :class="{'current':item.goodsType===data.guideNavCur}" @click="getguidenav(item.goodsType)">
              {{ item.name }}
            </p>
          </div>
        </div>
        <div class="goods-right">
          <List
            v-if="data.goodslist && data.goodslist.length > 0"
            id="activity-detail__main"
            v-model="data.loading"
            :finished="data.finished"
            :finished-text="data.showNoData ? '' : '没有更多了'"
            class="broad-cast__list"
            @load="getGoodslist"
          >
            <SelectTemplate :items="data.goodslist" :godcur="data.goodStotal" :godscurrent="data.goodsCurrent" :templatetype="2" @emitSelectgoods="emitSelectGoods" />
          </List>
          <div v-else class="tab-no-con">
            <img src="~@/assets/index_img/ice_tab_no.png" />
            <p>商品正在准备中，敬请期待 …</p>
          </div>
        </div>
      </div>
      <div class="but">
        <p>已选<span>{{ data.goodslength }}/{{ data.goodStotal }}</span>个商品</p>
        <button :class="{'selecttem': data.godsCur}" @click="selectComplete()">
          完成
        </button>
      </div>
    </div>
  </div>
</template>
<script setup>
import Shopname from '@/components/index/headercon'
import SelectTemplate from "@/components/marketingtemplate/selectTemplate.vue"
import Vue, { reactive, ref, getCurrentInstance, onBeforeMount } from "vue"
import { getToken } from "@/utils/login/localStorage.js"
import { Toast, List } from "vant"
import MarketTmp from "@/api/marketingtemplate"
import loginApi from "@/api/login"
import UA from '@/utils/ua'
const data = reactive({
  title: '商品推广',
  logUser: null,
  isLogined: false,
  shopId: null,
  goodsdatalists: [],
  goodslist: [],
  guideNav:[],
  guideNavCur:"",
  loading:false,
  finished:false,
  showNoData:false,
  godsCur: false,
  goodsCurrent: [],
  goodsIdList:[],
  goodslength: 0,
  goodStotal: ''
})
const showShopName =
  !UA.isWechat &&
  !UA.isWechatWork &&
  !UA.isApp &&
  !UA.isIosQQ &&
  !UA.isAndroidQQ
const RESULTCODE={
  SUCCESS:0
}
const temCurId = ref(null)
onBeforeMount(async()=>{
  const url = new URL(location.href)
  data.shopId = url.searchParams.get('shopId') || url.searchParams.get('shop_id')
  temCurId.value = url.searchParams.get('temCurId')
  loginApi.getUserInfo(getToken(),"0",5).then((res)=>{
    if (res.code == RESULTCODE.SUCCESS) {
      logined(res.data)
    }
  })
  getMarketingTemplateGoods()
})
async function logined(res) {
  if(!res){
    return false
  }
  data.logUser = res
  data.isLogined = res && res.UserName > ""
  if(res.shopId){
    data.shopId = res.shopId
  }
}
function getMarketingTemplateGoods() {
  data.loading = true
  MarketTmp.getMarketingTemplateGoods({ shopId:data.shopId,templateId:temCurId.value }).then((r) => {
    data.loading = false
    if(r.code==RESULTCODE.SUCCESS && r.data){
      data.goodsdatalists=r.data
      data.goodStotal=r.data.maxNum
      let datakey=[]
      for(let i in data.goodsdatalists){
        if(data.goodsdatalists[i].length!=0&&i!='total'&&i!='maxNum'){
          datakey.push(i)
        }
      }
      data.guideNavCur=datakey[0]
      if(datakey.length!=0){
        getGoodslist()
      }
    }else{
      if(r.message){
        Toast(r.message)
      }else{
        Toast('系统异常，请稍后重试')
      }
    }
    data.showNoData = false
    data.finished = true
  })
}
// 获取商品列表
function getGoodslist() {
  data.guideNav=[
    {
      name: "配件专区",
      goodsType: "partGoodsList",
    },{
      name: "宽带专区",
      goodsType: "broadbandGoodsList",
    },{
      name: "号码专区",
      goodsType: "numberGoodsList",
    },{
      name: "终端专区",
      goodsType: "terminalGoodsList",
    },{
      name: "套餐专区",
      goodsType: "packageGoodsList",
    },{
      name: "流量专区",
      goodsType: "flowGoodsList",
    }
  ]
  getguidenav(data.guideNavCur)
}
// 返回上一步
const getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null
function goBack(shopId) {
  proxy.$router.push({path:'/marketingtemplate/config/index.html?shopId='+shopId+'&temCurId='+temCurId.value})
}
// 选择商品回调
function emitSelectGoods(item) {
  data.goodsCurrent = item
  data.goodslength = data.goodsCurrent.length
  if(data.goodslength==data.goodStotal){
    data.godsCur = true
    data.goodsIdList=[]
    for(let i in item){
      if(item[i].goodsId){
        data.goodsIdList.push(item[i].goodsId+'')
      }
    }
  }else{
    data.godsCur = false
  }
}
//导航切换
function getguidenav(item) {
  data.guideNavCur=item
  data.goodslist= data.goodsdatalists[item]
}
// 选择完成
function selectComplete() {
  if(data.godsCur){
    MarketTmp.getAddRecord({
      shopId:data.shopId,
      templateId:temCurId.value,
      goodsIdList:data.goodsIdList
    }).then((r) => {
      if(r.code==RESULTCODE.SUCCESS && r.data){
        proxy.$router.push({path:'/marketingtemplate/config/poster.html?shopId='+data.shopId+'&recordId='+r.data.recordId})
      }else{
        if(r.message){
          Toast(r.message)
        }else{
          Toast('系统异常，请稍后重试')
        }
      }
    })
  }
}
</script>
<style>
  #app {
    background: #E5E5E5;
  }
  .head{
    font-size: 16px;
    font-weight: bold;
  }
  .icpinfo{
    margin-bottom: 86px;
  }
</style>
<style lang="scss" scoped>
  h6{
    color: #050505;
    font-size: 16px;
    line-height: 22px;
    padding: 10px 14px 7px;
    font-weight: bold;
  }
  .but{
    width: 100%;
    height: 86px;
    box-shadow: 0px 0px 1px 0px rgba(0,0,0,0.22);
    background: #fff;
    position: fixed;
    left: 0;
    bottom: 0;
    p{
      display: inline-block;
      font-size: 14px;
      padding: 27px 0 0 15px;
      line-height: 20px;
      span{
        color: #E40000;
      }
    }
    button{
      float: right;
      width: 179px;
      height: 44px;
      background: #D7D8D7;
      border-radius: 22px;
      line-height: 44px;
      color: #fff;
      margin: 13px 21px 0 0;
      font-size: 16px;
      border: none;
    }
    button.selecttem{
      background: linear-gradient(135deg,#3759ff, #34caf4);
    }
  }
  .marketing-goods{
    .return-back{
      padding: 0 14px;
      color: #3684fb;
      font-size: 14px;
      line-height: 20px;
      display: inline-block;
    }
    .goods-content{
      display: flex;
      // align-items: center;
      justify-content: center;
      padding-top: 10px;
      .goods-left{
        min-height: 74vh;
        width: 92px;
        background: #f6f6f6;
        font-size: 14px;
        line-height: 44px;
        text-align: center;
        color: #666666;
        .current{
          background: #fff;
          color: #050505;
        }
      }
      .goods-right{
        flex: 1;
        background: #fff;
        padding: 10px 0;
        .goods-list-con{
          img{
            height: 120px;
          }
        }
      }
    }
  }
  .cloud-poster{
    text-align: center;
    line-height: 30px;
    font-size: 14px;
    p{
      font-size: 14px;
      color: #666;
      line-height: 20px;
    }
    a{
      display: inline-block;
      width: 315px;
      height: 48px;
      line-height: 48px;
      font-size: 16px;
      color: #3774fd;
      border: 1px solid #367ffb;
      border-radius: 27px;
      margin: 40px auto 20px;
    }
    div{
      width: 288px;
      margin: 0 auto;
      padding: 30px 0 16px;
      img{
        display: block;
        width: 100%;
      }
      .shop_desc_content{
        display: flex;
        background: url("~@/assets/marketingtemplate/poster_bot.png") no-repeat 0 0;
        background-color: #0687F4;
        background-size: 100% auto;
        padding: 0;
        .shop_info{
          padding: 10px 0 0 14px;
          text-align: left;
          p{
            color: #fff;
            font-size: 12px;
          }
          .short_name_con{
            color: #fff;
            line-height: 15px;
            font-size: 13px;
          }
          .lxdh_phone{
            font-size: 10px;
            line-height: 14px;
            padding-left: 13px;
            span{
              margin-right: 2px;
            }
          }
        }
        .qrcode_info{
          width: 70px;
          padding: 11px 13px;
        }
      }
    }
  }
  .tab-no-con{
    text-align: center;
    color: #d6dce4;
    padding: 50px 0 75px;
    line-height: 37.5px;
    font-size: 13.5px;
  }
  .padding-top-34 {
    padding-top: 34px !important;
  }
</style>
