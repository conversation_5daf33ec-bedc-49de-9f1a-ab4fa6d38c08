<template>
  <div class="content" :style="{ backgroundImage: `url(${data.conbackg})` }">
    <div v-if="data.imgtextlist&&data.imgtextlist.length > 0">
      <div v-for="(item, index) in data.imgtextlist" :key="index">
        <div v-if="item.plateType==2" class="img-text">
          <div v-if="item.layout==2">
            <ImageTextPlate :items="item" :imgtxtype="1" />
          </div>
          <div v-else>
            <ImageTextPlate :items="item" :imgtxtype="2" />
          </div>
        </div>
        <div v-else-if="item.plateType==3" class="img-text">
          <div v-if="item.layout==4">
            <GoodsMarketing :items="item" :goodslist="data.goodsmarket" :imgtxtype="2" />
          </div>
          <div v-else>
            <GoodsMarketing :items="item" :goodslist="data.goodsmarket" :imgtxtype="1" />
          </div>
        </div>
      </div>
    </div>
    <div v-else class="no-con">
      <img src="~@/assets/index_img/ice_tab_no.png" />
      <p>暂无活动商品</p>
    </div>
  </div>
</template>
<script setup>
import GoodsMarketing from "@/components/marketingtemplate/goodsMarketing.vue"
import ImageTextPlate from "@/components/marketingtemplate/imageTextPlate.vue"
import { getToken } from "@/utils/login/localStorage.js"
import { Toast } from "vant"
import { reactive, ref , onBeforeMount } from "vue"
import { getImgUrl } from "@/utils/utils.js"
import MarketTmp from "@/api/marketingtemplate"
import loginApi from "@/api/login"
import "vant/lib/index.css"
const data = reactive({
  logUser: null,
  isLogined: false,
  shopId:null,
  conbackg: '',
  imgtextlist: [],
  goodsmarket: [],
})
const RESULTCODE={
  SUCCESS:0
}
const recordId = ref(null)
onBeforeMount(async()=>{
  const url = new URL(location.href)
  data.shopId = url.searchParams.get('shopId') || url.searchParams.get('shop_id')
  recordId.value = url.searchParams.get('recordId')
  loginApi.getUserInfo(getToken(),"0",5).then((res)=>{
    if (res.code == RESULTCODE.SUCCESS) {
      logined(res.data)
    }
  })
  getImgtext()
})
async function logined(res) {
  if(!res){
    return false
  }
  data.logUser = res
  data.isLogined = res && res.UserName > ""
  data.shopId = res.shopId
}
function getImgtext() {
  MarketTmp.getMarketingPageInfo({
    shopId:data.shopId,
    recordId:recordId.value
  }).then((r) => {
    if(r.code==RESULTCODE.SUCCESS && r.data){
      data.imgtextlist=r.data.plateList
      data.goodsmarket=r.data.goodsList
      for(let i in r.data.plateList){
        if(r.data.plateList[i].layout==1){
          data.conbackg=getImgUrl(r.data.plateList[i].titleImage)
        }
      }
    }else{
      if(r.message){
        Toast(r.message)
      }else{
        Toast('系统异常，请稍后重试')
      }
    }
  })
}
</script>
<style lang="scss" scoped>
  .content{
    min-height: 100vh;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100% auto;
    .img-text{
      padding: 10px;
    }
    .no-con{
      text-align: center;
      color: #999;
      padding: 220px 0 75px;
      line-height: 37.5px;
      font-size: 13.5px;
    }
  }
</style>
