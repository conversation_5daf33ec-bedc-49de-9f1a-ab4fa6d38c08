<template>
  <main class="aggregatedshare">
    <!-- <referees :tel="aggregetedData.telkey" /> -->

    <LoginDialog
      :islogin="aggregetedData.is4gLogined"
      :is-force-login="true"
      :isloginfn="logined"
      :is-auto-login="aggregetedData.autoLogin"
      :show-cancle="false"
    />

    <Collapse v-model="aggregetedData.activeNames">
      <CollapseItem
        v-for="(item, index) in aggregetedData.dataList"
        :key="index"
        :title="item.title"
        :name="index"
      >
        <Panel
          v-clipboard:copy="item.convertUrl"
          v-clipboard:success="onCopy"
          v-clipboard:error="onError"
          :title="item.title"
          :desc="item.convertUrl"
          status=""
        >
          <div class="erweima">
            <div class="title">
              {{ item.title }}
            </div>
            <div ref="imgroot" class="img"></div>
          </div>
          <template #footer>
            <div class="btnparent">
              <Button
                class="btn"
                size="large"
                type="info"
                @click.stop="handleDetail(item)"
              >
                进入聚合页面
              </Button>
            </div>
          </template>
          <template #header>
            <div class="ka1" style="color: #333">
              {{ item.title }}
            </div>
            <div class="ka2">
              {{ item.convertUrl }}
              <span>复制</span>
            </div>
          </template>
        </Panel>
      </CollapseItem>
    </Collapse>
  </main>
</template>

<script setup>
import { Collapse, CollapseItem, Panel, Button,Toast } from 'vant'
import Vue, { onMounted, reactive, getCurrentInstance,ref } from 'vue'
import aggregatedshare from '@/api/aggregatedshare'
import insertCode from '@/utils/insertCode'
import QRCode from 'qrcodejs2'
import VueClipboard from 'vue-clipboard2'
import { getShortUrl } from '@/api/share'
import UA from '@/utils/ua'
import shareUtilApi from '@/utils/share'
import loginUtils from '@/utils/login'
import LoginDialog from '@/components/login/index'
import ua from '@/utils/ua'
import { copy } from '@/utils/utils'

Vue.use(VueClipboard)

const aggregetedData = reactive({
  activeNames: [0],
  dataList: [],
  successarr: [],

  autoLogin: false,
  is4gLogined: null,

  key: null,
  telkey: null,
  proxy: null,
})

const shareConfig = {
  title: '聚合页',
  url: window.location.href,
  desc: window.location.href,
  imgUrl: 'https://img1.shop.10086.cn/goods/tc2txqenygq3nkry_940x7200',
}

function autoLoginCb(res) {
  aggregetedData.autoLogin = true
  aggregetedData.is4gLogined = false
}
function logined(res) {
  aggregetedData.myuser = res
  aggregetedData.isLogined = res && res.UserName > ''
  aggregetedData.unifiedChannelId = res.unifiedChannelId
  getInit()
}


// 获取key和分享数据
function getKey(){
  aggregetedData.proxy.$nextTick(() => {
    if (aggregetedData.dataList.length > 0) {
      let key = GetQueryString(
        aggregetedData.dataList[0].activityUrl,
        'key'
      )
      if (key) {
        let params = getQueryParam()
        params.key = key

        let filterurl = createURL(
          location.origin + location.pathname,
          params
        )
        shareConfig.url = filterurl
        shareConfig.desc = filterurl

        shareConfig.unifiedChannelId = aggregetedData.unifiedChannelId

        share()
      }
    }
  })
}
// 获取短链的方法
function getShortUrlFn(items){
  let item = copy(items)
  let a1 = new URL(item.activityUrl)
  let params = getQueryParam(a1.href)

  // aggregetedData.telkey = desDeclassified(params.key)
  if (aggregetedData.key) {
    params.key = aggregetedData.key
  }

  // 小程序环境下加入wt.mc_id
  if (aggregetedData.isXCX) {
    params['WT.mc_id'] = `YD_XCX_${aggregetedData.unifiedChannelId}`
  }

  params['WT.ac_id'] = 'SHOP_COLLECTIONPAGE_SHARE'

  item.activityUrl = createURL(a1.origin + a1.pathname, params)
  return getShortUrl({
    url: item.activityUrl,
  })
}

let imgroot = ref(null)

async function getInit() {
  aggregatedshare
    .getHdPageInfo({
      shopId: aggregetedData.shopId,
    })
    .then((res) => {
      if (res.code == 0) {
        aggregetedData.dataList = res.data
        // 首屏二维码先展示出来
        getShortUrlFn(aggregetedData.dataList[0]).then((res)=>{
          aggregetedData.dataList[0].convertUrl = res.data.url
          aggregetedData.dataList = copy(aggregetedData.dataList)
          aggregetedData.proxy.$nextTick(() => {
            if (imgroot.value) {
              let ref = imgroot.value[0]
              let text = res.data.url
              erweima(text, ref, 0)
            }
          })
        })
        // 获取短链接口组
        const promiceArr = res.data.map((item,index) => {
          return getShortUrlFn(item)
        })

        Promise.all(promiceArr).then((promiseall) => {
          promiseall.forEach((item, index) => {
            let url = item.data.url
            aggregetedData.dataList[index].convertUrl = `${url}`
          })
          aggregetedData.dataList.forEach((item, index) => {
            aggregetedData.activeNames.push(index)
          })
          aggregetedData.proxy.$nextTick(() => {
            aggregetedData.activeNames.forEach((item) => {
              if (imgroot.value) {
                let ref = imgroot.value[item]
                let text = aggregetedData.dataList[item]
                  ? aggregetedData.dataList[item].convertUrl
                  : ''
                erweima(text, ref, item)
              }
            })
          })
        })

        // 获取key和分享数据
        getKey()
      }
    })
}

function GetQueryString(url, name) {
  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
  let ms = new URL(url)
  var r = ms.search.substr(1).match(reg)
  if (r != null) {
    return unescape(r[2])
  }
  return null
}

function getQueryParam(url) {
  url = url == null ? window.location.href : url
  let search = url.substring(url.lastIndexOf('?') + 1)
  let query = {}
  let reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    let name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    query[name] = val
    return rs
  })
  return query
}
function createURL(url, param) {
  let Url
  let queryStr = ''
  for (let key in param) {
    let link = '&' + key + '=' + param[key]
    queryStr += link
  }
  Url = url + '?' + queryStr.substr(1)
  return Url
}

function onCopy() {
  Toast('复制地址成功')
}
function onError(e) {
  Toast('复制地址失败')
}

function handleDetail(item) {
  const url = item.convertUrl+(item.convertUrl.indexOf('?') > -1 ? '&' : '?')+'WT.ac_id=SHOP_COLLECTIONPAGE_SHARE'
  insertCode('', url)
}

function erweima(text, ref, index) {
  ref.innerHTML = ''
  new QRCode(ref, {
    text: text, // 需要转换为二维码的内容
    width: 250,
    height: 250,
    colorDark: '#000000',
    colorLight: '#ffffff',
    correctLevel: QRCode.CorrectLevel.H,
  })
  aggregetedData.successarr.push(index)

  if (aggregetedData.successarr.length >= aggregetedData.dataList.length) {
    aggregetedData.activeNames = [0]
  }
}

function share() {
  if (UA.isApp) {
    shareUtilApi.appShare(shareConfig)
  } else {
    shareUtilApi.changeWxShareConfig(shareConfig)
  }
}

onMounted(async() => {
  let getVueInstance = getCurrentInstance()
  aggregetedData.proxy = getVueInstance ? getVueInstance.proxy : null
  aggregetedData.key = GetQueryString(location.href, 'key')
  aggregetedData.shopId = GetQueryString(location.href, 'shopId')
  if (aggregetedData.proxy) {
    aggregetedData.proxy.$store.commit('SET_SHOPID', aggregetedData.shopId)
  }
  aggregetedData.isXCX = await ua.isWeChatMiniApp()
  let tmp = document.cookie.match(/nalogin=([^\s;]+)/i)
  let nalogin =
    GetQueryString(location.href, 'nalogin') == 1 || (tmp && tmp[1] == 1)
      ? false
      : true
  loginUtils.login(true, true, logined, false, nalogin, autoLoginCb, '0')
})
</script>

<style lang="scss" scoped>
.btnparent {
  display: flex;
  margin-top: 10px;
  align-items: center;
  justify-content: center;
}
.erweima {
  text-align: center;
  .title {
    margin-bottom: 10px;
    margin-top: 20px;
    font-size: 16px;
  }
  .img {
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 80%;
    }
  }
}

.ka1,
.ka2 {
  font-size: 14px;
  color: #999;
  span {
    display: inline-flex;
    background: #1989fa;
    color: #fff;
    padding: 4px 10px;
    margin-top: 4px;
  }
}
.ka2 {
  word-break: break-all;
}

.aggregatedshare {
  :deep(.van-hairline--top-bottom::after),
  :deep(.van-hairline-unset--top-bottom::after) {
    border-width: 0;
  }
  :deep(.van-collapse-item__title--expanded::after) {
    display: none;
  }
}
</style>
