/* eslint-disable no-async-promise-executor */
/*
 * @User: JOJO
 * @FilePath: \yundian-m\src\model\precisionMarketing.js
 */
import { getAllSearchParamsArray } from "@/utils/utils"
import shopAPI from "@/api/shop"
import store from "@/store"
import { setSearchParamsArray } from "@/utils/utils"
import _ from "lodash"
import { constant } from "@/config"
import EventBus from "@/api/eventbus"
import Vue from "vue"
import { Toast } from "vant"
import ua from "@/utils/ua"
Vue.use(Toast)

/* 精准营销核心业务 */
export default class PrecisionMarketing {
  static iswxapp

  /**
   * @Description: 是否登录
   * @User: JOJO <<EMAIL>>
   * @Date: 2021-12-23 10:26:27
   * @param {*}
   * @return {*}
   */
  static isLogin() {
    return _.get(store.state.user, "userInfo.UserName", null)
  }

  /**
   * @Description: 如果是精准营销分享,分享的时候要加入精准营销标识
   * @User: JOJO <<EMAIL>>
   * @Date: 2021-12-23 10:25:57
   * @param {*} url 分享链接
   * @return {*}
   */
  static addShareMarketing(url) {
    return setSearchParamsArray(url, { marketing: true })
  }

  /**
   * @Description: 加入精准营销数据
   * @User: JOJO <<EMAIL>>
   * @Date: 2021-12-21 17:54:32
   * @param {*} actDataRes 原数据
   * @param {*} openlogin 登录函数
   * @return {*}
   */
  static joinGetModel(actDataRes, openlogin) {
    return new Promise(async(resolve) => {
      // 如果当前精准营销分享进来的, 要自动前往办套餐精准营销位置
      const allParams = getAllSearchParamsArray(location.href)
      if (allParams.marketing) {
        sessionStorage.setItem("indexTabContentTcClickTrue", true)
      }

      // 如果当前已经登录了, 请求精准营销数据
      if (PrecisionMarketing.isLogin()) {
        let doMealRes = await shopAPI.getDoMeal({
          shopId: getAllSearchParamsArray(location.href).shopId,
        })

        let iswxapp = await ua.isWeChatMiniApp()
        this.iswxapp = iswxapp

        if (
          doMealRes.code == 0 &&
          doMealRes.data &&
          doMealRes.data.ad &&
          doMealRes.data.ad.length
        ) {
          let indexData_1 = JSON.parse(JSON.stringify(actDataRes.data.actData))
          let parentData = indexData_1.find((item) => item.floorSort == 70)
          let parentDataIndex = indexData_1.findIndex(
            (item) => item.floorSort == 70
          )

          if(!parentData){
            resolve(actDataRes.data.actData)
            return
          }

          parentData.dataList.ad = doMealRes.data.ad.map((item) => {
            let goodsStatus = 51
            let iopOperationPositionId = constant.H5iopOperationPositionId

            // 如果是微信小程序
            if (this.iswxapp) {
              iopOperationPositionId = constant.WXiopOperationPositionId
            }

            if (item.iopOperationPositionId == iopOperationPositionId) {
              goodsStatus = null
            }

            return {
              ...item,
              goodsStatus,
              // 设置是否是精准营销标识
              frontEndPrecisionMarketingSettingLogo: true,
              // 设置当前数据是否是办套餐数据
              isDoMeal:true
            }
          })
          indexData_1.splice(parentDataIndex, 1, parentData)

          // 如果iop有数据
          sessionStorage.setItem("isDomMealDataLength", true)

          resolve(indexData_1)
        } else {
          // 如果iop没有数据
          sessionStorage.setItem("isDomMealDataLength", false)

          let indexData_1 = JSON.parse(JSON.stringify(actDataRes.data.actData))
          let parentData = indexData_1.find((item) => item.floorSort == 70)
          let parentDataIndex = indexData_1.findIndex(
            (item) => item.floorSort == 70
          )

          if(parentData && parentData.dataList && parentData.dataList.ad){
            parentData.dataList.ad = parentData.dataList.ad.map(item => {
              return {
                ...item,
                // 设置当前数据是否是办套餐数据
                isDoMeal:true
              }
            })
            indexData_1.splice(parentDataIndex, 1, parentData)
          }

          resolve(indexData_1)
        }
      } else {
        // 如果iop没有数据
        sessionStorage.setItem("isDomMealDataLength", false)
        // 如果当前是精准营销商品分享后的页面
        const allParams = getAllSearchParamsArray(location.href)
        if (allParams.marketing) {
          openlogin()
        }else{
          sessionStorage.removeItem("indexTabContentTcClickTrue")
        }

        resolve(actDataRes.data.actData)
      }
    })
  }

  /**
   * @Description: 点击办套餐必须登录
   * @User: JOJO <<EMAIL>>
   * @Date: 2021-12-22 11:00:22
   * @param {*} index 索引
   * @param {*} openlogin=fn 登录函数
   * * 例：
 *  {
 *      callback:fn,
 *      showCancle
 *  }
   * @return {*}
   */
  static beforeChangeMoveName(index, openlogin, pagedata) {
    if (index == 70 && !PrecisionMarketing.isLogin()) {
      sessionStorage.setItem("indexTabContentTcClickTrue", true)
      openlogin(null,false)
      return false
    } else if (index != 70) {
      return true
    }

    if (
      index == 70 &&
      !JSON.parse(sessionStorage.getItem("isDomMealDataLength"))
    ) {
      return new Promise((resolve) => {
        Toast.loading({
          message: "加载中...",
          forbidClick: true,
          duration: 0,
        })
        this.joinGetModel({ data: { actData: pagedata } }, openlogin).then(
          (res) => {
            EventBus.$emit("joinGetMdeol", res)
            Toast.clear()
            setTimeout(() => {
              resolve(true)
            }, 20)
          }
        )
      })
    } else {
      return true
    }

    // return true
  }

  /**
   * @Description: 精准营销商品分享回调
   * @User: JOJO <<EMAIL>>
   * @Date: 2021-12-22 11:07:52
   * @param {*} ref 分享组件dom节点
   * @return {*}
   */
  static emitPrecisionMarketing(ref, item) {
    if (ref) {
      ref[0].shareShop(null, true, item)
    }
  }

  /**
   * @Description:
   * 如果sessionStorage有的话,
   * 并且已经登录了,
   * 并且是点击套餐进来的首页
   * @User: JOJO <<EMAIL>>
   * @Date: 2021-12-22 11:15:02
   * @param {*} dom 办套餐位置的dom节点 (内蒙与基础不一样)
   * @param {*} callback 执行成功后执行回调
   * @return {*}
   */
  static istclogin(dom, callback) {
    const indexTabContentTcClickTrue = sessionStorage.getItem(
      "indexTabContentTcClickTrue"
    )
    // const isDomMealDataLength = sessionStorage.getItem("isDomMealDataLength")
    // const isTrue =
    //   indexTabContentTcClickTrue &&
    //   JSON.parse(indexTabContentTcClickTrue) &&
    //   isDomMealDataLength &&
    //   JSON.parse(isDomMealDataLength) &&
    //   PrecisionMarketing.isLogin
    const isTrue =
      indexTabContentTcClickTrue &&
      JSON.parse(indexTabContentTcClickTrue) &&
      PrecisionMarketing.isLogin
    if (isTrue) {
      // 滚动条跳到办套餐的位置
      if (dom) {
        setTimeout(() => {
          document.documentElement.scrollTop = document.body.scrollTop =
            dom.offsetTop + dom.clientHeight
        }, 20)
      }

      // 函数执行完后的回调
      callback && callback()
      sessionStorage.setItem("indexTabContentTcClickTrue", false)
    }
  }

  /**
   * @Description: 设置办套餐精准营销banner
   * @User: JOJO <<EMAIL>>
   * @Date: 2021-12-27 16:00:02
   * @param {*} data getData数据
   * @return {*} 办套餐banner数据
   */
  static setMarketingBannerData(data) {
    if (!data) {
      return
    }

    // 找到办套餐数据
    const filterData = data.find((item) => item.floorSort == 70)

    // 判断办套餐数据是否为精准营销推荐
    const adData = _.get(filterData, "dataList.ad", [])
    const isMakting = adData.every(
      (item) => item.frontEndPrecisionMarketingSettingLogo
    )

    // 不是精准营销返回空
    if (!isMakting) {
      return null
    }

    let iopOperationPositionId = constant.H5iopOperationPositionId

    // 如果是微信小程序
    if (this.iswxapp) {
      iopOperationPositionId = constant.WXiopOperationPositionId
    }

    return (
      adData.find(
        (item) => item.iopOperationPositionId == iopOperationPositionId
      ) || null
    )
  }
}
