/*
 * @User: JOJO
 * @FilePath: \yundian-m\src\model\unifiedAuthenticationMigration.js
 */
import loginApi from "@/api/login"
import { getToken } from "@/utils/login/localStorage"

/*
  1. 如果跳转到别人页面的url链接中带有token={sourceid:019015},
  2. 则用它的sourceid获取大网的token
  4. 把跳转别人页面的url链接的token字段换成获取到的大网token
  */
export default class UnifiedAuthenticationMigration {
  static init(url) {
    return new Promise((resolve) => {
      // 前缀必须是http
      if (!url.startsWith("http")) {
        resolve(url)
        return
      }

      // 获取sourceid的key与value
      const newUrl = new URL(url)
      const ssoMatch = /([^&?-]+)={sourceid:(\d+)}/i.exec(url)
      let ssoKey = null
      let sourceId = null
      if (ssoMatch && ssoMatch.length === 3) {
        ssoKey = ssoMatch[1]
        sourceId = ssoMatch[2]
      } else {
        resolve(url)
        return
      }

      // 如果没有登录要去掉token的sourceid
      if (!getToken()) {
        newUrl.searchParams.delete(ssoKey)
        resolve(newUrl.toString())
        return
      }

      // 获取到sourceid请求大网token
      loginApi
        .getCmAuthTokenWU({ targetSourceId: sourceId })
        .then((res) => {
          if (res.data && res.data.token) {
            // 对当前的url的token字段进行替换
            newUrl.searchParams.set(ssoKey, res.data.token)
            resolve(newUrl.toString())
          } else {
            newUrl.searchParams.delete(ssoKey)
            resolve(newUrl.toString())
          }
        })
        .catch(() => {
          newUrl.searchParams.delete(ssoKey)
          resolve(newUrl.toString())
        })
    })
  }
}
