/*
 * @Author: 李亚龙
 * @Date: 2021-08-18 11:45:30
 * @LastEditTime: 2021-08-19 11:18:57
 * @LastEditors: your name
 * @Description:
 * @FilePath: \yundian-m\src\store\modules\pageinfo.js
 */
// import { getToken, removeToken } from '@/utils/auth'

const pageinfo = {
  state: {
    pageInfo: null,
    shopId:null,
    isladder:false
  },

  mutations: {
    SET_PAGEINFO: (state, pageInfo) => {
      // console.log(pageInfo)
      state.pageInfo = pageInfo
    },
    SET_SHOPID:(state, shopId) => {
      // console.log(pageInfo)
      state.shopId = shopId
    },
    SET_ISLADDER:(state, isladder) => {
      // console.log(pageInfo)
      state.isladder = isladder
    }
  },

  actions: {
    // 前端 登出
    // FedLogOut({ commit }) {
    //   return new Promise(resolve => {
    //     commit('SET_TOKEN', '')
    //     removeToken()
    //     resolve()
    //   })
    // }
  }
}

export default pageinfo
