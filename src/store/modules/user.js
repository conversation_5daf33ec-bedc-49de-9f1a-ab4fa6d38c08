// import { getToken, removeToken } from '@/utils/auth'

const user = {
  state: {
    userInfo: null,
    userName:null,
    isMember:0,
    loginfirst:0,
  },

  mutations: {
    SET_USERINFO: (state, userInfo) => {
      state.userInfo = userInfo
    },
    SET_USERNAME : (state,userName) => {
      state.userName = userName
    },
    SET_ISMEMBER : (state,isMember) => {
      state.isMember = isMember
    },
    SET_LOGINFIRST : (state,loginfirst) => {
      state.loginfirst = loginfirst
    }
  },

  actions: {
    // 前端 登出
    // FedLogOut({ commit }) {
    //   return new Promise(resolve => {
    //     commit('SET_TOKEN', '')
    //     removeToken()
    //     resolve()
    //   })
    // }
  }
}

export default user
