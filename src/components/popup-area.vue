<!--
<AUTHOR>
@date   21.03.26
@description   地区选择弹窗
参数
props: {
  title: '', // String/标题 // 暂时没用
  province: '', // String/省份 限制省份选择
  city: '', // String/市 限制市选择
  defaultArr: '', // Array/默认值 : [provinceName, citeName, districtName]
  isDistrict: '', // Boolean/是否只显示区县
  isChooseWholeProvince: '', // Boolean/是否可以选择其他省
  isListWhoole: '', // Boolean/市与区是否增加"可选择"
  isProvCity: '', // Boolean/直辖区就显示 省市区, 非直辖区就显示 省市
}
事件
event:
@confirm="onAreaConfirm" // 点击确定事件返回地区数据
/**
 * @param {Object} data
 * @param {String} data.provinceId - 省id
 * @param {String} data.provinceName - 省名
 * @param {String} data.cityId - 市id
 * @param {String} data.cityName - 市名
 * @param {String} data.regionCode - 县id
 * @param {String} data.regionName - 县名
 */
onAreaConfirm(data) {}
外部函数
const area = ref(null)
area.value.open() // 显示时调用
area.value.close() // 隐藏时调用
DEMO
<popup-area ref="area" @confirm="onAreaConfirm" />
-->
<template>
  <div class="popup-area">
    <van-popup v-model="areaRactiveData.show" position="bottom" class="pc-width">
      <div class="van-popup__content">
        <Picker
          show-toolbar
          :columns="areaRactiveData.areaList"
          :loading="areaRactiveData.loading"
          @change="onChange"
          @confirm="onConfirm"
          @cancel="onCancel"
        />
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import Vue,{ref,reactive,defineProps,defineEmits,watch} from "vue"
import { Popup, Picker } from "vant"
import { PROVINCE_LIST, getCityByProvinceId, getRegionByCityId } from '@/api/area'
import lodash from 'lodash'
import { getNearShop } from "@/api/lbs"
import {copy} from "@/utils/utils"
Vue.use(Popup)
const props = defineProps({
  // 组件的标题
  title: {
    type: String,
    default: "所在地区"
  },
  province: {
    type: Object,
    default: null
  },
  city: {
    type: Object,
    default: null
  },

  /* 默认值 : [provinceName, citeName, districtName] */
  defaultArr:{
    type:Array,
    default:() => []
  },

  /* 只显示区县 */
  isDistrict:{
    type:Boolean,
    default:false
  },

  /* 是否可以选择其他省 */
  isChooseWholeProvince:{
    type:Boolean,
    default:false
  },

  /* 市与区是否增加"可选择" */
  isListWhoole:{
    type:Boolean,
    default:false
  },

  /* 直辖区就显示 省市区, 非直辖区就显示 省市 */
  isProvCity:{
    type:Boolean,
    default:false
  },
  /* 直辖区就显示 省市区, 非直辖区就显示 省市 */
  isOpen:{
    type:Boolean,
    default:false
  }
})
const emit = defineEmits(["close","confirm"])
const showPopup = ref(false)
const areaRactiveData = reactive({
  show: false,
  areaList: [
    {
      values: ['省'],
    },
    {
      values: ['市']
    },
    {
      values: ['区']
    }
  ],
  loading: false,
})
let areaData = {
  defaultArr:[],
  provinceList: [],
  cityList: [],
  regionList: [],

  addressInfo: {
    id: "",
    recName: "",
    recPhone: "",
    cityId: "",
    cityName: "",
    provinceId: "",
    provinceName: "",
    regionCode: "",
    regionName: "",
    street: "",
    areaText: "",
    // postalCode: '',
    isDefault: 0,

    // 直辖市id
    government:['100', '220', '210', '230']
  },

  // 直辖市情况下不显示区名\置灰
  dontDisplayList: [],
}
function onCancel() {
  close()
}
watch(()=>props.isOpen,(value)=>{
  areaRactiveData.show  =value
  if(value){
    open()
  }
})
watch(()=>props.defaultArr,(value)=>{
  areaData.defaultArr = value
},{deep:true})
//为了解决点击模态框关闭页面isOpen不能改变的问题
watch(()=>areaRactiveData.show,(value)=>{
  if(!value){
    close()
  }
})
function open(){
  // 首次初始化
  if (areaData.provinceList.length === 0) {
    areaRactiveData.loading = true
    init()
  }
}
function close() {
  if(!props.isChooseWholeProvince){
    setDefault()
  }
  areaRactiveData.loading = false
  emit('close')
}
/* 如果当前是直辖市 */
function isRes(){
  return props.isProvCity && ['北京', '天津', '上海', '重庆', '100', '220', '230','210'].includes(areaData.provname)
}
/* 如果当前是直辖市 */
function isRes2(res){
  return ['北京', '天津', '上海', '重庆', '100', '220', '230','210',100,220,230,210].includes(res)
}

function onConfirm(data, index) {

  let province, city, region

  if(props.isProvCity){ // 只显示省市
    if(isRes()){ // 直辖市
      province = areaData.provinceList.find(item => item.provinceName === data[0])
      let {provinceId, provinceName} = areaData.provinceList.find(item => item.provinceName === data[0])
      city = {
        cityId:provinceId,
        cityName:provinceName
      }
      region = areaData.regionList.find(item => item.regionName === data[1].text)
    }else{ // 非直辖市
      province = areaData.provinceList.find(item => item.provinceName === data[0])
      city = areaData.cityList.find(item => item.cityName === data[1].text)
    }
  }else{
    province = areaData.provinceList.find(item => item.provinceName === data[0])
    city = areaData.cityList.find(item => item.cityName === data[1])

    // 只显示区县， 要取第0个
    if(props.isDistrict){
      region = areaData.regionList.find(item => item.regionName === data[0])
    }else{
      region = areaData.regionList.find(item => item.regionName === data[2])
    }
  }

  let res = {
    provinceId: province && province.provinceId,
    provinceName: province && province.provinceName,
    cityId: city && city.cityId,
    cityName: city && city.cityName,
    regionCode: region && region.regionCode,
    regionName: region && region.regionName
  }

  res.areaText = addressText(res)

  emit('confirm', res)
  close()
}

// 初始化地址
function init() {
  if (props.province) {

    if(props.isChooseWholeProvince){
      areaData.provinceList = [ ...PROVINCE_LIST ]
    }else{
      areaData.provinceList = [ props.province ]
    }

  } else {
    areaData.provinceList = [ ...PROVINCE_LIST ]
  }

  if(props.isChooseWholeProvince && areaData.defaultArr[0]){
    let provinceNameIndex = areaData.provinceList.findIndex(item => item.provinceName === areaData.defaultArr[0].name)
    areaData.provinceNameIndex = provinceNameIndex
    areaRactiveData.areaList[0] = {
      values:areaData.provinceList.map(item => item.provinceName),
      defaultIndex:provinceNameIndex
    }
    areaRactiveData.areaList = copy(areaRactiveData.areaList)
  }

  if(areaData.defaultArr[0]){
    areaData.provname = areaData.defaultArr[0].id
  }else{
    areaData.provname = '100'
  }

  if(props.isProvCity){
    areaRactiveData.areaList.pop()
  }

  getProvince()
}
// 获取样式
function getClass(index, item) {
  if (areaData.tag === index) {
    return "nav__item--active"
  }
  if (item.disabled) {
    return "nav__item--disabled"
  }
  return ""
}
// 切换省市区
function onChange(target, data, index) {

  // 如果只有区县显示， 不处理
  if(props.isDistrict){
    return
  }

  // 级联查市
  if (index === 0) {
    let item = areaData.provinceList.find(item => item.provinceName === data[index])
    item = item?item:{}

    areaData.provname = data[index]

    getCity(item.provinceId)
  }
  // 级联查区
  else if (index === 1) {
    areaData.cityname = data[index]

    if(isRes()){
      areaRactiveData.loading = false
      return
    }

    let item = areaData.cityList.find(item => item.cityName === data[index])
    item = item?item:{}

    getRegion(item.cityId)
  }
}

// 获取省数据
function getProvince() {
  // 获取地区
  const data = areaData.provinceList.map(item => item.provinceName)
  areaRactiveData.areaList[0] = {
    values: data,
    defaultIndex:areaData.provinceNameIndex||0
  }
  areaRactiveData.areaList = copy(areaRactiveData.areaList)

  // 查询第一条省的市数据
  let provinceId = areaData.provinceList[0].provinceId
  if(props.isChooseWholeProvince){
    if(!areaData.defaultArr[0]){
      areaData.defaultArr[0] = '100'
    }
    const curItem = areaData.provinceList.find(item => item.provinceId === areaData.defaultArr[0].id)
    provinceId = curItem?curItem.provinceId:'100'
  }else{
    provinceId = areaData.provinceList[0].provinceId
  }

  areaData.provinceId = provinceId
  getCity(provinceId)
}
// 获取市数据
function getCity(provinceId) {
  if(!provinceId){
    areaRactiveData.areaList[1] = {
      values: ['请选择'],
    }
    areaRactiveData.areaList = copy(areaRactiveData.areaList)
    return
  }

  areaRactiveData.loading = true

  if (props.city) {

    areaData.cityList = [ props.city ]
    const data = areaData.cityList.map(item => item.cityName)

    // 增加可选择选项
    if(props.isListWhoole){
      data.unshift('请选择')
    }

    areaRactiveData.areaList[1] = {
      values: data,
    }
    areaRactiveData.areaList = copy(areaRactiveData.areaList)

    // 查询第一条市的区数据
    const item = areaData.cityList[0]
    getRegion(item.cityId)
  } else {
    getCityByProvinceId(provinceId).then(async(res) => {
      if (res && res.data && res.data.province&&res.data.province.cities && res.data.province.cities.length > 0) {

        // 给列表增加isShopEmpty来判断当前市区是否有店铺
        res.data.province.cities = await downtownShops({
          list:res.data.province.cities,
          provinceId:provinceId
        })

        // 设置 city
        areaData.cityList = res.data.province.cities

        // 增加可选择选项
        if(props.isListWhoole){
          // 直辖市不要在市里面显示请选择
          if(!['100', '220', '210', '230'].includes(provinceId)){
            areaData.cityList.unshift({cityName:'请选择'})
          }
        }

        let data = areaData.cityList.map(item => item.cityName)
        data = data.map(item => ({text:item}))
        // 不是直辖市显示城市列表
        if(!(props.isProvCity && ['100', '220', '210', '230'].includes(provinceId))){
          areaRactiveData.areaList[1] = {
            values: data,
          }
          areaRactiveData.areaList = copy(areaRactiveData.areaList)
        }

        // 查询第一条市的区数据
        const item = areaData.cityList[0]

        getRegion(item.cityId)
      }
    }).catch((err) => {
      areaRactiveData.loading = false
    })
  }
}
// 获取区数据
function getRegion(cityId) {

  if(!cityId){
    if(!props.isProvCity){
      areaRactiveData.areaList[2] = {
        values: ['请选择'],
      }
      areaRactiveData.areaList = copy(areaRactiveData.areaList)
    }

    areaRactiveData.loading = false
    return
  }

  if(!props.isProvCity){
    areaRactiveData.loading = true
  }

  getRegionByCityId(cityId).then(async(res) => {
    if (res && res.data && res.data.city && res.data.city.towns && res.data.city.towns.length > 0) {

      // 给列表增加isShopEmpty来判断当前市区是否有店铺
      res.data.city.towns = await downtownShops({
        list:res.data.city.towns,
        cityId,
        provinceId:cityId
      })

      areaRactiveData.loading = false
      // 设置 region
      areaData.regionList = res.data.city.towns.map(item=>{
        item.regionCode = item.townId
        item.regionName = item.townName
        return item
      })
      let data = res.data.city.towns.map(item => item.regionName)
      // 增加可选择选项
      if(props.isListWhoole){
        data.unshift('请选择')
      }
      // 如果只有区县显示
      if(props.isDistrict){
        areaRactiveData.areaList = [
          {
            values: data,
          }
        ]
      }else{

        if(!props.isProvCity){
          if(!props.isProvCity){
            areaRactiveData.areaList[2] = {
              values: data,
            }
            areaRactiveData.areaList = copy(areaRactiveData.areaList)
          }
        }else{
          if(!props.isProvCity){
            areaRactiveData.areaList[2] = undefined
            areaRactiveData.areaList = copy(areaRactiveData.areaList)
          }
        }

        if(isRes()){
          data = data.map(item => ({text:item}))

          // 如果有隐藏的区名, 就不要显示
          if(areaData.dontDisplayList.length){
            data = data.map(item => {
              if(areaData.dontDisplayList.includes(item.text)){
                item.disabled = true
              }
              return item
            })
          }
          areaRactiveData.areaList[1] = {
            values: data,
          }
          areaRactiveData.areaList = copy(areaRactiveData.areaList)
        }

      }

      if(!areaData.isInit){
        setDefault()
        areaData.isInit = true
      }



    }
  }).catch((err) => {
    areaRactiveData.loading = false
  })
}
/* 根据id获取查询到的店铺数据 */
function downtownShops({
  list,
  cityId,
  provinceId
} = {}){
  return new Promise(resolve => {

    // 过滤请选择
    const pleaseSelectArray = list.filter(item => item.cityName === '请选择')
    if(pleaseSelectArray.length){
      list = list.filter(item => item.cityName !== '请选择')
    }

    // 过滤区id
    const idList = list.map(item => {
      return item.townId || item.cityId
    })

    // 如果不是附近店铺专用的省市选择
    if(!props.isListWhoole){
      resolve(list)
      return
    }

    // 如果当前cityid不是直辖市
    // if(!isRes2(cityId)){
    //   resolve(list)
    //   return
    // }

    // 返回直辖市每个区查询是否有店铺的promise数组
    const IdPromise = idList.map(item => {
      return new Promise((resolve) => {
        const options = {
          province:provinceId,
          city:cityId,
          county:item
        }

        // 非直辖市取的字段不一样
        if(!isRes2(provinceId)){
          options.county = ''
          options.province = provinceId
          options.city = item
        }

        getNearShop(options).then(res => {
          resolve(res.data?res.data:{})
        }).catch(() => {
          resolve({})
        })
      })
    })

    // 根据每个区id查询是否有店铺，没有店铺置灰禁用
    Promise.all(IdPromise).then(res2 => {
      // 给每一个区添加isShopEmpty用来判断是否有店铺
      for (let i = 0; i < res2.length; i++) {
        list[i].isShopEmpty = !res2[i].shops.length
      }
      // 给不要显示的区别数组赋值, 只有给这个数组赋值, 置灰才生效
      areaData.dontDisplayList = list.filter(item => item.isShopEmpty).map(item => {
        return item.townName || item.cityName
      })
      resolve(list)
    }).catch(() => {
      resolve(list)
    })
  })
}
function addressText(data) {
  const {
    provinceName,
    cityName,
    regionName
  } = data
  if (provinceName) {
    if (provinceName === cityName) {
      return `${cityName} ${regionName}`
    }
    return `${provinceName} ${cityName} ${regionName}`
  }
  return ""
}

/* 设置默认值 */
function setDefault(){

  if(areaData.defaultArr && areaData.defaultArr.length){

    let provinceNameIndex = 0, cityNameIndex = 0, regionNameIndex = 0

    if(areaData.defaultArr[0]){
      provinceNameIndex = lodash.get(areaRactiveData.areaList[0], 'values').findIndex(item => item === areaData.defaultArr[0].name)
      areaRactiveData.areaList[0] = {
        values:areaRactiveData.areaList[0].values,
        defaultIndex:provinceNameIndex
      }
      areaRactiveData.areaList = copy(areaRactiveData.areaList)
    }

    if(areaData.defaultArr[1]){
      cityNameIndex = lodash.get(areaRactiveData.areaList[1], 'values').findIndex(item => item === areaData.defaultArr[1].name)

      areaRactiveData.areaList[1] = {
        values:areaRactiveData.areaList[1].values,
        defaultIndex:cityNameIndex
      }
      areaRactiveData.areaList = copy(areaRactiveData.areaList)
    }

    if(areaData.defaultArr[2]){
      regionNameIndex = lodash.get(areaRactiveData.areaList[2], 'values').findIndex(item => item === areaData.defaultArr[2].name)
      areaRactiveData.areaList[2] = {
        values:areaRactiveData.areaList[2].values,
        defaultIndex:regionNameIndex
      }
      areaRactiveData.areaList = copy(areaRactiveData.areaList)
    }
  }
}


</script>

<style lang="scss">
.popup-area {
  font-size: 30px;

  .van-popup__content {
    padding-bottom: 0;
  }

}
</style>
