<!--
<AUTHOR>
@date   21.03.26
@description   地区选择弹窗
参数
props: {
  title: '', // String/标题 // 暂时没用
  province: '', // String/省份 限制省份选择
  city: '', // String/市 限制市选择
}
事件
event:
@confirm="onAreaConfirm" // 点击确定事件返回地区数据
/**
 * @param {Object} data
 * @param {String} data.provinceId - 省id
 * @param {String} data.provinceName - 省名
 * @param {String} data.cityId - 市id
 * @param {String} data.cityName - 市名
 * @param {String} data.regionCode - 县id
 * @param {String} data.regionName - 县名
 */
onAreaConfirm(data) {}
外部函数
$refs.area.open() // 显示时调用
$refs.area.close() // 隐藏时调用
DEMO
<popup-area ref="area" @confirm="onAreaConfirm" />
-->
<template>
  <div class="popup-area">
    <van-popup v-model="areaBroadband.show" position="bottom" class="pc-width">
      <!-- <h3 class="van-popup__title">
        {{ title }}
      </h3> -->
      <div class="van-popup__content">
        <van-picker
          show-toolbar
          :columns="areaBroadband.areaList"
          :loading="areaBroadband.loading"
          @change="onChange"
          @confirm="onConfirm"
          @cancel="onCancel"
        />
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import Vue,{ref,reactive,onMounted,defineProps,defineEmits,defineExpose,watch} from "vue"
import { Popup, Picker } from "vant"
import { PROVINCE_LIST, getCityByProvinceId, getRegionByCityId } from '@/api/area'
import {copy} from "@/utils/utils"
Vue.use(Popup).use(Picker)
const props = defineProps({
  // 组件的标题
  title: {
    type: String,
    default: "所在地区"
  },
  province: {
    type: Object,
    default: null
  },
  city: {
    type: Object,
    default: null
  }
})
const emit = defineEmits(["confirm"])
const areaBroadband = reactive({
  show: false,
  areaList: [
    {
      values: ['省'],
    },
    {
      values: ['市']
    },
    {
      values: ['区']
    }
  ],
  loading: false
})
let provinceList= []
let cityList= []
let regionList= []
// 初始化地址
function init() {
  if (props.province) {
    provinceList = [ props.province ]
  } else {
    provinceList = [ ...PROVINCE_LIST ]
  }
  getProvince()
}
// 切换省市区
function onChange(target, data, index) {
  // 级联查市
  if (index === 0) {
    const item = provinceList.find(item => item.provinceName === data[index])

    getCity(item.provinceId)
  }
  // 级联查区
  else if (index === 1) {
    const item = cityList.find(item => item.cityName === data[index])

    // console.log('onChange item,', item)
    getRegion(item.cityId)
  }
}
function onCancel() {
  close()
}
function onConfirm(data, index) {
  // console.log('popup-area.vue, onConfirm,', data, index)
  const province = provinceList.find(item => item.provinceName === data[0])
  const city = cityList.find(item => item.cityName === data[1])
  const region = regionList.find(item => item.regionName === data[2])
  const res = {
    provinceId: province.provinceId,
    provinceName: province.provinceName,
    cityId: city.cityId,
    cityName: city.cityName,
    regionCode: region.regionCode,
    regionName: region.regionName
  }
  res.areaText = addressText(res)
  emit('confirm', res)
  close()
}
function open() {
  areaBroadband.show = true
  // 首次初始化
  if (provinceList.length === 0) {
    init()
  }
}
function close() {
  areaBroadband.show = false
}
// 获取省数据
function getProvince() {
  // 获取地区
  const data = provinceList.map(item => item.provinceName)
  areaBroadband.areaList[0] = {
    values: data,
  }
  areaBroadband.areaList = copy(areaBroadband.areaList)
  // 查询第一条省的市数据
  const item = provinceList[0]
  getCity(item.provinceId)
}
// 获取市数据
function getCity(provinceId = 100) {
  // console.log(provinceId)
  areaBroadband.loading = true
  if (props.city) {
    cityList = [ props.city ]
    const data = cityList.map(item => item.cityName)
    areaBroadband.areaList[1] = {
      values: data,
    }
    areaBroadband.areaList = copy(areaBroadband.areaList)

    // 查询第一条市的区数据
    const item = cityList[0]
    getRegion(item.cityId)
  } else {
    getCityByProvinceId(provinceId).then((res) => {
      // console.log('popup-area.vue, getCityByProvinceId, res', res)
      if (res && res.data && res.data.province&&res.data.province.cities && res.data.province.cities.length > 0) {
        // 设置 city
        cityList = res.data.province.cities
        const data = cityList.map(item => item.cityName)
        areaBroadband.areaList[1] = {
          values: data,
        }
        areaBroadband.areaList = copy(areaBroadband.areaList)

        // 查询第一条市的区数据
        const item = cityList[0]
        getRegion(item.cityId)
      }
    }).catch((err) => {
      // console.log('popup-area.vue, getCityByProvinceId, err', err)
      areaBroadband.loading = false
    })
  }
}
// 获取区数据
function getRegion(cityId = 1000) {
  areaBroadband.loading = true
  getRegionByCityId(cityId).then((res) => {
    // console.log('popup-area.vue, getRegionByCityId, res', res)
    if (res && res.data && res.data.city && res.data.city.towns && res.data.city.towns.length > 0) {

      areaBroadband.loading = false
      // 设置 region
      regionList = res.data.city.towns.map(item=>{
        item.regionCode = item.townId
        item.regionName = item.townName
        return item
      })
      const data = res.data.city.towns.map(item => item.regionName)
      areaBroadband.areaList[2] = {
        values: data,
      }
      areaBroadband.areaList = copy(areaBroadband.areaList)

      // console.log('areaBroadband.areaList,', areaBroadband.areaList)
    }
  }).catch((err) => {
    // console.log('popup-area.vue, getRegionByCityId, err', err)
    areaBroadband.loading = false
  })
}
function addressText(data) {
  const {
    provinceName,
    cityName,
    regionName
  } = data
  if (provinceName) {
    if (provinceName === cityName) {
      return `${cityName} ${regionName}`
    }
    return `${provinceName} ${cityName} ${regionName}`
  }
  return ""
}
</script>

<style lang="scss">
.popup-area {
  font-size: 30px;

  .van-popup__content {
    padding-bottom: 0;
  }

}
</style>
