<!--
 * @User: JOJO
 * @FilePath: \yundian-m\src\components\index\indexHomeRecommend.vue
-->
<template>
  <div v-if="recommendType" class="indexHomeRecommend">
    <ul v-if="recommendType==TYPEOPTION.LEFT2ANSHASTITLE" class="ul">
      <li v-for="(item, index) in filterData" :key="index">
        <div class="pongetit">
          <span class="tit">{{ item.title }}</span>
          <div class="desc">
            <span>{{ item.recommendWords }}</span>
          </div>
        </div>

        <div class="subtitle">
          {{ item.subTitle }}
        </div>

        <dl>
          <dt
            v-for="(item2, index2) in item.recommendGoodsList"
            :key="index2"
            @click="handleTo(item2)"
          >
            <img :src="shopImg(item2.imgUrl)" alt="" />
          </dt>
        </dl>
      </li>
    </ul>
    <ul v-if="recommendType==TYPEOPTION.LEFT1" class="hot-active" :class="['type'+recommendType]">
      <li class="left">
        <div
          @click="handleTo(filterData[0].recommendGoodsList[0])"
        >
          <img :src="shopImg(filterData[0].recommendGoodsList[0].imgUrl)" alt="" />
        </div>
      </li>
      <li class="right">
        <div
          v-for="(item2, index2) in filterData[1].recommendGoodsList"
          :key="index2"
          class="item"
          @click="handleTo(item2)"
        >
          <img :src="shopImg(item2.imgUrl)" alt="" />
        </div>
      </li>
    </ul>
    <div v-if="recommendType==TYPEOPTION.LEFT2" class="hot-active" :class="['type'+recommendType]">
      <div v-for="(item, index) in filterData" :key="index">
        <div
          v-for="subItem in item.recommendGoodsList"
          :key="subItem.goodsId"
          class="midad"
          @click="handleTo(subItem)"
        >
          <img :src="shopImg(subItem.imgUrl)" alt="" />
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import {defineProps,watch,ref,} from 'vue'
import insertCode from "@/utils/insertCode.js"
import { getImgUrl } from "@/utils/utils.js"
const props = defineProps(
  {
    recommendData: {
      type: Object,
      default: (data) => {
        return {}
      },
    },
  }
)
const TYPEOPTION = {
  "LEFT2ANSHASTITLE":1,// 1：左2右2有标题
  "LEFT1":2, // 2：左1右4
  "LEFT2":3 // 3：左2右2,仅图片
}
const filterData = ref([])
const recommendType = ref(null)
//整合推荐数据
watch(()=>props.recommendData,(val)=>{
  filterData.value = val.dataList
  recommendType.value = val.type
},{
  immediate: true
})

function handleTo(item) {
  insertCode(null, item.goodsLink)
}
function shopImg(name) {
  if(name.indexOf("static")!==-1){
    return name
  }
  return getImgUrl(name)
}

</script>

<style lang="scss" scoped>
$mo2: 2;
.indexHomeRecommend {
  margin-bottom: 16px;
  .ul {
    display: flex;
    border-radius: 10px / $mo2;
    position: relative;
    width:345px;
    margin:0 auto;
    li {
      flex: 1;
      min-width: 0;
      padding: 16px 7px 10px;
      background-image: linear-gradient(180deg, #F2C6FF 0%, #FDFEFF 28%, #FFFFFF 100%);
      box-shadow: 0 2px 13px -2px #667e8c4d;
      &:first-child{
        margin-right: 6px;
        background-image: linear-gradient(180deg, #C6EAFF 0%, #FDFEFF 28%, #FFFFFF 100%);
      }
      border-radius: 6px;
      .pongetit {
        display: flex;
        align-items: center;
        .tit {
          font-size: 16px;
          color: #3C3C3C;
          font-weight: bold;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .desc {
          margin-left: 5px;
          border-radius: 10px;
          padding: 3px 7px;
          max-width: 75px;
          min-width: 60px;
          display: flex;
          align-items: center;
          position: relative;
          font-size: 11px;
          span {
            color: #fff;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }

      .subtitle {
        font-size: 11px;
        margin-top: 8.5px;
        line-height: 15px;
        margin-bottom: 7.5px;
      }

      &:nth-of-type(1) {
        .pongetit {
          .desc {
            background: linear-gradient(90deg, #2AA3FF 0%, #2D93FF 100%, #2D93FF 100%);
          }
        }
        .subtitle {
          color: #2D94FF;
        }
      }
      &:nth-of-type(2) {
        .pongetit {
          .desc {
            background: linear-gradient(90deg, #FF718F 0%, #FF3C6F 100%);
          }
        }
        .subtitle {
          color: #FF4172
        }
      }
      dl {
        display: flex;
        justify-content: space-between;
        dt {
          width:73px;
          height: 82px;
          border-radius: 5px;
          overflow: hidden;
          img {
            height: 100%;
            width: 100%;
            border-radius: 5px;
            overflow: hidden;
          }
        }
      }
    }
  }
  .hot-active{
    padding:  41.5px 8px 9px 8px;
    display: flex;
    background:
      url('~@/assets/home/<USER>') no-repeat center center;
    background-size: 100% 100%;
    width:345px;
    margin:0 auto;
    flex-wrap: wrap;
    justify-content: space-between;
    box-shadow: 0 2px 13px -2px #667e8c4d;
    border-radius: 6px;
    .left{
      width:139px;
      height:186px;
      margin-right: 5px;
      border-radius: 6px;
      overflow: hidden;
      img{
        width:100%;
        height: 100%;
      }
    }
    .right{
      flex:1;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .item{
        width:88px;
        height:90px;
        margin-bottom: 6px;
        border-radius: 6px;
        overflow: hidden;
        &:nth-child(3),&:nth-child(4){
          margin-bottom: 0;
        }
        img{
          width:100%;
          height: 100%;
        }
      }
    }
    &.type3{
      background:
      url('~@/assets/home/<USER>') no-repeat center center;
      background-size: 100% 100%;
      padding:  41.5px 9px 9px 9px;
    }
    .midad{
      width:159px;
      height: 72px;
      &:nth-child(1){
        margin-bottom: 8px;
      }
      img{
        width:100%;
        height: 100%;
      }
    }
  }
}
</style>
