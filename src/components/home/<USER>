<template>
  <div class="sharedialog">
    <van-dialog
      v-model="shareData.show"
      :show-confirm-button="false"
      :before-close="confirmShare"
      :close-on-click-overlay="true"
    >
      <div ref="qrCodeContainer">
        <div class="posterContainer">
          <img :src="shareData.qwUrl" alt="" />
        </div>
      </div>
      <div style="text-align: center">
        <a href="javascript:void(0);" class="bt_tip bt_save">
          长按图片保存海报
        </a>
      </div>
    </van-dialog>
    <div v-if="shareData.showShareBottom" v-show="shareData.show" class="bottom-dialog">
      <div class="icon-container">
        <div>
          <input id="foo" v-model="shareData.qrcodeurl" type="hidden" />
        </div>
        <ul>
          <li>
            <span class="iconfont icon-xiazai" style="color: #4974f3" @click="saveImg()"></span>
            <span>下载宣传图</span>
          </li>
          <li>
            <span v-if="UA.isApp" class="iconfont icon-weixin" style="color: #7cd576" @click="share()"></span>
            <span v-if="UA.isWechat" class="iconfont icon-weixin" style="color: #7cd576" @click="share('wx')"></span>
            <span>分享给好友</span>
          </li>
        </ul>
      </div>
      <div class="btn_container">
        <a href="javascript:void(0);" class="bt_save bt_only" @click="hideDialogList()">取消</a>
      </div>
    </div>
    <div v-if="shareData.showShare" class="wx-guide" @click="hideShare">
      <img src="~@/assets/index_img/wx_guide.png" alt="" width="200" />
    </div>
    <div class="copyQRCode">
      <div ref="qrCodeContainer1" class="qrCodeContainer">
        <div class="copyQRCodeTitle">
          <span>
            {{ props.shareInfo.shopShortName }}
          </span>
        </div>
        <div class="copyQRCodeAddress">
          <span>
            {{ props.shareInfo.address }}
          </span>
        </div>
        <div v-if="props.shareInfo && props.shareInfo.fansShareData" class="imgcontainer" :style="{backgroundImage: 'url(' + props.shareInfo.fansShareData.bgUrl + ')',}">
        </div>
        <div class="shareContent">
          <div id="qrCodeUrlCopy" ref="qrCodeUrlCopy" class="copyQRCodeImg"></div>
          <div v-if="props.shareInfo.type === 'goods'" class="goodsShare">
            <div class="goodsPrice">
              ￥{{ (props.shareInfo.price / 100).toFixed(2) }}
            </div>
            <div v-if="props.shareInfo.goodTitle && props.shareInfo.goodTitle.length > 25" class="goodsName">
              {{ props.shareInfo.goodTitle.slice(0, 25) }} ...
            </div>
            <div v-else class="goodsName">
              {{ props.shareInfo.goodTitle }}
            </div>
            <div class="tip">
              长按识别/扫描二维码
              <span class="iconfont icon-shixinjiantou-you"></span>
            </div>
          </div>
          <div v-if="props.shareInfo.type === 'shop'" class="shopShare">
            <div class="tip">
              长按识别/扫描二维码
              <span class="iconfont icon-shixinjiantou-you"></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Dialog, Toast } from "vant"
import UA from "@/utils/ua"
import QRCode from "qrcodejs2"
import shareUtilApi from "@/utils/share"
import shareApi from "@/api/share"
import Vue,{ reactive,ref,onMounted,watch,nextTick, onActivated} from "vue"
import appletcodeApi from '@/api/appletcode'
import {getImgUrl} from '@/utils/utils'
Vue.use(Dialog)
const props = defineProps({
  showshare: {//是否展示分享框
    type: [Boolean,Object],
    default: false,
  },
  shareInfo:{//店铺信息
    type: Object,
    default: (data) => {
      return data || {}
    },
  },
})
// 分享数据
const shareData = reactive({
  show: false,
  shareConfig: {},
  qrcode: null,
  qrcodeCopy: null,
  shareDialogTitle: null,
  qrcodeurl: null,
  showShare: false, //是否在展示分享引导（微信里展示）
  qwUrl: "", //企微地址
  showDAOhang: false, //是否展示分享内容
  showQrCodeHtml: false, //弹框展示内容
  showShareBottom:false
})
/**分享组件初始化处理 */
async function init(){
  //企业微信分享初始化province_id传参
  shareUtilApi.wechartWorkInit({
    province_id: props.shareInfo.provinceId,
  })
  shareData.isWeChatMiniApp = await UA.isWeChatMiniApp()
  shareData.showShareBottom = UA.isApp || (UA.isWechat && (!shareData.isWeChatMiniApp))
}
onMounted(() => {
  init()
})
onActivated(()=>{
  init()
})
const emit = defineEmits(["open","close","shareSuccesses","shareFailures"])
// 监听分享事件
watch(() => props.showshare, (val,oldval) => {
  if(val && oldval !== val){
    shareData.showQrCodeHtml = false
    document.querySelector('#qrCodeUrlCopy').innerHTML = ""
    creatQrCode()
  }
},{immediate: true})

function confirmShare(action, done) {
  if (action === "confirm") {
    done(false)
  } else {
    done()
  }
}
/**父组件调用方法 */
async function changeShareConfig(configData) {
  shareData.shareConfig.imgUrl = configData.imgUrl
  if(!shareData.isWeChatMiniApp){
    shareData.qrcodeurl = await getShortUrl(configData.url)
  }
  emit('open')
  if (UA.isWechatWork) { //企业微信
    share()
  }
}
/**获取短链接 */
function getShortUrl(url) {
  return shareApi.getShortUrl({ url: url }).then((res) => {
    if (res.data && res.data.url) {
      return res.data.url
    } else {
      return url
    }
  })
}
/**展示画报 */
function showQrCode() {
  shareData.show = true
  shareData.showQrCodeHtml = true
  emit('close')
}
function callbackfn(itm){
  if(itm=='success'){
    emit('shareSuccesses')
  }else{
    emit('shareFailures')
  }
}
/**分享事件 */
async function share(type) {
  //修改微信分享配置
  await shareUtilApi.share(props.shareInfo.fansShareData,callbackfn)
  //遮罩层显示
  if (type == "wxwork") return false //防止企微分享方法重复执行
  if (UA.isWechatWork) {
    shareUtilApi.getContext(function(entry) {
      if (entry == "single_chat_tools" || entry == "group_chat_tools") {
        shareUtilApi.sendChatMessage()
      } else {
        shareData.showShare = true
        shareUtilApi.qwShare(function() {
          shareData.showShare = false
        })
      }
    })
    return false
  }
  if (type == "wx") {
    shareData.showShare = true
    shareData.show = false
  }
}
/**取消分享 */
function hideDialogList() {
  shareData.show = false
  emit('close')
}
/**关闭微信分享引导页 */
function hideShare() {
  shareData.showShare = false
  emit('close')
}
/**获取小程序码或者二维码 */
async function creatQrCode() {
  if(shareData.isWeChatMiniApp){
    if(props.shareInfo.type === 'goods'){
      await getGoodsWxAcodeUnlimit()
    }else{
      await getStaffWxAcodeUnlimit()
    }
  }else{
    if (shareData.qrcodeCopy) {
      shareData.qrcodeCopy.clear()
    }
    //防止dialog首次加载不出二维码 在dialog上加@opened 回调
    shareData.qrcodeCopy = new QRCode(document.querySelector('#qrCodeUrlCopy'), {
      text: shareData.qrcodeurl,
      width: 400,
      height: 400,
      colorDark: "#000000",
      colorLight: "#ffffff",
      correctLevel: QRCode.CorrectLevel.H,
    })
  }
  nextTick(() => {
    getPoster()
  })
}
/**获取店铺小程序码*/
async function getStaffWxAcodeUnlimit(){
  console.log('sendData',"shop")
  return appletcodeApi.getappletcode({
    width:430,
    hyaline:false,
    page:"pages/fans/fans"
  }).then(res => {
    if(res.code) {
      Toast(res.message)
    }else{
      var img = document.createElement('img')
      img.src = getImgUrl(res.data.headers['fileid'],"material")
      document.querySelector('#qrCodeUrlCopy').innerHTML = ''
      document.querySelector('#qrCodeUrlCopy').appendChild(img)
    }
  })
}
/**获取商品小程序码*/
async function getGoodsWxAcodeUnlimit(){
  let sendData = {
    width:430,
    hyaline:false,
    page:"pages/web/web",
    url:props.shareInfo.fansShareData.url
  }
  return appletcodeApi.getGoodsWxAcodeUnlimit(sendData).then(res => {
    if(res.code) {
      Toast(res.message)
    }else{
      var img = document.createElement('img')
      img.src = getImgUrl(res.data.headers['fileid'],"material")
      document.querySelector('#qrCodeUrlCopy').innerHTML = ''
      document.querySelector('#qrCodeUrlCopy').appendChild(img)
    }
  })
}
const qrCodeContainer1 = ref(null)
/**生成海报 */
function getPoster() {
  shareData.qwUrl = ""
  shareUtilApi.getImg(qrCodeContainer1.value).then((imgUrl) => {
    shareData.qwUrl = imgUrl
    showQrCode()
  })
}
/**保存海报 */
function saveImg() {
  const targetDom = qrCodeContainer1.value
  shareUtilApi.saveImg(targetDom, () => {
    Toast("图片已保存")
    shareData.show = false
  })
}
</script>
<style lang="scss" scoped>
@import "@/styles/minix.scss";
$px: 3px;

.qrcode {
  width: 200px;
  height: 200px;
  margin: 0 auto 20px;

  :deep(img) {
    width: 200px !important;
    height: 200px !important;
  }
}

.van-dialog {
  width: 260px;
  padding: 15px 0;
}

.qrCodeContainer {
  width: 260 * $px;
  padding-bottom: 15 * $px;
}

.copyQRCodeTitle,
.copyQRCodeAddress {
  font-size: 16 * $px;
  display: flex;
  width: 200 * $px;
  margin: 0 auto;
  padding-top: 17 * $px;

  span {
    width: 100%;
    text-align: center;
    line-height: 17 * $px;
    align-self: center;
    font-weight: 500;
    color: #000000;
  }
}

.copyQRCodeAddress {
  padding: 5 * $px 0 12 * $px;

  span {
    font-size: 10 * $px;
    color: #959595;
    line-height: 13.5 * $px;
  }
}

.imgcontainer {
  width: 200 * $px;
  height: 160 * $px;
  margin: 0 auto;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;

  img {
    max-height: 160 * $px;
    display: block;
    margin: 0 auto;
    max-width: 200 * $px;
    height: auto;
  }
}

.wx-guide {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 3000;
  background: rgba(0, 0, 0, 0.7);

  img {
    width: 200px;
    position: absolute;
    top: 100px;
    left: 88px;
    z-index: 3001;
  }
}

.copyQRCodeImg {
  height: 55 * $px;
  width: 55 * $px;
  float: right;

  :deep(canvas),
  :deep(img) {
    width: 55 * $px !important;
    height: 55 * $px !important;
  }
}

.sharedialog {
  .posterContainer {
    width: 100%;

    img {
      width: 100%;
    }
  }

  .qrTitle {
    display: flex;
    height: 60px;

    span {
      width: 100%;
      text-align: center;
      line-height: 30px;
      align-self: center;
      @include ellipsis($clamp: 2);
    }
  }
  .bottom-dialog{
    bottom: 0 !important;
    height:120px;
    width:100vw;
    position: fixed;
    z-index: 3000;
    background: #fff;
  }
  .icon-container {
    text-align: center;
    font-size: 55px;
    padding-top: 10px;

    ul {
      display: flex;

      li {
        flex: 1;
      }
    }

    span,
    button {
      font-size: 40px;
      border: none;
      background: none;
      padding: 0;
      display: block;
      margin: 0 auto 10px;

      &:last-child {
        font-size: 14px;
      }
    }
  }

  .btn_container {
    text-align: center;
    padding-bottom: 20px;
  }

  a {
    display: inline-block;
    width: 108px;
    height: 32px;
    line-height: 32px;
    margin: 0px 9px 0 9px;
    border-radius: 16px;
    background-image: linear-gradient(to right, #5fc8ff, #8372fe);
    font-size: 14px;
    color: #fff;
    text-align: center;

    &.bt_save {
      background: #f2f2f1 !important;
      color: #333;
      width: 90px;
    }

    &.bt_tip {
      width: 150px;
      font-weight: 500;
    }
  }
}

.copyQRCode {
  position: fixed;
  top: -10000 * $px;
  // background: #fff;
  // top: -300px;
  // left: -300px;

}

.shareContent {
  width: 200 * $px;
  margin: 0 auto;
  padding: 13.5 * $px 7.5 * $px 12.5 * $px 7.5 * $px;

  .shopShare {
    height: 55 * $px;
    padding-top:20 * $px;
  }

  .goodsPrice {
    font-size: 12 * $px;
    color: #fa4343;
    font-weight: 600;

    .text-line-through {
      font-weight: normal;
      text-decoration-line: line-through;
      font-size: 12* $px;
      color: #ccc;
    }
  }

  .goodsName {
    font-size: 12 * $px;
    font-weight: 500;
    color: #222222;
    margin: 8 * $px 0 8.5 * $px;
    text-align: left;
    text-align: justify;
    width: 115 * $px;
  }

  .tip {
    font-size: 10 * $px;
    font-weight: 400;
    color: #959595;

    .iconfont {
      display: inline-block;
      font-size: 14 * $px;
      transform: translate(-2 * $px, 2 * $px);
    }
  }
}
</style>
