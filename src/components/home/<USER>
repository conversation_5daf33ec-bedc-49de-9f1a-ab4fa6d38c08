<template>
  <div class="flex" :style="[bgOption]">
    <span class="icon icon_user"></span>
    <div
      v-if="user && user.userInfo && user.userInfo.UserName"
      class="userName"
      @click="openBtnloginOut"
    >
      {{
        user.userInfo.UserName.slice(0, 3) +
          "****" +
          user.userInfo.UserName.slice(7)
      }}
      <span v-if="user.isMember" class="icon icon_vip"></span>
    </div>
    <div v-else class="userName" @click="openBtnlogin">
      立即登录
    </div>
    <Icon v-if="showSubMsgIcon" class="iconfont" class-prefix="icon" name="subscribe" color="#fff" @click="Submsg" />
    <Icon class="iconfont" class-prefix="icon" name="search" color="#fff" @click="goToSearch" />
    <Icon class="iconfont" class-prefix="icon" name="coupons" color="#fff" @click="openQuanOwn" />
    <Icon v-if="showShareIcon" class="iconfont" class-prefix="icon" name="share1" color="#fff" @click="shareShopOwn" />
    <Popup v-model="showLogout" :style="{ borderRadius: '9px' }">
      <div class="out">
        <div class="point">
          确认退出
        </div>
        <div class="select">
          <p class="cancle" @click="cancle()">
            取消
          </p>
          <p @click="sure()">
            确定
          </p>
        </div>
      </div>
    </Popup>
  </div>
</template>

<script setup>
import VUE,{ ref ,inject, getCurrentInstance, onMounted, watch, computed} from "vue"
import { Icon,Popup} from 'vant'
import insertCode from "@/utils/insertCode.js"
import UA from "@/utils/ua.js"

VUE.use(Popup)
const props = defineProps({
  openQuan:{
    type:Function,
    default:()=>{}
  },
  shareShop:{
    type:Function,
    default:()=>{}
  },
  hasBanner:{
    type:Boolean,
    default:false
  }
})
let myShopData,getSon
onMounted(()=>{
  getSon = inject("getSon")
  myShopData = getSon.shopData.value
})
const bgOption = ref(null)
watch(()=>props.hasBanner,(val)=>{
  if(val){
    bgOption.value = {
      background:"rgba(0, 0, 0, 0.06)",
      width:"100vw",
      position:"absolute"
    }
  }else{
    bgOption.value = null
  }
},{immediate: true})
const showShareIcon = inject("showShare")
function shareShopOwn() {
  props.shareShop()
}

const currentVue =  getCurrentInstance()
const proxy = currentVue ? currentVue.proxy : null
const user = ref(null)
if(proxy){
  user.value = proxy.$store.getters.user
}
const showSubMsgIcon = computed(()=>{
  // return user.value && user.value.userInfo && user.value.userInfo.UserName && UA.isApp
  if(user.value && user.value.userInfo && user.value.userInfo.UserName && UA.isApp){
    return true
  }
  return false
})
function openBtnlogin(callback,showCancle) {
  insertCode(
    "yd_index_" + myShopData.shopId + "_loginbar_login"
  )
  openloginSelf(callback,showCancle)
}
const showLogout = ref(false)
// 退出登录
function openBtnloginOut() {
  showLogout.value = true
}
// 退出登录取消按钮
function cancle() {
  showLogout.value = false
}
// 退出登录确认按钮
function sure() {
  showLogout.value = false
  getLogout()
}
function getLogout() {
  return getSon.getLogout()
}
function openloginSelf(callback,showCancle) {
  return getSon.openlogin(callback,showCancle)
}
function openQuanOwn() {
  props.openQuan()
}
function goToSearch() {
  if(proxy){
    proxy.$router.push({
      path: "/searchlist/index.html?shopId=" + myShopData.shopId,
    })
  }

}

// 打开订阅弹框
function Submsg(){
  return getSon.Submsg()
}



</script>
<style lang="scss" scoped>
.flex {
  display: flex;
  height: 40px;
  background-image: linear-gradient(0deg, #2AA3FF 0%, #2D93FF 100%);
  align-items: center;
  margin-bottom: 16px;
}
.userName {
  flex: 1;
  line-height: 47px;
  font-size: 16px;
  font-weight: 400;
  text-align: left;
  color: #fff;
}
.icon {
  display: block;

}
.icon_user {
  background: url(~@/assets/index_normal/userhome.png) 0 0 no-repeat;
  background-size: contain;
  width: 28px;
  height: 28px;
  margin: 0 5px 0 15px;
}
.icon-coupons {
  position: relative;
  font-size: 22px;
  margin-right: 22px;
  .van-info{
    top:3px;
    right:2px;
    padding:2px;
    width: 0px;
    height: 0px;
  }
}
.icon-search,.icon-subscribe {
  font-size: 19px;
  margin-right: 22px;
}
.icon-share1 {
  font-size: 20px;
  margin-right: 15px;
}
.out {
    width: 272px;
    height: 108px;
    background: #ffffff;
    .point {
      width: 100%;
      height: 66px;
      border-bottom: 1px solid #f2f2f2;
      text-align: center;
      line-height: 66px;
      font-size: 15px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #000000;
    }
    .select {
      width: 100%;
      height: 40px;
      display: flex;
      p {
        width: 135px;
        height: 40px;
        text-align: center;
        line-height: 40px;
        font-size: 13px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: #5b8edc;
      }
      .cancle {
        border-right: 1px solid #f2f2f2;
      }
    }
  }
</style>
