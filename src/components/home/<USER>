<template>
  <div v-if="props.bannerArr">
    <swiper
      v-if="props.bannerArr && props.bannerArr.length>1"
      class="centerbanner-swiper"
      :options="centerBannerOption"
    >
      <swiper-slide v-for="(item, key) in props.bannerArr" :key="key">
        <div v-if="item.imgUrl" class="centerbanner" @click="goImgLink(item.targetUrl)">
          <!-- <img :src="getImgUrl(item.imgUrl)" /> -->
          <div :style="{'backgroundImage':'url('+getImgUrl(item.imgUrl)+')'}" class="img"></div>
        </div>
      </swiper-slide>
      <div slot="pagination" class="swiper-pagination"></div>
    </swiper>
    <div v-if="props.bannerArr.length===1" class="centerbanner" @click="goImgLink(props.bannerArr[0].targetUrl)">
      <!-- <img :src="getImgUrl(props.bannerArr[0].imgUrl)" /> -->
      <div :style="{'backgroundImage':'url('+getImgUrl(props.bannerArr[0].imgUrl)+')'}" class="img"></div>
    </div>
  </div>
</template>

<script setup>
import Vue,{reactive,defineProps, watch}from "vue"
import insertCode from "@/utils/insertCode"
import {getImgUrl} from "@/utils/utils.js"
import VueAwesomeSwiper  from 'vue-awesome-swiper'
Vue.use(VueAwesomeSwiper)
const props = defineProps({
  bannerArr:{
    type:Array,
    default:(data)=>{
      return data || []
    }
  }
})
function goImgLink(targetUrl){
  location.href = targetUrl
}
function insertCodeGoods(key,goodsId,source,url){
  let dcs_id = key+'_'+goodsId+'_'+source
  insertCode("goods_"+dcs_id,url)
}
//配置文档https://www.swiper.com.cn/api/pagination/299.html
let centerBannerOption = reactive({
  pagination: {
    el: '.swiper-pagination',
    type:"fraction"
  },
  autoplay:true,
  loop:true
})
</script>
<style lang="scss" scoped>
.centerbanner-swiper{
  .swiper-pagination-fraction{
    text-align: center;
    color: #fcfbfb;
    bottom:20px;
    width:40px;
    right:20px;
    left:auto;
    background: rgba(0,0,0,0.3);
    height:16px;
    line-height: 16px;
    border-radius: 3px;
  }
}
.centerbanner{
  width: 345px;
  height: 99px;
  border-radius: 6px;
  overflow: hidden;
  margin:0px auto 12.5px;
  .img{
    width: 100%;
    height: 100%;
    background-size: contain;
  }
}

</style>
