<template>
  <div>
    <van-dialog
      v-model="subMsgShow"
      title=""
      :show-cancel-button="false"
      :show-confirm-button="false"
      confirm-button-color="#5fade8"
      class="dialog-container"
    >
      <div class="dialog">
        <div class="dialog-sub-check-img" @click="goSubMsg">
          <div class="dialog-sub-check-btn">
          </div>
        </div>
        <a href="javascript:void(0)" class="dialog-btn-exit" @click="closeSubMsgDialog"></a>
      </div>
    </van-dialog>
  </div>
</template>

<script setup>
import Vue,{ref,reactive,watch} from "vue"
import submsgApi from "@/api/submsg.js"
import { Dialog } from "vant"
import leadeonLoader from "@/utils/leadeonloader"
import UA from '@/utils/ua'
Vue.use(Dialog)
//消息订阅弹窗
const props = defineProps({
  show:{
    type:Boolean,
    default:false
  },
  shopId:{
    type:[String,Number],
    default:null
  }
})
const emit = defineEmits(["closeDialog","openDialog"])
const subMsgShow = ref(false)
watch(() => props.show, (newValue, oldValue) => {
  subMsgShow.value = newValue
})
const tmplIds= ["9U7Fq4b-2CN3tDSoPEvbrV6izVPI6DamJI_gJD_WDsM","EaZWl5lCmErF-XmS1NBlZ6uamb7BUWLHKDWZiyXtR3o"]
async function goSubMsg(){
  let pageUrl = `/pages/home/<USER>
  if(UA.isApp){
    const leadeon = await leadeonLoader()
    leadeon.openMiniProgram({
      debug: false,
      wx:{//微信小程序
        "userName": 'gh_74df7f2509b1', //小程序的username
        "path": pageUrl, //打开小程序指定页面路径
        "miniProgramType": '0', //0 正式版 1 开发板 2体验版
      },
      success: function(res) {
      },
      error: function(res) {
      }
    })
  }
}
let needScriTmplIds = []
// 登录成功后接口检查订阅
function checkSubscription(){
  let templates = []
  tmplIds.forEach((item)=>{
    templates.push({
      templateId: item
    })
  })
  submsgApi.checkSubscription({templates:templates}).then((res)=>{
    needScriTmplIds = []
    tmplIds.forEach((item)=>{
      if(!res.data[item]){
        needScriTmplIds.push(item)
      }
    })
    if(needScriTmplIds && needScriTmplIds.length>0){
      showSubMsgDialog()
    }else{
      // 已经授权过了
      this.$toast('您已订阅消息')
    }
  })
}

// 显示订阅弹出框
function showSubMsgDialog(){
  emit('openDialog')
  subMsgShow.value = true
}
// 不显示订阅弹出框
function closeSubMsgDialog(){
  emit('closeDialog')
  subMsgShow.value = false
}
</script>

<style lang="scss" scoped>
.dialog-container{
  background-color: transparent;
}
.dialog-sub-check-img{
  background: url("https://img1.shop.10086.cn/fs/goods/fs_66893478e4b0e2596df55434.png") no-repeat 0 0;
  background-size: 100% auto;
  width:290px;
  height:352px;
  position:relative;
  margin:0 auto;
}
.dialog-btn-exit{
  display: block;
  margin: 0 auto;
  width: 30px;
  height: 44px;
  background: url("~@/assets/home/<USER>") no-repeat 0 0;
  background-size: 100% auto;
}
</style>
