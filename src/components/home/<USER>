<template>
  <div class="goods">
    <div class="goods-tt">
      <div>{{ props.items.floorName }}</div>
      <div v-if="props.items.canEnter" class="more-goods" @click="goSecondary(props.items.floorId)">
        更多
        <Icon name="arrow" size="10" />
      </div>
    </div>
    <div v-if="props.items.imgUrl" class="banner" @click="goImgLink(props.items.adLink)">
      <img :src="getImgUrl(props.items.imgUrl)" />
    </div>
    <ul v-if="props.items.goodsList && props.items.goodsList.length" class="goods-list cp-1line3">
      <li v-for="(item, index) in props.items.goodsList" :key="'' + props.items.floorId + item.goodsId + index">
        <a
          href="javascript:void(0)"
          @click="insertCodeGoods(key + 1, item.goodsId, item.goodsSource, item.goodsLink)"
        >
          <img :src="getImgUrl(item.imgUrl)" />
          <p class="title">
            {{ item.goodsTitle }}
          </p>
          <p class="subtitle">
            {{ item.goodsSubTitle }}
          </p>
        </a>
        <span v-if="showShareIcon" class="share-goods" @click="openShare(item)"></span>
      </li>
    </ul>
  </div>
</template>
<script setup>
import EventBus from '@/api/eventbus.js'
import insertCode from "@/utils/insertCode.js"
import UA from "@/utils/ua.js"
import {ref,defineProps,getCurrentInstance, inject} from "vue"
import {getImgUrl} from "@/utils/utils.js"
import {Icon} from "vant"
const props = defineProps({
  floor: {
    type: Object,
    default: (obj) => {
      return obj || {}
    }
  },
  items: {
    type: Object,
    default: (obj) => {
      return obj || {}
    }
  },
  shopshare: {
    type: Object,
    default: (fn) => {
      return fn
    }
  }
})
// 分享
function openShare(item) {
  let share = {
    ...item,
    title: item.goodsTitle,
    url: item.goodsLink,
    desc: item.goodsSubTitle || item.goodsLink,
    imgUrl: item.goodsSource == 2 ? item.imgUrl : getImgUrl(item.imgUrl),
    price: item.price,
    isDoMeal: item.iopGoods
  }
  if (share.isDoMeal) { //iop数据
    share.url = props.shopshare.url
  }
  insertCode(getDcsId("yd_index", item) + "_share")
  share.dcsId = getDcsId("yd_sharegoods", item) + "_share"
  EventBus.$emit('openShareDialog', 'goods', share)
  if (UA.isWechatWork) {
    EventBus.$emit('share')
  }
}
function getDcsId(dcsId, item) {
  let dcs_id = dcsId + "_" + props.floor.shopId + "_" + props.items.floorId
  if (item.goodsSource == 2) {
    dcs_id += "_yysp"
  } else if (item.goodsSource == 1) {
    dcs_id += "_zysp"
  } else {
    dcs_id += "_szsp"
  }
  return dcs_id + "_" + item.goodsId
}
let getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null
const user = ref(null)
if (proxy) {
  user.value = proxy.$store.getters.user
}
const showShareIcon = inject("showShare")
function goSecondary(floorId){
  if(proxy){
    proxy.$router.push({
      path: '/secondarygoodslist/index.html',
      query: {
        floorId: floorId,
        shopId: props.floor.shopId
      }
    })
  }
}
function goImgLink(adLink) {
  location.href = adLink
}
function insertCodeGoods(key, goodsId, source, url) {
  if (props.preview) {
    return
  }
  let dcs_id = key + '_' + goodsId + '_' + source
  insertCode(props.floorId + "_goods_" + dcs_id, url)
}
</script>
<style lang="scss" scoped>
$distance15: 15px;

.goods-tt {
    font-weight: bolder;
    font-size: 18px;
    color: #3C3C3C;
    padding: 10px $distance15;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .more-goods {
        font-weight: 400;
        font-size: 13px;
        color: #999999;
    }
}

.banner {
    width: 345px;
    height: 81px;
    margin: 0 auto;
    border-radius: 6px;
    overflow: hidden;

    img {
        width: 100%;
        height: 100%;
    }
}

.goods-list {
    display: flex;
    flex-flow: row wrap;
    margin: 0 $distance15 6.5px;

    li {
        list-style: none;
        position: relative;
        font-size: 13px;
        line-height: 20px;
        background: #fff;
        text-align: center;
        width: 110px;
        padding-bottom: 7.5px;
        margin-right: 7.5px;
        margin-top: 7.5px;
        box-sizing: border-box;
        box-shadow: 0 2px 13px -2px #667e8c4d;
        border-radius: 6px;
        overflow: hidden;

        &:nth-child(3n) {
            margin-right: 0px;
        }

        a:before {
            content: '';
            display: table;
        }

        p {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-left: 7.5px;
            padding-right: 7.5px;
            margin-top: 2px;
        }

        img {
            width: 110px;
            height: 75px;
        }

        p.title {
            font-weight: bold;
            font-size: 13px;
            color: #3C3C3C;
            line-height: 18.5px;
            height: 18.5px;
        }

        p.subtitle {
            font-weight: 400;
            font-size: 11px;
            color: #666666;
            line-height: 15px;
            height: 15px;
        }

        p.price {
            font-size: 12.5px;
            color: #ED2668;
            height: 24px;
            line-height: 24px;
            font-weight: bold;

            em {
                font-size: 11px;
                margin-right: -5px;
            }
        }
    }

    &.cp-1line3 {
        li {

            &:nth-last-child(1) {
                margin-right: 0px;
            }

        }
    }

    .share-goods {
        // position: absolute;
        right: 7px;
        bottom: 7.5px;
        display: block;
        width: 22px;
        height: 22px;
        background: url(~@/assets/home/<USER>
        background-size: 100% auto;
        margin: 2.5px 7px 0 0;
        float: right;
    }
}

.shixiao {
    -webkit-filter: grayscale(100%);
    -moz-filter: grayscale(100%);
    -ms-filter: grayscale(100%);
    -o-filter: grayscale(100%);
    filter: grayscale(100%);
    filter: gray;

    .item-shixiao-icon {
        width: 40px;
        height: 25px;
        background: url(~@/assets/index_img/shixiao.png) no-repeat;
        background-size: 100%;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 10px;
    }
}
</style>
