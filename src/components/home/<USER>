<template>
  <div v-if="props.items && props.items.length" class="guide">
    <swiper
      v-if="guideList && guideList.length>1"
      :options="iconListOption"
    >
      <swiper-slide v-for="(guideListItems, index) in guideList" :key="index">
        <div v-for="(item) in guideListItems" :key="item.linkId" class="item" @click="insertCodeGoods(item)">
          <!-- <img :src="item.img" /> -->
          <div :style="{'backgroundImage':'url('+item.img+')'}" class="img"></div>
          {{ item.title }}
        </div>
      </swiper-slide>
      <div slot="pagination" class="swiper-pagination-iconlist"></div>
    </swiper>
    <template v-if="guideList.length===1">
      <div class="iconlist-con">
        <div v-for="(item) in guideList[0]" :key="item.linkId" class="item single" @click="insertCodeGoods(item)">
          <!-- <img :src="item.img" /> -->
          <div :style="{'backgroundImage':'url('+item.img+')'}" class="img"></div>
          {{ item.title }}
        </div>
      </div>

    </template>
  </div>
</template>

<script setup>
import Vue,{reactive,defineProps,inject, getCurrentInstance,computed} from "vue"
import {Toast } from "vant"
import insertCode from "@/utils/insertCode"
import "swiper/dist/css/swiper.css"
import VueAwesomeSwiper  from 'vue-awesome-swiper'
import lbsApi from "@/api/lbs"
Vue.use(VueAwesomeSwiper)
let getSon = inject("getSon")
const props = defineProps({
  items: {
    type: Array,
    default: (data) =>{
      return data || []
    }
  }
})
const iconListOption = {
  autoplay: false,
  pagination: {
    el: '.swiper-pagination-iconlist',
    type: 'bullets',
  },
}
const guideList = computed(()=>{
  let list = [[]]
  props.items.forEach((item,index)=>{
    if(index>4){
      if(list[1]){
        list[1].push(item)
      }else{
        list[1]=[item]
      }
    }else{
      list[0].push(item)
    }
  })
  return list
})

let guideItme = null
const getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null
const user = computed(()=>{
  return (proxy && proxy.$store.getters && proxy.$store.getters.user) ? proxy.$store.getters.user : null
})
const pageInfo = computed(()=>{
  return (proxy && proxy.$store.getters && proxy.$store.getters.pageInfo) ? proxy.$store.getters.pageInfo : null

})
function insertCodeGoods(item){
  guideItme = item
  // {
  //   "desc": "",//空或者"该功能正在开放中，敬请期待"
  //   "descId": "ydyw",//简称，可以用作唯一标识
  //   "iconType": 1,//1标识原业务，2标识新业务
  //   "img": "https://img1.shop.10086.cn/fs/goods/fs_62c2b21ee4b0b77767875c38.png",
  //   "link": "https://touch.10086.cn/i/mobile/busiqrydeal.html",
  //   "showStatus": 1,//1展示，0desc=该功能正在开放中，敬请期待
  //   "title": "已订业务",//中文标题
  //   "type": 6//后端对原业务的枚举
  // }
  if(!item.showStatus){
    insertCode('yd_index_'+pageInfo.value.shopId+"_icon_"+item.dcsId)
    Toast(item.desc)
    return false
  }else if(user.value.userInfo==null){
    //强登
    openloginSelf(commonInsert,true)
  }else{
    if(item.descId=='pdqh'){
      yuyue()
    }else{
      commonInsert()
    }

  }

}
function commonInsert(res) {
  insertCode('yd_index_'+pageInfo.value.shopId+"_icon_"+guideItme.dcsId,guideItme.link)
}
function yuyue(){
  lbsApi.getUrlReq({
    pageType:2,
    shopId: pageInfo.value.shopId
  }).then(res => {
    if(res.code ==0) {
      insertCode('yd_index_'+pageInfo.value.shopId+"_icon_pdqh",res.data.url)
    }else{
      Toast(res.message)
    }
  })
}
function openloginSelf(callback,showCancle) {
  return getSon.openlogin(callback,showCancle)
}
</script>

<style lang="scss" scoped>

  .guide{
    background: #fff;
    width: 345px;
    margin: 0 auto;
    display: flex;

    flex-flow:row wrap;
    .swiper-container{
      width:345px;
    }
    .swiper-slide{
      display: flex;
    }
    .item {
      height: 95px;
      width:60px;
      margin-right: 13px;
      padding-bottom: 17.5px;
      font-size: 13px;
      color: #3C3C3C;
      text-align: center;
      &.single{
        height:80px;
      }
      &:last-child{
        margin-right: 0;
      }
      .img {
        width: 40px;
        height: 40px;
        border-radius:41px;
        margin: 0 auto 8px auto;
        display: block;
        background-size: contain;
      }
    }
    .swiper-pagination-iconlist{
      position:absolute;
      text-align: center;
      :deep(.swiper-pagination-bullet){
        width: 9px;
        height: 3px;
        background: #5B99FA;
        border-radius: 1.5px;
        margin:0;
      }
    }
  }
  .iconlist-con{
    display: flex;
    width: 345px;
  }
</style>
