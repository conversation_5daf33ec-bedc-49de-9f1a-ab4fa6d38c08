<template>
  <div class="information">
    <div class="vip">
      <div class="viped-top">
        <span class="vip-content">
          {{ (contactData.member && user.userInfo) ?"已关注店铺 享受粉丝福利" :"关注店铺 粉丝福利专享" }}
        </span>
        <div v-if="contactData.member && user.userInfo" class="member-btn" @click="handlerfans(contactData.shopId)">
          <Icon name="like" />
          <span>粉丝福利</span>
        </div>
        <div v-else class="member-btn" @click="AddMemberInfo(contactData.shopId)">
          <Icon name="like-o" />
          <span>立即关注</span>
        </div>
      </div>
      <div class="card">
        <!-- 店铺标题信息 -->
        <div class="text-center">
          <div class="pongetitle" style="display:flex;">
            <span class="elli">{{ contactData.shopName }} </span>
          </div>
          <div class="business-hour">
            <div class="online-btn" @click="handleOnlineWatchShop">
              <span>在线看店</span>
              <Icon name="arrow" />
            </div>
            <div v-if="showShopStatusTag" class="business-status" :class="[SHOPOPENSTATUS[businessStatus].color]">
              {{ SHOPOPENSTATUS[businessStatus].text }}
            </div>
            <div v-if="showShopStatusPanel" class="businesscontent" @click="switchShowPanel">
              {{ contactData.currentHour }}
              <Icon name="arrow-left" class="arrow-down" />
              <div
                v-if="showShopStatusPanel && contactData.showPanel"
                class="panel"
                :class="{ panelHide: contactData.showPanel }"
              >
                <div v-for="(item, index) in businessHours" :key="index">
                  <span class="panel-left">{{ Day[index] }}</span>
                  <span class="panel-right">{{ item }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="address elli">
            {{ contactData.address || "暂无地址信息" }}
          </div>
        </div>
        <!-- 按钮列表 -->
        <div class="btnlist">
          <div class="btngroup">
            <div v-if="showShareIcon" class="icon-border" @click="shareShopOwn()">
              <Icon name="share" />
            </div>
            <a :href="telNumber" class="icon-border" @click="insertCodeGoods('phone')">
              <Icon name="phone" />
            </a>
          </div>
          <div v-if="!isInnerMon" class="wx" @click="getCustomerCode(props.shopInformation.sharingCode)">
            <img src="@/assets/index_img/wx.png" alt="" />
            <span>微信客服</span>
          </div>
        </div>
      </div>
    </div>
    <MemberGifts ref="memberGiftsRef" />
  </div>
</template>

<script setup>
import insertCode from "@/utils/insertCode"
import VipApi from "@/api/vip"
import Vue,{ref,reactive,defineProps,inject,getCurrentInstance,computed,watch, onMounted,defineEmits} from "vue"
import {
  DatetimePicker,
  Toast,
  Popup,
  Checkbox,
  CheckboxGroup,
  Dialog,
  Icon
} from "vant"
import MemberGifts from "@/components/index/membergifts.vue"
import EventBus from "@/api/eventbus"
import shopAPI from "@/api/shop"
import { isInnerM } from "@/utils/homedata.js"
import { objectMerge } from "@/utils/utils"

Vue.use(DatetimePicker)
  .use(Popup)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Dialog)
const props = defineProps({
  shopInformation:{
    type:Object,
    default:(data)=>{
      return data
    }
  },
  shareShop:{
    type:Function,
    default:()=>{}
  },
})
let getSon = inject("getSon")
// 获取店铺营业时间
const businessHours  = ref("")
// 获取店铺营业状态
const businessStatus = ref("")
// 5营业中 6休息中 1不显示 2关厅
/** 营业时间状态词典 */
const SHOPOPENSTATUS = {
  1:{
    text:"不显示",
    color:"",
    bgColor:"",
    status:1
  },
  2:{
    text:"关厅",
    color:"red",
    status:2
  },
  5:{
    text:"营业中",
    color:"blue",
    status:5
  },
  6:{
    text:"休息中",
    color:"grey",
    status:6,
  }
}
/** 是否展示营业状态 */
const showShopStatusTag = computed(()=>{
  const showTagStatus = [2,5,6]
  return showTagStatus.includes(businessStatus.value)
})
/** 是否展示营业时间 */
const showShopStatusPanel = computed(()=>{
  const showPanelStatus = [5,6]
  return showPanelStatus.includes(businessStatus.value)
})
const contactData = reactive({
  shopName: "店铺名称",
  address: "店铺地址",
  member: "",
  currentHour: null,
  showPanel: false,
  recommendCode:null
})
watch(()=>props.shopInformation,(val)=>{
  if (val) {
    Object.assign(contactData,props.shopInformation)
    businessHours.value = props.shopInformation.businessHours
    businessStatus.value = props.shopInformation.businessStatus
    if(showShopStatusPanel.value){
      getCurrentHour()
    }

  }
},{immediate: true})
/* 店铺手机 */
const telNumber = computed(()=>{
  if(!props.shopInformation){
    return ""
  }
  let keFuPhone = props.shopInformation.keFuPhone
  let telNumber = props.shopInformation.telNumber

  if (keFuPhone) {
    return "tel:" + keFuPhone
  } else {
    return "tel" + telNumber
  }
})
const isInnerMon = computed(()=>{
  //内蒙的店铺不在首页展示微信客服入口
  return isInnerM(props.shopInformation.province)
})
const Day= ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
const getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null
const user = ref(null),pageInfo = ref(null)
if(proxy){
  user.value = proxy.$store.getters.user
  pageInfo.value = proxy.$store.getters.pageInfo
}
watch(()=>user,(val)=>{
  if (val && val.value && val.value.userInfo) {
    ismember(props.shopInformation.shopId)
  }
},{immediate: true})
const showShareIcon = inject("showShare")
/* 跳转粉丝页面 */
function handlerfans() {
  if(!(user.value && user.value.userInfo)){
    openloginSelf(null,true)
    return false
  }
  proxy.$router.push({
    path: '/vermicelli/index.html',
    query: {
      shopId: contactData.shopId,
      key:props.shopInformation.sharingCode
    }
  })
}
/* 跳转登录页面 */
function openloginSelf(callback,showCancle) {
  return getSon.openlogin(callback,showCancle)
}
const memberGiftsRef = ref(null)
/* 查询优惠券信息 */
function CouponCheck() {
  memberGiftsRef.value.CouponCheck(contactData.shopId)
}
/* 展示优惠券信息 */
function ShowCouponPop() {
  memberGiftsRef.value.ShowCouponPop(contactData.shopId)
}
/* 点击在线看店 */
function handleOnlineWatchShop() {
  if (pageInfo.value && pageInfo.value.pageInfo && pageInfo.value.pageInfo.shopOnlineStatus == 1) {
    insertCode(
      "yd_index_" + contactData.shopId + "_icon_zxkd",
      "/yundian/onlinewatchshop/index.html?shopId=" +
        contactData.shopId
    )
  }else{
    insertCode("yd_index_" + contactData.shopId + "_icon_zxkd")
    Toast("信息正在准备中，敬请期待~")
    return false
  }
}
function switchShowPanel() {
  contactData.showPanel = !contactData.showPanel
}

/** 查询是否是会员 */
function ismember(shopId) {
  VipApi.IsMember({
    shopId: shopId
  }).then(res => {
    if (res.code == 0) {
      contactData.member = res.data.member
      proxy.$store.commit("SET_ISMEMBER", res.data.member)
      if (res.data.member == 1) {
        CouponCheck() //查询入会礼可用券
      }
    }
  })
}
/** 加入会员 */
function AddMemberInfo() {
  if(!(user.value && user.value.userInfo)){
    openloginSelf(null,true)
    return false
  }
  insertCodeGoods("add")
  if (contactData.recommendCode) {
    contactData.staffId = contactData.recommendCode
  }
  VipApi.AddMemberInfo({
    shopId: contactData.shopId,
    staffId: contactData.staffId
  }).then(res => {
    if (res.code == 0) {

      if (!(res.data.shopId && res.data.memberId)) {
        // Toast("您已经是会员了")
        Toast("您已经关注了")
      } else {
        // Toast("领取成功")
        Toast("关注成功")
      }

      ismember(contactData.shopId)
      ShowCouponPop() //弹出入会礼弹窗
    } else if (res.code == 110101) {
      Toast(res.message)
    } else {
      Toast("领取出问题了，请再试一次吧")
    }
  })
}
function insertCodeGoods(key) {
  let dcs_id = key
  insertCode("yd_index_" + contactData.shopId + "_" + dcs_id)
}

/** 店铺的分享 */
function shareShopOwn() {
  props.shareShop()
}
/** 查看微信客服 */
function getCustomerCode(sharingCode) {
  insertCodeGoods("managerwecom")
  // sharingQrCode 是代表是否开启了企微客服功能
  //sharingCode 是代表企微请求客服二维码的加密串
  if (!props.shopInformation.sharingQrCode || !sharingCode) {
    Toast("信息正在准备中，敬请期待")
    return
  }
  shopAPI.getCustomerCode({ sharingCode }).then(r => {
    if (r.code) {
      Toast(r.message)
    } else {
      if (r.data.contactQrcode) {
        EventBus.$emit("openShareDialog", "service", r.data.contactQrcode)
      } else if (location.origin.indexOf("grey.touch.10086.cn") && r.data.qrCode) {
        EventBus.$emit("openShareDialog", "service", r.data.qrCode)
      } else {
        Toast("信息正在准备中，敬请期待")
      }
    }
  })
}
/** 营业时间展示 */
function getCurrentHour() {
  if (businessHours.value) {
    let week = new Date().getDay(),
      str = ""
    if (week > 0) {
      str = businessHours.value[week - 1]
    } else {
      str = businessHours.value[6]
    }
    contactData.currentHour = str
  } else {
    contactData.currentHour = ""
  }
}
</script>

<style lang="scss" scoped>
.hidescroll {
  overflow: hidden;
}
.information{
  margin-bottom: 16px;
}
.vip {
  background:url(~@/assets/home/<USER>
  background-size: contain;
  box-shadow: 0 -1px 13px -2px #43609a33;
  border-radius: 6px;
  margin: 1px 15px;
  position: relative;
  height: auto;
  .viped-top {
    background-image: linear-gradient(90deg, #E3EAF6 18%, #BEC5F0 99%);
    height: 36px;
    display: flex;
    font-size: 12px;
    color: #3D42CB;
    padding: 0 15px;
    align-items: center;
    font-weight: bold;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    .member-btn{
      background: rgba(256,256,256,0.5);
      width: 72.5px;
      height: 22px;
      border-radius: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      font-weight: bold;
      .van-icon{
        margin-right: 6px;
        font-weight: bold;
      }
    }
    .vip-content{
      flex:1;
    }
  }
  .card {
    border-radius: 0 0 8px 8px;
    margin: 0 auto;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    padding: 12px 15px 13px;
    height:92px;
    .text-center {
      flex: 1;
      height:65px;
      min-width: 0;
      display: flex;
      flex-direction: column;
      justify-content:space-between;
      padding: 0px;
      .pongetitle {
        font-weight: bold;
        font-size: 16px;
        line-height: 18px;
        color: #000000;
        .elli{
          display: block;
        }
      }
      .business-hour {
        position: relative;
        display: flex;
        align-items: center;
        .online-btn{
          width: 61.5px;
          height: 18px;
          background: #FCEDDB;
          border-radius: 3px;
          font-size: 10px;
          color: #784D23;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .business-status{
          width: 46px;
          height: 18px;
          border-radius: 2px;
          font-size: 10px;
          margin:0 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          &.blue{
            color: #2B9FFF;
            background-image: linear-gradient(0deg, #2aa3ff1a 0%, #2d93ff1a 100%);
          }
          &.grey{
            color: #333333;
            background: #E2EAF1;
          }
          &.red{
            background: #FCE5EB;
            color: #FF4977;
          }
        }
        .businesscontent {
          font-size: 12px;
          color: #666666;
          position: relative;
          align-items: center;
          .arrow-down {
            transform: rotate(-90deg);
          }
          &>span {
            margin-left: 5px;
          }
        }
      }
      .address{
        font-size: 12px;
        color: #666666;
      }
    }
    .btnlist {
      display: flex;
      align-items: center;
      margin-left: 20px;
      flex-direction: column-reverse;
      justify-content: space-between;
      height:68px;
      .wx {
        display: flex;
        justify-content: center;
        width: 66px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 20px;
        background: #28C445;
        img {
          width: 16px;
          height: 16px;
        }
        span {
          color: #fff;
          font-size: 9px;
          line-height: 20px;
        }
      }
      .btngroup{
        display: flex;
        width:66px;
        align-items: center;
        flex-direction: row-reverse;
        justify-content: space-between;
        padding-left:13px;
        .icon-border{
          width:20px;
          height: 20px;
          // border: 0.5px solid #2D2F35;
          border-radius: 50%;
          text-align: center;
          padding-top:4px;
          background-image: linear-gradient(180deg, #53b1fb 0%, #2f95ff 100%);
          .van-icon{
            font-size:12px;
            color:#fff;
          }
        }
      }
    }
    .panel {
      position: absolute;
      z-index: 10;
      top: 20px;
      left: 0;
      padding: 10px 0;
      width: 142px;
      background: #fff;
      box-shadow: 0 1px 6.5px 0 #23425133;
      border-radius: 4px;
      text-align: center;
      font-size: 13px;
      color: #333333;
      line-height: 24px;
    }
  }
  .elli{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .panel-left{
    margin-right: 10px;
  }
}
</style>
