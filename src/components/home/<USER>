<template>
  <div v-if="props.bannerObj.imgUrl" class="banner" :style="{'backgroundImage':'url('+getImgUrl(props.bannerObj.imgUrl)+')'}" @click="goImgLink(props.bannerObj.targetUrl)">
    <!-- <img :src="getImgUrl(props.bannerObj.imgUrl)" /> -->
  </div>
</template>
<script setup>
import { defineProps,reactive } from "vue"
import insertCode from "@/utils/insertCode.js"
import {getImgUrl} from "@/utils/utils.js"
const props = defineProps({
  bannerObj:{
    type:Object,
    default:(data)=>{
      return data || {}
    }
  }
})
function goImgLink(targetUrl){
  location.href = targetUrl
}
function insertCodeGoods(key,goodsId,source,url){
  let dcs_id = key+'_'+goodsId+'_'+source
  insertCode("goods_"+dcs_id,url)
}
</script>
<style lang="scss" scoped>
.banner{
  width: 375px;
  height: 200px;
  // background: url("https://img1.zz.ydsc.liuliangjia.cn/fs/goods/fs_65e03f8de4b07c253215c7cc.png") no-repeat;
  background-size: cover;
  background-repeat: no-repeat;
  margin-bottom: 12px;
  img{
    width: 100%;
    height: 100%;
  }
}
</style>
