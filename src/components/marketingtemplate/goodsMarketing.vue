<template>
  <div class="goods-market">
    <h3 v-if="props.items.title||props.items.titleImage">
      <img v-if="props.items.titleImage" :src="getImgUrl(props.items.titleImage)" alt="">
      <span v-else :style="titleColor">{{ props.items.title }}</span>
    </h3>
    <div v-if="props.goodslist.length > 0" class="market-con" :class="{'marketflex':props.imgtxtype == 2,'padg20':!props.items.title&&!props.items.titleImage}">
      <div v-for="(item, key) in props.goodslist" :key="key" class="goods-list">
        <img :src="getImgUrl(item.picture)" alt="">
        <div class="goods-text">
          <h6 class="ellipsis">
            {{ item.goodsName }}
          </h6>
          <p v-if="props.imgtxtype == 1" class="subtitle ellipsis">
            {{ item.goodsSubhead }}
          </p>
          <p v-if="props.imgtxtype == 2&& item.goodsSubhead" class="subtitle ellipsis">
            {{ item.goodsSubhead }}
          </p>
          <p class="amount">
            <span>¥<i>{{ (item.minPrice / 100).toFixed(2) }}</i>元起</span>
            <a href="javascript:void(0);" @click="handleLink(item.goodsLink)">
              立即办理
            </a>
          </p>
        </div>
      </div>
    </div>
    <div v-else class="foods-no-con">
      <img src="~@/assets/index_img/ice_tab_no.png" />
      <p>暂无活动商品</p>
    </div>
  </div>
</template>
<script setup>
import { getToken, removeLocalStorage } from "@/utils/login/localStorage.js"
import { Toast } from "vant"
import { reactive, defineProps, ref, onBeforeMount,computed } from "vue"
import { getImgUrl } from "@/utils/utils.js"
import loginApi from "@/api/login"
import "vant/lib/index.css"
const props = defineProps({
  items: {
    type: Object,
    default: () => {
      return {}
    },
  },
  goodslist: {
    type: Array,
    default: () => {
      return []
    },
  },
  imgtxtype: {
    type: [String, Number],
    default: null
  }
})
const data = reactive({
  logUser: null,
  isLogined: false,
  conbackg: '#FFF4E7',
  color: '#000',
  goodslist: [],
})
const titleColor=computed(()=>{
  let titColor=props.items.titleColor?props.items.titleColor:'#333'
  let backgroundColor=props.items.color?props.items.color:'#fff'
  return {color:titColor,background:backgroundColor}
})
const shopId = ref(null)
onBeforeMount(async()=>{
  loginApi.getUserInfo(getToken(),"0",5).then((res)=>{
    if (res.code == 0) {
      logined(res.data)
    } else if (res.code == 1) {
      removeLocalStorage()
      return null
    }
  })
})
async function logined(res) {
  if(!res){
    return false
  }
  data.logUser = res
  data.isLogined = res && res.UserName > ""
  shopId.value = res.shopId
}
function handleLink(goodsLink){
  location.href = goodsLink+((goodsLink.indexOf('?')> -1 )?'&':'?')+'WT.ac=PosterShare'
}
</script>
<style lang="scss" scoped>
  .goods-market{
    margin-bottom: 20px;
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
    h3{
      img{
        display: block;
        width: 100%;
      }
      span{
        font-size: 16px;
        line-height: 40px;
        display: block;
        text-align: center;
      }
    }
    .market-con{
      padding: 10px 8px 6px;
      .goods-list{
        width: 50%;
        display: inline-block;
        padding: 0 12px 16px;
        text-align: center;
        h6{
          font-size: 16px;
          color: #333333;
          line-height: 22px;
          margin-bottom: 7px;
          white-space: nowrap; /* 确保文本在一行内显示 */
        }
        .subtitle{
          text-align: left;
          color: #666666;
          height: 36px;
          line-height: 18px;
          font-size: 12px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          margin-bottom: 4px;
        }
        .amount{
          font-size: 14px;
          color: #ff0000;
          line-height: 20px;
          i{
            font-size:24px;
          }
          a{
            display: block;
            width: 75px;
            height: 26px;
            line-height: 26px;
            background: linear-gradient(135deg,#3759ff, #34caf4);
            border-radius: 16px;
            color: #fff;
            font-size: 13px;
            margin: 5px auto 0;
          }
        }
        img{
          display: block;
          width: 145px;
          height: 145px;
          margin: 8px auto;
        }
      }
    }
    .marketflex{
      .goods-list{
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align:left;
        padding: 0 12px 10px;
        h6{
          margin-bottom: 2px;
        }
        .subtitle{
          height: auto;
          max-height: 34px;
          line-height: 17px;
          margin-bottom: 6px;
        }
        .amount{
          position: relative;
          font-size: 12px;
          line-height: 26px;
          padding-top: 5px;
          span{
            display: inline-block;
            width: 110px;
            word-wrap: break-word;
            overflow-wrap: break-word;
          }
          i{
            font-size:18px;
          }
          a{
            position: absolute;
            right: 0;
            top: 50%;
            text-align: center;
            margin: 0;
            margin-top: -11px;
          }
        }
        img{
          width: 100px;
          height: auto;
          margin-right: 24px;
        }
        .goods-text{
          flex: 1;
          overflow: hidden;
        }
      }
    }
    .ellipsis {
      overflow: hidden; /* 隐藏溢出的内容 */
      text-overflow: ellipsis; /* 使用省略号表示溢出的文本 */
    }
    .foods-no-con{
      text-align: center;
      color: #999;
      padding: 30px 0 20px;
      line-height: 37.5px;
      font-size: 13.5px;
    }
    .padg20{
      padding: 16px 8px 8px;
    }
  }
</style>
