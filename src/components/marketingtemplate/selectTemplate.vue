<template>
  <ul class="goods-list">
    <li v-for="(item, key) in props.items" :key="key" :class="{'goodswid':props.templatetype == 2}">
      <div class="goods-list-con" :class="{'current': data.goodslist.indexOf(item)!=-1||props.godscurrent.indexOf(item)!=-1||data.selectKey.indexOf(item)!=-1}" @click="handleClick(item)">
        <img v-if="props.templatetype == 1" :src="getImgUrl(item.previewPicture)" alt="">
        <img v-else :src="getImgUrl(item.picture)" alt="">
        <span class="goods-ice"></span>
        <p v-if="props.templatetype == 1" class="title ellipsis">
          {{ item.templateName }}
        </p>
        <p v-else class="title ellipsis">
          {{ item.goodsName }}
        </p>
        <p v-if="props.templatetype == 1" class="total">
          支持<span>{{ item.goodsNumber }}</span>个商品
        </p>
        <p v-else class="price">
          <em>￥</em>
          {{
            (item.minPrice / 100).toFixed(2)
          }}
        </p>
      </div>
    </li>
  </ul>
</template>
<script setup>
import Vue ,{reactive,defineProps,defineEmits } from "vue"
import { getImgUrl } from "@/utils/utils.js"
const emit = defineEmits(["emitSelectTemplate","emitSelectgoods"])
const props = defineProps({
  items: {
    type: Array,
    default: () => {
      return []
    },
  },
  godscurrent: {
    type: Array,
    default: () => {
      return []
    },
  },
  godcur: {
    type: [String, Number],
    default: null,
  },
  templatetype: {
    type: [String, Number],
    default: null
  }
})
const data = reactive({
  goodslist: [],
  templatetype: props.templatetype,
  selectKey: []
})
function handleClick(item) {
  if(props.templatetype == 1){
    data.goodslist=[]
    data.goodslist.push(item)
    emit("emitSelectTemplate",data.goodslist)
  }else{
    const godCur = props.godcur?props.godcur:0
    data.selectKey=props.godscurrent
    if(data.selectKey.indexOf(item)!=-1){
      data.selectKey.splice(data.selectKey.indexOf(item),1)
    }else{
      if(godCur>data.selectKey.length){
        data.selectKey.push(item)
      }
    }
    emit("emitSelectgoods",data.selectKey)
  }
}
</script>
<style lang="scss">
  .goods-list {
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    flex-flow: row wrap;
    text-align: center;
    padding: 0 5px;
    li{
      width: 50%;
      padding: 0 6px;
      .goods-list-con {
        position: relative;
        padding: 10px 0;
        line-height: 24px;
        img{
          display: block;
          width: 100%;
          height: 170px;
          margin-bottom: 3px;
        }
        .goods-ice{
          position: absolute;
          right: 7px;
          top: 16px;
          width: 22px;
          height: 22px;
          background: #ffffff;
          border: 1px solid #dbdbdb;
          border-radius: 50%;
        }
        p{
          overflow: hidden;
          white-space: nowrap;
          span{
            color: red;
          }
        }
        .title{
          font-size: 16px;
          font-weight: bold;
        }
        .total,.price{
          font-size: 13px;
          line-height: 18px;
          color: #999999;
        }
        .price{
          color: #FF0000;
        }
      }
      .current{
        .goods-ice{
          border: none;
          background: url(~@/assets/marketingtemplate/selected.png) center top no-repeat;
          background-size: 100% auto;
        }
        img{
          opacity: 0.51;
        }
      }
    }
    .goodswid{
      .goods-list-con {
        padding: 0 0 10px;
        line-height: 20px;
        img{
          height: 125px;
          margin-bottom: 5px;
        }
        .goods-ice{
          width: 17px;
          height: 17px;
          right: 4px;
          top: 5px;
        }
        .title,.price{
          font-size: 12px;
        }
      }
    }
    .ellipsis {
      overflow: hidden; /* 隐藏溢出的内容 */
      text-overflow: ellipsis; /* 使用省略号表示溢出的文本 */
    }
  }
</style>
