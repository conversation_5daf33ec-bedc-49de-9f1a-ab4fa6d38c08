<template>
  <div class="text-plate">
    <h6 v-if="props.items.title||props.items.titleImage">
      <img v-if="props.items.titleImage" :src="getImgUrl(props.items.titleImage)" alt="">
      <span v-else :style="{color:titColor,background:backgroundColor}">{{ props.items.title }}</span>
    </h6>
    <div :class="{'plateflex':props.imgtxtype == 2,'padg20':!props.items.title&&!props.items.titleImage}">
      <img :src="getImgUrl(props.items.picture)" alt="">
      <p>{{ props.items.description }}</p>
    </div>
  </div>
</template>
<script setup>
import { getToken, removeLocalStorage } from "@/utils/login/localStorage.js"
import { Toast } from "vant"
import { reactive, defineProps,computed, ref, getCurrentInstance, onBeforeMount } from "vue"
import { getImgUrl } from "@/utils/utils.js"
import loginApi from "@/api/login"
import "vant/lib/index.css"
const props = defineProps({
  items: {
    type: Object,
    default: () => {
      return {}
    },
  },
  imgtxtype: {
    type: [String, Number],
    default: null
  }
})
const data = reactive({
  logUser: null,
  isLogined: false,
  conbackg: '#FFF4E7',
  color: '#000',
})
const titColor=computed(()=>{
  return props.items.titleColor?props.items.titleColor:'#333'
})
const backgroundColor=computed(()=>{
  return props.items.color?props.items.color:'#fff'
})
const shopId = ref(null)
onBeforeMount(async()=>{
  loginApi.getUserInfo(getToken(),"0",5).then((res)=>{
    if (res.code == 0) {
      logined(res.data)
    } else if (res.code == 1) {
      removeLocalStorage()
      return null
    }
  })
})
async function logined(res) {
  if(!res){
    return false
  }
  data.logUser = res
  data.isLogined = res && res.UserName > ""
  shopId.value = res.shopId
}
const getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null
function jumpLink(item){
  //route yundian是云店链接，hd是商城，outside是第三方 getUrl("/hd/xskd/index.html?cmToken={sourceid:12006}&shopId=" + myFooter.urlInfo.shopId)
  switch(item.route){
  case "yundian":
    proxy.$router.push({path:item.links})
    break
  default:
    location.href = item.links
  }
  window.scrollTo(0, 1)
}
</script>
<style lang="scss" scoped>
  .text-plate{
    margin-bottom: 20px;
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
    text-align: center;
    h6{
      img{
        display: block;
        width: 100%;
      }
      span{
        font-size: 16px;
        line-height: 40px;
        display: block;
        text-align: center;
      }
    }
    div{
      padding: 10px 30px 20px;
      font-size: 12px;
      line-height: 20px;
      color: #666666;
      img{
        display: block;
        width: 100%;
        height: auto;
        margin: 8px auto 10px;
      }
    }
    .plateflex{
      padding: 10px 30px 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align:left;
      p{
        flex: 1;
      }
      img{
        width: 100px;
        height: 100px;
        // height: auto;
        margin-right: 24px;
      }
    }
    .padg20{
      padding: 20px 30px;
    }
  }
</style>
