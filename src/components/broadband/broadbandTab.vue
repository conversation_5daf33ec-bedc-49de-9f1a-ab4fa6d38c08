<template>
  <div>
    <Tab v-for="item in props.floor" :key="item" :title="item">
    </Tab>
    <div class="broadbandcon">
      <List
        v-if="props.items && props.items.length"
        v-model="broadbandData.myLoading"
        :finished="props.finished"
        :finished-text="props.nodataMessage"
        class="broadband-list ul"
        @load="onLoad"
      >
        <Cell v-for="(it) in props.items" :key="it.orderId" class="li">
          <div class="bd-list-content clearfix">
            <h2>
              <p @click="intoDetail(it.orderId)">
                预约单号：{{ it.orderId }}
              </p>
              <a v-if="props.tplId=='roleUser'&&it.state!='TOC'" @click="openDeleteOrderDialog(it.orderId)">删除</a>
              <a v-if="props.tplId=='roleUser'&&it.state=='TOC'" @click="openCancleReservationDialog(it.orderId)">取消预约</a>
              <a v-if="props.tplId!='roleUser'&& ['HUD','TOC','AUF','AUS'].indexOf(it.state)!==-1" @click="openChangeOrderDialog(it.orderId)">
                立即处理
              </a>
              <a v-if="props.tplId!='roleUser'&& ['HUD','TOC','AUF'].indexOf(it.state)!==-1" @click="handlerDealWith(it)">
                修改信息
              </a>
              <a v-if="props.tplId!='roleUser'&& (it.state=='TOA')" @click="handlerUndo(it)">撤消修改</a>
            </h2>
            <div class="bd-con clearfix">
              <div v-if="props.tplId!='roleUser'" class="bd-con-ner">
                <p @click="intoDetail(it.orderId)">
                  预约类型：{{ it.appointmentType == '20'?'终端预约':'宽带预约' }}
                </p>
                <p v-if="it.appointmentName" class="bd-hs-ys fl-rid" @click="intoDetail(it.orderId)">
                  <span>预约人姓名：</span><span class="maw80 breakAll">{{ it.appointmentName }}</span>
                </p>
                <div class="clearfix">
                  <p v-if="it.appointmentMobile"> 预约手机号：
                    <span>
                      {{ getpassphone(it.appointmentMobile) }}
                    </span>
                    <a
                      v-if="it.appointmentType == broadbandData.appointmentType.terminal || broadbandData.showPhone.indexOf(it.state)!==-1"
                      :href="'tel:'+it.appointmentMobile"
                      class="icon-phone-img"
                    >
                      <img src="~@/assets/index_normal/phone.png" alt="" />
                    </a>
                  </p>
                  <p :class="it.appointmentMobile?'fl-rid':''" @click="intoDetail(it.orderId)">
                    预约状态：
                    <span :class="broadbandData.colorConfig[it.state]">
                      {{ broadbandData.shopState.indexOf(it.state)!==-1 ? broadbandData.stateText[it.state] : "" }}
                    </span>
                  </p>
                </div>
              </div>
              <div v-else class="bd-con-ner" @click="intoDetail(it.orderId)">
                <p>预约类型：{{ it.appointmentType == '20'?'终端预约':'宽带预约' }}</p>
                <p class="fl-rid">
                  预约手机号：{{ getpassphone(it.appointmentMobile) }}
                </p>
              </div>
              <p v-if="props.tplId!='roleUser'" class="bd-hs-ys hs mx" @click="intoDetail(it.orderId)">
                <span>预约地址明细：</span>
                <span class="w80 breakAll">{{ it.province+' '+it.city+' '+it.district+' '+it.appointmentAddress }}</span>
              </p>
              <p class="bd-xd-yyxt hs" @click="intoDetail(it.orderId)">
                下单时间：{{ it.createTime }}
                <span v-if="props.tplId=='roleUser'" :class="broadbandData.colorConfig[it.state]" class="fl-rid">
                  {{ broadbandData.roleUserState.indexOf(it.state)!==-1 ? broadbandData.stateText[it.state] : "" }}
                </span>
              </p>
              <p v-if="it.reason&&it.reason!=''" class="bd-hs-ys" @click="intoDetail(it.orderId)">
                <span>失败原因：</span><span class="w80 breakAll">{{ it.reason }}</span>
              </p>
            </div>
          </div>
        </Cell>
      </List>
      <ul
        v-else
        class="broadband-tab-no ul"
      >
        <li class="tab-no-con li">
          <div>
            <img src="~@/assets/index_img/ice_tab_no.png" />
            <p>
              {{ props.nodataMessage }}
            </p>
          </div>
        </li>
      </ul>
    </div>

    <!-- 修改信息弹窗 -->
    <modifythe :modifythe-data="broadbandData.modifytheData" :modify-dialog="broadbandData.modifyDialog" @updateOrder="refresh" @closeModifyDialog="closeDialog" />
    <!-- 预约处理弹框 -->
    <van-dialog
      v-model="broadbandData.showChangeOrderStatus"
      title="预约处理"
      show-cancel-button
      confirm-button-color="#5fade8"
      :before-close="changeOrderStatus"
    >
      <RadioGroup v-model="sendData1.state" direction="horizontal" class="dialogContent">
        <Radio name="MAS">
          预约成功
        </Radio>
        <Radio name="MAF">
          预约失败
        </Radio>
      </RadioGroup>
      <Field
        v-if="sendData1.state=='MAS'"
        v-model="sendData1.productCode"
        class="boradbandInput"
        type="text"
        placeholder="请输入BOSS侧宽带账号"
        label-width="100px"
        clearable
      />
      <Field
        v-if="sendData1.state=='MAF'"
        v-model="sendData1.reason"
        class="boradbandInput"
        type="textarea"
        :autosize="{maxHeight: 100}"
        placeholder="请填写预约失败原因，例：用户主动取消"
        label-width="100px"
        :maxlength="256"
        clearable
      />
    </van-dialog>
    <!-- 取消弹框 -->
    <van-dialog
      v-model="broadbandData.showCancleReservation"
      title="取消提示"
      show-cancel-button
      confirm-button-color="#5fade8"
      :before-close="cancleReservation"
    >
      <p class="dialogContent" style="text-align:center">
        确定要取消吗？
      </p>
      <Field
        v-model="sendData2.reason"
        class="boradbandInput"
        type="textarea"
        autosize
        placeholder="请填写取消原因"
        colon
        clearable
        label-width="100px"
      />
    </van-dialog>
    <!-- 删除弹框 -->
    <van-dialog v-model="broadbandData.showdeleteOrder" title="删除提示" confirm-button-color="#5fade8" show-cancel-button @confirm="deleteOrder">
      <p class="dialogContent" style="text-align:center">
        确定删除吗？
      </p>
    </van-dialog>
  </div>
</template>
<script setup>
import Vue ,{ref,reactive,onMounted,watch,defineProps,getCurrentInstance} from "vue"
import {Dialog ,Tab,RadioGroup, Radio,Field, Toast,List ,Cell} from 'vant'
import broadbandApi from "@/api/broadband"
import {getPassPhone} from "@/utils/utils"
import modifythe from '@/components/broadband/modifythe'
Vue.use(Dialog)
const props = defineProps({
  items: {
    type: Array,
    default: () =>{
      return []
    }
  },
  floor:{
    type:Array,
    default:()=>{
      return []
    }
  },
  tplId:{
    type: String,
    default: null
  },
  ruleid:{
    type: [String,Number],
    default: null
  },
  refresh:{
    type:Function,
    default:fn=>{
      return fn
    }
  },
  nodataMessage:{
    type: String,
    default: "暂无预约列表信息"
  },
  loading:{
    type:Boolean,
    default:false
  },
  finished:{
    type:Boolean,
    default:false
  },
  onload:{
    type:Function,
    default:fn=>{
      return fn
    }
  }
})

const sendData1 = reactive({
  state:'MAS',
  productCode:'',
  reason:''
})

const sendData2 = reactive({
  reason:''
})

const broadbandData = reactive({
  modifyDialog:false,
  modifytheData:{},
  showChangeOrderStatus:false,
  showCancleReservation:false,
  showdeleteOrder:false,
  myLoading:false,
  stateText:{
    TOC:"待确认",
    MAS:"预约成功",
    MAF:"预约失败",
    HBC:"已取消",
    TOA:"待审核",
    AUS:"审核成功",
    AUF:"审核失败",
    HUD:"已撤消"
  },
  // 商户看到的状态
  shopState:['TOC','MAS','MAF','HBC','TOA','AUS','AUF','HUD'],
  // 用户看到的状态
  roleUserState:['TOC','MAS','MAF','HBC'],
  colorConfig:{
    TOC:'yll',
    MAS:'ls',
    MAF:'red',
    HBC:'hs',
    HUD:"red",
    TOA:"yll"
  },
  appointmentType:{
    broadband:10,
    terminal:20
  },
  showPhone:["TOC","TOA","HUD","AUS","AUF"],
})
watch(()=>broadbandData.showChangeOrderStatus,(val)=>{
  if(!val){
    sendData1.state = 'MAS'
    sendData1.productCode = ''
    sendData1.reason = ''
  }
})
watch(()=>broadbandData.showCancleReservation,(val)=>{
  if(!val){
    sendData2.reason = ""
  }
})
watch(()=>props.loading,(val)=>{
  if(!val){
    broadbandData.myLoading = val
  }
})
/* 撤销修改 */
function handlerUndo(item){
  broadbandApi.undoModifyBAOrder(item).then(res => {
    if(res.code){
      return
    }
    props.refresh()
  })
}

/* 点击修改信息 */
function handlerDealWith(item){
  broadbandData.modifytheData = item
  broadbandData.modifyDialog = true
}

function closeDialog(){
  broadbandData.modifyDialog = false
}

function openChangeOrderDialog(orderId){
  broadbandData.currentOrderId = orderId
  broadbandData.showChangeOrderStatus=true
}
function changeOrderStatus(action,done){
  if(action=='cancel'){
    done()
    return false
  }
  if(sendData1.state=='MAS'){//预约成功
    if(!sendData1.productCode){
      Toast('请输入BOSS侧宽带账号')
      done(false)
      return false
    }else if(!/^[\w\d]+$/.test(sendData1.productCode)){
      Toast('请输入字母或数字')
      done(false)
      return false
    }
    delete sendData1.reason
  }
  if(sendData1.state=='MAF'){//预约失败
    let myreason = sendData1.reason ? sendData1.reason.replace(/[\n\s]+/g,'') : null
    if(!myreason){
      Toast('请输入失败原因')
      sendData1.reason=""
      done(false)
      return false
    }
    delete sendData1.productCode
  }
  sendData1.orderId = broadbandData.currentOrderId
  broadbandApi.updateBAOrderState(sendData1,5).then(res=>{
    if(res.code){
      Toast(res.message)
      done(false)
    }else{
      //刷新页面
      done()
      props.refresh()
    }
  })
}
function openCancleReservationDialog(orderId){
  broadbandData.currentOrderId = orderId
  broadbandData.showCancleReservation=true
}
function getpassphone(mobile){
  return getPassPhone(mobile)
}
function cancleReservation(action,done){
  if(action=='cancel'){
    done()
    return false
  }
  if(!sendData2.reason){
    Toast('请填写取消原因')
    done(false)
    return false
  }
  sendData2.orderId = broadbandData.currentOrderId
  broadbandApi.userCannelBAOrder(sendData2).then(res=>{
    if(res.code){
      Toast(res.message)
      done(false)
    }else{
      //刷新页面
      done()
      props.refresh()
    }
  })
}
function openDeleteOrderDialog(orderId){
  broadbandData.currentOrderId = orderId
  broadbandData.showdeleteOrder=true
}
function deleteOrder(){
  broadbandApi.userDeleteBAOrder({
    orderId:broadbandData.currentOrderId
  }).then(res=>{
    if(res.code){
      Toast(res.message)
    }else{
      //刷新页面
      props.refresh()
    }
  })
}
function intoDetail(orderId){
  if(broadbandData.proxy){
    broadbandData.proxy.$router.push({
      path:'/broadband/details.html',query:{tplId:props.tplId,orderId:orderId,ruleid:props.ruleid,shopId:broadbandData.shopId}
    })
  }
}
function onLoad(){
  props.onload()
}
onMounted(()=>{
  const url = new URL(location.href)
  broadbandData.shopId = url.searchParams.get('shopId')
  let getVueInstance = getCurrentInstance()
  broadbandData.proxy = getVueInstance ? getVueInstance.proxy : null
})
</script>
<style>
body{
  background: #f9fafc;
}
.van-tab--active{
  color: #a7bfd1 !important;
}
.van-tabs__line{
  background-color: #5fade8 !important;
}
</style>
<style lang="scss" scoped>
.broadbandcon{
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding-top: 7.5px;
  color: #333;
  background: #f9fafc;
  .broadband-list :deep(.van-cell){
    padding:0
  }
  .ul{
    .li{
      background: #fff;
      margin-bottom: 7.5px;
      font-size: 13.5px;
      .clearfix:after {
        content: ".";
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
      .breakAll{
        white-space:normal;
        word-break:break-all;
        word-wrap: break-word;
      }
      .fl-rid{
        float: right;
      }
      h2{
        position: relative;
        border-bottom: 1px solid #f9fafc;
        padding: 6px 13.5px 4.5px;
        display: flex;
        align-items: center;
        p{
          display: inline-block;
          line-height: 33.75px;
          flex:1;
          min-width:0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        a{
          position: static;
          top:8.25px;
          right: 11.25px;
          border: 1px solid #0085d0;
          border-radius: 6px;
          width: 75px;
          height: 26.25px;
          line-height: 26.25px;
          text-align: center;
          color: #0085d0;
          margin-left: 10px;
        }
      }
      .bd-con{
        padding: 7.5px 13.5px;
        line-height: 26.25px;
        div{
          color: #666;
          p{
            display: inline-block;
            min-width: 40%;
            max-width: 100%;
          }
        }
        .hs{
          color: #999;
        }
        .ls{
          color: #00ad29;
        }
        .red{
          color: #ed2668;
        }
        .yll{
          color: #e97a00;
        }
        .bd-hs-ys{
          &.mx{
            display: flex;
            span{
              &:nth-of-type(2){
                flex:1;
                min-width:0;
              }
            }
          }
          span{
            float: left;
          }
          .w80{
            width: 273.75px;
          }
          .maw80{
            max-width: 75%;
          }
        }
      }
    }
    .tab-no-con{
        background: none;
        text-align: center;
        padding: 30px;
        p{
          padding-top: 15px;
        }
    }
  }
}
.dialogContent{
  font-size:14Px;margin:18.75px 56.25px 11.25px;
}
.boradbandInput{
  :deep(.van-field__body){
    width:94%;
    margin:0 auto;
    border:1px solid #ccc;
    padding:3.75px 7.5px;
    border-radius: 7.5px;
  }
}
.icon-phone-img{
  width:18px;
  height:18px;
  display: inline-block;
  transform: translate(0px, 5px);
  img{
    width:100%;
  }
}
</style>
