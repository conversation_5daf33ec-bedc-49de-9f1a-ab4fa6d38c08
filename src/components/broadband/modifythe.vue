<template>
  <!-- 修改预约单 -->
  <van-dialog
    v-model="isShow"
    title="修改预约单"
    v-bind="modifytheArray.dialogOption"
    :confirm-button-text="'提交审核'"
    @cancel="handleCancel"
    @confirm="handleConfirm"
  >
    <Form :label-width="100" class="pa20">
      <Field
        v-model="modifytheArray.username"
        name="appointmentName"
        class="yuyue"
        label="预约人姓名"
        placeholder="请输入预约人姓名"
        :maxlength="32"
        :error-message="modifytheArray.ErrorMessage1"
        required
        colon
        label-width="100px"
      />
      <Field
        v-model="modifytheArray.tel"
        name="appointmentMobile"
        label="预约手机号"
        type="tel"
        required
        placeholder="请填写预约手机号"
        :error-message="modifytheArray.ErrorMessage2"
        colon
        label-width="100px"
      />
      <Field
        readonly
        clickable
        name="areaText"
        :value="modifytheArray.areaText"
        label="预约地址:"
        required
        :error-message="modifytheArray.ErrorMessage3"
        placeholder="点击选择省市区"
        @click.prevent="onClickArea"
      />
      <Field
        v-model="modifytheArray.appointmentAddress"
        name="appointmentAddress"
        type="text"
        label=" "
        placeholder="请填写安装地址"
        :error-message="modifytheArray.ErrorMessage4"
        :maxlength="60"
        label-width="100px"
      />
      <div v-if="modifytheArray.tplContent" v-myhtml="modifytheArray.tplContent" class="tplContent rich"></div>
    </Form>

    <popupArea
      ref="area"
      :province="modifytheArray.province"
      :city="modifytheArray.city"
      :title="'西城区'"
      :default-arr="modifytheArray.defaultArr"
      :is-open="isOpen"
      @close="close"
      @confirm="onAreaConfirm"
    />
  </van-dialog>
</template>

<script setup>
import Vue,{ref,reactive,defineProps,defineEmits,computed,watch, onMounted, getCurrentInstance} from "vue"
import { set, get } from 'lodash'
import popupArea from "@/components/popup-area.vue"
import shopApi from "@/api/shop.js"
import getBroadband from "@/api/broadband.js"
import {setScrollTop} from "@/utils/utils.js"
import {Toast,Dialog,Form,Field} from "vant"
Vue.use(Dialog)
const props = defineProps({
  modifyDialog:{
    type:Boolean,
    default:false
  },
  modifytheData:{
    type:Object,
    default:() =>{}
  }
})

const emit = defineEmits(['updateOrder','closeModifyDialog'])

const modifytheArray = reactive({
  pattern:/^1\d{10}$/,

  province:null,
  city:null,

  username: '',
  ErrorMessage1:'',

  tel: '',
  ErrorMessage2:'',

  areaText:null,
  ErrorMessage3:'',

  appointmentAddress:'',
  ErrorMessage4:'',

  tplContent: null,

  dialogOption: {
    // 是否在点击遮罩层后关闭弹窗
    closeOnClickOverlay: false,
    // 标题
    title: '添加用户',
    // 是否展示取消按钮
    showCancelButton: true,
    // 是否展示确认按钮
    showConfirmButton: true,
    width: '90%',
    beforeClose: beforeClose
  },

  addressInfo:{},

  areaArray:[],
  show:false,
  proxy:null
})
const isShow =  ref(false)
watch(
  ()=>props.modifyDialog,
  (value)=>{
    isShow.value = value
  },{immediate:true})
watch(()=>props.modifytheData,(value)=>{
  setInt(value)
})
onMounted(()=>{
  const url = new URL(location.href)
  modifytheArray.shopId = url.searchParams.get('shopId')
  let getcurrentVue = getCurrentInstance()
  modifytheArray.proxy = getcurrentVue ? getcurrentVue.proxy : null
  getShopInfo()
})
/* 获取店铺信息 */
function getShopInfo(){
  shopApi.queryShopInfo({shopId:modifytheArray.shopId}).then(res=>{
    if(!res.code){
      modifytheArray.province = {
        provinceId: res.data.province,
        provinceName: res.data.provinceName
      }
      modifytheArray.city = {
        cityId: res.data.city,
        cityName: res.data.cityName
      }
    }
  })
}
/* 初始化赋值 */
function setInt(value){
  let provinceName = get(value, 'province', '')
  let citeName = get(value, 'city', '')
  let districtName = get(value, 'district', '')
  let appointmentMobile = get(value, 'appointmentMobile', '')
  let appointmentName = get(value, 'appointmentName', '')
  let appointmentAddress = get(value, 'appointmentAddress', '')

  modifytheArray.username = appointmentName
  modifytheArray.tel = appointmentMobile
  modifytheArray.appointmentAddress = appointmentAddress
  modifytheArray.areaText = `${provinceName} ${citeName} ${districtName}`

  modifytheArray.areaArray = [provinceName, citeName, districtName]
}

function onAreaConfirm(data) {
  modifytheArray.addressInfo.provinceId = data.provinceId
  modifytheArray.addressInfo.province = data.provinceName
  modifytheArray.addressInfo.cityId = data.cityId
  modifytheArray.addressInfo.city = data.cityName
  modifytheArray.addressInfo.regionCode = data.regionCode
  modifytheArray.addressInfo.regionName = data.regionName
  modifytheArray.areaText = `${data.provinceName} ${data.cityName} ${data.regionName}`
}

function handleShow(val) {
  const { title, width, dialogOption } = modifytheArray.$attrs
  title && set(modifytheArray.dialogOption, 'title', title)
  width && set(modifytheArray.dialogOption, 'width', width)
  dialogOption && Object.assign(modifytheArray.dialogOption, dialogOption)
}

function handleConfirm() {
  if(!modifytheArray.username){
    modifytheArray.ErrorMessage1 = "请填写预约人姓名"
    return false
  }else{
    modifytheArray.ErrorMessage1 = ""
  }
  if(!modifytheArray.tel){
    modifytheArray.ErrorMessage2 = "请填写预约手机号"
    return false
  }else if(!modifytheArray.pattern.test(modifytheArray.tel)){
    modifytheArray.ErrorMessage2 = "请填写正确的手机号"
    return false
  }else{
    modifytheArray.ErrorMessage2=""
  }
  if(!modifytheArray.areaText){
    modifytheArray.ErrorMessage3 = "点击选择省市区"
    return false
  }else{
    modifytheArray.ErrorMessage3 = ""
  }
  if(!modifytheArray.appointmentAddress){
    modifytheArray.ErrorMessage4 = "请填写安装地址"
    return false
  }else{
    modifytheArray.ErrorMessage4 = ""
  }
  modifytheArray.appointmentAddress=modifytheArray.appointmentAddress.replace(/[\n\s]+/g,'')

  let district = modifytheArray.areaText.split(' ')[2]

  getBroadband.updateBAOrder({
    appointmentAddress:modifytheArray.appointmentAddress,
    appointmentMobile:modifytheArray.tel,
    appointmentName:modifytheArray.username,
    orderId:props.modifytheData.orderId,
    district
  }).then(res => {
    if(res.code){
      Toast(res.message)
      return
    }

    emit('updateOrder')

    emit('closeModifyDialog')
  })
}

// 取消按钮
function handleCancel() {
  emit('closeModifyDialog')
  setInt(props.modifytheData)
}

function beforeClose(action, done) {
  if (action === 'confirm') {
    done(false)
  } else {
    done()
  }
}
function onClickArea(){
  setScrollTop()
  open()
}
const isOpen = ref(false)
function open() {
  isOpen.value = true
}
function close(){
  isOpen.value = false
}
</script>

<style lang="scss" scoped>
.yuyue{
    margin-top: 10px;
}
.pa20{
    padding:10px;
}
</style>