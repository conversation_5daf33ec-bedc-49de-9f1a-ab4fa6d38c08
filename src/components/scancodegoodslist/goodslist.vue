<template>
  <div class="goods">
    <List
      v-if="goodslist && goodslist.length > 0"
      v-bind="data.loadingnew"
      :finished="finished"
      finished-text="没有更多了"
      class="goods-list cp-list"
      @load="onLoad"
    >
      <Cell
        v-for="(item, key) in goodslist"
        :key="key"
        class="li"
        :style="{ background: props.bgColor ? props.bgColor : '#f1f1f1' }"
      >
        <a href="javascript:void(0)">
          <img :src="item.imgUrl" />
          <div class="goodscontent">
            <p class="title">
              {{ item.goodsName }}
            </p>
            <p class="subtitle">
              <span>{{ item.subTitle }}</span>
            </p>
            <p class="price">
              <em>优惠价：</em>
              {{ (item.price / 100).toFixed(2) }}元起
            </p>
          </div>
          <div
            class="buyBtn"
            @click="
              insertCodeGoods(
                key + 1,
                item.goodsId,
                item.goodsSource,
                item.goodsLink
              )
            "
          >
            <Button
              round
              block
              type="info"
              size="mini"
              color="linear-gradient(to right, #5fc6ff, #8273fe)"
            >
              立即购买
            </Button>
          </div>
        </a>
        <span
          v-if="props.showShare"
          class="share-goods"
          @click="openShare(item)"
        ></span>
      </Cell>
    </List>
    <div v-else class="tab-no-goods">
      暂无活动商品
    </div>
    <Share v-if="data.pageInfo && data.pageInfo.pageInfo" :shopshare="props.shopshare" />
  </div>
</template>
<script setup>
import EventBus from "@/api/eventbus"
import insertCode from "@/utils/insertCode"
import UA from "@/utils/ua"
import Share from "@/components/index/share"
import Vue , {reactive,ref,defineProps, computed,getCurrentInstance, onMounted} from "vue"
import { List, Cell, Button } from "vant"
import { mapGetters } from "vuex"

const props = defineProps({
  items: {
    type: Array,
    default: () => {
      return []
    }
  },
  componentCode: {
    type: String,
    default: null
  },
  tplId: {
    type: String,
    default: null
  },
  pageNum: {
    type: Number,
    default: 1
  },
  showShare: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  finished: {
    type: Boolean,
    default: false
  },
  onload: {
    type: Function,
    default: fn => {
      return fn
    }
  },
  bgColor: {
    type: String,
    default: ""
  },
  shopshare: {
    type: Object,
    default: (data) => {
      return data || {}
    },
  }
})

const data = reactive({
  loadingnew:false
})

const goodslist = computed(()=>{
  let goodslist = []
  if (props.items && props.items.length > 0) {
    props.items.forEach(element => {
      if (element.goodsSource == 2) {
        //异业都显示
        goodslist.push(element)
        return
      }
      //1、o2oGoodsStatus 2 下线 3上线 运营位下线，店铺首页不展示商品
      //2、goodsStatus 51是上架 52商品下架，这两种状态店铺首页应该展示商品，可以进入详情页
      if (
        element.o2oGoodsStatus == 2 ||
        (element.goodsStatus != 51 && element.goodsStatus != 52)
      ) {
        return
      }

      goodslist.push(element)
    })
  }
  return goodslist
})

const openShare = (item) => {
  let share = {
    ...item,
    title: item.goodsName,
    url: item.goodsLink,
    desc: item.subTitle || item.goodsLink,
    imgUrl: item.imgUrl,
    price: item.price
  }
  EventBus.$emit("openShareDialog", "goods", share)
  if (UA.isWechatWork) {
    EventBus.$emit("share")
  }
}

const insertCodeGoods = (key, goodsId, source, url) => {
  let sourceName = source===2 ? "yysp" : "zysp"
  let dcs_id = "yd_smgj_"+data.pageInfo.pageInfo.shopId+"_"+sourceName+"_" + goodsId + "_buy"
  insertCode(dcs_id, url)
}

const onLoad = () => {
  props.onload()
}

onMounted(()=>{
  data.loadingnew = props.loading
  const getVueInstance = getCurrentInstance()
  const proxy = getVueInstance ? getVueInstance.proxy : null
  data.pageInfo = proxy ? proxy.$store.getters.pageInfo : null
})
</script>

<style lang="scss" scoped>
  @mixin ellipsis($clamp: 1) {
    overflow: hidden;
    text-overflow: ellipsis;
    @if ($clamp==1) {
      white-space: nowrap;
    } @else {
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: $clamp;
      -webkit-box-orient: vertical;
      // align-self: center;
      width: 100%;
    }
  }
  .goods-tt {
    height: 45px;
    line-height: 45px;
    font-size: 16px;
    color: #333333;
    padding-left: 39px;
    margin-bottom: 1px;
  }
  .goods-list {
    display: flex;
    // flex-direction: row;
    flex-flow: row wrap;
    .li {
      list-style: none;
      position: relative;
      font-size: 13px;
      height: 124px;
      line-height: 20px;
      background: #fff;
      border-bottom: 1px dashed rgba($color: #000000, $alpha: 0.33);
      padding: 17px 0px;
      width: 352.5px;
      margin: 0 auto;
      img {
        width: 90px;
        height: 90px;
        float: left;
        margin-right: 10px;
      }
      .goodscontent {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;
      }
      p.title {
        font-size: 13px;
        color: #333333;
        line-height: 24px;
        font-weight: bold;
        @include ellipsis($clamp: 1);
      }
      p.subtitle {
        font-size: 12px;
        color: #666666;
        height: 42px;
        line-height: 24px;
        display: flex;
        span {
          line-height: 20px;
          @include ellipsis($clamp: 2);
        }
      }
      p.price {
        font-size: 14px;
        color: #e26666;
        // height: 24px;
        line-height: 24px;
        @include ellipsis($clamp: 1);
        width: 154px;
        font-weight: bold;
      }
    }
    .share-goods {
      position: absolute;
      left: 60px;
      bottom: 0px;
      display: block;
      width: 30px;
      height: 30px;
      background: url(~@/assets/scancode/share.png) center top no-repeat;
      background-size: 100% auto;
      z-index: 2;
    }
  }
  .tab-no-goods {
    text-align: center;
    font-size: 13px;
    color: #666;
    margin: 30px auto;
  }
  .goods-list{
    :deep(.van-list__finished-text) {
      width: 100%;
    }
  }
  .buyBtn {
    position: absolute;
    right: 0;
    bottom: 2px;
    .van-button {
      width: 88px;
      height: 28px;
      line-height: 28px;
      font-size: 12px;
    }
  }
</style>
