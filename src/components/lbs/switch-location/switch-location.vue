<template>
  <div class="switch-location">
    <!-- 选择地区 -->
    <div class="aside" :style="{color:props.color?props.color:'#333'}" @click="open">
      <span>{{ filterLocStr }}</span>
      <Icon size="16px" name="arrow-down" />
    </div>

    <!-- 省选择 -->
    <popup-area
      ref="area"
      :default-arr="locationData.province && locationData.province.provinceName?[{name:locationData.province.provinceName,id:locationData.province.provinceId }]:[]"
      :is-choose-whole-province="true"
      :is-list-whoole="true"
      :is-prov-city="true"
      :is-open="isOpen"
      @confirm="confirm"
      @close="close"
    />
  </div>
</template>

<script setup>
import {reactive,defineProps,onMounted,computed,watch,ref,defineEmits,getCurrentInstance} from "vue"
import { Icon } from "vant"
import popupArea from "@/components/popup-area"
import { lbsProvinceQuery } from "@/api/lbs"
import { PROVINCE_LIST, PROVINCE_MAP } from '@/api/area'
console.log( PROVINCE_LIST,PROVINCE_MAP)
const props = defineProps({
  userLocation:{
    type:Object,
    default:()=>{
      return {}
    }
  },
  color:{
    type:String,
    default:null
  }
})

const emit = defineEmits(['confirm'])
const locationData = reactive({
  province:{},
  city:{},
  region:{}
})

const filterLocStr = computed(()=>{
  /* 直辖市id列表 */
  const proList = ['100', '220', '210', '230']

  if(locationData.region && locationData.region.regionName){
    return `${locationData.province.provinceName} ${proList.includes(locationData.province.provinceId)?'':locationData.city.cityName} ${locationData.region.regionName}`
  }

  if(locationData.city.cityName && locationData.city.cityId){
    return `${locationData.province.provinceName} ${proList.includes(locationData.province.provinceId)?'':locationData.city.cityName}`
  }

  if(locationData.province.provinceName){
    return `${locationData.province.provinceName}`
  }

  return '请选择'
})

watch(()=>props.userLocation,(value)=>{
  init()
})

function confirm(res) {
  locationData.province = {
    provinceName:res.provinceName,
    provinceId:res.provinceId
  }
  locationData.city = {
    cityName:res.cityName,
    cityId:res.cityId
  }
  locationData.region = {
    regionName:res.regionName,
    regionCode:res.regionCode
  }

  emit('confirm', res)
}
const isOpen = ref(false)
function open() {
  isOpen.value = true
}
function close(){
  isOpen.value = false
}

async function init(){
  if(!props.userLocation){
    return
  }

  const latitude = props.userLocation.latitude
  const longitude = props.userLocation.longitude

  if (!(latitude && longitude)) {
    return
  }

  const res = await lbsProvinceQuery({
    latitude,
    longitude,
  })

  if(!res.data){
    return
  }
  res.data.provinceName = PROVINCE_MAP[res.data.provinceId]
  sessionStorage.setItem("userLocationToStore", JSON.stringify(res.data))

  const {provinceName, provinceId} = res.data

  locationData.province = {
    provinceId,
    provinceName
  }

  emit('confirm', locationData.province)
}
</script>

<style lang="scss" scoped>
.switch-location {
  margin-right: 10px;
  .aside {
    display: flex;
    align-items: center;
    span {
      font-size: 14px;
      margin-right: 6px;
      position: relative;
    }
  }
}
</style>
