<template>
  <div>
    <Tab v-for="item in props.floor" :key="item" :title="item">
    </Tab>
    <div class="broadbandcon">
      <List
        v-if="props.items && props.items.length"
        v-model="letterData.myLoading"
        :finished="props.finished"
        :finished-text="props.nodataMessage"
        class="broadband-list ul"
        @load="onLoad"
      >
        <Cell v-for="it in props.items" :key="it.orderId" class="li">
          <div class="bd-list-content">
            <div class="bd-con">
              <p class="bd-hs-ys clearfix" @click="intoDetail(it.orderId)">
                <span class="bd-let">意向单号：</span>
                <span class="w80 breakAll">{{ it.orderId }}</span>
              </p>
              <p class="bd-hs-ys clearfix" @click="intoDetail(it.orderId)">
                <span class="bd-let">意向活动：</span>
                <span class="w80 breakAll">
                  {{ it.productAttr }}
                </span>
              </p>
              <p class="bd-hs-ys clearfix" @click="intoDetail(it.orderId)">
                <span class="bd-let">甩单类型：</span>
                <span class="w80 breakAll">
                  <!-- 1：宽带甩单，2：终端甩单 -->
                  {{ it.coopType == COOPTYPES.BROADBAND ? "宽带甩单" : "终端甩单" }}
                </span>
              </p>
              <p class="bd-hs-ys clearfix" @click="intoDetail(it.orderId)">
                <span class="bd-let">意向单状态：</span>
                <span class="w80 breakAll">
                  {{
                    shopstatus.indexOf(it.status) !== -1
                      ? statusText[it.status]
                      : ''
                  }}
                </span>
              </p>
              <p class="bd-hs-ys clearfix" @click="intoDetail(it.orderId)">
                <span class="bd-let">意向人姓名：</span><span>{{ it.customerName }}</span>
              </p>
              <p class="bd-hs-ys label-phone">
                <span class="bd-let">电话号码：</span>
                <span class="value-phone">
                  <template v-if="it.customerPhone">
                    {{ getpassphone(it.customerPhone) }}
                  </template>
                  <a
                    v-if="shopstatus.indexOf(it.status) !== -1 && statusText[it.status]=='已接单'"
                    :href="'tel:'+it.customerPhone"
                    class="icon-phone-img"
                  >
                    <img src="~@/assets/index_normal/phone.png" alt="" />
                  </a>
                </span>
              </p>
              <p class="bd-hs-ys hs mx" @click="intoDetail(it.orderId)">
                <span class="bd-let">详细地址：</span>
                <span class="w80 breakAll">{{
                  it.province +
                    ' ' +
                    it.city +
                    ' ' +
                    it.county +
                    ' ' +
                    it.address
                }}</span>
              </p>
              <p class="bd-hs-ys hs mx" @click="intoDetail(it.orderId)">
                <span class="bd-let">下单时间：</span>
                <span class="w80 breakAll">{{ it.createTime }} </span>
              </p>
            </div>
            <a
              v-if="it.status == 'RO'"
              class="ljclbt"
              @click="openChangeOrderDialog(it)"
            >
              立即处理
            </a>
          </div>
        </Cell>
      </List>
      <ul v-else-if="props.nodataMessage" class="broadband-tab-no ul">
        <li class="tab-no-con li">
          <div>
            <img src="~@/assets/index_img/ice_tab_no.png" />
            <p>
              {{ props.nodataMessage }}
            </p>
          </div>
        </li>
      </ul>
    </div>
    <!-- 弹框 -->
    <van-dialog
      v-model="letterData.showChangeOrderStatus"
      title="意向单处理"
      show-cancel-button
      confirm-button-color="#5fade8"
      :before-close="changeOrderStatus"
    >
      <RadioGroup
        v-model="letterData.sendData1.status"
        direction="horizontal"
        class="dialogContent"
      >
        <Radio name="SS">
          甩单成功
        </Radio>
        <Radio name="CL">
          甩单取消
        </Radio>
      </RadioGroup>
      <!--currentOrderCooptype 1：宽带甩单，2：终端甩单 -->
      <Field
        v-if="letterData.sendData1.status == 'SS' && letterData.currentOrderCooptype == COOPTYPES.TERMINAL"
        v-model="letterData.sendData1.imei"
        class="boradbandInput"
        type="text"
        placeholder="请输入设备的终端串号"
        label-width="100px"
        clearable
      />
      <p v-if="letterData.sendData1.status == 'SS' && letterData.currentOrderCooptype==COOPTYPES.TERMINAL" class="tips">
        限20个字符长度
      </p>
      <Field
        v-if="letterData.sendData1.status == 'CL' && letterData.currentOrderCooptype==COOPTYPES.TERMINAL"
        v-model="letterData.sendData1.cannelReason"
        class="boradbandInput"
        type="textarea"
        :autosize="{ maxHeight: 100 }"
        placeholder="请输入取消原因"
        label-width="100px"
        :maxlength="256"
        clearable
      />
      <p v-if="letterData.sendData1.status == 'CL' && letterData.currentOrderCooptype==COOPTYPES.TERMINAL" class="tips">
        限30个字符长度
      </p>
    </van-dialog>
  </div>
</template>
<script setup>
import Vue,{reactive,onMounted,computed,watch,defineProps,getCurrentInstance} from 'vue'
import { RadioGroup, Radio, Field, Toast, List, Cell ,Tab, Dialog } from 'vant'
import intentOrderApi from '@/api/letterofintent'
import { getPassPhone } from "@/utils/utils"
Vue.use(Dialog)
const props = defineProps({
  items: {
    type: Array,
    default: () => {
      return []
    },
  },
  floor: {
    type: Array,
    default: () => {
      return []
    },
  },
  tplId: {
    type: String,
    default: null,
  },
  ruleid: {
    type: [String, Number],
    default: null,
  },
  refresh: {
    type: Function,
    default: (fn) => {
      return fn
    },
  },
  nodataMessage: {
    type: String,
    default: '暂无意向单信息',
  },
  loading: {
    type: Boolean,
    default: false,
  },
  finished: {
    type: Boolean,
    default: false,
  },
  onload: {
    type: Function,
    default: (fn) => {
      return fn
    },
  }
})

const statusText = {
  RO: '已接单',
  SS: '已完成',
  CL: '已取消',
  PO: '待接单',
}
const  COOPTYPES = {
  BROADBAND:"1",
  TERMINAL:"2"
}

// 商户看到的状态
const  shopstatus =  ['RO', 'SS', 'CL', 'PO']

const letterData = reactive({
  showChangeOrderStatus: false,
  sendData1: {
    status: 'SS',
    imei: '',
    cannelReason: '',
  },
  myLoading: false,
  currentOrderId:null,
  currentOrderCooptype:null
})
const myrule = computed(()=>{
  return letterData.ruleid
})
watch(()=>letterData.showChangeOrderStatus,(val)=>{
  if(!val){
    letterData.sendData1={
      status: 'SS',
      imei: '',
      cannelReason: '',
    }
  }
})
watch(()=>props.loading,(val)=>{
  if(!val){
    letterData.myLoading = val
  }
})
function openChangeOrderDialog(orderItem) {
  letterData.currentOrderId = orderItem.orderId
  letterData.currentOrderCooptype = orderItem.coopType
  letterData.showChangeOrderStatus = true
}
function getpassphone(mobile){
  return getPassPhone(mobile)
}
function changeOrderStatus(action, done) {
  if (action == 'cancel') {
    done()
    return false
  }
  let emojiReg = /[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g
  if (letterData.sendData1.status == 'SS') {
    //预约成功
    if (letterData.sendData1.imei && letterData.sendData1.imei.length > 20) {
      Toast('限20个字符长度')
      done(false)
      return false
    }
    if (letterData.sendData1.imei && emojiReg.test(letterData.sendData1.imei)) {
      Toast('输入不合规')
      done(false)
      return false
    }
    delete letterData.sendData1.cannelReason
  }
  if (letterData.sendData1.status == 'CL') {
    //预约失败
    let myreason = letterData.sendData1.cannelReason
      ? letterData.sendData1.cannelReason.replace(/[\n|\s]+/g, '')
      : null
    if (myreason && myreason.length > 30) {
      Toast('限30个字符长度')
      done(false)
      return false
    }
    if (myreason && emojiReg.test(myreason)) {
      Toast('输入不合规')
      done(false)
      return false
    }
    delete letterData.sendData1.imei
  }
  letterData.sendData1.orderId = letterData.currentOrderId
  intentOrderApi.updateOrderState(letterData.sendData1).then((res) => {
    if (res.code) {
      Toast(res.message)
      done(false)
    } else {
      //刷新页面
      done()
      props.refresh()
    }
  })
}
function intoDetail(orderId) {
  proxy.$router.push({
    path: '/letter/details.html',
    query: { orderId: orderId },
  })
}
function onLoad() {
  props.onload()
}
let proxy = null
onMounted(()=>{
  const url = new URL(location.href)
  letterData.shopId = url.searchParams.get('shopId')
  const getCurrentVue = getCurrentInstance()
  proxy = getCurrentVue ? getCurrentVue.proxy : null
})
</script>
<style>
body {
  background: #f9fafc;
}
.van-tab--active {
  color: #a7bfd1 !important;
}
.van-tabs__line {
  background-color: #5fade8 !important;
}
</style>
<style lang="scss" scoped>
.broadbandcon {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding-top: 7.5px;
  color: #333;
  background: #f9fafc;
  .broadband-list :deep(.van-cell) {
    padding: 0;
  }
  .ul {
    .li {
      background: #fff;
      margin-bottom: 7.5px;
      font-size: 13.5px;
      .clearfix:after {
        content: '.';
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
      .breakAll {
        white-space: normal;
        word-break: break-all;
        word-wrap: break-word;
      }
      .fl-rid {
        float: right;
      }
      h2 {
        position: relative;
        border-bottom: 1px solid #f9fafc;
        padding: 6px 13.5px 4.5px;
        display: flex;
        align-items: center;
        p {
          display: inline-block;
          line-height: 33.75px;
          flex: 1;
          min-width: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        a {
          position: static;
          top: 8.25px;
          right: 11.25px;
          border: 1px solid #0085d0;
          border-radius: 6px;
          width: 75px;
          height: 26.25px;
          line-height: 26.25px;
          text-align: center;
          color: #0085d0;
          margin-left: 10px;
        }
      }
      .bd-con {
        padding: 7.5px 13.5px;
        line-height: 26.25px;
        div {
          color: #666;
          p {
            display: inline-block;
            min-width: 40%;
            max-width: 100%;
          }
        }
        .hs {
          color: #999;
        }
        .ls {
          color: #00ad29;
        }
        .red {
          color: #ed2668;
        }
        .yll {
          color: #e97a00;
        }
        .bd-hs-ys {
          margin-top: 10px;
          display: flex;
          &.mx {
            display: flex;
            span {
              &:nth-of-type(2) {
                flex: 1;
                min-width: 0;
              }
            }
          }
          span {
            float: left;
            &:nth-of-type(2){
              flex:1;
              min-width:0;
            }
          }
          .bd-let {
            width: 90px;
            text-align: right;
          }
          .w80 {
            flex: 1;
          }
          .maw80 {
            max-width: 75%;
          }
        }
      }
    }
    .tab-no-con {
      background: none;
      text-align: center;
      padding: 30px;
      p {
        padding-top: 15px;
      }
    }
  }
}
.clearfix {
  display: flex;
}
.dialogContent {
  font-size: 14px;
  margin: 18.75px 56.25px 11.25px;
}
.boradbandInput {
  :deep(.van-field__body) {
    width: 94%;
    margin: 0 auto;
    border: 1px solid #ccc;
    padding: 3.75px 7.5px;
    border-radius: 7.5px;
  }
}
.ljclbt {
  width: 75px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #0085d0;
  margin-left: 10px;
  float: right;
  position: absolute;
  right: 29px;
  bottom: 7px;
}
.tips{
  padding-left:30px;
  color:red;
  font-size: 14px;
  margin-bottom: 5px;;
}
.van-cell::after{
  border-bottom: none!important;
}
.label-phone{
  .value-phone{
    display: flex;
  }
}
.icon-phone-img{
  width:18px;
  height:18px;
  display: block;
  transform: translate(0px, 5px);
  img{
    width:100%;
  }
}
</style>
