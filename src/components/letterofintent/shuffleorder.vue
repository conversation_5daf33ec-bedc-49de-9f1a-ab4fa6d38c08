<template>
  <div>
    <!-- 弹框 -->
    <Dialog
      v-model="showChangeOrderStatus"
      title="意向单处理"
      show-cancel-button
      confirm-button-color="#5fade8"
      :before-close="changeOrderStatus"
    >
      <van-radio-group v-model="sendData1.status" direction="horizontal" class="dialogContent">
        <van-radio name="SS">
          甩单成功
        </van-radio>
        <van-radio name="CL">
          甩单失败
        </van-radio>
      </van-radio-group>
      <van-field
        v-if="sendData1.status=='SS'"
        v-model="sendData1.imei"
        class="boradbandInput"
        type="text"
        placeholder="请输入设备的终端串号"
        label-width="100px"
        clearable
      />
      <van-field
        v-if="sendData1.status=='CL'"
        v-model="sendData1.cannelReason"
        class="boradbandInput"
        type="textarea"
        :autosize="{maxHeight: 100}"
        placeholder="请输入取消原因"
        label-width="100px"
        :maxlength="256"
        clearable
      />
    </Dialog>
  </div>
</template>
<script setup>
import { reactive } from '@vue/composition-api'
import intentOrderApi from '@/api/letterofintent'
import {Dialog,Toast} from 'vant'
let data = reactive({
  sendData1: {
    status: 'SS',
    imei: '',
    cannelReason: '',
  },
  showChangeOrderStatus:false
})

let openChangeOrderDialog = () => {
  data.showChangeOrderStatus = true
}
let changeOrderStatus = (action, done) => {
  if (action == 'cancel') {
    done()
    return false
  }
  if (data.sendData1.status == 'SS') {
    //预约成功
    if (data.sendData1.imei && data.sendData1.imei.length>20) {
      Toast('限20个字符长度')
      done(false)
      return false
    }
    delete data.sendData1.cannelReason
  }
  if (data.sendData1.status == 'CL') {
    //预约失败
    let myreason = data.sendData1.cannelReason
      ? data.sendData1.cannelReason.replace(/[\n|\s]+/g, '')
      : null
    if (myreason && myreason.length>30) {
      Toast('限30个字符长度')
      done(false)
      return false
    }
    delete data.sendData1.imei
  }
  data.sendData1.orderId = data.orderDetail.orderId
  intentOrderApi.updateOrderState(data.sendData1).then((res) => {
    if (res.code) {
      Toast(res.message)
      done(false)
    } else {
      //刷新页面
      done()
    }
  })
}
</script>

<style lang="scss" scoped>

</style>
