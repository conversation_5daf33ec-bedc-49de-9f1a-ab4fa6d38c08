<!-- eslint-disable vue/no-mutating-props -->

<!-- 传入iconlist，传入登录信息，不自带登录信息 -->
<template>
  <div class="footer">
    <Tabbar v-model="myFooter.iconActive" @change="onChange">
      <TabbarItem
        v-for="(item, index) in myListArray"
        :key="index"
        :class="[{ displayNo: !item.showLink }, item.class]"
      >
        <template #icon="">
          <img :src="item.isactive ? item.active : item.inactive" />
          <p>{{ item.title }}</p>
        </template>
      </TabbarItem>
    </Tabbar>
    <div v-if="myFooter.onlinePop" class="onlinePopout">
      <div class="onlinepop_con">
        <a href="javascript:void(0);" @click="onlinePopOff"></a>
        <img src="~@/assets/onlinewatchshop/onlinepop_ice1.png" />
        <img src="~@/assets/onlinewatchshop/onlinepop_ice2.png" />
        <img src="~@/assets/onlinewatchshop/onlinepop_ice3.png" />
      </div>
    </div>
  </div>
</template>
<script setup name="commonfooter">
import { Tabbar, TabbarItem } from "vant"
import "vant/lib/index.css"
import Vue,{reactive,getCurrentInstance, computed,ref, onMounted, watch} from "vue"
import getService from "@/api/service"
import getLiveInfo from "@/api/shop"
import getOnline from "@/api/online"
import { getUrl, getCookie, delCookie, editCookie } from "@/utils/utils"
import insertCode from "@/utils/insertCode"
import UA from "@/utils/ua"
import { toMy, toHome } from "@/utils/wxMiniProgram"
import UnifiedAuthenticationMigration from "@/model/unifiedAuthenticationMigration"
// Vue.use(Tabbar).use(TabbarItem)
const props = defineProps({
  list: {
    type: Object,
    default: data => {
      return data
    }
  },
  isactive: {
    type: String,
    default: data => {
      return data
    }
  },
  user: {
    type: Object,
    default: data => {
      return data
    }
  },
  tplId: {
    type: Number,
    default: data => {
      return data
    }
  }
})

const myFooter = reactive({
  urlInfo: null,
  kflink: "",
  onlinePop: false,
  iconActive: null,
  list:[]
})
myFooter.list = props.list
watch(props.list,(val)=>{
  if(val){
    myFooter.list = val
  }
},{deep:true,immediate:true})
const currentVue = getCurrentInstance()
const proxy = currentVue ? currentVue.proxy : null
let pageInfo = {}
if (proxy) {
  pageInfo = proxy.$store.getters.pageInfo
}
function init() {
  //页面渲染之前调用
  const url = new URL(location.href)
  let user = props.user || {}
  const shopId =
        url.searchParams.get("shopId") ||
        url.searchParams.get("shop_id") ||
        user.shopId
  const actId = url.searchParams.get("actId")
  const preview = url.searchParams.get("preview")
  myFooter.urlInfo = {
    shopId,
    actId,
    preview
  }
  if ((!shopId) && proxy) {
    proxy.$router.push({
      name: "nearby"
    })
    return false
  }
  if (myFooter.list && myFooter.list.live) {
    getLiveInfoMsg(shopId)
  }
  if (props.tplId == 3 && props.user == null) {
    delCookie("online_pop")
  }
  if (myFooter.list && myFooter.list.onlineshop) {
    getOnlinech(shopId)
  }
}
const currentIcon = ref(null)
const myListArray = computed(() => {
  let array = []
  Object.keys(myFooter.list).forEach((element, index) => {
    if (element == props.isactive) {
      myFooter.iconActive = index
      currentIcon.value = index
    }
    array.push(myFooter.list[element])
  })
  return array
})
function onlinePopOff() {
  myFooter.onlinePop = false
}
// 是否展示在线看店
function getOnlinech(shopId) {
  getOnline
    .getOnlineCheck({
      shopId: shopId
    })
    .then(res => {
      if (res.code == 0 && res.data) {
        let userInfoOnl = Object.assign({}, pageInfo.pageInfo)
        userInfoOnl.shopOnlineStatus = res.data.status
        if (proxy) {
          proxy.$store.commit("SET_PAGEINFO", userInfoOnl)
        }

        if (res.data.status == 1) {
          // eslint-disable-next-line vue/no-mutating-props
          myFooter.list.onlineshop.links =
                        "/yundian/onlinewatchshop/index.html?shopId=" +
                        myFooter.urlInfo.shopId

          if (props.tplId == 3 || myFooter.list.onlineshop.isactive) {
            myFooter.list.onlineshop.showLink = true
          } else {
            myFooter.list.onlineshop.showLink = false
          }
          if (props.tplId == 3 && !getCookie("online_pop")) {
            myFooter.onlinePop = true
            if (props.user != null) {
              editCookie("online_pop", "1", "30d")
            }
            let timer = setInterval(() => {
              clearInterval(timer)
              onlinePopOff()
            }, 3000)
          }
        }
      } else {
        myFooter.list.onlineshop.showLink = false
      }
    })
}
// 是否展示直播
function getLiveInfoMsg(shopId) {
  getLiveInfo
    .getLiveInfo({
      shopId: shopId
    })
    .then(async res => {
      if (res.code == 0 && res.data.url) {
        myFooter.list.live.links = res.data.url
        /* 在微信小程序下不显示客服 */
        const isWXMapp = await UA.isWeChatMiniApp()
        if (isWXMapp) {
          myFooter.list.live.showLink = false
        } else {
          myFooter.list.live.showLink = true
        }
      } else {
        myFooter.list.live.showLink = false
      }
    })
}
// 是否展示客服
function getServiceMsg(id) {
  getService
    .getCustomerservice({
      id: id
    })
    .then(async res => {
      if (res.code == 0 && res.data.customerService == 1) {
        myFooter.list.service.links =
                    "https://touch.10086.cn/ad/yjyd/livechat-touch-client/pub-page/liveChatTouchHome.html?tenantId=S" +
                    id +
                    "&code=BS&channelType=3012&commodityId=&skuId=&proSource=0"

        /* 在微信小程序下不显示客服 */
        const isWXMapp = await UA.isWeChatMiniApp()
        if (isWXMapp) {
          myFooter.list.service.showLink = false
        } else {
          myFooter.list.service.showLink = true
        }
      } else {
        myFooter.list.service.showLink = false
      }
    })
}
function linksShop() {
  //思特奇个人中心：'https://touch.10086.cn/i/mobile/home.html'
  //新店个人中心："/yundian/my/index.html?shopId="+myFooter.urlInfo.shopId
  if (myFooter.list.shopindex) {
    myFooter.list.shopindex.links =
            "/yundian/index.html?shopId=" + myFooter.urlInfo.shopId
  }
  if (myFooter.list.manage) {
    myFooter.list.manage.links = "/yundian/ydmanage/index.html?shopId=" + myFooter.urlInfo.shopId
  }
  if (myFooter.list.my) {
    myFooter.list.my.links =
            "/yundian/my/index.html?shopId=" + myFooter.urlInfo.shopId
  }

}
// 切换tabbar
async function onChange(index) {
  linksShop()
  let iconList = [],
    dcslist = []
  for (let key in myFooter.list) {
    iconList.push(myFooter.list[key])
    dcslist.push(key)
  }

  // 如果当前在小程序环境下,首页与我的页面要跳转小程序原生页面
  let iconlistKey = iconList[index].key
  if (iconlistKey == "shopindex") {
    // 跳转微信首页
    toHome()
  }
  if (iconlistKey == "my") {
    // 跳转微信我的页
    toMy()
  }

  if (dcslist[index] == "live") {
    insertCode(
      "yd_index_" + pageInfo.shopId + "_bottombar_zhibo"
    )
  }
  if (dcslist[index] == "service") {
    insertCode(
      "yd_index_" + pageInfo.shopId + "_bottombar_kefu"
    )
  }
  if (dcslist[index] == "onlineshop") {
    insertCode(
      "yd_index_" + pageInfo.shopId + "_storephotosbar"
    )
  }
  myFooter.iconActive = currentIcon.value
  // 处理如果链接中有sourceid, 则获取大网token到链接里
  if (iconList[index].links) {
    const urlFilter = await UnifiedAuthenticationMigration.init(iconList[index].links)
    window.location.href = urlFilter
    return
  }

  window.location.href = iconList[index].links
}
onMounted(()=>{
  init()
})

</script>
<style lang="scss" scoped>
.footer {
    background: #ffffff;
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 9;

    .displayNo {
        display: none;
    }

    .van-hairline--top-bottom {
        height: 68px;

        :deep(.van-tabbar-item__icon) {
            display: flex !important;
            flex-direction: column !important;
            justify-content: center !important;
            align-items: center !important;

            p {
                font-size: 12px !important;
                font-weight: 400 !important;
                text-align: center !important;
                margin-top: 7px;
            }
        }

        :deep(.van-tabbar-item--active) {
            color: #5099fe !important;
        }
    }

    .onlinePopout {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        z-index: 20;

        .onlinepop_con {
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            text-align: right;

            a {
                position: absolute;
                top: 123.75px;
                left: 50%;
                width: 150px;
                height: 41.25px;
                display: block;
                margin-left: -79.875px;
            }

            img {
                display: block;

                &:nth-of-type(1) {
                    width: 264px;
                    margin: 0 auto 75px;
                }

                &:nth-of-type(2) {
                    width: 163.875px;
                    display: inline-block;
                    margin-right: 37.5px;
                    margin-bottom: -15px;
                }

                &:nth-of-type(3) {
                    width: 99.375px;
                    margin-left: 90px;
                }
            }
        }
    }

    &.ladderClass {
        :deep(.van-tabbar-item--active) {
            color: #CEA04A !important;
        }
    }
}
</style>
