<template>
  <div class="buoy">
    <ul>
      <li
        v-for="(item, key) in buoylist"
        :key="key"
        @click="goLink(buoylist[key])"
      >
        <img v-if="buoylist[key].img" :src="buoylist[key].img" />
      </li>
    </ul>
  </div>
</template>
<script setup>
import {ref,reactive,defineProps,onMounted} from 'vue'
import shopAPI from "@/api/shop"
import { getImgUrl,getUrl,copy } from "@/utils/utils"
import insertCode from "@/utils/insertCode"
import { Toast } from 'vant'
const CMDATA = {
  SHOW:"1"
}
const props = defineProps({
  shopId:{
    type:Object,
    default:(val)=>{
      if(val){
        return val.shopId
      }else{
        return val
      }
    }
  },
  configure:{
    type:String,
    default: null
  }
})
const buoylist = reactive({
  wcsb:{}
})
onMounted(()=>{
  if(!props.configure){
    getCmData()
  }
})
function getCmData(){
  shopAPI
    .getCmData({
      shopId: props.shopId.shopId,
      cmCodeList:["wenchasuanbi"]
    })
    .then((res) => {
      if (res.code == 0 && res.data) {
        let wcsb = copy(buoylist.wcsb)
        if(res.data.wenchasuanbi){
          let cmdata = res.data.wenchasuanbi.data[1].tabData[0]
          wcsb.img = cmdata.subtitle !== "-1" ? getImgUrl(cmdata.subtitle,"material") : require("@/assets/index_img/wcsb.png")
          wcsb.show = cmdata.title === CMDATA.SHOW
          wcsb.link = getUrl("/hd/qd.html?url=/hd/wcsb1/index.html&key="+res.data.key+"&WT.ac_id=yd_h5_index_wcsb&shopId="+props.shopId.shopId)
          wcsb.dcsid = "yd_h5_index_wcsb"
        }else{
          wcsb.show = false
        }
        if(wcsb.show){
          buoylist.wcsb = copy(wcsb)
        }
      } else {
        Toast(res.message)
      }
    })
}
function goLink(item){
  if(item.link){
    insertCode(item.dcsid,item.link)
    // location.href = item.link
  }
}
</script>
<style
  lang="scss"
  scoped
>
.buoy {
  position: fixed;
  right: 20px;
  bottom: 73px;
  z-index:100000;
  img {
    width: 56px;
    height: 56px;
  }
}
</style>
