<template>
  <div class="loginDialog">
    <van-popup
      v-model="showLogin"
      :style="{ height: '65%',width:'100%'}"
      overlay-class="loginContent"
      :close-on-click-overlay="false"
      position="bottom"
      round
    >
      <h1 class="contentTitle">
        本机号码一键登录
      </h1>
      <div v-if="showCancle" class="cancle" @click="cancle()">
      </div>
      <div class="img">
        <img src="~@/assets/index_img/logo.png" />
      </div>
      <p class="phone">
        {{ phoneInfo ? phoneInfo.msisdnmask :'***' }}
      </p>
      <p class="tips">
        由中国移动提供认证服务
      </p>
      <p class="agreementBox">
        <van-checkbox v-model="checked">
          我已阅读并同意
        </van-checkbox>
        <a style="color:#017AFE" href="https://wap.cmpassport.com/resources/html/contract.html" target="_blank" rel="noopenner noreferrer">《中国移动认证服务协议》</a>

        <template v-if="isWXMapp">
          <span style="color:#017AFE" rel="noopenner noreferrer" @click="handlerKh">《客户服务协议》</span>
          和
          <span style="color:#017AFE" rel="noopenner noreferrer" @click="handlerYs">《隐私政策》</span>
        </template>
      </p>
      <van-button type="info" round size="small" @click="throttleGetLogin">
        同意授权
      </van-button>
      <p class="other-login" @click="gotoLogin">
        其他登录方式
      </p>
      <p class="tips2">
        <template v-if="isWXMapp">
          <img class="img1" src="https://img1.shop.10086.cn/fs/goods/fs_64e9c116e4b07358ad1cd8a2.png" alt="">
        </template>
        <template v-else>
          <van-icon name="warning-o" />
        </template>
        请勿连接热点登录，并确认该号码为您的本机号码
      </p>
    </van-popup>

    <AgreeMent :show-agreement="showAgreement" :type="AgreementType" />
  </div>
</template>
<script>
import loginApi   from "@/api/login.js"
import loginUtils from "@/utils/login/index.js"
import EventBus   from '@/api/eventbus.js'
import {getUserInfoByArtifact} from "@/utils/login/getUserInfo.js"
import Vue from 'vue'
import {Button ,Popup, Toast,Checkbox,Dialog,Icon} from "vant"
import AgreeMent from "./agreement.vue"
import { loadJs } from '@/utils/utils.js'
import UA from "@/utils/ua.js"
import { addUID } from "@/utils/login/tysso.js"
import { debounce } from 'lodash'

Vue.use(Button).use(Popup).use(Checkbox).use(Dialog).use(Icon)
export default {
  name:"LoginDialog",
  components:{
    AgreeMent
  },
  props:{
    islogin:{//是否登录
      type:Boolean,
      default:(data)=>{
        return data
      }
    },
    isForceLogin:{//是否强登
      type:Boolean,
      default:(data)=>{
        // console.log(data)
        return data
      }
    },
    isloginfn:{
      type:Function,
      default:(fn)=>{
        return fn
      }
    },
    isAutoLogin:{ //是否要开启4G免登
      type:Boolean,
      default:(data)=>{
        return data ? data : false
      }
    },
    showCancle:{
      type:Boolean,
      default:(data)=>{
        return data ? data : true
      }
    },
    customError: {
      type: Function,
      default: async() => {
        return true
      }
    },
    isSmsLogin :{
      type:Boolean,
      default:(data)=>{
        return data ? data : false
      }
    }
  },
  data(){
    return {
      phoneInfo:null,
      userinfo: null,
      showLogin:false,
      showAgreement:false,
      temporaryToken:null,
      AgreementType:null,
      preSign: "",
      // 是否同意协议
      checked:false,

      isWXMapp:false
    }
  },
  watch:{
    islogin:{
      handler:function(val){
        if(val!=null&&!val){ //为避免检查登录的时候就调用免登
          // this.autoLogin() //没有登录就去4G免登
        }else{
          this.showLogin = false
        }
      },
      immediate:true
    },
    isAutoLogin:{
      handler:function(val){
        if(val){ //为避免检查登录的时候就调用免登
          this.autoLogin() //开启4G免登
        }
      },
      immediate:true
    }
  },
  async mounted(){
    EventBus.$on('autologin' ,this.myautologin)
    this.isWXMapp = await UA.isWeChatMiniApp()
    // 4g免登步骤：
    // 1、通过调用java的获取签名接口4glogin/getSign，拿到签名
    // 2、根据拿到的签名调用sdk的方法getTokenInfo，获取手机号信息（能拿到说明可以使用4G，拿不到不能使用4G）
    // 3、用户点击一键登录，调用登录接口netShop/4glogin/auto4GLogin
  },
  methods: {
    handlerKh(){
      const wx = window.wx
      wx.miniProgram.navigateTo({
        url: '/subPackages/security/serviceAgreement',
      })
    },
    handlerYs(){
      const wx = window.wx
      wx.miniProgram.navigateTo({
        url: '/subPackages/security/serviceAgreement2',
      })
    },


    myautologin({h5ForceLogin,appForceLogin,smsLogin}){
      this.autoLogin(h5ForceLogin,appForceLogin,smsLogin)
    },
    async autoLogin(h5ForceLogin,appForceLogin,smsLogin){
      // this.autoLoginError(h5ForceLogin,appForceLogin)
      loadJs('https://www.cmpassport.com/NumberAbility/jssdk/jssdk.min.js', async(status)=> {

        // this.phoneInfo = {}
        // this.showLogin = true
        // return
        if(status !='ok') {
          return this.autoLoginError(h5ForceLogin,appForceLogin,smsLogin)
        }
        // 对网络类型做校验
        let connectionType="bluetooth,ethernet,none,mixed,other,unknown,wifi,wimax"//网络类型
        if (/windows | leadeon | wifi/gi.test(navigator.userAgent.toLowerCase())||(!!navigator.connection&&!!navigator.connection.type&&connectionType.indexOf(navigator.connection.type)>-1)) {
          return this.autoLoginError(h5ForceLogin,appForceLogin,smsLogin)
        }
        let appid = '300012364262'
        //授权弹框版本
        // this.preSign = window.ywAuth.getSign({
        //   appid: '000613',
        //   version: '1.0'
        // })
        //非授权弹框版本
        this.preSign = window.YDRZ.getSign(appid,'1.0')
        // 加载成功，进行后续操作
        let tokenRes = await loginApi.cmccGetSign(this.preSign)
        const sign = tokenRes.data ? tokenRes.data : null
        if(tokenRes.code == 0 ) {
          window.YDRZ.getTokenInfo ({
            data:{//请求的参数
              version:'1.0', //接口版本号 （必填）
              appId:appid, //应用Id （必填）
              sign,//RSA加密后的sign（必填）
              openType:'1',
              getMsisdnMask: '1',
              expandParams:'',//扩展参数  格式：参数名=值  多个时使用 | 分割（选填，联调环境只能模拟取号，联调时需填写phoneNum=188185*****  手机号可以随便填写，生产可不填）
              isTest:"1"//是否启用测试线地址（传0时为启用不为0或者传空字符时启用正式环境）
            },
            success:(res)=>{//成功回调
              if(res.code == "000000"){
                this.phoneInfo = res
                this.showLogin = true
              }else{
                this.autoLoginError(h5ForceLogin,appForceLogin,smsLogin)
              }
            },
            error:()=>{//错误回调
              this.autoLoginError(h5ForceLogin,appForceLogin,smsLogin)
            }
          })
        }else{
          this.autoLoginError(h5ForceLogin,appForceLogin,smsLogin)
        }
      })
    },
    throttleGetLogin: debounce(function() {
      // 在这里编写你的逻辑
      this.getlogin()
    }, 1000),
    async getlogin(){//本机号码登录
      let string1 = `请先勾选中国移动认证服务协议`
      if(this.isWXMapp){
        string1 = `请先阅读并同意服务协议和隐私政策`
      }
      // this.isloginfn({ success: true })
      if(!this.checked){
        Toast({
          duration: 3000,
          message: string1
        })
        return false
      }
      let artifact = await loginApi.autoLogin(this.phoneInfo)
      if(artifact.resultCode == 0) {
        getUserInfoByArtifact(artifact.data.artifact,(userinfo)=>{
          this.userinfo = userinfo
          this.showLogin = false
          addUID(artifact.data.artifact)
          this.isloginfn(this.userinfo)
        },(error)=>{
          Toast({
            duration: 3000,
            message: "获取用户信息失败"
          })
        },"0")
      }else{
        //获取artifact失败
        Toast({
          duration: 3000,
          message: artifact.resultMsg
        })
      }
    },
    cancle(){
      this.showLogin = false
    },
    gotoLogin(){
      if(this.isWXMapp){
        if(!this.checked){
          Toast({
            duration: 3000,
            message: "请先阅读并同意服务协议和隐私政策"
          })
          return false
        }
        loginUtils.login(true,true,this.isloginfn,this.isSmsLogin)
      }else{
        loginUtils.login(true,true,this.isloginfn)
      }
    },
    async autoLoginError(h5ForceLogin,appForceLogin,smsLogin=false){ //4G取号失败

      this.showLogin = false
      if(await this.customError() === false) {
        return false
      }
      let sms = this.isSmsLogin || smsLogin
      if(this.isForceLogin || h5ForceLogin){
        //强登
        loginUtils.login(true,true,this.isloginfn,sms)
      }else{
        //不强登
        loginUtils.login(false,false,this.isloginfn,sms)
      }
    },
    openAgreement(type){
      this.AgreementType = type
      this.showAgreement = true
    }
  }
}
</script>
<style lang="scss" scoped>
  .loginDialog{
    .loginContent{
      //遮罩层
      background:rgba(0,0,0,.5);
      z-index: 10000000;
    }
    :deep(.van-popup){
      text-align: center;
      padding:30px;
      p{
        font-size: 14px;
        margin-top:18px;
        font-weight: 400;
      }
      .contentTitle{
        font-size: 16px;
        font-weight: 600;
        color: #333333;
        line-height: 22px;
      }
      .img{
        margin:35px auto 16px;
        img{
          width:50px;
          height:50px;
        }
      }
      .phone{
        font-size: 30px;
        font-weight: 500;
        color: #333333;
        line-height: 33px;
      }
      .tips{
        width: 182px;
        height: 24px;
        background: rgba(#6699ff,0.1);
        border-radius: 11px;
        font-size: 13px;
        font-weight: 400;
        color: #0079fd;
        line-height: 25px;
        margin:5px auto 29px;
      }
      .other-login{
        color: #017afe;
        font-weight: 400;
      }
      .tips2{
        font-size: 12px;
        color: #999999;
        img{
          width:20px;
          vertical-align: middle;
        }
        i{
          font-size: 15px;
          vertical-align: middle;
        }
      }
    }
    :deep(.van-button){
      background: #6699FF;
      width:100%;
      color:#fff;
      height: 44px;
      font-size: 16px;
      font-weight: 600;
      margin-top: 10px;
    }
    :deep(.van-button--small){
      margin-right: 11.25px;
    }
    .agreementBox{
      display: flex;
      flex-flow:wrap;
      line-height: 22px;
    }
  }
  .cancle{
    position: absolute;
    width: 27px;
    height: 26px;
    border-radius: 50%;
    right: 12px;
    top: 12px;
    background: #ddd url(~@/assets/index_img/icon_close_w.png) no-repeat center center;
    background-size: 50%;
  }
</style>
