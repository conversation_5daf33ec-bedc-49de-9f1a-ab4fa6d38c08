<template>
  <div class="agreeDialog">
    <van-popup
      v-model="myShow"
      :style="{ height: '100%',width:'100%'}"
      overlay-class="loginContent"
      :close-on-click-overlay="false"
    >
      <div class="content">
        <Service v-if="type==1" />
        <Privacy v-if="type==2" />
      </div>
      <div>
        <van-button type="info" round size="small" class="close" @click="closeAgreement">
          关闭
        </van-button>
      </div>
    </van-popup>
  </div>
</template>
<script>
import Privacy from "./privacy_agreement"
import Service from "./service_agreement"
export default {
  components:{
    Privacy,
    Service
  },
  props:{
    type:{
      type:Number,
      default:(data)=>{
        return data
      }
    },
    showAgreement:{
      type:Boolean,
      default:false
    }
  },
  data(){
    return {
      myShow:this.showAgreement
    }
  },
  watch:{
  },
  mounted(){
  },
  methods: {
    closeAgreement(){
      this.$parent.showAgreement = false
    }
  }
}
</script>
<style lang="scss" scoped>
  .agreeDialog{
    .loginContent{
      //遮罩层
      background:transparent;
      z-index: 10000000;
    }
    :deep(.van-popup){
      text-align: center;
      padding:37.5px 20px 0 20px!important;
      .contentTitle{
        display: flex;
        img{
          width:30px;
          margin-right:10px;
        }
        margin-bottom:20px;
      }
      .font-16{
        font-size: 26px;
      }
      p{
        font-size: 12px;
        margin-top:7.5px!important;
        line-height:22.5px;
        text-align: left;
      }
    }
    :deep(.van-button){
      width:135px;
    }
    :deep(.van-button--small){
      margin-right: 11.25px;
    }
    .content{
      height: 562.5px;
      overflow: scroll;
    }
  }
  .close{
    margin-top:18.75px
  }
</style>
