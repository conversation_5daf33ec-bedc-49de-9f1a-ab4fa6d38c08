<template>
  <div class="receivecoupon">
    <van-icon name="arrow-left" class="back" @click="closeDialog()" />
    <div v-if="!success">
      <div class="logo_container">
        <div class="logo_img">
          <img v-if="couponDetail.logoImg" :src="couponDetail.logoImg" alt="">
        </div>
        <div class="logo_content">
          <div class="logo_title">
            {{ couponDetail.couponName }}
          </div>
          <div class="logo_subtitle">
            {{ couponDetail.couponPropaganda }}
          </div>
        </div>
      </div>
      <div class="logo_form">
        <van-field v-model="form.formTel" maxlength="30" label="联系方式" />
        <van-field v-model="form.formName" maxlength="30" label="姓  名" />
      </div>
      <div class="store_name">
        <span v-if="couponDetail.storeName" class="icon_store"></span>
        <span class="store">
          {{ couponDetail.storeName }}
        </span>
      </div>
      <div class="bottom_btn" @click="receiveCoupon(couponDetail)">
        <span>立即领取</span>
      </div>
    </div>
    <div v-else>
      <div class="receive_success"></div>
      <p class="receive_label">
        领取成功
      </p>
      <div class="bottom_btn fix_bottom" @click="closeDialog()">
        <span>确定</span>
      </div>
    </div>
  </div>
</template>
<script>
import Vue from 'vue'
import { Field,Cell  } from 'vant'
import LadderShopApi from "@/api/ladder/ladder-shop"
Vue.use(Field,Cell )
export default {
  props: {
    sizeClass: {
      type: String,
      default: null
    },
    couponDetail: {
      type: Object,
      default: ()=>{
        return {}
      }
    },
    receiveCouponSuccessFn:{
      type: Function,
      default: (fn)=>{
        return fn
      }
    }
  },
  data(){
    return {
      form:{
        formName:"",
        formTel:""
      },
      success:false
    }
  },
  created(){
    document.body.height = "100vh"
    document.body.style.overflow='hidden'
  },
  methods:{
    receiveCoupon(goodsInfo){
      if(!this.form.formName){
        this.$toast("请输入姓名")
        return false
      }
      if(!this.form.formTel){
        this.$toast("联系方式")
        return false
      }
      this.form.couponId = goodsInfo.id
      this.form.receiveType = goodsInfo.receiveType
      // return false
      LadderShopApi.receiveCoupon(this.form).then((res)=>{
        if(res.code===0){
          this.success = true
          this.receiveCouponSuccessFn()
        }
      })
    },
    closeDialog(){
      document.body.style.overflow='auto'
      this.$parent.showReceiveDialog = false
    }
  }
}
</script>
<style lang="scss" scoped>
.receivecoupon{
  position: fixed;
  top:0;
  bottom:0;
  left:0;
  right:0;
  background: #fff;
  z-index:30;
  overflow-y: auto;
  .back{
    position: absolute;
    top:10px;
    left:10px;
    font-size: 20px;
    color:#fff;
  }
}
.logo_container{
  width: 475px;
  height: 306px;
  margin-left:-50px;
  border-bottom-left-radius: 77px;
  border-bottom-right-radius: 85px;
  background: linear-gradient(115deg,#634e40 48%, #3a2a23 76%);
  &::before{
    content: "";
    display: table;
  }
  .logo_img{
    width:115px;
    height:115px;
    margin:60px auto 0;
    img{
      width:115px;
      height:115px;
      border-radius: 50%;
    }
  }
  .logo_content{
    width:350px;
    height:133px;
    margin:10px auto;
    padding:22px 27px;
    background:url(~@/assets/ladderplan/detail_bg.png) no-repeat;
    background-size: contain;
    .logo_title{
      font-size: 22px;
      font-weight: 500;
      text-align: left;
      color: #58240c;
      line-height: 30px;
    }
    .logo_subtitle{
      opacity: 0.6;
      font-size: 16px;
      font-weight: 400;
      text-align: left;
      color: #59240c;
      line-height: 22px;
      margin-top:7px;
    }
  }
}
.logo_form{
  padding:50px 25px 0;
  .van-field{
    border-radius: 20px;
    background:#f7f8f9;
    margin-bottom:20px;
  }
  :deep(.van-field__label){
    width:74px;
    text-align:justify;
    font-size: 16px;
    font-weight: 400;
    text-align: left;
    color: #000000;
    line-height: 22px;
  }
}
.store_name{
  display: flex;
  margin-left:40px;
  .store{
    font-size:16px;
    width:280px;
    line-height:23px;
    margin-top:-3px;
  }
}
.icon_store{
  width: 16px;
  height:16px;
  background:url(~@/assets/ladderplan/address_name.png) no-repeat;
  background-size: contain;
  margin-right:6px;
}
.bottom_btn{
  width: 355px;
  height: 46px;
  font-size:17px;
  background: linear-gradient(138deg,#ffde3a 0%, #ffad09 97%);
  border-radius: 23px;
  margin:10px;
  text-align: center;
  color:#fff;
  line-height:46px;
  margin-top: 40px;
}
.bottom_btn.fix_bottom{
  margin-top: 0px;
  position: absolute;
  bottom:20px;
}
.receive_success{
  width: 66px;
  height:66px;
  background:url(~@/assets/ladderplan/success.png) no-repeat;
  background-size: contain;
  margin:228px auto 20px;
}
.receive_label{
  font-size: 17px;
  font-weight: 400;
  text-align: center;
  color: #000000;
  line-height: 24px;
}
</style>
