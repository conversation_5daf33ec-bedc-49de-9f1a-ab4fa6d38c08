<template>
  <div>
    <div class="voucher">
      <p v-if="Number(couponDetail.receiveType)===2 && couponDetail.couponStatus === 1" class="voucher-title">
        恭喜您成功领取优惠！
      </p>
      <div v-if="Number(couponDetail.receiveType)===2" class="tip-container">
        <p v-if="couponDetail.couponStatus === 1" class="tips">
          请凭借以下信息到店领取：
        </p>
        <p class="tips-content">
          姓名：{{ couponDetail.formName }}
        </p>
        <p class="tips-content">
          联系方式：{{ couponDetail.formTel }}
        </p>
      </div>
      <p class="voucher-title">
        {{ couponDetail.couponName }}
      </p>
      <!-- //1已领取 2 已核销 3 已过期 -->
      <p class="voucher-status" :class="{grey:couponDetail.couponStatus !== 1}">
        {{
          couponDetail.couponStatus === 1
            ? "未使用"
            : couponDetail.couponStatus === 2
              ? "已核销"
              : couponDetail.couponStatus === 3
                ? "已过期"
                : ""
        }}
      </p>
      <div v-if="couponDetail.couponStatus === 1" class="voucher-container">
        <span class="circle-grey left"></span>
        <span class="circle-grey right"></span>
        <div class="voucher-qrcode">
          <div v-if="qrcodeValue" class="qrcode">
            <qrcode :qrcode-value="qrcodeValue">
            </qrcode>
          </div>
        </div>
        <p class="voucher-code">
          {{ couponDetail.couponCode }}
        </p>
      </div>
      <div v-else class="voucher-container grey">
        <span class="circle-grey left"></span>
        <span class="circle-grey right"></span>
        <span class="circle-grey left-bottom"></span>
        <span class="circle-grey right-bottom"></span>
        <p class="voucher-code">
          {{ couponDetail.couponCode }}
        </p>
      </div>
      <p v-if="couponDetail.couponStatus === 2" class="voucher-time">
        核销日期：{{ couponDetail.updateTime }}
      </p>
      <p v-else class="voucher-time">
        有效期至{{ couponDetail.effectiveTimeEnd }}
      </p>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from "vue"
import qrcode from "@/components/ladderplan/vueqrcode.vue"
export default Vue.extend({
  name: "Ladpvoucher",
  components: {
    qrcode: qrcode
  },
  props: {
    couponDetail: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      qrcodeValue: null,
      showHead: false
    }
  },
  created() {
  },
  mounted(){
    this.qrcodeValue = location.origin + "/yundian/ladderplan/voucherreader.html?encryptKey=" + this.couponDetail.encryptKey
  },
  methods: {}
})
</script>
<style scoped lang="scss">
.voucher {
  text-align: center;
  .voucher-title {
    font-size: 20px;
    font-weight: 500;
    text-align: center;
    color: #000000;
    line-height: 28px;
  }
  .voucher-status{
    width: 81px;
    height: 27px;
    line-height: 27px;;
    background: linear-gradient(138deg,#ffde3a 0%, #ffad09 97%);
    border-radius: 14px;
    font-size: 14px;
    font-weight: 400;
    text-align: center;
    color: #ffffff;
    margin:8px auto 20px;
    &.grey{
      background:#fff;
      border: 1px solid #dfdfdf;
      color: #dfdfdf;
    }
  }
  .tip-container {
    font-size: 16px;
    font-weight: 400;
    text-align: left;
    color: #000000;
    line-height: 30px;
    margin: 10px;
    padding-left: 10px;
    padding-bottom:5px;
    margin-bottom: 20px;
    border-bottom: 1px solid #e2e2e2;
    .tips {
      font-size: 14px;
    }
  }
  .voucher-container {
    width: 285px;
    height: 333px;
    margin: 18px auto;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    .voucher-qrcode {
      height: 285px;
      background: #fff;
      padding-top: 42px;
      .qrcode {
        width: 200px;
        height: 200px;
        margin: 0 auto 10px;
      }
    }
    .voucher-code {
      height: 48px;
      background: linear-gradient(138deg, #ffde3a 0%, #ffad09 97%);
      font-size: 20px;
      font-weight: 500;
      text-align: center;
      color: #000000;
      line-height: 48px;
    }
    &.grey{
      height: 48px;
      .voucher-code{
        background:#fff;
      }
    }
  }
  .circle-grey {
    content: "";
    width: 20px;
    height: 20px;
    background: #f7f8f9;
    border-radius: 50%;
    position: absolute;
    bottom: 38px;
    &.left {
      left: -10px;
    }
    &.right {
      right: -10px;
    }
    &.left-bottom{
      bottom:-10px;
      left: -10px;
    }
    &.right-bottom{
      bottom:-10px;
      right: -10px;
    }
  }
  .voucher-time {
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    color: rgba(#000000, 0.6);
    line-height: 22px;
  }
}
</style>
