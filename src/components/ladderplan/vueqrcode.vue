<template>
  <div>
    <div ref="qrCodeUrl"></div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import QRCode from "qrcodejs2"
export default Vue.extend({
  name:"Ladpvoucher",
  props:{
    qrcodeValue:{
      type:String,
      default:""
    }
  },
  data(){
    return {
      qrcode:null
    }
  },
  watch:{
    qrcodeValue:{
      handler:function(val){
        this.creatQrCode()
      },
      immediate:false
    }
  },
  mounted(){
    this.creatQrCode()
  },
  methods:{
    creatQrCode() {
      if(this.qrcode){
        this.$refs.qrCodeUrl.innerHTML = ""
        this.qrcode.clear()
      }
      //防止dialog首次加载不出二维码 在dialog上加@opened 回调
      this.qrcode = new QRCode(this.$refs.qrCodeUrl, {
        text: this.qrcodeValue,
        width: 200,
        height: 200,
        colorDark: "#000000",
        colorLight: "#ffffff"
      })
    }
  }
})
</script>
<style scoped lang="scss">

</style>