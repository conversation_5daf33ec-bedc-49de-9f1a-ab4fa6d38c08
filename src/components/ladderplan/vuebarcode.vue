<template>
  <div>
    <barcode :value="barcodeValue" font-options="bold">
      Show this if the rendering fails.
    </barcode>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import VueBarcode from 'vue-barcode'
export default Vue.extend({
  name:"Ladpvoucher",
  components:{
    'barcode': VueBarcode
  },
  props:{
    barcodeValue:{
      type:String,
      default:""
    }
  },
  data(){
    return {
    }
  }
})
</script>
<style scoped lang="scss">

</style>