<template>
  <div v-if="myScancode" class="scan">
    <qrcode-stream style="height: 100vh;" @decode="onDecode" @init="onInit">
      <div>
        <div class="qr-scanner">
          <div class="box">
            <div class="line"></div>
            <div class="angle"></div>
          </div>
        </div>
      </div>
    </qrcode-stream>
  </div>
</template>
<script>
// 下载插件
// cnpm install --save  vue-qrcode-reader
// 引入
import { QrcodeStream } from "vue-qrcode-reader"
export default {
  // 注册
  components: { QrcodeStream },
  props: {
    scancode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      result: "", // 扫码结果信息
      error: "" ,// 错误信息
      myScancode:false
    }
  },
  watch:{
    scancode:function(val){
      this.myScancode = val
    }
  },
  created() {
    this.clickCode()
  },
  methods: {
    clickCode() {
      this.myScancode = true
    },
    //回调扫描结果
    onDecode(result) {
      if (result !== "") {
        alert(result)
        this.$emit("ok", result)
      }
    },
    // 检查是否调用摄像头
    async onInit(promise) {
      try {
        await promise
      } catch (error) {
        let audio = await new Audio('http://soundbible.com/mp3/Industrial Alarm-SoundBible.com-1012301296.mp3')
        audio.play()
        if (error.name === "NotAllowedError") {
          this.error = "ERROR: 您需要授予相机访问权限"
        } else if (error.name === "NotFoundError") {
          this.error = "ERROR: 这个设备上没有摄像头"
        } else if (error.name === "NotSupportedError") {
          this.error = "ERROR: 所需的安全上下文(HTTPS、本地主机)"
        } else if (error.name === "NotReadableError") {
          this.error = "ERROR: 相机被占用"
        } else if (error.name === "OverconstrainedError") {
          this.error = "ERROR: 安装摄像头不合适"
        } else if (error.name === "StreamApiNotSupportedError") {
          this.error = "ERROR: 此浏览器不支持流API"
        }
        // console.log(error)
      }
    }
  }
}
</script>
<style scoped>
.error {
  font-weight: bold;
  color: red;
}
</style>
<style scoped>
@import "~@/styles/scancode.scss";
</style>
