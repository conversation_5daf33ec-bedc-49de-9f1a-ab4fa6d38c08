<template>
  <div
    class="goodContainer"
    :class="[sizeClass]"
    @click.stop="gotoMyDetail(myGoodsInfo.id)"
  >
    <div class="goodsImg">
      <img :src="myGoodsInfo.voucherimg" alt="" width="100%" />
    </div>
    <div class="content">
      <div class="title elli">
        {{ myGoodsInfo.vouchertitle }}
      </div>
      <div class="subTitle elli">
        {{ myGoodsInfo.vouchersubTitle }}
      </div>
      <div v-if="showQuan" class="quanCon">
        <div class="quan">
          满20-3
          <span class="circle circle_left"></span>
          <span class="circle circle_right"></span>
        </div>
        <div class="quan">
          新客8折
          <span class="circle circle_left"></span>
          <span class="circle circle_right"></span>
        </div>
        <div class="quan">
          新客8折
          <span class="circle circle_left"></span>
          <span class="circle circle_right"></span>
        </div>
        <div class="quan">
          新客8折
          <span class="circle circle_left"></span>
          <span class="circle circle_right"></span>
        </div>
      </div>
      <div class="iconCon">
        <!-- 0 就是未领取，1 是领取 -->
        <div
          v-if="myGoodsInfo.couponStatus === 0"
          class="icon icon-quan"
          @click.stop="toggleReceive()"
        ></div>
        <div v-else class="icon icon-quan-added" @click.stop="toggleReceive()"></div>
        <!-- （0：取关 1：关注） -->
        <div
          v-if="myGoodsInfo.isFollow === 0"
          class="icon icon-collec"
          @click.stop="followShop(1, myGoodsInfo.storeId)"
        ></div>
        <div
          v-else
          class="icon icon-collec-added"
          @click.stop="followShop(0, myGoodsInfo.storeId)"
        ></div>
        <div v-if="showQuan" class="icon distance elli">
          {{ myGoodsInfo.distance }}
        </div>
        <div v-if="showQuan" class="icon icon-location-red"></div>
      </div>
    </div>
  </div>
</template>
<script>
import LadderShopApi from "@/api/ladder/ladder-shop"
import loginUtils from "@/utils/login"
export default {
  components: {
  },
  props: {
    sizeClass: {
      type: String,
      default: null
    },
    goodsInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    showQuan: {
      type: Boolean,
      default: false
    },
    shopId: {
      type: String,
      default: null
    },
    changeCurrentCoupon:{
      type:Function,
      default:null
    },
    changeFollow:{
      type:Function,
      default:null
    },
    gotoDetail:{
      type:Function,
      default:null
    },
    serviceClass:{
      type: String,
      default: null
    },
    serviceImgDisappear:{
      type:Function,
      default:null
    },
  },
  data() {
    return {
      showReceiveDialog: false,
      myGoodsInfo:{}
    }
  },
  watch:{
    goodsInfo:{
      handler(val){
        this.myGoodsInfo = val
      },
      deep: true
    }
  },
  methods: {
    followShop(operateType, storeId) {
      let that = this,setTime = 0
      if(this.serviceClass.indexOf("service_img_bubble_show")!==-1){
        this.serviceImgDisappear()
        setTime=2000
      }
      setTimeout(()=>{
        LadderShopApi.followShop({
          operateType,
          storeId
        }).then(res => {
          if (res.code === 0) {
            if (operateType === 0) {
              that.$toast("已取消关注")
              that.changeMyFollow(storeId,0)
            }
            if (operateType === 1) {
              that.$toast("已关注商家")
              that.changeMyFollow(storeId,1)
            }
          }else{
            this.$toast(res.message)
          }
        }).catch(error=>{
          // console.log(error)
        })
      },setTime)
    },
    changeMyFollow(storeId,isFollow){
      this.changeFollow(storeId,isFollow)
    },
    toggleReceive() {
      let that = this,setTime = 0
      if(this.serviceClass.indexOf("service_img_bubble_show")!==-1){
        this.serviceImgDisappear()
        setTime=2000
      }
      setTimeout(()=>{
        loginUtils.login(true, true, ()=>{
          if(that.myGoodsInfo.couponStatus===1){
            that.$toast("在“我的-我的券包”查看优惠券")
            return false
          }
          //0：常规领取 1：跳转链接 2：填写表单
          if (that.myGoodsInfo.receiveType === 0) {
            //直接领取
            that.receiveCoupon(that.myGoodsInfo)
          }
          if (that.myGoodsInfo.receiveType === 1) {
            //直接领取
            // window.location.href = that.myGoodsInfo.outerLink
            this.$toast("请在“移云店”小程序中领取")
          }
          if (that.myGoodsInfo.receiveType === 2) {
            //直接领取
            that.changeCurrentCoupon(that.myGoodsInfo)
          }
        }, false, false, "", "0")
      },setTime)
    },
    receiveCoupon(myGoodsInfo) {
      // return false
      LadderShopApi.receiveCoupon({
        couponId: myGoodsInfo.id,
        receiveType:myGoodsInfo.receiveType
      }).then(res => {
        if(res.code===0){
          this.$toast("领取成功")
          this.myGoodsInfo.couponStatus = 1
        }else{
          this.$toast(res.message)
        }
      }).catch(error=>{
        // console.log(error)
      })
    },
    gotoMyDetail(couponId) {
      this.gotoDetail(couponId)
    }
  }
}
</script>
<style lang="scss" scoped>
.goodContainer {
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0px 2px 5px 0px rgba(225, 225, 225, 0.5);
  overflow: hidden;
  .title {
    font-size: 13px;
    font-weight: 500;
    text-align: left;
    color: #333333;
    line-height: 18px;
    margin-top: 8px;
  }
  .subTitle {
    font-size: 10px;
    font-weight: 400;
    text-align: left;
    color: #999999;
    line-height: 14px;
    margin-top: 5px;
  }
  .quanCon {
    display: flex;
    flex-wrap: wrap;
    .quan {
      padding: 0px 7px;
      line-height: 13px;
      margin-top: 4px;
      height: 15px;
      margin-right: 5px;
      border: 1px solid #ff8b7e;
      font-size: 10px;
      font-weight: 400;
      color: #ff8b7e;
      border-radius: 4px;
      position: relative;
      .circle {
        border: 1px solid #ff8b7e;
        width: 5px;
        height: 5px;
        border-radius: 5px;
        position: absolute;
        top: 4px;
        border-left-color: transparent;
        border-bottom-color: transparent;
        background: #fff;
        transform: rotate(41deg);
        &.circle_left {
          left: -3px;
          transform: rotate(41deg);
        }
        &.circle_right {
          right: -3px;
          transform: rotate(-145deg);
        }
      }
    }
  }
  .iconCon {
    display: flex;
    flex-direction: row-reverse;
    .icon {
      height: 24px;
      align-self: center;
      flex-direction: row-reverse;
      &.icon-quan {
        width: 24px;
        margin: 0 12px;
        background: url("~@/assets/ladderplan/quan.png") no-repeat;
        background-size: contain;
      }
      &.icon-quan-added {
        width: 24px;
        margin: 0 12px;
        background: url("~@/assets/ladderplan/quan_added.png") no-repeat;
        background-size: contain;
      }
      &.icon-collec {
        width: 24px;
        background: url("~@/assets/ladderplan/collec.png") no-repeat;
        background-size: contain;
      }
      &.icon-collec-added {
        width: 24px;
        background: url("~@/assets/ladderplan/collec_added.png") no-repeat;
        background-size: contain;
      }
      &.icon-location-red {
        height: 13px;
        width: 13px;
        background: url("~@/assets/ladderplan/location_red.png") no-repeat;
        background-size: contain;
      }
      &.distance {
        flex: 1;
        font-size: 11px;
        font-weight: 400;
        text-align: left;
        color: #cea04a;
        line-height: 25px;
        padding-left: 4px;
      }
    }
  }
  .elli {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .goodsImg {
    overflow: hidden;
  }
  &.normal {
    width: 148px;
    padding-bottom: 9px;
    .goodsImg {
      width: 100%;
      height: 122px;
    }
    .content {
      padding-left: 7.5px;
    }
    .title,
    .subTitle {
      width: 138px;
    }
    .iconCon {
      margin-top: 10px;
    }
  }
  &.large {
    width: 173px;
    padding-bottom: 11px;
    .content {
      padding-left: 8.5px;
    }
    .title,
    .subTitle {
      width: 160px;
    }
    .goodsImg {
      width: 100%;
      height: 143px;
    }
    .iconCon {
      margin-top: 6px;
    }
  }
}
</style>
