<template>
  <div class="shareBar">
    <div class="share_bar">
      <a
        v-if="props.floorData.sharingQrCode"
        href="javascript:void(0);"
        class="wxkf"
        @click="getCustomerCode(props.floorData.sharingCode)"
      >
        <img src="~@/assets/index_img/wqkf.png" />微信客服
      </a>
      <a
        href="javascript:void(0);"
        class="fxdp"
        @click="shareShop"
      ><img src="~@/assets/index_img/share_shop.png" />分享店铺</a>
    </div>
  </div>
</template>

<script setup>
import { getAllSearchParamsArray, getCookie, setSearchParamsArray } from "@/utils/utils"
import EventBus from "@/api/eventbus"
import { Toast } from "vant"
import insertCode from "@/utils/insertCode"
import shopAPI from "@/api/shop"
import UA from "@/utils/ua"
import PrecisionMarketing from "@/model/precisionMarketing"
import {reactive,defineProps} from "vue"
const props = defineProps({
  preview: {
    type: String,
    default: null,
  },
  floorData: {
    type: Object,
    default: null,
  },
  floorId: {
    type: String,
    default: null,
  },
  configure: {
    type: String,
    default: null,
  },
  shopId: {
    type: [Number, String],
    default: null,
  }
})
/* precisionMarketing 代表是否是精准营销的分享 */
function shareShop(obj = {}, precisionMarketing, item = {}) {
  if (props.preview || props.configure) return false
  insertCode("yd_index_" + props.shopId + "_sharestore")
  let allParams = getAllSearchParamsArray(location.href)
  let ac_id = allParams["WT.ac_id"] || getCookie("ac_id")
  let sharingShopUrl = props.floorData.sharingShopUrl
  let isExecuteNow = UA.isWechatWork

  // 如果是精准营销分享,加入精准营销标识
  if (precisionMarketing) {
    sharingShopUrl = PrecisionMarketing.addShareMarketing(sharingShopUrl)
  }

  EventBus.$emit(
    "openShareDialog",
    "shop",
    {
      ...item,
      url: ac_id
        ? setSearchParamsArray(sharingShopUrl, { "WT.ac_id": ac_id })
        : sharingShopUrl,
    },
    isExecuteNow
  )
  insertCodeGoods("shop")
}
function customerService() {
  if (props.preview || props.configure) return false
  insertCodeGoods("qw")
  if (props.floorData.contactQrcode) {
    EventBus.$emit(
      "openShareDialog",
      "service",
      props.floorData.contactQrcode
    )
  } else {
    Toast("功能优化中")
  }
}
function getCustomerCode(sharingCode) {
  if (!sharingCode) return
  insertCode("yd_index_" + props.shopId + "_managerwecom")
  shopAPI.getCustomerCode({ sharingCode }).then((r) => {
    if (r.code) {
      Toast(r.message)
    } else {
      if (r.data.contactQrcode) {
        EventBus.$emit("openShareDialog", "service", r.data.contactQrcode)
      } else {
        Toast("功能优化中")
      }
    }
  })
}
function insertCodeGoods(key) {
  let dcs_id = key
  insertCode(props.floorId + "_share_" + dcs_id)
}
</script>

<style lang="scss" scoped>
.shareBar {
  .share_bar {
    display: flex;
    height: 33.75px;
    line-height: 33.75px;
    text-align: center;
    background: #fff;
    font-size: 11.25px;
    color: #4a90e2;
    margin-bottom: 1px;
    a {
      color: inherit;
      flex: 1;
      img {
        width: 15px;
        margin-right: 11.25px;
        vertical-align: middle;
      }
    }
  }
}
</style>
