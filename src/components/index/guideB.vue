<template>
  <div v-if="props.items && props.items.length" :class="[props.guideClass? props.guideClass : 'guideB',user.isMember&&(!props.hasbanner) ? 'nobanner' : '']">
    <div v-for="(item, key) in props.items" :key="key" class="item" :class="item.class">
      <a href="javascript:void(0)" @click="insertCodeGoods(key,item.adLink,item)">
        <img :src="item.imgSrc" />
        {{ item.title }}
      </a>
    </div>
  </div>
</template>

<script setup>
import {reactive,defineProps,inject, getCurrentInstance,computed} from "vue"
import {Toast } from "vant"
import lbsApi from "@/api/lbs"
import insertCode from "@/utils/insertCode"
let getSon = inject("getSon")
const props = defineProps({
  items: {
    type: Array,
    default: () =>{
      return []
    }
  },
  preview: {
    type: String,
    default: null
  },
  guideClass:{
    type:String,
    default: null
  },
  hasbanner:{
    type: Boolean,
    default:false
  }
})
let guideItme = null
const getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null
const user = computed(()=>{
  return (proxy && proxy.$store.getters && proxy.$store.getters.user) ? proxy.$store.getters.user : null
})
const pageInfo = computed(()=>{
  return (proxy && proxy.$store.getters && proxy.$store.getters.pageInfo) ? proxy.$store.getters.pageInfo : null

})
function insertCodeGoods(key,url,item){
  guideItme = item
  if(props.preview){
    return
  }
  if(item.linkId=='quhao'){
    if(user.value || user.value.userInfo==null){
      //强登
      openloginSelf(yuyue)
    }else{
      yuyue()
    }
  }else if(item.linkId=='broadband'){

    // sectionId为空，则提示
    if(!item.sectionId){
      Toast('该功能正在开放中，敬请期待')
      insertCode('yd_index_'+pageInfo.value.shopId+"_icon_kdyy")
      return false
    }

    if(user.value.userInfo==null){
      //强登
      openloginSelf(broadband)
    }else{
      insertCode('yd_index_'+pageInfo.value.shopId+"_icon_kdyy",url)
    }
  }else if(item.linkId=='shopOnline'){
    if(pageInfo.value.pageInfo.shopOnlineStatus!=1){
      insertCode('yd_index_'+pageInfo.value.shopId+"_icon_"+item.dcsId)
      Toast('该功能正在开放中，敬请期待')
      return false
    }
    if(user.value.userInfo==null){
      //强登
      openloginSelf(commonInsert)
    }else{
      commonInsert()
    }
  }else{
    if(user.value.userInfo==null){
      //强登
      openloginSelf(commonInsert)
    }else{
      commonInsert()
    }
  }

}
function broadband(res) {
  insertCode('yd_index_'+pageInfo.value.shopId+"_icon_kdyy",guideItme.adLink)
}
function commonInsert(res) {
  insertCode('yd_index_'+pageInfo.value.shopId+"_icon_"+guideItme.dcsId,guideItme.adLink)
}
function yuyue(){
  lbsApi.getUrlReq({
    pageType:2,
    shopId: pageInfo.value.shopId
  }).then(res => {
    if(res.code ==0) {
      insertCode('yd_index_'+pageInfo.value.shopId+"_icon_pdqh",res.data.url)
    }else{
      Toast(res.message)
    }
  })
}
function openloginSelf(callback) {
  return getSon.openlogin(callback)
}
</script>

<style lang="scss" scoped>

  .guideB ,.guide_4 {
    background: #fff;
    width: 375px;
    margin: 0 auto;
    display: flex;
    // flex-direction: row;
    min-height: 115px;
    flex-flow:row wrap;
    padding-top: 35px;
    .item {
      width:90px;
      padding-bottom: 25px;
      font-size: 13px;
      text-align: center;
      img {
        width: 42px;
        height: 42px;
        border-radius:41px;
        margin: 0 auto 10px auto;
        display: block;
      }
    }
  }
  .guide_4{
    width: 351px;
    &.nobanner{
      padding-top:52px;
    }
    .item {
      width:25%;
      img{
        border-radius: 0%;
      }
    }
  }
</style>
