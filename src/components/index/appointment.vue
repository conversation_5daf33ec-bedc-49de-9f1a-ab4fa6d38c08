<template>
  <div class="waitfloat">
    <a v-if="queueStatus == 1 && appointmentStatus == 0" @click="quhao(shopID,'zxqh')"><img src="@/assets/index_img/pic_zxqh.png" /></a>
    <a v-if="queueStatus == 0 && appointmentStatus == 1" @click="yuyue(shopID,'zxqh')"><img src="@/assets/index_img/pic_zxyy.png" /></a>
    <a v-if="queueStatus == 1 && appointmentStatus == 1" @click="yuyue(shopID,'zxqh')"><img src="@/assets/index_img/pic_yyqh.png" /></a>
  </div>
</template>

<script setup>
import {defineProps,reactive,getCurrentInstance} from "vue"
import {Toast } from "vant"
import {mapGetters} from "vuex"
import lbsApi from "@/api/lbs"
import insertCode from "@/utils/insertCode"
const props =defineProps({
  preview: {
    type: String,
    default: null
  }
})
const apponintment = reactive({
  queueStatus:null,
  appointmentStatus:null,
  shopID:null
})
function yuyue(shopID,dcs_id){
  if(props.preview){
    return
  }
  lbsApi.getUrlReq({
    pageType:1,
    shopId: shopID
  }).then(res => {
    if(res.code == 0) {
      insertCodeGoods("yd_index_" + this.pageInfo.pageInfo.shopId + "_icon_" + dcs_id,res.data.url)
    }else{
      Toast(res.message)
    }
  })
}
function quhao(shopID,dcs_id){
  if(props.preview){
    return
  }
  lbsApi.getUrlReq({
    pageType:0,
    shopId: shopID
  }).then(res => {
    if(res.code == 0) {
      insertCodeGoods("yd_index_" + this.pageInfo.pageInfo.shopId + "_icon_" + dcs_id,res.data.url)
    }else{
      Toast(res.message)
    }
  })
}
function insertCodeGoods(key,url){
  if(props.preview){
    return
  }
  let dcs_id = key
  insertCode(dcs_id,url)
}
const getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null
let pageInfo = proxy.$store.getters.pageInfo
apponintment.appointmentStatus = pageInfo.pageInfo.appointmentStatus
apponintment.queueStatus = pageInfo.pageInfo.queueStatus
apponintment.shopID = pageInfo.pageInfo.shopId
</script>

<style lang="scss" scoped>
.waitfloat{
    position: fixed;
    right: 4px;
    top: 25%;
    width: 66px;
    height:57px;
    z-index: 2;
    }
    img{
      width:100%;
      height: 100%;
    }
</style>
