<template>
  <a href="javascript:void(0)" @click="insertCodeGoods(adlink)">
    <img :class="[singleClass?singleClass : 'single']" :src="bannersrc">
  </a>
</template>

<script setup>
import { defineProps } from 'vue'
import insertCode from "@/utils/insertCode.js"
import UA from "@/utils/ua"
import { getCookie } from "@/utils/utils.js"
const props = defineProps({bannersrc: {
  type: String,
  default: ''
},
adlink: {
  type: String,
  default: ''
},
preview: {
  type: String,
  default: null
},
floorId:{
  type:String,
  default: null
},
singleClass:{
  type:String,
  default: null
},
dcsId:{
  type:String,
  default: null
},
shopId:{
  type: [String,Number],
  default: ''
}})

async function insertCodeGoods(url){
  if(props.preview){
    return
  }
  let ac_id = getCookie("ac_id")
  let goods_link=url
  if (!ac_id && goods_link!=null) {
    const isWXMapp = await UA.isWeChatMiniApp()
    // 如果是聚合页的链接
    if(/\/hd\/ydskin/.test(url)){
      goods_link = url+(url.indexOf('?')==-1?'?':'&')+'WT.ac_id=SHOP_COLLECTIONPAGE_SHARE'
    } else if (isWXMapp) {
      goods_link = url+(url.indexOf('?')==-1?'?':'&')+'WT.ac_id=SHOP_APPLET_TRANSACT'
    } else {
      goods_link = url+(url.indexOf('?')==-1?'?':'&')+'WT.ac_id=SHOP_DIRECT_TRANSACTION'
    }
  }
  insertCode("yd_index_"+props.shopId+"_"+props.dcsId,goods_link)
}


</script>

<style lang="scss" scoped>
.single{
    width:375px;
    display: block;
}
.single_4{
    width:100%;
    max-width:351px;
    margin:0 auto;
    display: block;
}
</style>
