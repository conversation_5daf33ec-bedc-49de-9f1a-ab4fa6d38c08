<template>
  <div class="sharedialog">
    <van-dialog
      v-model="shareData.showDAOhang"
      :show-confirm-button="false"
      :before-close="confirmShare"
      :close-on-click-overlay="true"
    >
      <div class="icon_container">
        <div>
          <input id="foo" v-model="shareData.qrcodeurl" type="hidden" />
        </div>
        <ul>
          <li>
            <button
              v-clipboard:copy="shareData.qrcodeurl"
              v-clipboard:success="onCopy"
              v-clipboard:error="onError"
              class="iconfont icon-copylink"
              style="color: #7f7ff6"
            ></button>
            <span>复制链接</span>
          </li>
          <li>
            <span class="iconfont icon-picture" style="color: #4974f3" @click="showQrCode"></span>
            <span>保存{{
              shareData.shareDialog.type === "goods" || shareData.shareDialog.isDoMeal ? "商品" :
              shareData.shareDialog.isPoster ? "" : "店铺"
            }}海报</span>
          </li>
          <li v-if="UA.isApp || UA.isWechat">
            <span v-if="UA.isApp" class="iconfont icon-share" style="color: #7cd576" @click="share"></span>
            <span v-if="UA.isWechat" class="iconfont icon-share" style="color: #7cd576" @click="share('wx')"></span>
            <span>分享</span>
          </li>
        </ul>
      </div>
      <div class="btn_container">
        <a href="javascript:void(0);" class="bt_save bt_only" @click="hideDialogList">取消</a>
      </div>
    </van-dialog>
    <van-dialog
      v-model="shareData.show"
      :show-confirm-button="false"
      :before-close="confirmShare"
      :close-on-click-overlay="true"
    >
      <div ref="qrCodeContainer">
        <div v-if="shareData.isQW" class="qrTitle">
          <span>
            {{ shareData.shareDialogTitle }}
          </span>
        </div>
        <div v-if="shareData.isQW" class="qrcode">
          <img :src="shareData.qwUrl" alt="" />
        </div>
        <div v-else class="posterContainer">
          <img :src="shareData.qwUrl" alt="" />
        </div>
      </div>
      <div v-if="!shareData.isQW" style="text-align: center">
        <a href="javascript:void(0);" class="bt_tip bt_save">
          长按图片保存海报
        </a>
      </div>
    </van-dialog>
    <div v-if="shareData.showShare" class="wx-guide" @click="hideShare">
      <img src="~@/assets/index_img/wx_guide.png" alt="" width="200" />
    </div>
    <div class="copyQRCode">
      <div ref="qrCodeContainer1" class="qrCodeContainer">
        <div class="copyQRCodeTitle">
          <span>
            {{ pageInfo.shortName }}
          </span>
        </div>
        <div class="copyQRCodeAddress">
          <span>
            {{ pageInfo.address }}
          </span>
        </div>
        <div class="imgcontainer" :style="{backgroundImage: 'url(' + shareData.shareDialog.dialogImg + ')',}">
          <!-- <img :src="shareDialog.dialogImg" alt="" height="100%" /> -->
        </div>
        <div class="shareContent">
          <div ref="qrCodeUrlCopy" class="copyQRCodeImg"></div>
          <div v-if="shareData.shareDialog.type === 'goods' || shareData.shareDialog.isDoMeal" class="goodsShare">
            <PriceCom
              v-if="shareData.shareDialog.priceFrom"
              :price-from="shareData.shareDialog.priceFrom"
              :shop-price="shareData.shareDialog.shopPrice"
              :price-section="shareData.shareDialog.priceSection"
              price-class="goodsPrice"
            />
            <div v-else class="goodsPrice">
              ￥{{
                shareData.shareDialog.frontEndPrecisionMarketingSettingLogo ? shareData.shareDialog.price :
                (shareData.shareDialog.price
                  / 100).toFixed(2)
              }}
            </div>
            <div v-if="shareData.shareDialog.title && shareData.shareDialog.title.length > 25" class="goodsName">
              {{ shareData.shareDialog.title.slice(0, 25) }} ...
            </div>
            <div v-else class="goodsName">
              {{ shareData.shareDialog.title }}
            </div>
            <div class="tip">
              长按识别/扫描二维码
              <span class="iconfont icon-shixinjiantou-you"></span>
            </div>
          </div>
          <div v-if="shareData.shareDialog.type === 'shop' && !shareData.shareDialog.isDoMeal" class="shopShare">
            <div class="tip">
              长按识别/扫描二维码
              <span class="iconfont icon-shixinjiantou-you"></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Dialog, Toast } from "vant"
import UA from "@/utils/ua"
import QRCode from "qrcodejs2"
import EventBus from "@/api/eventbus"
import shareUtilApi from "@/utils/share"
import shareApi from "@/api/share"
import insertCode from "@/utils/insertCode"
import VueClipboard from "vue-clipboard2"
import createEnum from '@/utils/createEnum'
import _ from 'lodash'
import PriceCom from "@/components/index/price.vue"
import Vue,{ reactive,ref,defineProps,getCurrentInstance,onMounted,watch,nextTick, computed, onActivated} from "vue"
Vue.use(Dialog)
const props = defineProps({
  shopshare: {//店铺分享数据
    type: Object,
    default: (data) => {
      return data || {}
    },
  },
  orderAcid:{//云店订单acid标识
    type: [String, Number],
    default: null,
  }
})
Vue.use(VueClipboard)
const shareData = reactive({
  show: false,
  shareConfig: {},
  qrcode: null,
  qrcodeCopy: null,
  shareDialogTitle: null,
  qrcodeurl: null,
  rwmqrcodeurl: null,
  isQW: false, //是否是企微弹框
  showShare: false, //是否在展示分享引导（微信里展示）
  qwUrl: "", //企微地址
  // eslint-disable-next-line
  defaultImg: require("@/assets/index_img/shop_share_img.png"),//默认分享图片
  shareDialog: "", //分享弹框展示内容
  showDAOhang: false, //是否展示分享内容
  showQrCodeHtml: false, //弹框展示内容
})
const getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null
const pageInfo = computed(()=>{
  if(proxy){
    return proxy.$store.getters.pageInfo && proxy.$store.getters.pageInfo.pageInfo && proxy.$store.getters.pageInfo.pageInfo.shopId ? proxy.$store.getters.pageInfo.pageInfo : props.shopshare
  }
  return null
})
function init(){
  EventBus.$off("openShareDialog")
  EventBus.$off("share")
  EventBus.$on("openShareDialog", changeShareConfig)
  EventBus.$on("share", share)
  if (UA.isWechat) {
    //初始化店铺分享
    shareUtilApi.changeWxShareConfig(props.shopshare)
  }
  //企业微信分享初始化province_id传参
  shareUtilApi.wechartWorkInit({
    province_id: pageInfo.value.provinceId,
  })
}
onMounted(() => {
  init()
})
onActivated(()=>{
  init()
})
const qrCodeUrlCopy = ref(null)
watch(() => shareData.show, (val) => {

  if (!val) {
    shareData.showQrCodeHtml = false
    qrCodeUrlCopy.value.innerHTML = ""
    hideShare()
  }
})
watch(() => shareData.showDAOhang, (val) => {
  if (!val && !shareData.showQrCodeHtml) {
    shareData.showQrCodeHtml = false
    qrCodeUrlCopy.value.innerHTML = ""
    hideShare()
  }
})
watch(() => props.shopshare, (shopshare) => {
  if (shopshare && shopshare.shortName) {
    pageInfo.value = shopshare
  }

})
/* 分享插码js */
async function commonShareFun({ item, type }) {
  /* 判断商品来源 */
  const goodsSourceEnum = createEnum({
    zysp: [1, "自有商品"],
    yysp: [2, "异业商品"],
    szsp: ["", "其他商品"],
  })

  // 商品为0, 说明分享的是店铺
  const shop = 0
  let goodsSource = item.goodsSource
  let goodsId = item.goodsId || 0
  let goodsSourceString = null
  if (goodsSource === goodsSourceEnum.zysp) {
    goodsSourceString = "zysp"
  } else if (goodsSource === goodsSourceEnum.yysp) {
    goodsSourceString = "yysp"
  } else {
    goodsSourceString = "szsp"
  }
  if (goodsId === shop) {
    goodsSourceString = 'dp'
  }

  // 获取推荐人id, refid
  let url = item.url
  if (item.goodsLink) {
    url = item.goodsLink
  }
  let resId = null
  if (new URL(url) && new URL(url).searchParams && new URL(url).searchParams.get) {
    resId = new URL(url).searchParams.get('key')
  }

  let ac_id = null
  if (UA.isMobile) {
    ac_id = 'yd_share_llq'
  }
  if (UA.isWechat) {
    ac_id = 'yd_share_wx'
  }
  if (UA.isApp) {
    ac_id = 'yd_share_app'
  }

  let isWeChatMiniApp = await UA.isWeChatMiniApp()
  if (isWeChatMiniApp) {
    ac_id = "yd_share_xcx"
  }

  let currentUrl = new URL(location.href), my_ac_id, cookies
  cookies = document.cookie.match(/ac_id=([^\s;]+)/i) ?
    document.cookie.match(/ac_id=([^\s;]+)/i)[1] :
    ""
  my_ac_id = currentUrl.searchParams.get('WT.ac_id') || cookies || null
  if (my_ac_id) ac_id = my_ac_id

  insertCode({
    'WT.event': `yd_share_${type}_${goodsSourceString}_${goodsId}`,
    'WT.refid': resId,
    'WT.ac_id': ac_id
  })
}

function confirmShare(action, done) {
  if (action === "confirm") {
    done(false)
  } else {
    done()
  }
}
async function changeShareConfig(type, data, isExecuteNow) {
  shareData.isQW = false
  let ordercodeUrl = data.url
  if(props.orderAcid&&props.orderAcid!=''){
    shareData.rwmqrcodeurl = data.url+(data.url.includes('?')?'&':'?')+'WT.ac_id=SHOP_QR_SHARE'
  }else {
    shareData.rwmqrcodeurl = await getShortUrl(data.url)
  }
  if (type == "goods") {
    shareData.shareConfig = data
    shareData.shareConfig.unifiedChannelId = props.shopshare.unifiedChannelId
    shareData.shareConfig.dcsId = data.dcsId
    if(props.orderAcid&&props.orderAcid!=''){
      //1, "自有商品";2, "异业商品"
      if(shareData.shareConfig.goodsSource===1){
        ordercodeUrl = ordercodeUrl+(ordercodeUrl.includes('?')?'&':'?')+'WT.ac_id=SHOP_OWNGOODS_SHARE'
      }else if(shareData.shareConfig.goodsSource===2){
        ordercodeUrl = ordercodeUrl+(ordercodeUrl.includes('?')?'&':'?')+'WT.ac_id=SHOP_GOODS_SHARE'
      }
    }
    shareData.qrcodeurl = await getShortUrl(ordercodeUrl)
    shareData.dialogImg = data.img
    shareData.shareDialog = {
      type: type,
      dialogImg: data.imgUrl,
      title: data.title,
      price: data.price,
      isDoMeal: data.isDoMeal,
      frontEndPrecisionMarketingSettingLogo: data.frontEndPrecisionMarketingSettingLogo,
      //以下三个是工作台配置的价格参数
      priceFrom: data.priceFrom == 3 ? 1 : data.priceFrom,
      shopPrice: data.shopPrice,
      priceSection: data.priceSection,
    }
    shareData.showDAOhang = true
  } else if (type == "shop") {
    shareData.shareConfig = props.shopshare
    shareData.shareConfig.url = ordercodeUrl
    shareData.shareConfig.dcsId = props.shopshare.dcsId
    if(props.orderAcid&&props.orderAcid!=''){
      //1, "自有商品";2, "异业商品"
      if(shareData.shareConfig.goodsSource===1){
        ordercodeUrl = ordercodeUrl+(ordercodeUrl.includes('?')?'&':'?')+'WT.ac_id=SHOP_OWNGOODS_SHARE'
      }else if(shareData.shareConfig.goodsSource===2){
        ordercodeUrl = ordercodeUrl+(ordercodeUrl.includes('?')?'&':'?')+'WT.ac_id=SHOP_GOODS_SHARE'
      }
    }
    shareData.qrcodeurl = await getShortUrl(ordercodeUrl) //暂时修改
    // shareData.qrcodeurl="https://www.baidu.com/"
    let dialogImg = shareData.defaultImg
    if (data.isDoMeal) {//套餐图片是商品图片，分享的是店铺链接
      dialogImg = data.imgUrl
    } else if (data.bgImg) {//可配置的海报背景图
      dialogImg = data.bgImg
    }
    shareData.shareDialog = data
    shareData.shareDialog.type = type
    shareData.shareDialog.dialogImg = dialogImg
    shareData.shareDialog.title = data.title ? data.title : data.goodsName
    shareData.showDAOhang = true
  } else {
    //企微客服
    shareData.shareDialogTitle = "请使用微信扫码添加店铺客服"
    shareData.qwUrl = data
    shareData.isQW = true
    shareData.show = true
  }
  creatQrCode()
  if (isExecuteNow) {
    share()
  }
}
function getShortUrl(url) {
  return shareApi.getShortUrl({ url: url }).then((res) => {
    if (res.data && res.data.url) {
      return res.data.url
    } else {
      return url
    }
  })
}
function showQrCode() {
  commonShareFun({
    item: shareData.shareConfig,
    type: 'bchb'
  })

  shareData.show = true
  shareData.showQrCodeHtml = true
  shareData.showDAOhang = false
}
async function share(type) {
  //修改微信分享配置
  insertCode(shareData.shareConfig.dcsId)

  commonShareFun({
    item: shareData.shareConfig,
    type: 'fx'
  })

  await shareUtilApi.share(shareData.shareConfig)
  //遮罩层显示
  if (type == "wxwork") return false //防止企微分享方法重复执行
  if (UA.isWechatWork) {
    shareUtilApi.getContext(function(entry) {
      if (entry == "single_chat_tools" || entry == "group_chat_tools") {
        shareUtilApi.sendChatMessage()
      } else {
        shareData.showShare = true
        shareUtilApi.qwShare(function() {
          shareData.showShare = false
        })
      }
    })
    return false
  }
  if (type == "wx") {
    shareData.showShare = true
    shareData.show = false
  }
}
function hideDialogList() {
  shareData.showDAOhang = false
}
function hideShare() {
  shareData.shareConfig = props.shopshare
  shareData.isQW = false
  shareData.showShare = false
  shareData.showDAOhang = false
  if (UA.isApp) {
    shareUtilApi.appShare(shareData.shareConfig)
  } else {
    shareUtilApi.changeWxShareConfig(shareData.shareConfig)
  }
}
function creatQrCode() {
  if (shareData.isQW) {
    return false
  }
  if (shareData.qrcodeCopy) {
    shareData.qrcodeCopy.clear()
  }
  //防止dialog首次加载不出二维码 在dialog上加@opened 回调
  shareData.qrcodeCopy = new QRCode(qrCodeUrlCopy.value, {
    text: shareData.rwmqrcodeurl,
    width: 400,
    height: 400,
    colorDark: "#000000",
    colorLight: "#ffffff",
    correctLevel: QRCode.CorrectLevel.H,
  })
  nextTick(() => {
    getPoster()
  })
}
const qrCodeContainer1 = ref(null)
function getPoster() {
  shareData.qwUrl = ""
  shareUtilApi.getImg(qrCodeContainer1.value).then((imgUrl) => {
    shareData.qwUrl = imgUrl
  })
}
function onCopy(e) {
  commonShareFun({
    item: shareData.shareConfig,
    type: 'fzlj'
  })

  hideDialogList()
  Toast("复制成功")
}
function onError(e) {
  hideDialogList()
  Toast("复制失败")
}
function saveImg() {
  const targetDom = qrCodeContainer1.value
  shareUtilApi.saveImg(targetDom, () => {
    Toast("图片已保存")
    shareData.show = false
  })
}
</script>
<style lang="scss" scoped>
@import "@/styles/minix.scss";
$px: 3px;

.qrcode {
  width: 200px;
  height: 200px;
  margin: 0 auto 20px;

  :deep(img) {
    width: 200px !important;
    height: 200px !important;
  }
}

.van-dialog {
  width: 260px;
  padding: 15px 0;
}

.qrCodeContainer {
  width: 260 * $px;
  padding-bottom: 15 * $px;
}

.copyQRCodeTitle,
.copyQRCodeAddress {
  font-size: 16 * $px;
  display: flex;
  width: 200 * $px;
  margin: 0 auto;
  padding-top: 17 * $px;

  span {
    width: 100%;
    text-align: center;
    line-height: 17 * $px;
    align-self: center;
    font-weight: 500;
    color: #000000;
  }
}

.copyQRCodeAddress {
  padding: 5 * $px 0 12 * $px;

  span {
    font-size: 10 * $px;
    color: #959595;
    line-height: 13.5 * $px;
  }
}

.imgcontainer {
  width: 200 * $px;
  height: 160 * $px;
  margin: 0 auto;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;

  img {
    max-height: 160 * $px;
    display: block;
    margin: 0 auto;
    max-width: 200 * $px;
    height: auto;
  }
}

.wx-guide {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 3000;
  background: rgba(0, 0, 0, 0.7);

  img {
    width: 200px;
    position: absolute;
    top: 100px;
    left: 88px;
    z-index: 3001;
  }
}

.copyQRCodeImg {
  height: 55 * $px;
  width: 55 * $px;
  float: right;
  margin-top: 23*$px;

  :deep(canvas),
  :deep(img) {
    width: 55 * $px !important;
    height: 55 * $px !important;
  }
}

.sharedialog {
  .posterContainer {
    width: 100%;

    img {
      width: 100%;
    }
  }

  .qrTitle {
    display: flex;
    height: 60px;

    span {
      width: 100%;
      text-align: center;
      line-height: 30px;
      align-self: center;
      @include ellipsis($clamp: 2);
    }
  }

  .icon_container {
    text-align: center;
    font-size: 55px;
    padding-top: 20px;

    ul {
      display: flex;

      li {
        flex: 1;
      }
    }

    span,
    button {
      font-size: 40px;
      border: none;
      background: none;
      padding: 0;
      display: block;
      margin: 0 auto 10px;

      &:last-child {
        font-size: 14px;
      }
    }
  }

  .btn_container {
    text-align: center;
    padding-bottom: 20px;
  }

  a {
    display: inline-block;
    width: 108px;
    height: 32px;
    line-height: 32px;
    margin: 22px 9px 0 9px;
    border-radius: 16px;
    background-image: linear-gradient(to right, #5fc8ff, #8372fe);
    font-size: 14px;
    color: #fff;
    text-align: center;

    &.bt_save {
      background: #f2f2f1 !important;
      color: #333;
      width: 90px;
    }

    &.bt_tip {
      width: 150px;
      font-weight: 500;
    }
  }
}

.copyQRCode {
  position: fixed;
  // top:0;
  // background: #fff;
  top: -10000 * $px;
}

.shareContent {
  width: 200 * $px;
  margin: 0 auto;
  padding: 13.5 * $px 7.5 * $px 12.5 * $px 10.5 * $px;

  .shopShare {
    height: 55 * $px;
    line-height: 55 * $px;
  }

  .goodsPrice {
    font-size: 12 * $px;
    color: #fa4343;
    font-weight: 600;

    .text-line-through {
      font-weight: normal;
      text-decoration-line: line-through;
      font-size: 12* $px;
      color: #ccc;
    }
  }

  .goodsName {
    font-size: 12 * $px;
    font-weight: 500;
    color: #222222;
    margin: 8 * $px 0 8.5 * $px;
    text-align: left;
    text-align: justify;
    width: 115 * $px;
  }

  .tip {
    font-size: 10 * $px;
    font-weight: 400;
    color: #959595;

    .iconfont {
      display: inline-block;
      font-size: 14 * $px;
      transform: translate(-2 * $px, 2 * $px);
    }
  }
}
</style>
