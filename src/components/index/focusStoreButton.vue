<template>
  <span class="wrapper" @click="handlerClick">
    <!-- <span class="flash"></span>
    <span class="text">关注店铺</span> -->
    <img src="~@/assets/my/anniu.gif" alt="">
  </span>
</template>

<script setup>
import {defineEmits} from 'vue'
const emit = defineEmits(['handlerClick'])
function handlerClick(){
  emit('handlerClick')
}
</script>

<style lang="scss" scoped>
.wrapper {
            position: relative;
            color: #d2bc88;
            display: inline-flex;
            background: #7b7876;
            align-items: center;
            overflow: hidden;
            justify-content: center;
            border-radius: 10px;
            top:-4px;
            img{
              width:70px;
            }
        }

        .flash {
            position: absolute;
            top: 0;
            left: 100%;
            width: 30px;
            height: 100%;
            background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0)0%, rgba(255, 255, 255, 0.5)50%, rgba(255, 255, 255, 0)100%);
            transform: skewX(-30deg);
            animation: cross 2s ease-in-out;
            animation-iteration-count: infinite
        }

        @keyframes cross {
            from {
                left: -100%;
            }

            to {
                left: 100%;
            }
        }
</style>