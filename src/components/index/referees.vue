<template>
  <van-notice-bar v-if="debuggerTrue" mode="link">
    当前计件人信息(只限测试环境) : {{ tel }}
    <br>
  </van-notice-bar>
</template>

<script setup>
import {defineProps,ref,} from 'vue'
const props = defineProps({
  tel:{type:String,default:()=>{
    return ""
  }}
})
const debuggerTrue = ref(null)
if(location.origin.indexOf("localhost") !== -1 || location.origin.indexOf("staff") !== -1 || location.origin.indexOf("grey") !== -1){
  debuggerTrue.value = true
}else{
  debuggerTrue.value = false
}
</script>

<style>

</style>
