<template>
  <p :class="(props.priceClass?props.priceClass:'price')">
    <template v-if="typeList['discountPrice'] === props.priceFrom">
      <span>
        <em v-if="props.showSymbol">￥</em>
        {{
          (props.shopPrice / 100).toFixed(2)
        }}
      </span>
      <span class="text-line-through">
        {{ (props.priceSection / 100).toFixed(2) }}
      </span>
    </template>
    <template v-if="typeList['rangePrice'] === props.priceFrom">
      <span>
        <em v-if="props.showSymbol">￥</em>
        {{ (props.shopPrice / 100).toFixed(2) }}-{{ (props.priceSection / 100).toFixed(2) }}
      </span>
    </template>
    <template v-if="typeList['singlePrice'] === props.priceFrom">
      <em v-if="props.showSymbol">￥</em>
      {{ (props.shopPrice / 100).toFixed(2) }}
    </template>
  </p>
</template>
<script setup>
import {typeList} from "@/utils/data-dictionary"
import {reactive,defineProps} from "vue"
const props = defineProps({
  //价格形式（1：一口价 2：价格区间 3：显示折扣价）
  //当 priceFrom =1 时，价格用 shopPrice;当 priceFrom =2 或 3 ，是有两个价格的
  priceFrom: {
    type:[String,Number],
    default: null
  },
  shopPrice:{//shopPrice 代表数值小的 
    type:[String,Number],
    default: null
  },
  priceSection :{//priceSection 代表数值大的
    type:[String,Number],
    default: null
  },
  priceClass:{ //价格样式
    type:String,
    default: ""
  },
  showSymbol:{//展示人民币标识
    type:Boolean,
    default:true
  }
})
</script>
<style lang="scss" scoped>
  // @import "@/styles/minix.scss";
  $px: 3px;
  .text-line-through {
    font-weight: normal;
    text-decoration-line: line-through;
  }
  .goods-price {
    span{
      line-height: 11.25px!important;
      display: inline-block;
    }
  }
  .price {
    font-size: 13px;
    color: #ed2668;
    height: 24px;
    line-height: 24px;
    font-weight: bold;
    em {
      font-size: 11px;
      margin-right: -5px;
    }
    .text-line-through {
      font-size: 10px;
      color: #ccc;
    }
    &.pricegrey {
      color: #ccc;
      span:first-child {
        color: #ed2668;
      }
    }
  }
  .numColor .text-line-through{
    font-size: 12px;
    color: #ccc;
  }
  //分享海报专用
  .goodsPrice .text-line-through {
    // font-size: 12* $px;
    font-size: 4* $px;
    color: #ccc;
  }
  
</style>
