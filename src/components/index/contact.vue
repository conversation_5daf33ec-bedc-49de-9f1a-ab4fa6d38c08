<template>
  <div>
    <div :class="['vip',contactData.member == '1' ? 'viped' : '', contactData.ToggoleAddress ? 'addressShow' : '',contactData.guanZhuDianPuIcon?'hide-vip':'']">
      <div
        class="card n4"
        :class="{
          bigbg:
            shopOpenStatus === 5 || shopOpenStatus === 6 || shopOpenStatus === 2
        }"
      >
        <!-- 移动logo -->
        <div class="kj">
          <img src="~@/assets/index_normal/<EMAIL>" alt="" />
          <span v-if="!contactData.zaiXianKanDianIcon" @click="handleOnlineWatchShop">在线看店</span>
        </div>

        <!-- 标题描述 -->
        <div class="textCenter">
          <div class="pongetitle" style="display:flex;" :class="{ bottom1: shopOpenStatus > 1 }">
            <span>{{ contactData.shopname }} </span>
            <img
              v-if="props.showShare"
              style="position:relative;"
              src="~@/assets/index_normal/shopshare.png"
              class="shopshare"
              @click="shareShop()"
            />
          </div>
          <div class="desc">
            <div v-if="telNumber && !contactData.boDaDianHuaIcon" class="tel">
              <a :href="telNumber" @click="insertCodeGoods('phone')">
                <img src="~@/assets/index_normal/jajsdj.png" alt="" />
              </a>
            </div>
            <div v-if="contactData.shopAddress" :class="['icon_address']" @click="handeAddress">
              <img src="~@/assets/index_normal/location.png" alt="" />
              <span ref="addressTextReally" class="addressTextReally">{{ contactData.shopAddress }}</span>
            </div>
            <span ref="addressTextSpuriousWhite" class="addressTextSpuriousWhite">{{ contactData.shopAddress }}</span>
            <span ref="addressTextSpurious" class="addressTextSpurious">{{ contactData.shopAddress }}</span>
          </div>
          <div v-if="shopOpenStatus > 1" class="businessHour" @click="switchShowPanel">
            <div class="businesscontent">
              <!-- 5营业中 6休息中 1不显示 2关厅 -->
              <img v-if="shopOpenStatus === 5" src="~@/assets/index_img/shop_open.png" alt="" />
              <img v-if="shopOpenStatus === 6" src="~@/assets/index_img/shop_close.png" alt="" />
              <img v-if="shopOpenStatus === 2" src="~@/assets/index_img/shop_close_grey.png" alt="" />
              <span>
                {{
                  shopOpenStatus === 5
                    ? "营业中"
                    : shopOpenStatus === 6
                      ? "休息中"
                      : shopOpenStatus === 2
                        ? "关厅"
                        : ""
                }}
              </span>
              <span v-if="shopOpenStatus === 5 || shopOpenStatus === 6" style="position:relative">
                {{ contactData.currentHour }}
                <Icon name="arrow-left" class="arrow-down" />
                <div
                  v-if="(shopOpenStatus === 5 || shopOpenStatus === 6) && contactData.showPanel"
                  class="panel"
                  :class="{ panelHide: contactData.showPanel }"
                >
                  <div v-for="(item, index) in businessHours" :key="index">
                    <span class="panelLeft">{{ Day[index] }}</span> :
                    <span class="panelRight">{{ item }}</span>
                  </div>
                </div>
              </span>
            </div>
          </div>
        </div>

        <!-- 按钮列表 -->
        <div class="btnlist">
          <div v-if="contactData.member == '1' && !contactData.fenSiFuLiIcon" class="cancel" @click="handlerfans">
            <img src="@/assets/index_normal/guanzhudianpu.png" alt="" />
            <span>粉丝福利</span>
          </div>
          <div v-if="props.tplId !== '3' && !contactData.weiXinKeFuIcon" class="wx" @click="getCustomerCode(props.floorData.sharingCode)">
            <img src="@/assets/index_img/wx.png" alt="" />
            <span>微信客服</span>
          </div>
        </div>
      </div>
      <div v-if="(!contactData.member == '1') && !contactData.guanZhuDianPuIcon" class="vipedTop">
        <span class="icon_vip"></span>
        <span class="vipContent">关注店铺 粉丝福利专享</span>
        <FocusStoreButton @handlerClick="AddMemberInfo(contactData.shopID)" />
      </div>
    </div>
    <div v-if="contactData.info" class="memberinfo">
      <div class="infocard">
        <div class="left">
          <div class="pic">
            <img class="shoplogo" src="@/assets/index_img/vipicon_infohead.png" />
          </div>
          <p>店铺会员</p>
        </div>
        <div class="right">
          <p>{{ getPassPhone(contactData.memberphone) }}</p>
          <p>NO.{{ contactData.memberno }}</p>
          <p>{{ contactData.shopname }}</p>
        </div>
      </div>
      <div class="birthday">
        <Cell title="生日" @click="showPopup">
          <template v-if="contactData.birthDate">
            {{ dateFormat(contactData.birthDate,"yyyy-MM-dd") }}
          </template>
          <template v-else>
            请填写出生年月日
          </template>
        </Cell>
        <Popup v-model="contactData.show" position="bottom" :overlay="true">
          <DatetimePicker
            v-model="contactData.currentDate"
            type="date"
            title="选择年月日"
            :min-date="contactData.minDate"
            :max-date="contactData.maxDate"
            @confirm="onConfirm"
            @cancel="contactData.show = false"
          />
        </Popup>
      </div>
      <div class="xieyi">
        <Checkbox v-model="contactData.checked" label-disabled>
          同意<span class="agreementStyle" @click="contactData.showhyxy = true">《会员授权协议》</span>
        </Checkbox>
        <Popup v-model="contactData.showhyxy" round closeable position="bottom" :style="{ height: '60%' }" class="ss">
          <Authorization />
        </Popup>
      </div>
      <a href="javascript:void(0);" class="btlqhy" @click="AddMemberInfo()">领取会员</a>
    </div>

    <MemberGifts ref="memberGiftsRef" />
  </div>
</template>

<script setup>
import { mapGetters } from "vuex"
import { dateFormat } from "@/filters"
import insertCode from "@/utils/insertCode"
import VipApi from "@/api/vip"
import Vue,{ref,reactive,defineProps,inject,getCurrentInstance,computed,watch, onMounted,defineEmits} from "vue"
import {
  DatetimePicker,
  Toast,
  Popup,
  Checkbox,
  CheckboxGroup,
  Dialog,
  Icon,
  Cell
} from "vant"
import { getAllSearchParamsArray, setSearchParamsArray,getPassPhone, getCookie } from "@/utils/utils"
import Authorization from "@/components/index/authorizationAgreement.vue"
import MemberGifts from "@/components/index/membergifts.vue"
import EventBus from "@/api/eventbus"
import shopAPI from "@/api/shop"
import UA from "@/utils/ua"
import PrecisionMarketing from "@/model/precisionMarketing"
import {setScrollTop} from "@/utils/utils"
import FocusStoreButton from "./focusStoreButton.vue"
import lodash from 'lodash'
Vue.use(DatetimePicker)
  .use(Popup)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Dialog)

let getSon = inject("getSon")
//获取营业时间
function getInject(){
  businessHours.value = getSon.businessHoursfn()
  getCurrentHour()
}
onMounted(()=>{
  getInject()
})
// 获取店铺营业时间
let businessHours  = ref("")
// 获取店铺营业状态
const shopOpenStatus = computed(()=>{
  if(getSon && getSon.shopOpenStatusfn){
    return getSon.shopOpenStatusfn()
  }
  return {}
})
const myShowData = computed(()=>{
  if(getSon && getSon.myShowDatafn){
    return getSon.myShowDatafn()
  }else{
    return {}
  }
})
const props = defineProps({
  logined: {
    type: [Boolean, Function],
    default: null
  },
  preview: {
    type: String,
    default: null
  },
  floorId: {
    type: String,
    default: null
  },
  dataList: {
    type: Object,
    default: null
  },
  cardnewvip: {
    type: String,
    default: null
  },
  floorData: {
    type: Object,
    default: null
  },
  showShare: {
    type: Boolean,
    default: false
  },
  tplId: {
    type: String,
    default: null
  }
})
const emit = defineEmits(["handleOnlineWatchShop"])
const contactData = reactive({
  shopname: "店铺名称",
  shopAddress: "店铺地址",
  member: "",
  memberphone: "139 1234 5676",
  memberno: "139139139",
  info: false,
  date: "",
  show: false,
  showhyxy: false,
  showbmxy: false,
  minDate: new Date("1960-01-01"),
  maxDate: new Date(2025, 10, 1),
  currentDate: null,
  birthDate: null,
  staffId: "",
  checked: true,
  hyxy: "",
  hybmxy: "",
  rule: 0,
  currentHour: null,
  showPanel: false,
  ToggoleAddress: false,
  /* 是否可以点击地址展开 */
  isAddressClick: false,
  fenSiFuLiIcon:1, //配置显示粉丝福利
  weiXinKeFuIcon:1,//配置显示微信客服
  boDaDianHuaIcon:1,//配置显示拨打电话
  zaiXianKanDianIcon:1,//配置显示在线看店
  guanZhuDianPuIcon:1,//配置显示关注店铺
})
const Day= ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
const getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null
let pageInfo = {pageInfo:{}},user
if(proxy){
  pageInfo = proxy.$store.getters.pageInfo
  user = proxy.$store.getters.user
  contactData.shopname = pageInfo.pageInfo.shortName
  contactData.shopAddress = pageInfo.pageInfo.address
  contactData.shopID = pageInfo.pageInfo.shopId
}

/* 店铺手机 */
const telNumber = computed(()=>{
  let keFuPhone = pageInfo.pageInfo.keFuPhone
  let telNumber = pageInfo.pageInfo.telNumber

  if (keFuPhone) {
    return "tel:" + keFuPhone
  } else {
    return "tel" + telNumber
  }
})
watch(()=>props.logined,(val)=>{
  if (val && proxy) {
    contactData.memberphone =
      (user && user.userInfo && user.userInfo.UserName) ||
      user.userName
    ismember(pageInfo.pageInfo.shopId)
  }
},{immediate: true})
const addressTextSpuriousWhite = ref(null)
const addressTextSpurious = ref(null)
const addressTextReally = ref(null)
watch(()=>contactData.shopAddress,(val)=>{
  let addressTextReallyClientWidth = (addressTextReally.value && addressTextReally.value.clientWidth) || 0
  // 如果没有找到对应的dom节点, 后续不执行
  if (!(addressTextSpuriousWhite.value)) {
    return
  }
  addressTextSpuriousWhite.value.style.width = `${addressTextReallyClientWidth}px`
  addressTextSpurious.value.style.width = `${addressTextReallyClientWidth}px`

  let atswHeight = addressTextSpuriousWhite.value.clientHeight
  let atsHeight = addressTextSpurious.value.clientHeight

  if (atsHeight > atswHeight) {
    contactData.isAddressClick = true
  } else {
    contactData.isAddressClick = false
  }

})
watch(()=>contactData.currentDate,(val)=>{
  contactData.birthDate = val
})

watch(myShowData,(val)=>{
  if(val){
    contactData.fenSiFuLiIcon = val.fenSiFuLiIcon
    contactData.boDaDianHuaIcon = val.boDaDianHuaIcon
    contactData.weiXinKeFuIcon = val.weiXinKeFuIcon
    contactData.zaiXianKanDianIcon = val.zaiXianKanDianIcon
    contactData.guanZhuDianPuIcon = val.guanZhuDianPuIcon
  }
},{immediate:true})

if (user && user.userInfo) {
  contactData.memberphone = user.userInfo.UserName
  contactData.rule = user.userInfo.RuleId
} else {
  contactData.memberphone = localStorage.getItem("userName")
}

/* 跳转粉丝页面 */
function handlerfans() {
  if(!props.logined){
    openloginSelf(null,true)
    return false
  }
  proxy.$router.push({
    path: '/vermicelli/index.html',
    query: {
      shopId: contactData.shopID,
      tplId: lodash.get(pageInfo, 'pageInfo.tplId', 4)
    }
  })
}
function openloginSelf(callback,showCancle) {
  return getSon.openlogin()(callback,showCancle)
}
/* 点击地址展开收起 */
function handeAddress() {
  if (contactData.isAddressClick) {
    contactData.ToggoleAddress = !contactData.ToggoleAddress
  }
}
const memberGiftsRef = ref(null)
function CouponCheck() {
  memberGiftsRef.value.CouponCheck(contactData.shopID)
}
function ShowCouponPop() {
  memberGiftsRef.value.ShowCouponPop(contactData.shopID)
}
/* 点击在线看店 */
function handleOnlineWatchShop() {
  // contactData.insertCodeGoods('storephotosbar')
  emit("handleOnlineWatchShop")
}
function closevip() {
  contactData.info = false
  document.body.style.overflow = "auto"
}
function showPopup() {
  setScrollTop()
  contactData.currentDate = new Date(contactData.birthDate)
  contactData.show = true
}
function onConfirm(date) {
  contactData.value = date
  contactData.show = false
}
function switchShowPanel() {
  contactData.showPanel = !contactData.showPanel
}
function cancelDialog() {
  contactData.insertCodeGoods("vip_cancelvip")
  Dialog.confirm({
    title: "温馨提示",
    message: "确定取消VIP会员?",
    confirmButtonColor: "#5099fe",
    confirmButtonText: "确认取消",
    cancelButtonText: "我再想想"
  }).then(() => {
    withdeaw()
  })
}
function ismember(shopID) {
  VipApi.IsMember({
    shopId: shopID
  }).then(res => {
    if (res.code == 0) {
      contactData.member = res.data.member
      proxy.$store.commit("SET_ISMEMBER", res.data.member)
      if (res.data.member == 1) {
        CouponCheck() //查询入会礼可用券
      }
    }
  })
}
function MemberInfo() {
  if (contactData.member) {
    return false
  }
  if (contactData.preview) {
    return
  }
  insertCodeGoods("vip_joinvip")
  VipApi.UserInfo().then(res => {
    if (res.code == 0) {
      setScrollTop()
      contactData.info = true
      document.body.style.overflow = "hidden"
      contactData.birthDate = res.data.birthday
        ? res.data.birthday
        : contactData.birthDate
    } else {
      // Toast('您不能领取')
      closevip()
    }
  })
}
function AddMemberInfo() {
  if(!props.logined){
    openloginSelf(null,true)
    return false
  }
  insertCodeGoods("add")
  if (contactData.dataList && contactData.dataList.floor) {
    contactData.staffId = contactData.dataList.floor.recommendCode
  }
  VipApi.AddMemberInfo({
    shopId: contactData.shopID,
    staffId: contactData.staffId
  }).then(res => {
    if (res.code == 0) {

      if (!(res.data.shopId && res.data.memberId)) {
        // Toast("您已经是会员了")
        Toast("您已经关注了")
      } else {
        // Toast("领取成功")
        Toast("关注成功")
      }

      ismember(contactData.shopID)
      ShowCouponPop() //弹出入会礼弹窗
    } else if (res.code == 110101) {
      Toast(res.message)
    } else {
      Toast("领取出问题了，请再试一次吧")
    }
  })
}

function withdeaw() {
  VipApi.withdeaw({
    shopId: contactData.shopID
  }).then(res => {
    if (res.code) {
      Toast(res.message)
    } else {
      ismember(contactData.shopID)
    }
  })
}
function insertCodeGoods(key) {
  let dcs_id = key
  insertCode("yd_index_" + contactData.shopID + "_" + dcs_id)
}

/* precisionMarketing 代表是否是精准营销商品的分享 */
function shareShop(obj, precisionMarketing, item = {}) {
  if (contactData.preview) return false
  let allParams = getAllSearchParamsArray(location.href)
  let ac_id = allParams["WT.ac_id"] || getCookie("ac_id")
  let sharingShopUrl = props.floorData.sharingShopUrl
  let isExecuteNow = UA.isWechatWork

  // 如果是精准营销分享,加入精准营销标识
  if (precisionMarketing) {
    sharingShopUrl = PrecisionMarketing.addShareMarketing(sharingShopUrl)
  }
  // 加入店铺分享标识
  ac_id='SHOP_SHARE'
  // obj, 有机会删掉, 代码review
  EventBus.$emit(
    "openShareDialog",
    "shop",
    {
      ...item,
      url: ac_id
        ? setSearchParamsArray(sharingShopUrl, { "WT.ac_id": ac_id })
        : sharingShopUrl
    },
    isExecuteNow,
    obj,
  )
  insertCodeGoods("sharestore")
}
function getCustomerCode(sharingCode) {
  insertCodeGoods("managerwecom")
  if (!props.floorData.sharingQrCode || !sharingCode) {
    Toast("该功能正在开放中，敬请期待")
    return
  }
  shopAPI.getCustomerCode({ sharingCode }).then(r => {
    if (r.code) {
      Toast(r.message)
    } else {
      if (r.data.contactQrcode) {
        EventBus.$emit("openShareDialog", "service", r.data.contactQrcode)
      } else if (location.origin.indexOf("grey.touch.10086.cn") && r.data.qrCode) {
        EventBus.$emit("openShareDialog", "service", r.data.qrCode)
      } else {
        Toast("该功能正在开放中，敬请期待")
      }
    }
  })
}
function getCurrentHour() {
  if (businessHours.value) {
    let week = new Date().getDay(),
      str = ""
    if (week > 0) {
      str = businessHours.value[week - 1]
    } else {
      str = businessHours.value[6]
    }
    contactData.currentHour = str
  } else {
    contactData.currentHour = ""
  }
}
</script>

<style lang="scss" scoped>
.hidescroll {
  overflow: hidden;
}

:deep(.van-overlay),
.memberinfo {
  height: 101vh;
}

.membergifts {
  width: 315px;
  background: linear-gradient(139deg, #ff6b85 5%, #ff7ee4 98%);
  border-radius: 8px;
  overflow-y: inherit;
  padding-bottom: 86px;

  .title {
    height: 67px;
    line-height: 67px;
    text-align: center;

    img {
      height: 20px;
    }
  }

  .quanclose {
    position: absolute;
    bottom: -54px;
    left: 140px;
    right: 140px;

    img {
      width: 35px;
      height: 54px;
    }
  }

  .quanbox {
    max-height: 214px;
    overflow: hidden;
    clear: both;

    .quan {
      width: 272px;
      height: 86px;
      background: url(~@/assets/index_img/quan_bg.png) 0 0 no-repeat;
      background-size: 100% 100%;
      margin: 0 auto 21px;
      line-height: 86px;
      text-align: center;
      color: #cf4765;
      position: relative;

      .quanprice {
        width: 94px;
        float: left;
        font-weight: 600;
        font-size: 28px;
        overflow: hidden;
        /*超过部分不显示*/
        text-overflow: ellipsis;
        /*超过部分用点点表示*/
        white-space: nowrap;
        /*不换行*/
      }

      .quanname {
        width: 177px;
        float: right;
        padding: 0 10px;
        font-size: 17px;
        overflow: hidden;
        /*超过部分不显示*/
        text-overflow: ellipsis;
        /*超过部分用点点表示*/
        white-space: nowrap;
        /*不换行*/
      }

      .quantype {
        display: block;
        width: 44px;
        height: 34px;
        position: absolute;
        top: 0px;
        right: 5px;

        &.type1 {
          background: url(~@/assets/index_img/quan_got.png) 0 0 no-repeat;
          background-size: 100% 100%;
        }

        &.type2 {
          background: url(~@/assets/index_img/quan_end.png) 0 0 no-repeat;
          background-size: 100% 100%;
        }

        &.type3 {
          background: url(~@/assets/index_img/quan_empty.png) 0 0 no-repeat;
          background-size: 100% 100%;
        }
      }
    }
  }

  .bgwhite {
    width: 315px;
    height: 86px;
    background: #ffffff;
    border-radius: 0px 0px 8px 8px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;

    a {
      display: block;
      width: 266px;
      height: 46px;
      line-height: 46px;
      background: linear-gradient(90deg, #ffb72e, #ff4848 100%);
      border-radius: 26px;
      box-shadow: 0px 2px 11px 0px rgba(255, 0, 0, 0.28);
      font-size: 16px;
      color: #ffffff;
      font-weight: 400;
      margin: 19px auto 0;

      &.disable {
        background-image: linear-gradient(90deg, #e6e6e6 0%, #ababab 100%);
        box-shadow: 0 2px 11px 0 rgba(161, 161, 161, 0.28);
      }
    }
  }
}

.icon_address {
  font-weight: normal !important;
}

.vip {
  border-radius: 9px;
  margin-bottom: 1px;
  position: relative;
  margin-top: 43px;
  height: auto;
  margin-bottom: 0 !important;
  &.hide-vip{
    margin-top: 15px;
  }
  .card {
    background: url(~@/assets/index_normal/vipbg_go.da2a3436.png) 0 0 no-repeat;
    width: 100%;
    border-radius: 0 0 8px 8px;
    margin: 0 auto;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    height: auto !important;
    padding: 15px 10px;
    background-size: 120% 120%;
    background-position: 20%;
    border-radius: 10px;

    img.shoplogo {
      width: 44px;
      height: 44px;
      float: left;
      background: #fff;
      border-radius: 9px;
      margin: 6px 0 0 6px;
    }

    .shopGo {
      float: left;
      font-size: 10px;
      padding-left: 7px;
      padding-top: 57px;

      span {
        padding: 3px 5px;
        background: #0185d1;
        color: #fff;
        border-radius: 8px;
      }
    }

    .kj {
      width: 70px;
      height: 70px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: #fff;
      border-radius: 8px;
      padding: 4px 4px;
      box-sizing: border-box;

      img {
        width: 40px;
        height: 40px;
        display: block;
      }

      span {
        background: #06acea;
        color: #fff;
        border-radius: 10px;
        font-size: 10px;
        padding: 4px 8px;
        white-space: nowrap;
        margin-top: 4px;
      }
    }

    .btnlist {
      display: flex;
      align-items: center;
      margin-left: 20px;
      flex-direction: column;

      div {
        display: flex;
        justify-content: center;
        margin-bottom: 10px;
        width: 80px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 20px;
        padding: 1px 0;

        &.wx {
          background: #09bb07;
        }

        img {
          width: 20px;
        }

        &.cancel {
          background: #fdad00;

          img {
            // width: 20px;
            width: 15px;
          }
        }

        &:last-child {
          margin-bottom: 0;
        }



        span {
          color: #fff;
          font-size: 10px;
          margin-left: 3px;
          line-height: 23px;
        }
      }
    }

    .businessHour {
      position: relative;
      margin-top: 5px;

      img {
        height: 15px;
        width: 24px;
        vertical-align: bottom;
      }

      .businesscontent {
        width: 170px;
        height: 20px;
        // opacity: 0.8;
        font-size: 10px;
        font-weight: 400;
        text-align: left;
        color: #000000;
        position: relative;

        .arrow-down {
          transform: translate(0, 2px) rotate(-90deg);
        }

        &>span {
          margin-left: 5px;
        }
      }
    }
    .panel {
      position: absolute;
      z-index: 10;
      top: 60%;
      right: -12px;
      padding: 15px 0;
      width: 120px;
      height: 155px;
      // background: linear-gradient(0deg, #dfe1fb 0%, #ece4f5 100%);
      background: url("~@/assets/index_normal/panel_bg.png") no-repeat;
      background-size: contain;
      border-radius: 8px;
      // box-shadow: 0px -4px 9px 0px rgba(111, 114, 129, 0.22);
      font-size: 10px;
      font-weight: 400;
      text-align: left;
      padding-left:20px;
      color: #000000;
      line-height: 18px;

      div {
        // text-align: center;
        height: 14.5%;
      }
    }

    .shopInfoRight {
      width: 74px;
      padding-left: 5px;
      margin-left: 0;
      float: right;

      // add
      width: auto;
      display: flex;
      flex-direction: column;
      justify-content: center;
      height: 100%;
      padding-bottom: 5px;
      padding-right: 10px;

      .cancel,
      .wx {
        width: 80px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 20px;

        span {
          color: #fff;
          font-size: 10px;
          margin-left: 3px;
          line-height: 23px;
        }

        img {
          width: 15px;
        }
      }

      .wx {
        background: #09bb07;
      }

      .cancel {
        background: #f60;
        margin-bottom: 5px;
      }

      p {
        padding-left: 0;
      }
    }

    .textCenter {
      flex: 1;
      padding-left: 9px;
      min-width: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .pongetitle {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        span {
          // flex: 1;
          min-width: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 15px;
          color: #222;
          font-weight: bold;
          line-height: 22px;
        }

        img {
          width: 18px;
          // position: static;
          position: absolute;
          height: 18px;
          margin-left: 8px;
        }
      }

      .desc {
        display: flex;
        align-items: center;

        .icon_address {
          display: flex;
          align-items: center;
          flex: 1;
          min-width: 0;

          img {
            width: 14px;
            margin-right: 5px;
          }

          span {
            flex: 1;
            min-width: 0;
            color: #222;
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 22px;
          }
        }

        .tel {
          margin-right: 6px;

          a {
            display: flex;
            align-items: center;

            img {
              width: 18px;
            }
          }
        }

      }
    }
    .shopinfo {
      float: left;
      // margin-left:55px;
      line-height: 24px;
      display: flex;
      flex-direction: column;

      .shop_name,
      .icon_address,
      p {
        padding-left: 9px;
        // height:24px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        /*设置p元素最大4行，父元素需填写宽度才明显*/
        text-overflow: ellipsis;
        overflow: hidden;
        -webkit-box-orient: vertical;
        word-wrap: break-word;
        word-break: break-all;
        font-size: 14px;
        font-weight: bold;
        color: #383838;
        line-height: 29px;
        max-width: 220px;
      }

      .shop_name {
        padding-right: 20px;
        position: relative;
        float: left;
      }

      .icon_address {
        clear: both;
        font-size: 12px;
      }

      .other2 {
        display: flex;
        align-items: center;
        padding-left: 10px;

        .icon_address {
          display: flex;
          align-items: center;
          padding-left: 8px;

          img {
            width: 15px;
            margin-right: 3px;
          }
        }

        .tel {
          a {
            display: block;

            img {
              width: 13px;
              height: 13px;
              display: block;
              color: #409eff;
            }
          }
        }
      }

    }

    .bottom1 {
      margin-bottom: 5px !important;
    }
  }

  .addressTextSpurious,
  .addressTextSpuriousWhite {
    position: absolute;
    z-index: -99;
    left: 0;
    top: 0;
    opacity: 0;
    font-size: 12px;
  }

  .addressTextSpuriousWhite {
    white-space: nowrap;
  }
  &.addressShow {
    .card {
      align-items: flex-start;

      .textCenter {
        .desc {
          align-items: flex-start;

          .icon_address {
            align-items: flex-start;
          }

          .icon_address span {
            white-space: inherit;
          }
        }
      }
    }
  }

  &.viped {
    margin-top: 15px;
    position: static;
    margin-bottom: 15px;

    .card {
      height: auto;
      background: linear-gradient(to right, #f7f1f1, #d7deff);
      border-radius: 10px 10px;
      position: relative;

      .vipExit {
        width: 50px;
        height: 50px;
        transform: rotate(45deg) translate(-38px, -5px);
        top: 0;
        left: 0;
      }
    }
  }

  padding-left: 12px;
  padding-right: 12px;
  width: 100%;



  .cardvip {
    background: url(~@/assets/index_img/vipnew_go.png) 240px 3px no-repeat #fff;
    background-size: 112px 49px;
  }
}

.memberinfo {
  width: 100%;
  height: 101vh;
  background: #fff;
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 10;
  padding: 60px 0 0;

  .infocard {
    width: 345px;
    height: 122px;
    background: url(~@/assets/index_img/vipinfocardbg.png) 0 0 no-repeat;
    background-size: 100% 100%;
    margin: 0 auto;

    .left {
      width: 108px;
      height: 122px;
      float: left;

      .pic {
        width: 73px;
        margin: 0 0 0 22px;
        padding-top: 8px;
        text-align: center;

        img {
          width: 73px;
          height: 83px;
        }
      }

      p {
        margin: 2px auto 0;
        text-align: center;
        font-size: 13px;
        color: #e7b65a;
      }
    }

    .right {
      width: 237px;
      float: left;

      p:nth-child(1) {
        padding-top: 18px;
        height: 48px;
        line-height: 30px;
        font-size: 22px;
        color: #a98f48;
      }

      p:nth-child(2) {
        padding-top: 9px;
        height: 29px;
        line-height: 20px;
        font-size: 14px;
        color: #e1be6d;
      }

      p:nth-child(3) {
        padding-top: 12px;
        height: 35px;
        line-height: 23px;
        font-size: 14px;
        color: #a38b53;
        background: url(~@/assets/index_img/vipicon_add.png) 0 15px no-repeat;
        background-size: 16px 15px;
        padding-left: 20px;
      }
    }
  }

  .birthday {
    height: 50px;
    width: 345px;
    margin: 15px auto 0;
  }

  .xieyi {
    height: 50px;
    padding-top: 15px;
    padding-left: 15px;
    color: #999999;
    font-size: 14px;

    .ss {
      padding: 40px;
    }
  }

  .btlqhy {
    display: block;
    width: 344px;
    height: 48px;
    line-height: 48px;
    text-align: center;
    background: linear-gradient(90deg, #e8cb88, #dbac51);
    border-radius: 24px;
    box-shadow: 0px 2px 4px 0px #f0e5ce;
    font-size: 16px;
    color: #ffffff;
    font-weight: 600;
    margin: 20px auto;
  }

  .agreementStyle {
    color: #409eff;
  }

  :deep(.van-cell) {
    border-bottom: 1px solid #f4f4f4;
  }
}

.icon_kefu {
  width: 60px;
  height: 18px;
  margin-top: 20px;
  background: url(~@/assets/index_normal/wqkf_btn.png) 0 0 no-repeat;
  background-size: contain;
}

.vipedTop {
  background: url(~@/assets/index_normal/viptop1.png) 0 0 no-repeat;
  background-size: contain;
  position: absolute;
  top: -21px;
  width: 315px;
  left: 31px;
  height: 25px;
  display: flex;
  padding: 7px 0 0 42px;
  color: #f0e2c2;

  .icon_vip {
    background: url(~@/assets/index_normal/guanzhu.png) 0 0 no-repeat;
    width: 16px;
    height: 16px;
    background-size: cover;
    margin-right: 5px;
    position: relative;
    top:-3px
  }

  .vipContent {
    font-size: 12px;
    margin-right: 15px;
  }

  .addMember {
    // width: 53px;
    width: 68px;
    height: 16px;
    background: url(~@/assets/index_normal/add_member.gif) -6px 0 no-repeat;
    margin-top: -2px;
    background-size: contain;
  }
}

</style>
