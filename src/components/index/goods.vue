<template>
  <div :class="{ 'mt7': floor.dec != '1' }" class="goods">
    <h1 v-if="floor.title && (goodslist && goodslist.length > 0 || configure)" class="goods-tt">
      {{ floor.title }}<span v-if="configure && (tplId == 2 || tplId == 4)">专区</span>
    </h1>

    <!-- 通过class名控制商品样式
    oneline2：一行两个上图下文
    oneline2 picleft：一行两个左图右文
    oneline2 picright：一行两个右图左文
    oneline3：一行三个
    twoline：列表
     :class="{'twoline':componentCode=='list',
               'oneline3':componentCode=='1line3',
               'oneline2':componentCode=='1line2',
               'picleft':floor.design=='3',
               'picright':floor.design=='2'}"
     -->
    <ul
      v-if="goodslist && goodslist.length"
      class="goods-list"
      :class="['cp-' + componentCode, 'design-' + floor.design]"
    >
      <li v-for="(item, key) in goodslist" :key="key" :class="{ 'shixiao': item.invalid == 1 && configure }">
        <a
          href="javascript:void(0)"
          @click="insertCodeGoods(key + 1, item.goodsId, item.goodsSource, item.goodsLink)"
        >
          <img :src="item.imgUrl" />
          <p class="title">
            {{ item.goodsName }}
          </p>
          <p class="subtitle">
            {{ item.subTitle }}
          </p>
          <PriceCom
            v-if="item.priceFrom"
            :price-from="item.priceFrom"
            :shop-price="item.shopPrice"
            :price-section="item.priceSection"
          />
          <p v-else class="price">
            <em>￥</em>
            {{ (item.price / 100).toFixed(2) }}
          </p>
        </a>
        <a
          v-if="configure"
          href="javascript:void(0);"
          class="item-close ac_remove_goods"
          @click="delGoods(item.adId)"
        ></a>
        <span v-if="!configure" class="share-goods" @click="openShare(item)"></span>
        <div class="item-shixiao-icon"></div>
      </li>
      <li v-if="configure" class="item-add" @click="openAddGoods(goodslist)"></li>
    </ul>
    <ul v-else-if="floor.title && configure" class="goods-list">
      <li class="item-add" @click="openAddGoods"></li>
    </ul>
    <Goodselector
      ref="goodsList"
      :tpl-id="tplId"
      :confirm="confirmCallback"
      :source="source"
      :goods-used-id="goodsUsedId"
      :goods-type="goodsType"
      :use-sort-type="true"
      :title="floor.title"
    />
  </div>
</template>
<script setup>
import EventBus from '@/api/eventbus.js'
import shopAPI from '@/api/shop.js'
import Goodselector from '@/components/index/goodselector.vue'
import insertCode from "@/utils/insertCode.js"
import UA from "@/utils/ua.js"
import { constant } from '@/config/index.js'
import PrecisionMarketing from '@/model/precisionMarketing.js'
import PriceCom from "@/components/index/price.vue"
import { ref, computed, defineProps, watch } from "vue"

const props = defineProps({
  items: {
    type: Array,
    default: () => {
      return []
    }
  },
  floor: {
    type: Object,
    default: () => {
      return {}
    }
  },
  componentCode: {
    type: String,
    default: null
  },
  moduleId: {
    type: Number,
    default: null
  },
  floorId: {
    type: String,
    default: null
  },
  floorSort: {
    type: [String, Number],
    default: null
  },
  selectorKey: {
    type: [String, Number],
    default: null
  },
  selectorSortKey: {
    type: [String, Number],
    default: null
  },
  preview: {
    type: String,
    default: null
  },
  configure: {
    type: String,
    default: null
  },
  goodsShare: {
    type: Function,
    default: (fn) => {
      return fn
    }
  },
  actId: {
    type: String,
    default: null
  },
  tplId: {
    type: String,
    default: null
  }
})
const showGoodsDialog = ref(false)
const lock = ref(false)
const lockAdId = ref(false)
const goodsList = ref()
const source = computed(() => {
  return props.floorSort == "90" ? 2 : 1
})
const goodsType = computed(() => {
  return props.selectorSortKey
})
const goodslist = computed(() => {
  let goodslist = []
  if (props.items && props.items.length > 0) {
    props.items.forEach(element => {
      if (element.goodsSource == 2) {//异业都显示
        goodslist.push(element)
        return
      }
      //1、o2oGoodsStatus 2 下线 3上线 运营位下线，店铺首页不展示商品
      //2、goodsStatus 51是上架 52商品下架，这两种状态店铺首页应该展示商品，可以进入详情页
      if ((element.o2oGoodsStatus == 2 || (element.goodsStatus != 51 && element.goodsStatus != 52)) && props.configure != 1) {
        return
      }

      // 如果是精准营销banner图,加入精准营销banner图标识
      let iopOperationPositionId = constant.H5iopOperationPositionId
      if (PrecisionMarketing.iswxapp) {
        iopOperationPositionId = constant.WXiopOperationPositionId
      }
      if (element.iopOperationPositionId == iopOperationPositionId) {
        return
      }

      goodslist.push(element)
    })
  }
  return goodslist
})
const goodsUsedId = computed(() => {
  let goodsIdInUse = []
  if (props.items && props.items.length > 0) {
    props.items.forEach(element => {
      goodsIdInUse.push(element.goodsId)
    })
  }
  return goodsIdInUse
})
function openShare(item) {
  let share = {
    ...item,
    title: item.goodsName,
    url: item.goodsLink,
    desc: item.subTitle || item.goodsLink,
    imgUrl: item.imgUrl,
    price: item.price
  }
  EventBus.$emit('openShareDialog', 'goods', share)
  if (UA.isWechatWork) {
    EventBus.$emit('share')
  }
}
function openAddGoods() {
  goodsList.value.data.showGoodsDialog = true
}
function confirmCallback(value) {
  let sort = 0
  if (props.items && props.items.length > 0) {
    sort = props.items[props.items.length - 1].sort + 1
  }
  let sendData = {
    actId: props.actId,
    goodsSource: 1,
    moduleId: props.moduleId,
    floorId: props.floorId,
    componentCode: props.componentCode,
    sort: sort
  }
  sendData.goodsList = []
  value.forEach(element => {
    if (this.floorSort == "90") {//异业商品
      sendData.goodsList.push({
        goodsName: element.goodsName,
        goodsId: element.goodsId,
        image: element.picture,
        price: element.minPrice,
        storeId: element.storeId
      })
    } else {
      sendData.goodsList.push({
        goodsId: element.goodsId
      })
    }
  })
  if (props.floorSort == "90") {//异业商品
    sendData.goodsSource = 2
  }
  shopAPI.addGoods(sendData).then(r => {
    EventBus.$emit('goodsChecked', r)
  })
  // EventBus.$emit('refresh'
}
function delGoods(adId) {
  if (lockAdId.value == adId || lock.value) return
  lock.value = true
  shopAPI.delGoods(adId).then(r => {
    EventBus.$emit('goodsChecked', r)
    lockAdId.value = adId
    lock.value = false
  })
}
function insertCodeGoods(key, goodsId, source, url) {
  if (props.preview) {
    return
  }
  let dcs_id = key + '_' + goodsId + '_' + source
  insertCode(props.floorId + "_goods_" + dcs_id, url)
}
</script>
<style lang="scss" scoped>
.goods-tt {
    height: 45px;
    line-height: 45px;
    font-size: 16px;
    color: #333333;
    padding-left: 39px;
    margin-bottom: 1px;
}

.goods {
    &:nth-child(5n+1) .goods-tt {
        background: url(~@/assets/index_img/icon_01.png) 15px 12px no-repeat #fff;
        background-size: 20px 20px;
    }

    &:nth-child(5n+2) .goods-tt {
        background: url(~@/assets/index_img/icon_02.png) 15px 12px no-repeat #fff;
        background-size: 20px 20px;
    }

    &:nth-child(5n+3) .goods-tt {
        background: url(~@/assets/index_img/icon_03.png) 15px 12px no-repeat #fff;
        background-size: 20px 20px;
    }

    &:nth-child(5n+4) .goods-tt {
        background: url(~@/assets/index_img/icon_04.png) 15px 12px no-repeat #fff;
        background-size: 20px 20px;
    }

    &:nth-child(5n+5) .goods-tt {
        background: url(~@/assets/index_img/icon_05.png) 15px 12px no-repeat #fff;
        background-size: 20px 20px;
    }
}

.goods-list {
    display: flex;
    // flex-direction: row;
    flex-flow: row wrap;

    li {
        list-style: none;
        position: relative;
        font-size: 13px;
        line-height: 20px;
        background: #fff;
        margin-bottom: 1px;

        p {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        p.title {
            font-size: 13px;
            color: #333333;
            height: 24px;
            line-height: 24px;
        }

        p.subtitle {
            font-size: 12px;
            color: #666666;
            height: 24px;
            line-height: 24px;
        }

        p.price {
            font-size: 12.5px;
            color: #ED2668;
            height: 24px;
            line-height: 24px;
            font-weight: bold;

            em {
                font-size: 11px;
                margin-right: -5px;
            }
        }
    }

    &.design-2 {
        li {
            height: 90px;

            img {
                width: 56px;
                height: 56px;
                float: right;
                margin-left: 12px;
            }
        }
    }

    &.design-3 {
        li {
            height: 90px;

            img {
                width: 56px;
                height: 56px;
                float: left;
                margin-right: 12px;
            }
        }
    }

    &.cp-list {
        li {
            width: 375px;
            padding: 14px 22px;

            img {
                width: 73px;
                height: 73px;
                float: left;
                margin-right: 22px;
            }

            p.subtitle {
                color: #999
            }

            &.item-add {
                background: #e6f0f4 url(~@/assets/index_img/icon_add.png) no-repeat center;
                background-size: 13%;
            }
        }
    }

    &.cp-1line3 {
        li {
            text-align: center;
            width: 123.75px;
            height: 136px;
            margin-right: 1px;
            padding: 13px 12px 8px;

            &:nth-last-child(1) {
                margin-right: 0px;
            }

            img {
                width: 45px;
                height: 45px;
            }

            p.title {
                font-weight: bold;
                font-size: 14px;
                height: 22px;
                line-height: 22px;
            }

            p.subtitle {
                font-size: 12px;
                height: 22px;
                line-height: 22px;
                color: #999
            }
        }
    }

    &.cp-1line2 {
        li {
            width: 186.75px;
            height: 244px;
            padding: 15px 18px 12px;

            &:nth-child(2n+1) {
                margin-right: 1px;
            }

            img {
                width: 150px;
                height: 150px;
            }
        }
    }

    .share-goods {
        position: absolute;
        right: 0;
        bottom: 0;
        display: block;
        width: 18px;
        height: 18px;
        background: url(~@/assets/index_img/icon_share.png) center top no-repeat;
        background-size: 100% auto;
        z-index: 2;
    }

    .item-add {
        background: #e6f0f4 url(~@/assets/index_img/icon_add.png) no-repeat center;
        background-size: 30%;
        width: 186.75px;
        height: 244px;
    }

    .item-close {
        position: absolute;
        width: 37.5px;
        height: 37.5px;
        border-radius: 50%;
        right: 0;
        top: 0;
        background: rgb(237, 38, 104) url(~@/assets/index_img/icon_close_w.png) no-repeat center center;
        background-size: 50%;
    }
}

.shixiao {
    -webkit-filter: grayscale(100%);
    -moz-filter: grayscale(100%);
    -ms-filter: grayscale(100%);
    -o-filter: grayscale(100%);
    filter: grayscale(100%);
    filter: gray;

    .item-shixiao-icon {
        width: 40px;
        height: 25px;
        background: url(~@/assets/index_img/shixiao.png) no-repeat;
        background-size: 100%;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 10px;
    }
}
</style>
