<template>
  <NoticeBar 
    class="notice" 
    left-icon="volume-o"
    :text="props.content"
    :scrollable="true"
  />
</template>

<script setup>
import {reactive,defineProps} from "vue"
import { NoticeBar } from "vant"
const props = defineProps({
  content:{
    type: String,
    default: ''
  }
})
</script>

<style lang="scss" scoped>
  .notice{
    height:25px;
    line-height:25px;
    background: #FFFBEC;
    font-size: 12px;
    color: #C68C5D;
  }
  
</style>
