<template>
  <div>
    <div v-if="!props.configure&&props.design==1" class="addcredit">
      <div class="cz llcz">
        <a :href="props.preview?'javascript:void(0)': props.flowlink" @click="insertCodeGoods('llcz',props.flowlink)">
          <div class="img"><img src="~@/assets/index_img/llcz.png" /></div>
          <div class="txt">
            <p>流量充值</p>
            <p>价格超划算</p>
          </div>
        </a>
      </div>
      <div class="cz hfcz">
        <a href="javascript:void(0)" @click="insertCodeGoods('hfcz',props.creditlink)">
          <div class="img"><img src="~@/assets/index_img/hfcz.png" /></div>
          <div class="txt">
            <p>话费充值</p>
            <p>享折扣优惠</p>
          </div>
        </a>
      </div>
    </div>
    <div v-if="props.configure" class="mt7">
      <h1 class="goods-tt">
        流量/话费充值
      </h1>
      <div class="config-row">
        <div class="config-row-box">
          <van-checkbox v-model="checked" shape="square" icon-size="16px" @click="changeShow">
            流量/话费直充
          </van-checkbox>
        </div>
        <div class="config-row-box-content">
          备注：选中表示在店铺上架流量/话费直充商品
        </div>
      </div>
    </div>
    
  </div>
</template>

<script setup>
import Vue,{reactive,defineProps,ref,watch} from 'vue'
import { Checkbox} from 'vant'
import shopAPI from '@/api/shop'
import insertCode from "@/utils/insertCode"
Vue.use(Checkbox)
const props = defineProps({
  creditlink: {
    type: String,
    default: ''
  },
  flowlink: {
    type: String,
    default: ''
  },
  design: {
    type: String,
    default: ''
  },
  preview: {
    type: String,
    default: null
  },
  configure:{
    type:String,
    default: null
  },
  floorId:{
    type:String,
    default: null
  },
})
const checked = ref(false)
watch(props.design,(val)=>{
  checked.value = val==1
},{immediate:true})
function changeShow(){
  let sendData = {
    design:checked.value?1:0,
    floorId:props.floorId
  }
  shopAPI.changeAddCreditShow(sendData).then(r => {
    // console.log(r)
  })
}
function insertCodeGoods(key,url){
  let dcs_id = key
  if(props.preview){
    return
  }
  insertCode(props.floorId+"_"+dcs_id,url)
}
</script>

<style lang="scss" scoped>
.addcredit{
  width: 375px;
  margin: 0 auto;
  display: flex;
  // flex-direction: row;
  flex-flow:row wrap;
  // flex-flow: wrap;
  .cz{
    width:186.75px;
    height:70px;
    background: #fff;
    margin-bottom:1px;
    padding-top:18px;
    .img{
      float:left;
      padding:0 15px 0 18px;
      img{
        width:40px;
        height:40px;
      }
    }
    .txt p:nth-child(1){
      font-size:14px;
      line-height:20px;
      color:#333333;
    }
    .txt p:nth-child(2){
      font-size:12px;
      color:#999999;
      line-height:17px;
    }
  }
  .cz:nth-child(2n+1){
    margin-right:1px;
  }  
}
.goods-tt{
    height:45px;
    line-height:45px;
    font-size:16px;
    color:#333333;
    padding-left:39px;
    margin-bottom:1px;
    background:url(~@/assets/index_img/icon_05.png) 15px 12px no-repeat #fff;
  }
  .config-row {
    overflow: hidden;
    padding:15px;
    background: #fff;
    margin-bottom: 7px;
    .config-row-box{
      font-size:14px;
      line-height:20px;
      color:#333333;
    }
    .config-row-box-content{
      font-size:12px;
      color:#999999;
      line-height:17px;
      padding: 10px 24px;
    }
  }
</style>