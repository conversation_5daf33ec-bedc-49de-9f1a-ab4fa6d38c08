<template>
  <div class="bgwhite">
    <swiper
      v-if="props.items && props.items.length"
      class="swipebanner_b"
      :options="option"
    >
      <swiper-slide v-for="(item, index) in props.items" :key="index">
        <a href="javascript:void(0)" @click="insertCodeGoods(key,item.adLink)"><img :src="item.imgSrc" /></a>
      </swiper-slide>
      <div slot="pagination" class="swiper-pagination"></div>
    </swiper>
  </div>
</template>

<script setup>
import "swiper/dist/css/swiper.css"
import Vue,{reactive,defineProps} from "vue"
import VueAwesomeSwiper  from 'vue-awesome-swiper'
import insertCode from "@/utils/insertCode"
Vue.use(VueAwesomeSwiper)
const props = defineProps({
  items: {
    type: Array,
    default: () =>{
      return []
    }
  },
  preview: {
    type: String,
    default: null
  },
  floorId:{
    type:String,
    default: null
  }
})
const option = {
  pagination: {
    el: '.swiper-pagination',
    type: 'fraction',
    totalClass : 'my-pagination-total',  //分式类型分页器总数的类名
    renderFraction: function(currentClass, totalClass) {
      return '<span class="' + currentClass + '"></span>' + '/' + '<span class="' + totalClass + '"></span>'
    },
  },
  slidesPerView:1.14,
  spaceBetween :15,
}
function insertCodeGoods(key,url){
  let dcs_id = key
  if(props.preview||url==''){
    return
  }
  insertCode(props.floorId+"_advertisement_"+dcs_id,url)
}

</script>

<style lang="scss" scoped>
.bgwhite{padding:10px 0;margin-bottom:1px}
.swiper-pagination{
  z-index:-1
} 
.swipebanner_b {
  width:365px;
  height: 127.2px;
  margin-left:12px;
  img{
    border-radius: 5px;
    width: 100%;
    height: 127.2px;
  }
  .swiper-slide-next{
    height: 127.2px;
    padding-top:25px;
    img{
      height:102.2px;
    }
  }
  .swiper-pagination{
    top:3px;
    text-align:right;
    font-size:18px;
  }
  :deep(.my-pagination-total){margin-right:8px;font-size:12px;}
}
.swiper-container{width:365px;}
.swiper-slide{width:333px;}
</style>