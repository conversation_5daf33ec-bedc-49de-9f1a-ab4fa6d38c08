<!--
 * @User: JOJO
 * @FilePath: \yundian-m\src\components\index\indexHomeRecommend.vue
-->
<template>
  <div class="indexHomeRecommend">
    <ul>
      <li v-for="(item, index) in filterData" :key="index">
        <div class="pongetit">
          <span class="tit">{{ item.tabData[0].title }}</span>
          <div class="desc">
            <span>{{ item.tabData[0].description }}</span>
            <i></i>
            <i></i>
            <i></i>
          </div>
        </div>
        <div class="subtitle">
          {{ item.tabData[0].subtitle }}
        </div>
        <dl>
          <dt
            v-for="(item2, index2) in item.tabData"
            :key="index2"
            @click="handleTo(item2)"
          >
            <img :src="shopImg(item2.imgSrc)" alt="" />
          </dt>
        </dl>
      </li>
    </ul>
  </div>
</template>

<script setup>
import { defineProps, watch, ref } from 'vue'
import insertCode from "@/utils/insertCode.js"
import UA from "@/utils/ua"
import { getShopImgUrl, getCookie } from "@/utils/utils.js"

const props = defineProps(
  {
    dataList: {
      type: Object,
      default: (data) => {
        return {}
      },
    },
  }
)
const filterData = ref([])
watch(()=>props.dataList,()=>{
  let data = props.dataList.data
  let newArr = []
  for (const key in data) { newArr.push(data[key])}
  filterData.value = newArr
},{
  immediate: true
})

async function handleTo(item) {
  let ac_id = getCookie("ac_id")
  let goods_link=item.goodsLink
  if (!ac_id && goods_link!=null) {
    const isWXMapp = await UA.isWeChatMiniApp()
    if(/\/hd\/ydskin/.test(item.goodsLink)){
      goods_link = item.goodsLink+(item.goodsLink.indexOf('?')==-1?'?':'&')+'WT.ac_id=SHOP_COLLECTIONPAGE_SHARE'
    } else if (isWXMapp) {
      goods_link = item.goodsLink+(item.goodsLink.indexOf('?')==-1?'?':'&')+'WT.ac_id=SHOP_APPLET_TRANSACT'
    } else {
      goods_link = item.goodsLink+(item.goodsLink.indexOf('?')==-1?'?':'&')+'WT.ac_id=SHOP_DIRECT_TRANSACTION'
    }
  }
  insertCode(null, goods_link)
}
function shopImg(name) {
  return getShopImgUrl(name)
}

</script>

<style lang="scss" scoped>
$mo2: 2;
.indexHomeRecommend {
  padding: 0 25px / $mo2;
  margin-bottom: 20px;

  ul {
    display: flex;
    padding: 0 10px / $mo2;
    border-radius: 10px / $mo2;
    position: relative;
    background-image: url("https://img1.shop.10086.cn/fs/goods/fs_62828f2fe4b0809da9b237c8.png");
    background-size: cover;
    background-repeat: no-repeat;

    &::before {
      position: absolute;
      content: "";
      width: 12px / $mo2;
      height: 80%;
      left: 0;
      top: 0;
      bottom: 0;
      right: 0;
      margin: auto;
      background: #fff;
      border-radius: 30px / $mo2;
    }

    .imga {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
    }

    li {
      flex: 1;
      min-width: 0;
      padding: 26px / $mo2;

      .pongetit {
        display: flex;
        align-items: center;
        padding-right: 15px / $mo2;
        .tit {
          font-size: 32px / $mo2;
          color: #010101;
          font-weight: bold;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          line-height: 1.3;
        }
        .desc {
          margin-left: 10px / $mo2;
          border-radius: 20px / $mo2 0 20px / $mo2 20px / $mo2;
          padding: 6px / $mo2 14px / $mo2;
          max-width: 130px / $mo2;
          display: flex;
          align-items: center;
          position: relative;
          i {
            position: absolute;
            border-radius: 50%;
            right: 0;
            top: 0;
            &:nth-of-type(1) {
              width: 9px / $mo2;
              height: 9px / $mo2;
              right: -4px / $mo2;
            }
            &:nth-of-type(2) {
              width: 8px / $mo2;
              height: 8px / $mo2;
              right: -15px / $mo2;
            }
            &:nth-of-type(3) {
              width: 8px / $mo2;
              height: 8px / $mo2;
              right: -27px / $mo2;
            }
          }

          span {
            font-size: 20px / $mo2;
            color: #fff;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }

      .subtitle {
        font-size: 24px / $mo2;
        margin-top: 15px / $mo2;
        margin-bottom: 15px / $mo2;
      }

      &:nth-of-type(1) {
        .pongetit {
          .desc {
            background: linear-gradient(to right, #2983fe, #7abeff);
            i {
              background: #7abeff;
            }
          }
        }
        .subtitle {
          color: #7abeff;
        }
      }
      &:nth-of-type(2) {
        .pongetit {
          .desc {
            background: linear-gradient(to right, #fa4a40, #fe977c);
            i {
              background: #fe977c;
            }
          }
        }
        .subtitle {
          color: #fe977c;
        }
      }





      dl {
        display: flex;
        align-items: center;
        dt {
          flex: 1;
          min-width: 0;
          height: 140px / $mo2;
          background: #fff;
          border-radius: 10px / $mo2;
          // padding: 20px / $mo2;
          overflow: hidden;
          &:nth-of-type(1) {
            margin-right: 10px / $mo2;
          }
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            height: 100%;
            width: 100%;
            border-radius: 10px / $mo2;
            overflow: hidden;
          }
        }
      }
    }
  }
}
</style>
