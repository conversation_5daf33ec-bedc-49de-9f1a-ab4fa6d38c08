<template>
  <div>
    <div
      :class="['vip', member == '1' ? 'viped' : '']"
      @click="MemberInfo(shopID)"
    >
      <div class="card" :class="[cardnewvip ? 'cardvip' : '']">
        <img class="shoplogo" src="@/assets/index_img/shop_icon_def.png" />
        <button
          v-if="tplId == '3' && member == '1'"
          class="cancelMember"
          @click="cancelDialog"
        ></button>
        <div class="shopinfo">
          <p>{{ shopname }}</p>
        </div>
      </div>
    </div>
    <div v-if="info" class="memberinfo">
      <div class="infocard">
        <div class="left">
          <div class="pic">
            <img
              class="shoplogo"
              src="@/assets/index_img/vipicon_infohead.png"
            />
          </div>
          <p>店铺会员</p>
        </div>
        <div class="right">
          <p>{{ encrypPhone }}</p>
          <p>NO.{{ memberno }}</p>
          <p>{{ shopname }}</p>
        </div>
      </div>
      <div class="birthday">
        <van-cell title="生日" @click="showPopup">
          <template v-if="birthDate">
            {{ birthDate | dateFormat("yyyy-MM-dd") }}
          </template>
          <template v-else>
            请填写出生年月日
          </template>
        </van-cell>
        <van-popup v-model="show" position="bottom" :overlay="true">
          <van-datetime-picker
            v-model="currentDate"
            type="date"
            title="选择年月日"
            :min-date="minDate"
            :max-date="maxDate"
            @confirm="onConfirm"
            @cancel="show = false"
          />
        </van-popup>
      </div>
      <div class="xieyi">
        <van-checkbox v-model="checked" label-disabled>
          同意<span
            class="agreementStyle"
            @click="showhyxy = true"
          >《会员授权协议》</span>
        </van-checkbox>
        <van-popup
          v-model="showhyxy"
          round
          closeable
          position="bottom"
          :style="{ height: '60%' }"
          class="ss"
        >
          <Authorization />
        </van-popup>
      </div>
      <a
        href="javascript:void(0);"
        class="btlqhy"
        @click="AddMemberInfo()"
      >领取会员</a>
    </div>
    <MemberGifts ref="MemberGifts" />
  </div>
</template>

<script>
import { mapGetters } from "vuex"
import { dateFormat } from "@/filters"
import insertCode from "@/utils/insertCode"
import VipApi from "@/api/vip"
import Vue from "vue"
import {
  DatetimePicker,
  Toast,
  Popup,
  Checkbox,
  CheckboxGroup,
  Dialog
} from "vant"
import Authorization from "@/components/index/authorizationAgreement.vue"
import MemberGifts from "@/components/index/membergifts.vue"
Vue.use(DatetimePicker)
  .use(Popup)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Dialog)

export default {
  name: "Vip",
  components: {
    Authorization,
    MemberGifts
  },
  filters: {
    dateFormat
  },
  props: {
    logined: {
      type: [Boolean, Function],
      default: null
    },
    preview: {
      type: String,
      default: null
    },
    floorId: {
      type: String,
      default: null
    },
    dataList: {
      type: Object,
      default: null
    },
    cardnewvip: {
      type: String,
      default: null
    },
    tplId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      shopname: "店铺名称",
      shopsummary: "店铺介绍可能会有很多的文字",
      member: "",
      memberphone: "139 **** 5676",
      memberno: "139139139",
      info: false,
      date: "",
      show: false,
      showhyxy: false,
      minDate: new Date("1960-01-01"),
      maxDate: new Date(2025, 10, 1),
      currentDate: null,
      birthDate: null,
      staffId: "",
      checked: true,
      hyxy: "",
      hybmxy: ""
    }
  },
  computed: {
    ...mapGetters(["pageInfo", "user"]),
    encrypPhone: function() {
      let pat = /(\d{3})\d*(\d{4})/
      return this.memberphone.replace(pat, "$1****$2")
    }
  },
  watch: {
    logined: {
      handler: function(val) {
        if (val) {
          this.memberphone =
            (this.user && this.user.userInfo && this.user.userInfo.UserName) ||
            this.user.userName
          this.ismember(this.pageInfo.pageInfo.shopId)
        }
      },
      immediate: true
    },
    currentDate: {
      handler: function(val) {
        this.birthDate = val
      }
    }
  },
  created() {
    this.shopname = this.pageInfo.pageInfo.shortName
    this.shopsummary = this.pageInfo.pageInfo.shopDesc
    this.shopID = this.pageInfo.pageInfo.shopId
    if (this.user && this.user.userInfo) {
      this.memberphone = this.user.userInfo.UserName
    } else {
      this.memberphone = localStorage.getItem("userName")
    }
  },
  methods: {
    CouponCheck() {
      this.$refs.MemberGifts.CouponCheck(this.shopID)
    },
    ShowCouponPop() {
      this.$refs.MemberGifts.ShowCouponPop(this.shopID)
    },
    closevip() {
      this.info = false
      document.body.style.overflow = "auto"
    },
    showPopup() {
      this.setScrollTop()
      this.currentDate = new Date(this.birthDate)
      this.show = true
    },
    onConfirm(date) {
      this.value = date
      this.show = false
    },
    ismember(shopID) {
      VipApi.IsMember({
        shopId: shopID
      }).then(res => {
        if (res.code == 0) {
          this.member = res.data.member
          if (res.data.member == 1) {
            this.CouponCheck() //查询入会礼可用券
          }
        }
      })
    },
    MemberInfo(shopID) {
      if (this.member) {
        return false
      }
      if (this.preview) {
        return
      }
      this.insertCodeGoods("vip_joinvip")
      VipApi.UserInfo().then(res => {
        if (res.code == 0) {
          this.setScrollTop()
          this.info = true
          document.body.style.overflow = "hidden"
          this.birthDate = res.data.birthday
            ? res.data.birthday
            : this.birthDate
          // this.staffId = res.data.shopMember.staffId
        } else {
          // Toast('您不能领取')
          this.closevip()
        }
      })
    },
    AddMemberInfo(shopID) {
      // console.log(dateFormat(this.currentDate, "yyyy-MM-dd"))
      if (!this.checked) {
        Toast("请勾选同意《会员授权协议》")
        return
      }
      if (!this.birthDate) {
        Toast("请选择生日")
        return
      }
      this.insertCodeGoods("add")
      if (this.dataList && this.dataList.floor) {
        this.staffId = this.dataList.floor.recommendCode
      }
      VipApi.AddMemberInfo({
        shopId: this.shopID,
        birthday: dateFormat(this.birthDate, "yyyy-MM-dd"),
        staffId: this.staffId
      }).then(res => {
        if (res.code == 0) {
          Toast("领取成功")
          this.ismember(this.shopID)
          this.closevip()
          this.ShowCouponPop() //弹出入会礼弹窗
        } else if (res.code == 110101) {
          Toast(res.message)
          this.closevip()
        } else {
          Toast("领取出问题了，请再试一次吧")
          this.closevip()
        }
      })
    },
    cancelDialog() {
      this.insertCodeGoods("vip_cancelvip")
      Dialog.confirm({
        title: "温馨提示",
        message: "确定取消VIP会员?",
        confirmButtonColor: "#5099fe",
        confirmButtonText: "确认取消",
        cancelButtonText: "我再想想"
      }).then(() => {
        this.withdeaw()
      })
    },
    withdeaw() {
      VipApi.withdeaw({
        shopId: this.shopID
      }).then(res => {
        if (res.code) {
          Toast(res.message)
        } else {
          this.ismember(this.shopID)
        }
      })
    },
    insertCodeGoods(key) {
      let dcs_id = key
      insertCode("yd_index_" + this.shopID + "_" + dcs_id)
    },
    setScrollTop() {
      var scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop
      if (scrollTop == 0) {
        window.scrollTo(0, 1)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.hidescroll {
  overflow: hidden;
}
.vip {
  // width:353px;
  height: 70px;
  // border:1px solid #E8E8E8;
  border-radius: 9px;
  // margin:10px auto;
  margin-bottom: 1px;
  &.viped {
    background: url(~@/assets/index_img/vipbg.png) 0 0 no-repeat;
    background-size: 100% 100%;
    .card {
      background: url(~@/assets/index_img/vipbg_vip.png) 240px 3px no-repeat;
      background-size: 112px 49px;
      .shopinfo {
        p:nth-child(1) {
          color: #a98f48;
        }
        p:nth-child(2) {
          color: #e1be6d;
        }
      }
    }
  }
  .card {
    // width:353px;
    height: 70px;
    // border-radius: 9px;
    padding: 12px 0 0 15px;
    box-sizing: border-box;
    // background: url(~@/assets/index_img/vipbg_go.png) 240px 3px no-repeat #fff;
    // background-size: 112px 49px;
    img.shoplogo {
      width: 44px;
      height: 44px;
      float: left;
      background: #fff;
      border-radius: 9px;
    }
    .shopinfo {
      float: left;
      width: 180px;
      line-height: 24px;
      p {
        padding-left: 18px;
        // height:24px;
        display: -webkit-box;
        -webkit-line-clamp: 1; /*设置p元素最大4行，父元素需填写宽度才明显*/
        text-overflow: ellipsis;
        overflow: hidden;
        /* autoprefixer: ignore next */
        -webkit-box-orient: vertical;
        word-wrap: break-word;
        word-break: break-all;
      }
      p:nth-child(1) {
        font-size: 14px;
        font-weight: bold;
        color: #383838;
        padding-top: 12px;
      }
      // p:nth-child(2){
      //   font-size:13px;
      //   color: #999999;
      // }
    }
  }
  .cardvip {
    background: url(~@/assets/index_img/vipnew_go.png) 240px 3px no-repeat #fff;
    background-size: 112px 49px;
  }
}
.memberinfo {
  width: 100%;
  height: 101vh;
  background: #fff;
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 10;
  padding: 60px 0 0;
  .infocard {
    width: 345px;
    height: 122px;
    background: url(~@/assets/index_img/vipinfocardbg.png) 0 0 no-repeat;
    background-size: 100% 100%;
    margin: 0 auto;
    .left {
      width: 108px;
      height: 122px;
      float: left;
      .pic {
        width: 73px;
        margin: 0 0 0 22px;
        padding-top: 8px;
        text-align: center;
        img {
          width: 73px;
          height: 83px;
        }
      }
      p {
        margin: 2px auto 0;
        text-align: center;
        font-size: 13px;
        color: #e7b65a;
      }
    }
    .right {
      width: 237px;
      float: left;
      p:nth-child(1) {
        padding-top: 18px;
        height: 48px;
        line-height: 30px;
        font-size: 22px;
        color: #a98f48;
      }
      p:nth-child(2) {
        padding-top: 9px;
        height: 29px;
        line-height: 20px;
        font-size: 14px;
        color: #e1be6d;
      }
      p:nth-child(3) {
        padding-top: 12px;
        height: 35px;
        line-height: 23px;
        font-size: 14px;
        color: #a38b53;
        background: url(~@/assets/index_img/vipicon_add.png) 0 15px no-repeat;
        background-size: 16px 15px;
        padding-left: 20px;
      }
    }
  }
  .birthday {
    height: 50px;
    width: 345px;
    margin: 15px auto 0;
  }
  .xieyi {
    height: 50px;
    padding-top: 15px;
    padding-left: 15px;
    color: #999999;
    font-size: 14px;
    .ss {
      padding: 40px;
    }
  }
  .btlqhy {
    display: block;
    width: 344px;
    height: 48px;
    line-height: 48px;
    text-align: center;
    background: linear-gradient(90deg, #e8cb88, #dbac51);
    border-radius: 24px;
    box-shadow: 0px 2px 4px 0px #f0e5ce;
    font-size: 16px;
    color: #ffffff;
    font-weight: 600;
    margin: 20px auto;
  }
  .agreementStyle {
    color: #409eff;
  }
  :deep(.van-cell) {
    border-bottom: 1px solid #f4f4f4;
  }
}
:deep(.van-overlay),
.memberinfo {
  height: 101vh;
}
.cancelMember {
  background: transparent;
  width: 100px;
  height: 30px;
  margin-top: 10px;
  border-radius: 30px;
  border: none;
}
</style>
