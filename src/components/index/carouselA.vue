<template>
  <div class="bgwhite pt12">
    <swiper
      v-if="props.items && props.items.length"
      :class="[props.carouselClass ? props.carouselClass :'swipebanner']"
      :options="options"
    >
      <swiper-slide v-for="(item, key) in props.items" :key="key">
        <a href="javascript:void(0)" @click="insertCodeGoods((key+1),item.adLink,item)">
          <img :src="item.imgSrc" />
        </a>
      </swiper-slide>
      <div slot="pagination" class="swiper-pagination"></div>
    </swiper>
  </div>
</template>

<script setup>
import Vue,{reactive,defineProps,computed,watch,getCurrentInstance}from "vue"
import "swiper/dist/css/swiper.css"
import VueAwesomeSwiper  from 'vue-awesome-swiper'
import insertCode from "@/utils/insertCode"
import UA from "@/utils/ua"
import { getCookie } from "@/utils/utils.js"
Vue.use(VueAwesomeSwiper)
const props = defineProps({
  items:{
    type:Array,
    default:()=>{
      return []
    }
  },
  preview: {
    type: String,
    default: null
  },
  floorId:{
    type:String,
    default: null
  },
  carouselClass:{
    type:String,
    default: null
  },
  pageName:{
    type:String,
    default: null
  },
  isforcelogin:{
    type:Boolean,
    default: false
  },
  goToLink:{
    type:Function,
    default:(fn)=>{
      return fn
    }
  }
})
//配置文档https://www.swiper.com.cn/api/pagination/299.html
let option = {
  pagination: {
    el: '.swiper-pagination',
  },
  autoplay: {delay: 3000},
  loop:true,
}
let carouselA_4_option = {
  pagination: {
    el: '.swiper-pagination',
    type:'fraction'
  },
  autoplay: {delay: 3000},
  loop:true,
}
let options = computed(()=>{
  let myOption = option
  if(props.carouselClass == "carouselA_4"){
    myOption = carouselA_4_option
  }
  if(props.items && props.items.length==1){
    myOption.autoplay = false
    myOption.pagination={
      el: 'null',
    }
  }
  return myOption
})
const getCurrentVue = getCurrentInstance()
const proxy= getCurrentVue ? getCurrentVue.proxy :null
async function insertCodeGoods(key,url,item){
  let ac_id = getCookie("ac_id")
  let goods_link=url
  if (!ac_id && goods_link!=null) {
    const isWXMapp = await UA.isWeChatMiniApp()
    if (isWXMapp) {
      goods_link = url+(url.indexOf('?')==-1?'?':'&')+'WT.ac_id=SHOP_APPLET_TRANSACT'
    } else {
      goods_link = url+(url.indexOf('?')==-1?'?':'&')+'WT.ac_id=SHOP_DIRECT_TRANSACTION'
    }
  }
  if(props.isforcelogin){
    props.goToLink(item)
    return false
  }else{
    let id = 'p'+key,dcs_id
    if(props.preview||url==''){
      return
    }
    if(props.pageName){
      dcs_id = "yd_"+props.pageName+"_"+proxy.$store.getters.shopId+"_img_"+key
    }else{
      dcs_id = "yd_index_"+proxy.$store.getters.shopId+"_slidingbanner_"+id
    }

    // 如果是聚合页的链接就加上shopId
    if(/\/hd\/ydskin/.test(url)){
      let params = new URLSearchParams({
        shopId:proxy.$store.getters.shopId
      })
      url += '?' + params.toString()+'&WT.ac_id=SHOP_COLLECTIONPAGE_SHARE'
    }
    insertCode(dcs_id,goods_link)
  }
}

</script>
<style lang="scss" scoped>
.swipebanner,.carouselA_4{
  background:#fff;
  margin:0 10px 1px;
  width: 355px;
  height: 172px;
  overflow: hidden;
  border-radius: 5px;
  img {
    width: 355px;
    height: 142px;
    border-radius: 5px;
  }
}
.circle-right,.circle-center {
  background:#fff;
  margin:0 10px 1px;
  width: 375px;
  height: 150px;
  overflow: hidden;
  margin-left:-12px;
  img {
    width: 100%;
    height: 100%;
  }
  .swiper-pagination{
    width:100%;
    padding-right:10px;
    text-align: right;
    bottom:5px;
    :deep(.swiper-pagination-bullet){
      background: #fff!important;
      opacity: .7;
    }
    :deep(.swiper-pagination-bullet-active){
      background: #fff;
      opacity: 1;
    }
  }
}
.circle-center{
  width:355px;
  overflow: hidden;
  border-radius: 10px;
  margin:0 auto;
  .swiper-pagination{
    text-align: center;
  }
}
.bgwhite {
  padding-left: 10px;
  padding-right: 10px;
  .swiper-slide{
    a{
      display: block;
      img{
        width:101%;
      }
    }
  }
}
.carouselA_4{
  // width: 351px;
  width:100%;
  margin:0 auto;
  height: 140px;
  .swiper-pagination-fraction{
    width: 36px;
    height: 16px;
    line-height: 16px;
    opacity: 0.2;
    background: #000000;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 400;
    text-align: center;
    color: #ffffff;
    bottom:10px;
    left:86%;
  }
}
</style>
