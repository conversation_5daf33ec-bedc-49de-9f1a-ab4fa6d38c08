<!-- 使用规则
<carouselSliperv :items="peripheralSelectList">
  <template
    v-for="goodsinfo in peripheralSelectList"
    v-slot:[goodsinfo.slotname]
  >
    <GoodsItem
      :key="goodsinfo.id"
      size-class="normal"
      :goods-info="goodsinfo"
      :shop-id="shopId"
      :change-current-coupon="changeCurrentCoupon"
      :change-follow="changeFollow"
      :goto-detail="gotoDetail"
      :service-class="serviceClass"
      :service-img-disappear="serviceImgDisappear"
    />
  </template>
</carouselSliperv>

-->
<template>
  <div class="sliprev">
    <swiper
      v-if="props.items && props.items.length"
      :options="options"
      :slider-type="1"
      pagination=""
    >
      <swiper-slide v-for="(item, index) in props.items" :key="index">
        <slot :name="item.slotname"></slot>
      </swiper-slide>
      <div slot="pagination" class="swiper-pagination"></div>
      <div slot="pagination" class="swiper-button-prev"></div><!--左箭头。如果放置在swiper外面，需要自定义样式。-->
      <div slot="pagination" class="swiper-button-next"></div><!--右箭头。如果放置在swiper外面，需要自定义样式。-->
    </swiper>
  </div>
</template>

<script setup>
import "swiper/dist/css/swiper.css"
import Vue,{reactive,computed} from "vue"
import VueAwesomeSwiper  from 'vue-awesome-swiper'
Vue.use(VueAwesomeSwiper)
const props = defineProps({
  items: {
    type: Array,
    default: () =>{
      return []
    }
  },
  propOptions: {
    type: Object,
    default: () =>{
      return {}
    }
  }
})
const option = reactive({
  slidesPerView :2.3,
  autoplay: false,
})
let options = computed(()=>{
  let myOption = option
  if(props.propOptions){
    myOption = props.propOptions
  }
  return myOption
})
</script>
<style lang="scss" scoped>
.sliprev{padding-left:10px;}
</style>
