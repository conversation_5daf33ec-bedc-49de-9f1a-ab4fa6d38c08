<template>
  <div class="head" :style="{ 'z-index': props.styleindex ? props.styleindex : 1111111,'background':props.bgColor?props.bgColor:'#f1f1f1' }">
    <Icon v-if="props.showBack" name="arrow-left" class="back" @click="back" />
    <span>{{ props.content }}</span>
  </div>
</template>

<script setup>
import {reactive,defineProps} from "vue"
import { Icon } from "vant"
const props = defineProps({
  content: {
    type: String,
    default: ""
  },
  bgColor: {
    type: String,
    default: ""
  },
  styleindex: {
    type: Number,
    default: null
  },
  showBack: {
    type: Boolean,
    default: false
  }
})
function back() {
  history.go(-1)
}
</script>
<style lang="scss" scoped>
.head {
  position: fixed;
  height: 34px;
  line-height: 34px;
  font-size: 18px;
  width: 375px;
  background: #f1f1f1;
  // z-index:1111111111;
  span {
    display: inline-block;
    width: 100%;
    text-align: center;
  }
  i {
    position: absolute;
    height: 20px;
    left: 15px;
    top: 7px;
  }
}
</style>
