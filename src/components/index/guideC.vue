<template>
  <div v-if="props.items && props.items.length" class="guideC" :class="user.isMember&&(!props.hasbanner) ? 'nobanner' : ''">
    <div v-for="(item, key) in props.items" :key="key" class="item" :class="item.class">
      <a href="javascript:void(0)" @click="insertCodeGoods(key,item.adLink,item)">
        <img :src="item.imgSrc" />
        {{ item.title }}
      </a>
    </div>
  </div>
</template>

<script setup>
import {defineProps,inject, getCurrentInstance,computed} from "vue"
import {ENV} from "@/utils/env.js"
import { Toast } from "vant"
import { mapGetters } from "vuex"
import lbsApi from "@/api/lbs"
import insertCode from "@/utils/insertCode"
let getSon = inject("getSon")
const props = defineProps({
  items: {
    type: Array,
    default: () =>{
      return []
    }
  },
  preview: {
    type: String,
    default: null
  },
  quanlink: {
    type: String,
    default: null,
  },
  hasbanner:{
    type: Boolean,
    default:false
  }
})
let guideItme = null
const getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null

const user = computed(()=>{
  return (proxy && proxy.$store.getters && proxy.$store.getters.user) ? proxy.$store.getters.user : null
})
const pageInfo = computed(()=>{
  if(proxy){
    return proxy.$store.getters.pageInfo
  }else{
    return null
  }
})
function insertCodeGoods(key, url, item) {
  if (props.preview) {
    return
  }
  let dcs_id = key
  guideItme = item
  if (dcs_id == 3 && user.value.userInfo == null) {
    //强登
    getSon.openlogin(logined)
  }else if(item.linkId=='quhao'){
    if(item.queueStatus == 0 && item.appointmentStatus == 0){
      Toast('该功能正在开放中，敬请期待')
      insertCode('yd_index_'+pageInfo.value.pageInfo.shopId+"_icon_"+item.dcsId)
      return false
    }
    if (user.value.userInfo == null) {
      //强登
      openloginSelf(yuyue)
    } else {
      yuyue()
    }
  }else if(item.linkId=="broadband"){
    if(!item.sectionId){
      Toast('该功能正在开放中，敬请期待')
      return
    }

    let dcs_id = 'waistbanner_kdyy'
    guideItme.dcsId = 'waistbanner_kdyy'
    if(props.preview){
      return
    }
    if(user.value.userInfo==null){
      //强登
      openloginSelf(broadband)
    }else{
      insertCode('yd_index_'+pageInfo.value.pageInfo.shopId+"_"+dcs_id,url)
    }
  }else{
    if(user.value.userInfo==null){
      //强登
      openloginSelf(commonInsert)
    }else{
      commonInsert()
    }

  }
}
function broadband(res) {
  insertCode('yd_index_'+pageInfo.value.pageInfo.shopId+"_"+guideItme.dcsId,guideItme.adLink)
}
function commonInsert(){
  insertCode('yd_index_'+pageInfo.value.pageInfo.shopId+"_icon_"+guideItme.dcsId,guideItme.adLink)
}
function logined(res) {
  if (res && res.UserName > "") {
    insertCode(
      "yd_index_" + pageInfo.value.pageInfo.shopId + "_icon_lqzz",
      `${ENV.getB2bDomain()}/cnr-web/counponsCenter?shareParam=` +
        props.quanlink
    )
  }
}
function yuyue() {
  lbsApi
    .getUrlReq({
      pageType: 2,
      shopId: pageInfo.value.pageInfo.shopId,
    })
    .then((res) => {
      if (res.code == 0) {
        insertCode(
          "yd_index_" + pageInfo.value.pageInfo.shopId + "_icon_zxqh",
          res.data.url
        )
      } else {
        Toast(res.message)
      }
    })
}
function openloginSelf(callback) {
  return getSon.openlogin(callback)
}
</script>

<style lang="scss" scoped>

  .guideC {
    background: #fff;
    // width: 351px;
    width:100%;
    padding-left: 10px;
    padding-right: 10px;
    margin: 0 auto;
    display: flex;
    // flex-direction: row;
    min-height: 115px;
    flex-flow:row wrap;
    padding-top: 35px;
    &.nobanner{
      padding-top:52px;
    }
    .item {
      width:25%;
      padding-bottom: 25px;
      font-size: 13px;
      text-align: center;
      img {
        width: 42px;
        height: 42px;
        border-radius:0%;
        margin: 0 auto 10px auto;
        display: block;
      }
    }
  }

</style>
