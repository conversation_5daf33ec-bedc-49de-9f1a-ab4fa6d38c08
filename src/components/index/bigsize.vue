<template>
  <a href="javascript:void(0)" @click="insertCodeGoods(0,props.adlink)"><img class="bigsize" :src="props.bannersrc"></a>
</template>

<script setup>
import {defineProps} from "vue"
import insertCode from "@/utils/insertCode"
const props = defineProps({
  bannersrc:{
    type:String,
    default: '',
  },
  adlink: {
    type: String,
    default: ''
  },
  preview: {
    type: String,
    default: null
  },
  floorId:{
    type:String,
    default: null
  }
})
function insertCodeGoods(key,url){
  let dcs_id = key
  if(props.preview){
    return
  }
  insertCode(props.floorId+"_advertisement_"+dcs_id,url)
}
</script>

<style lang="scss" scoped>
.bigsize{
    width:375px;
    display: block;
}
</style>