<template>
  <Tab :title="props.floor.title" :name="props.floorSort">
    <div ref="shzqTabTilte"></div>
    <template v-if="props.floor.title == '手机专区' && props.configure != 1">
      <div class="shzq_couponMain" :class="{ shzq_couponMain_fixed: goodsTabData.searchBarFixed }">
        <ul class="shzqTabTilte van-doc-demo-section demo-icon">
          <li
            v-for="(tit, index) in mzIndex"
            :key="index"
            :class="[
              'mobile_zone' + index,
              {
                active: goodsTabData.cur == index,
                active_vc: index == 2 && goodsTabData.activeVc,
                active_gb: index == 3 && goodsTabData.activeGb,
                brand_xz: (index == 2 && goodsTabData.brandXz) || (index == 3 && goodsTabData.sectionXz),
              },
            ]"
            @click="
              goodsTabData.cur = index
              clickMobileZone(index)
              if (index == 2 || index == 3) {
                showPopup(index)
              }
            "
          >
            <span>{{ tit }}</span>
            <Icon
              name="play"
              :class="{
                iconplaytop: index == 1,
                iconplaytop_blu1: index == 1 && shopMobileZone.order == 0,
                iconplaytop_blu2: index == 1 && shopMobileZone.order == 1,
              }"
            />
          </li>
        </ul>
        <div :style="[goodsTabData.styleObj1]" class="van-dropdown-brand van-dropdown-item van-dropdown-item--down">
          <div class="van-overlay" style="position: absolute; animation-duration: 0.2s" @click="hidePopup(2)">
          </div>
          <div class="van-popup van-popup--top van-dropdown-item__content">
            <div class="sjzq_brand">
              <span
                v-for="(tit, index) in brandIndex"
                :key="index"
                :class="{ spanxz: goodsTabData.spanIndex.indexOf(tit.id) > -1 }"
                @click="brandXzP(tit.id, tit.ind)"
              >{{ tit.ind }}</span>
            </div>
            <div style="padding: 5px 16px">
              <Button type="danger" round @click="hidePopup(2, 'popcz')">
                重置
              </Button>
              <Button class="van-button-wc" type="danger" round @click="wcshoPopup(2)">
                完成
              </Button>
            </div>
          </div>
        </div>
        <div
          :style="[goodsTabData.styleObj2]"
          class="van-dropdown-section van-dropdown-item van-dropdown-item--down"
        >
          <div class="van-overlay" style="position: absolute; animation-duration: 0.2s" @click="hidePopup(3)">
          </div>
          <div class="van-popup van-popup--top van-dropdown-item__content">
            <div class="sjzq_brand sjzq_section">
              <div>
                <input v-model="pageIndex.minPrice" type="text" placeholder="最低价" @keyup="inputChange(1)" />
                -
                <input v-model="pageIndex.maxPrice" type="text" placeholder="最高价" @keyup="inputChange(2)" />
              </div>
              <span
                v-for="(tit, index) in sectionIndex"
                :key="index"
                :titPrice="tit"
                :class="{ spanxz: goodsTabData.popSectionXz == index }"
                @click="
                  goodsTabData.popSectionXz = index
                  sectionPopup($event)
                "
              >¥<i>{{ tit }}</i></span>
            </div>
            <div style="padding: 5px 16px">
              <Button type="danger" round @click="hidePopup(3, 'popcz')">
                重置
              </Button>
              <Button class="van-button-wc" type="danger" round @click="wcshoPopup(3)">
                完成
              </Button>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template
      v-if="goodsTabData.pageDatasingle != null &&
        props.floor.title == '办流量' &&
        props.configure != 1 &&
        !props.showPageDatasingle
      "
    >
      <template v-for="(single, index) in goodsTabData.pageDatasingle.dataList.ad">
        <Single
          :key="`5_single_${index}`"
          :bannersrc="single.imgSrc"
          :adlink="single.adLink ? single.adLink : 'javascript:void(0)'"
          :preview="props.preview"
          :configure="props.configure"
          dcs-id="ydyw_pic"
          :shop-id="shopId"
        />
      </template>
    </template>
    <template v-if="props.pageDatasingle4 != null && props.showPageDatasingle && props.moveName != 70">
      <template v-for="(single, index) in props.pageDatasingle4.dataList.ad">
        <div :key="index" class="single_4_container">
          <Single
            :key="`5_single_${index}`"
            :bannersrc="single.imgSrc"
            :adlink="single.adLink ? single.adLink : 'javascript:void(0)'"
            :preview="props.preview"
            :configure="props.configure"
            single-class="single_4"
            dcs-id="ydyw_pic"
            :shop-id="shopId"
          />
        </div>
      </template>
    </template>
    <div v-if="props.moveName == 70" ref="single_4_container" class="comSingleContainerWrap" style="block">
      <template v-if="props.pageDatasingle4 != null && props.showPageDatasingle">
        <template v-for="(single, index) in props.pageDatasingle4.dataList.ad">
          <div :key="index" class="single_4_container">
            <Single
              :key="`5_single_${index}`"
              :bannersrc="single.imgSrc"
              :adlink="single.adLink ? single.adLink : 'javascript:void(0)'"
              :preview="props.preview"
              :configure="props.configure"
              single-class="single_4"
              dcs-id="ydyw_pic"
              :shop-id="shopId"
            />
          </div>
        </template>
      </template>
    </div>

    <!-- 精准营销banner图 -->
    <template v-if="goodsTabData.comPageDatasingle2">
      <div class="single_4_container">
        <Single
          :key="`5_single_makting_0`"
          :bannersrc="goodsTabData.pageDatasingle2.imgUrl"
          :adlink="goodsTabData.pageDatasingle2.goodsLink"
          :preview="props.preview"
          :configure="props.configure"
          single-class="single_4"
          dcs-id="ydyw_pic"
          :shop-id="shopId"
        />
      </div>
    </template>

    <div :class="{ mt7: props.floor.dec != '1' }" class="goods">
      <List
        v-if="goodslist && goodslist.length && !goodsTabData.goodsListSJZQ"
        id="activity-detail__main"
        v-model="moveTabData.loading"
        :finished="moveTabData.moveTabFinish"
        :immediate-check="false"
        finished-text="没有更多了"
        class="broad-cast__list"
        @load="getGoodsList"
      >
        <ul class="goods-list" :class="['cp-' + props.componentCode, 'design-' + props.floor.design]">
          <li v-for="(item, key) in goodslist" :key="key">
            <a href="javascript:void(0)" @click="goToDetail(item)">
              <img v-if="item.imageUrl" :src="item.imageUrl" />
              <img v-else :src="item.imgUrl" />
              <p class="title">
                {{ item.goodsName }}
              </p>
              <p v-if="props.showSubtitle" class="subtitle">
                {{ item.subTitle }}
              </p>
              <PriceCom
                v-if="item.priceFrom"
                :price-from="item.priceFrom"
                :shop-price="item.shopPrice"
                :price-section="item.priceSection"
              />
              <p
                v-else-if="props.selectorKey == 3"
                class="price"
                :class="{ pricegrey: props.selectorKey == 3 }"
              >
                <span>
                  <em>￥</em>
                  {{
                    item.priceMin
                      ? (item.priceMin / 100).toFixed(2)
                      : (item.price / 100).toFixed(2)
                  }}起
                </span>
                <span class="text-line-through">
                  {{ (item.price / 100).toFixed(2) }}
                </span>
              </p>
              <p v-else-if="item.sku && item.sku[0] && item.sku[0].price != null" class="price">
                <em>￥</em>
                {{ (item.sku[0].price / 100).toFixed(2) }}
              </p>
              <p v-else class="price">
                <em>￥</em>
                {{
                  item.frontEndPrecisionMarketingSettingLogo
                    ? item.price
                    : (item.price / 100).toFixed(2)
                }}
              </p>
            </a>
            <span
              v-if="!props.configure && props.showShare"
              class="share-goods"
              :class="{ 'share-tpl4': props.tplId == 4 }"
              @click="openShare(item)"
            ></span>
            <div class="item-shixiao-icon"></div>
          </li>
        </ul>
      </List>
      <List
        v-else-if="props.floor.title == '手机专区' && goodsTabData.goodsListSJZQ && goodsTabData.goodsListSJZQ.length
        "
        id="activity-detail__main"
        v-model="goodsTabData.loading"
        :finished="goodsTabData.scrollFinish"
        :immediate-check="false"
        finished-text="没有更多了"
        class="broad-cast__list"
        @load="getMobileZone"
      >
        <ul class="goods-list" :class="['cp-' + props.componentCode, 'design-' + props.floor.design]">
          <li
            v-for="(item, key) in goodsTabData.goodsListSJZQ"
            :key="key"
            :class="{ shixiao: item.invalid == 1 && props.configure }"
          >
            <a href="javascript:void(0)" @click="goToDetail(item)">
              <img v-if="item.imageUrl" :src="item.imageUrl" />
              <img v-else :src="item.imgUrl" />
              <p class="title">
                {{ item.goodsName }}
              </p>
              <p class="subtitle">
                {{ item.subTitle }}
              </p>
              <p v-if="item.sku && item.sku[0] && item.sku[0].price != null" class="price">
                <em>￥</em>
                {{ (item.sku[0].price / 100).toFixed(2) }}
              </p>
              <p v-else class="price">
                <em>￥</em>
                {{ (item.price / 100).toFixed(2) }}
              </p>
            </a>
            <a
              v-if="props.configure"
              href="javascript:void(0);"
              class="item-close ac_remove_goods"
              @click="delGoods(item.adId)"
            ></a>
            <span
              v-if="!props.configure && props.showShare"
              class="share-goods"
              :class="{ 'share-tpl4': props.tplId == 4 }"
              @click="openShare(item)"
            ></span>
            <div class="item-shixiao-icon"></div>
          </li>
          <li v-if="props.configure && goodslist.length < 20" class="item-add" @click="openAddGoods(goodslist)">
          </li>
        </ul>
      </List>
      <ul v-else class="goods-tab-no">
        <li class="tab-no-con">
          <div>
            <img src="~@/assets/index_img/ice_tab_no.png" />
            <p>商品正在准备中，敬请期待 …</p>
          </div>
        </li>
      </ul>
    </div>
  </Tab>
</template>
<script setup>
import EventBus from "@/api/eventbus"
import shopAPI from "@/api/shop"
import Single from "@/components/index/single"
import insertCode from "@/utils/insertCode"
import UA from "@/utils/ua"
import { getAllSearchParamsArray, getCookie, setSearchParamsArray } from "@/utils/utils"
import Vue ,{reactive,defineProps,defineEmits,computed,watch, getCurrentInstance, onMounted,onUnmounted, onBeforeMount} from "vue"
import { DropdownMenu, DropdownItem,List,Icon,Button,Tab } from "vant"
import PrecisionMarketing from "@/model/precisionMarketing"
import _ from "lodash"
import PriceCom from "@/components/index/price.vue"
const emit = defineEmits(["emitPrecisionMarketing"])
const props = defineProps({
  pagedata: {
    type: Array,
    default: (data) => {
      return data
    },
  },
  items: {
    type: Array,
    default: () => {
      return []
    },
  },
  floor: {
    type: Object,
    default: () => {
      return {}
    },
  },
  componentCode: {
    type: String,
    default: null,
  },
  floorId: {
    type: String,
    default: null,
  },
  floorSort: {
    type: [String, Number],
    default: null,
  },
  selectorKey: {
    type: [String, Number],
    default: null,
  },
  preview: {
    type: String,
    default: null,
  },
  configure: {
    type: String,
    default: null,
  },
  getGoodsData: {
    type: Function,
    default: (fn) => {
      return fn
    },
  },
  tplId: {
    type: String,
    default: null,
  },
  showPageDatasingle: {
    type: Boolean,
    default: false,
  },
  pageDatasingle4: {
    type: Object,
    default: () => {
      return null
    },
  },
  showShare: {
    type: Boolean,
    default: false,
  },
  showSubtitle: {
    type: Boolean,
    default: true,
  },

  /* 子导航标识 */
  moveName: {//70是办套餐
    type: Number,
    default: 13,
  },

  /* 导航标识 */
  activeName: {
    type: [Number, String],
    default: 0,
  },
})
const moveTabData = reactive({
  moveTabType: null,//每一栏的类型
  moveTabFinish: false,//每一栏分页的终点
  loading: false,
  pageSize: 18,
  pageNum: 2
})
const shopMobileZone = reactive({
  brands: [],
  maxPrice: null,
  minPrice: null,
  order: null,
  pageNum: 1,
  pageSize: 10,
  shopId: null,
})
const pageIndex = reactive({
  minPrice: "",
  maxPrice: "",
})
const brandIndex = [
  { id: "1194787974859661312", ind: "新飞" },
  { id: "908187710050103299", ind: "华为(HUAWEI)" },
  { id: "1017664999086825472", ind: "中国移动" },
  { id: "1014081019875893248", ind: "荣耀" },
  { id: "908187710058491907", ind: "OPPO" },
  { id: "908187710058491908", ind: "vivo" },
  { id: "1231916995942617088", ind: "小度" },
  { id: "1296724663567581184", ind: "天猫精灵" },
  { id: "1296726752276447232", ind: "魅博" },
  { id: "1296726840612683776", ind: "蜗牛生活" },
  { id: "1296726928563044352", ind: "Ezvalo几光" },
  { id: "1296727016509210624", ind: "KAWOO" },
  { id: "1296727105285849088", ind: "DGQ" },
  { id: "1296727273162866688", ind: "诺比克" },
  { id: "1296727360349863936", ind: "林央" },
  { id: "1296727449378160640", ind: "深名扬" },
  { id: "1296727534186987520", ind: "美士家" },
  { id: "908187710071074817", ind: "中兴(ZTE)" },
  { id: "1392326308941189120", ind: "摩托罗拉" },
  { id: "1381524224488587264", ind: "Realme真我" },
  { id: "908187710050103300", ind: "小米(MI)" },
]
const mzIndex = ["综合推荐", "价格", "品牌", "价格区间"]
const sectionIndex = ["0-1499", "1499-2699", "2699-10000"]
const goodsTabData = reactive({
  pageData: null,
  loading: false,
  scrollFinish: false,
  pageDatasingle: null,
  showGoodsDialog: false,
  lock: false,
  searchBarFixed: false,
  activeVc: false,
  activeGb: false,
  godsSource: null,
  godsDUrl: null,
  lockAdId: null,
  goodsListSJZQ: null,
  brandXz: false,
  sectionXz: false,
  mobileZone: 0,
  cur: 0,
  spanIndex: [],
  spanIndexInd: [],
  popSectionXz: -1,
  show: false,
  tutalnum: 1,
  pageNum: 1,
  styleObj1: {
    top: null,
    display: "none",
  },
  styleObj2: {
    top: null,
    display: "none",
  },
  rule: 0,
  /* 办套餐精准营销banner数据 */
  pageDatasingle2: null,
  // 首页分页展示第二页及以上数据
  pageGoodsList: [],
})
/* 是否显示精准营销banner图 */
const comPageDatasingle2 = computed(() => {
  // 是否有iop数据
  const filterData =
        props.pagedata && props.pagedata.find((item) => item.floorSort == 70)
  const adData = _.get(filterData, "dataList.ad", [])
  const isMakting = adData.every(
    (item) => item.frontEndPrecisionMarketingSettingLogo
  )

  // 当前切换位办套餐
  const title = props.floor.title == "办套餐"
  const pageDatasingle2 = goodsTabData.pageDatasingle2
  return pageDatasingle2 && title && props.configure && isMakting
})
/* 办套餐是否为精准营销数据 */
const isPackageMakrting = computed(() => {
  if (!props.pagedata) return null
  const filterData = props.pagedata.find((item) => item.floorSort == 70)
  const adData = _.get(filterData, "dataList.ad", [])
  const isMakting = adData.every(
    (item) => item.frontEndPrecisionMarketingSettingLogo
  )
  return isMakting && adData.length
})
const getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null
let pageInfo = {}
if (proxy) {
  pageInfo = proxy.$store.getters.pageInfo
}
let shopId = pageInfo.pageInfo ? pageInfo.pageInfo.shopId : null
const goodslist = computed(() => {
  let mygoodslist = []
  if (props.items && props.items.length > 0) {
    props.items.forEach((element) => {
      if (element.goodsSource == 2) {
        //异业都显示
        mygoodslist.push(element)
        return
      }
      //1、o2oGoodsStatus 2 下线 3上线 运营位下线，店铺首页不展示商品
      //2、goodsStatus 51是上架 52商品下架，这两种状态店铺首页应该展示商品，可以进入详情页
      if (
        (element.o2oGoodsStatus == 2 ||
                    (element.goodsStatus != 51 && element.goodsStatus != 52)) &&
                props.configure != 1
      ) {
        return
      }
      mygoodslist.push(element)
    })
  }
  if (goodsTabData.pageGoodsList && goodsTabData.pageGoodsList.length > 0) {
    mygoodslist = mygoodslist.concat(goodsTabData.pageGoodsList)
  }
  return mygoodslist
})
watch(() => props.pagedata, (val) => {
  if (props.floor.title == "办流量" && props.configure != 1 && val) {
    val.forEach((element, index) => {
      if (element.componentCode == "single") {
        goodsTabData.pageDatasingle = element
      }
    })
  }

  // 设置办套餐精准营销banner
  goodsTabData.pageDatasingle2 = PrecisionMarketing.setMarketingBannerData(val)
},{immediate: true})
watch(()=>props.moveName,(val)=>{
  let ref = document.querySelectorAll(".comSingleContainerWrap")
  if (props.activeName == "q1" && val == 70) {
    // 如果是办套餐
    if (isPackageMakrting.value) {
      if (ref) {
        Array.from(ref).forEach((item) => (item.style.display = "none"))
      }
    } else {
      if (ref) {
        Array.from(ref).forEach((item) => (item.style.display = "block"))
      }
    }
  }
},{immediate: true})
onBeforeMount(()=>{
  if (props.floor.title == "手机专区") {
    getMobileZone()
    window.addEventListener("scroll", goodsTabData.handleScroll)
  }
})
onMounted(()=>{
  hidePopup(1)
  hidePopup(2)
})
onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll)
})
function openShare(item) {
  // 如果点击商品分享的是精准营销商品, 调用店铺分享并加入标识
  if (
    item.frontEndPrecisionMarketingSettingLogo ||
        props.floorSort == 70 ||
        item.isDoMeal
  ) {
    emit("emitPrecisionMarketing", item)
    return
  }

  let share = {
    ...item,
    title: item.goodsName,
    url: item.goodsLink,
    desc: item.subTitle || item.goodsLink,
    imgUrl: item.goodsSource == 2 ? item.imgUrl : "https:" + item.imgUrl,
    price:
            props.selectorKey == 3 && item.priceMin ? item.priceMin : item.price,
  }
  if (item.goodsDetailUrl && item.imageUrl) {
    share = {
      title: item.goodsName,
      url: item.goodsDetailUrl,
      desc: "",
      imgUrl: item.imageUrl,
      price:
                item.sku && item.sku[0] && item.sku[0].price != null
                  ? item.sku[0].price
                  : item.price,
    }
  }
  let allParams = getAllSearchParamsArray(location.href)
  let ac_id = allParams["WT.ac_id"] || getCookie("ac_id")
  if (ac_id) {
    share.url = setSearchParamsArray(share.url, { "WT.ac_id": ac_id })
  }
  insertCode(getDcsId("yd_index", item) + "_share")
  share.dcsId = getDcsId("yd_sharegoods", item) + "_share"
  let isExecuteNow = UA.isWechatWork
  EventBus.$emit("openShareDialog", "goods", share, isExecuteNow)
}
async function getGoodsList(type) {//商品分页用
  if (goodslist.value && goodslist.value.length < moveTabData.pageSize) {
    //没有第二页
    moveTabData.moveTabFinish = true
    return false
  }
  moveTabData.loading = true
  let goodsData = await props.getGoodsData(props.floorSort, moveTabData.pageSize, moveTabData.pageNum)
  goodsTabData.pageGoodsList = goodsTabData.pageGoodsList.concat(goodsData)
  moveTabData.loading = false
  if (goodsData.length < moveTabData.pageSize) {
    moveTabData.moveTabFinish = true
  } else {
    moveTabData.pageNum++
  }
}
function inputChange(ind) {
  //输入框值改变
  if (ind == 1) {
    pageIndex.minPrice = pageIndex.minPrice.replace(/[^\d]/g, "")
  } else {
    pageIndex.maxPrice = pageIndex.maxPrice.replace(/[^\d]/g, "")
  }
  if (
    pageIndex.minPrice != shopMobileZone.minPrice ||
        pageIndex.maxPrice != shopMobileZone.maxPrice
  ) {
    goodsTabData.popSectionXz = -1
  }
}
function clickMobileZone(index) {
  goodsTabData.styleObj1.top = null
  goodsTabData.styleObj2.top = null
  goodsTabData.styleObj1.display = "none"
  goodsTabData.styleObj2.display = "none"
  if (goodsTabData.spanIndex.length > 0) {
    document.querySelector(".mobile_zone2 span").innerText =
            goodsTabData.spanIndexInd
    goodsTabData.brandXz = true
  } else {
    document.querySelector(".mobile_zone2 span").innerText = "品牌"
    goodsTabData.brandXz = false
  }
  if (index == 0) {
    shopMobileZone.order = null
    shopMobileZone.pageNum = 1
    getMobileZone()
  }
  if (index == 1) {
    shopMobileZone.pageNum = 1
    if (shopMobileZone.order == 0) {
      shopMobileZone.order = 1
      getMobileZone()
    } else {
      shopMobileZone.order = 0
      getMobileZone()
    }
  }
}
function showPopup(index) {
  if (index == 2) {
    goodsTabData.activeVc = false
    goodsTabData.styleObj2.top = null
    goodsTabData.styleObj2.display = "none"
    goodsTabData.styleObj1.top = "80px"
    goodsTabData.styleObj1.display = "block"
  } else {
    goodsTabData.activeGb = false
    goodsTabData.styleObj1.top = null
    goodsTabData.styleObj1.display = "none"
    goodsTabData.styleObj2.top = "80px"
    goodsTabData.styleObj2.display = "block"
  }
}
function handleScroll() {
  var scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop
  if (!document.querySelector(".van-tabs")) {
    return false
  }
  var offsetTop = document.querySelector(".van-tabs").offsetTop
  if (scrollTop >= offsetTop + 44) {
    goodsTabData.searchBarFixed = true
  } else {
    goodsTabData.searchBarFixed = false
  }
}
function brandXzP(tit, ind) {
  let arrIndex = goodsTabData.spanIndex.indexOf(tit)
  let arrIndexind = goodsTabData.spanIndexInd.indexOf(ind)
  if (arrIndex > -1) {
    goodsTabData.spanIndex.splice(arrIndex, 1)
    goodsTabData.spanIndexInd.splice(arrIndexind, 1)
  } else {
    goodsTabData.spanIndex.push(tit)
    goodsTabData.spanIndexInd.push(ind)
  }
}
function hidePopup(index, popcz) {
  if (index == 2) {
    if (popcz && popcz == "popcz") {
      shopMobileZone.brands = []
      goodsTabData.spanIndex = []
      document.querySelector(".mobile_zone2 span").innerText = "品牌"
      goodsTabData.brandXz = false
    } else {
      goodsTabData.styleObj1.top = null
      goodsTabData.styleObj1.display = "none"
      goodsTabData.activeVc = true
      goodsTabData.activeGb = true
    }
  } else {
    if (popcz && popcz == "popcz") {
      pageIndex.maxPrice = ""
      pageIndex.minPrice = ""
      shopMobileZone.maxPrice = null
      shopMobileZone.minPrice = null
      document.querySelector(".mobile_zone3 span").innerText = "价格区间"
      goodsTabData.sectionXz = false
      goodsTabData.popSectionXz = -1
    } else {
      goodsTabData.styleObj2.top = null
      goodsTabData.styleObj2.display = "none"
      goodsTabData.activeVc = true
      goodsTabData.activeGb = true
    }
  }
}
function sectionPopup(e) {
  let int = e.currentTarget.querySelector("i").innerText
  let spli = int.split("-")
  pageIndex.maxPrice = spli[1]
  pageIndex.minPrice = spli[0]
  shopMobileZone.maxPrice = spli[1]
  shopMobileZone.minPrice = spli[0]
}
function wcshoPopup(index) {
  hidePopup(index)
  if (index == 2) {
    shopMobileZone.brands = goodsTabData.spanIndex
    goodsTabData.activeVc = true
    if (goodsTabData.spanIndex.length > 0) {
      document.querySelector(".mobile_zone2 span").innerText =
                goodsTabData.spanIndexInd
      goodsTabData.brandXz = true
      shopMobileZone.pageNum = 1
      getMobileZone()
    } else {
      document.querySelector(".mobile_zone2 span").innerText = "品牌"
      shopMobileZone.pageNum = 1
      getMobileZone()
    }
  } else {
    let maxP = 0
    let minP = 0
    goodsTabData.activeGb = true
    if (pageIndex.maxPrice != "") {
      shopMobileZone.maxPrice = pageIndex.maxPrice * 100
      maxP = pageIndex.maxPrice
    }
    if (pageIndex.minPrice != "") {
      shopMobileZone.minPrice = pageIndex.minPrice * 100
      minP = pageIndex.minPrice
    }
    if (pageIndex.maxPrice == "" && pageIndex.minPrice == "") {
      document.querySelector(".mobile_zone3 span").innerText = "价格区间"
      shopMobileZone.maxPrice = null
      shopMobileZone.minPrice = null
      goodsTabData.sectionXz = false
    } else {
      if (parseInt(minP) > parseInt(maxP)) {
        pageIndex.maxPrice = minP
        pageIndex.minPrice = maxP
        shopMobileZone.maxPrice = minP * 100
        shopMobileZone.minPrice = maxP * 100
        document.querySelector(".mobile_zone3 span").innerText =
                    "¥" + maxP + "-" + minP
      } else {
        pageIndex.maxPrice = maxP
        pageIndex.minPrice = minP
        document.querySelector(".mobile_zone3 span").innerText =
                    "¥" + minP + "-" + maxP
      }
      goodsTabData.sectionXz = true
    }
    shopMobileZone.pageNum = 1
    getMobileZone()
  }
}
function getMobileZone() {
  shopMobileZone.shopId = shopId
  goodsTabData.loading = true
  shopAPI.getMobileZone(shopMobileZone).then((r) => {
    goodsTabData.loading = false
    if (r.code == 0) {
      if (r.data && r.data.goods != null) {
        goodsTabData.tutalnum = Math.ceil(r.data.total / 10)
        if (shopMobileZone.pageNum == 1) {
          goodsTabData.goodsListSJZQ = r.data.goods
        } else {
          goodsTabData.goodsListSJZQ = [...goodsTabData.goodsListSJZQ, ...r.data.goods]
        }
        if (shopMobileZone.pageNum < goodsTabData.tutalnum) {
          shopMobileZone.pageNum = shopMobileZone.pageNum + 1
        } else {
          goodsTabData.scrollFinish = true
        }
      }
    }
  })
}
async function goToDetail(item) {
  if (props.preview) {
    return
  }
  let dcsId = getDcsId("yd_index", item)
  let godsDUrl = item.goodsLink
  if (item.goodsType && item.goodsDetailUrl) {
    godsDUrl = item.goodsDetailUrl
  }
  let allParams = getAllSearchParamsArray(location.href)
  let ac_id = allParams["WT.ac_id"] || getCookie("ac_id")
  if (ac_id) {
    godsDUrl = setSearchParamsArray(godsDUrl, { "WT.ac_id": ac_id })
  } else {
    const isWXMapp = await UA.isWeChatMiniApp()
    if (isWXMapp) {
      godsDUrl = setSearchParamsArray(godsDUrl, { "WT.ac_id": 'SHOP_APPLET_TRANSACT' })
    } else {
      godsDUrl = setSearchParamsArray(godsDUrl, { "WT.ac_id": 'SHOP_DIRECT_TRANSACTION' })
    }

  }
  insertCode(dcsId, godsDUrl)
}
function getDcsId(dcsId, item) {
  let source = item.goodsSource
  let sortKey = props.selectorKey
  if (item.goodsType && item.goodsDetailUrl) {
    source = item.goodsType
    sortKey = 9
  } else if (source === 2 && sortKey !== 6) {
    sortKey = 1000
  }
  let dcs_id = dcsId + "_" + shopId
  switch (sortKey) {
  case 1:
    dcs_id += "_bll" //办流量
    break
  case 2:
    dcs_id += "_btc" //办套餐
    break
  case 3:
    dcs_id += "_ydyx" //移动优选
    break
  case 4:
    dcs_id += "_xhm" //选号码
    break
  case 5:
    dcs_id += "_szjt" //数智家挺
    break

  case 6:
    dcs_id += "_bkcp" //爆款潮品  //非内蒙专用 key临时为6 异业商品
    break
  case 7:
    dcs_id += "_qycs" //权益超市
    break
  case 8:
    dcs_id += "_byw" //办业务
    break
  case 9:
    dcs_id += "_sjzq" //手机专区   //内蒙专用 key临时为9 数智商城数据
    break
  case 1000:
    dcs_id += "_smcp" //数码潮品  //内蒙专用 key临时为10 异业自有都有
    break
  default:
    dcs_id += ""
  }

  if (source == 2) {
    dcs_id += "_yysp"
  } else if (source == 1) {
    dcs_id += "_zysp"
  } else {
    dcs_id += "_szsp"
  }
  return dcs_id + "_" + item.goodsId
}
</script>
<style>
span {
    margin-top: 0;
}
</style>
<style lang="scss" scoped>
body.sroll {
    overflow: hidden;
    position: fixed;
}

.goods-tt {
    height: 45px;
    line-height: 45px;
    font-size: 16px;
    color: #333333;
    padding-left: 39px;
    margin-bottom: 1px;
}

.van-tab__pane .single img {
    padding: 7.5px 0;
}

.mobilezone_style .van-tabs__wrap {
    height: auto;
}

.iconplaytop::after {
    // content: "\F0A1";
    content: "\E702";
    display: inline-block;
    font-size: 12.375px;
    margin-left: -2.6250000000000004px;
    overflow: hidden;
}

.iconplaytop::before {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    font-size: 12.375px;
    display: inline-block;
    margin-right: -2.6250000000000004px;
    overflow: hidden;
}

.goods {
    &:nth-child(5n + 1) .goods-tt {
        background: url(~@/assets/index_img/icon_01.png) 15px 12px no-repeat #fff;
        background-size: 20px 20px;
    }

    &:nth-child(5n + 2) .goods-tt {
        background: url(~@/assets/index_img/icon_02.png) 15px 12px no-repeat #fff;
        background-size: 20px 20px;
    }

    &:nth-child(5n + 3) .goods-tt {
        background: url(~@/assets/index_img/icon_03.png) 15px 12px no-repeat #fff;
        background-size: 20px 20px;
    }

    &:nth-child(5n + 4) .goods-tt {
        background: url(~@/assets/index_img/icon_04.png) 15px 12px no-repeat #fff;
        background-size: 20px 20px;
    }

    &:nth-child(5n + 5) .goods-tt {
        background: url(~@/assets/index_img/icon_05.png) 15px 12px no-repeat #fff;
        background-size: 20px 20px;
    }
}

.goods-list {
    display: flex;
    flex-direction: row;
    flex-flow: row wrap;

    li {
        list-style: none;
        position: relative;
        font-size: 13px;
        line-height: 20px;
        background: #fff;
        margin-bottom: 1px;

        p {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        p.title {
            font-size: 13px;
            color: #333333;
            height: 24px;
            line-height: 24px;
        }

        p.subtitle {
            font-size: 12px;
            color: #666666;
            height: 24px;
            line-height: 24px;
        }

        p.price {
            font-size: 12.5px;
            color: #ed2668;
            height: 24px;
            line-height: 24px;
            font-weight: bold;

            em {
                font-size: 11px;
                margin-right: -5px;
            }

            &.pricegrey {
                color: #ccc;

                span:first-child {
                    color: #ed2668;
                }
            }
        }
    }

    &.cp-1line2 {
        li {
            width: 49%;
            height: 244px;
            padding: 15px 18px 12px;

            &:nth-child(2n + 1) {
                margin-right: 1px;
            }

            img {
                width: 150px;
                height: 150px;
            }
        }
    }

    &.design-2 {
        li {
            height: 90px;

            img {
                width: 56px;
                height: 56px;
                float: right;
                margin-left: 12px;
            }
        }
    }

    &.design-3 {
        li {
            height: 90px;

            img {
                width: 56px;
                height: 56px;
                float: left;
                margin-right: 12px;
            }
        }
    }

    &.cp-1line3 {
        li {
            text-align: center;
            width: 33%;
            height: 136px;
            margin-right: 1px;
            padding: 13px 12px 8px;

            &:nth-last-child(1) {
                margin-right: 0px;
            }

            img {
                width: 45px;
                height: 45px;
            }

            p.title {
                font-weight: bold;
                font-size: 14px;
                height: 22px;
                line-height: 22px;
            }

            p.subtitle {
                font-size: 12px;
                height: 22px;
                line-height: 22px;
                color: #999;
            }
        }
    }

    &.cp-list {
        li {
            width: 375px;
            padding: 14px 22px;

            img {
                width: 73px;
                height: 73px;
                float: left;
                margin-right: 22px;
            }

            p.subtitle {
                color: #999;
            }

            &.item-add {
                background: #e6f0f4 url(~@/assets/index_img/icon_add.png) no-repeat center;
                background-size: 13%;
            }
        }
    }

    .share-goods {
        position: absolute;
        right: 0;
        bottom: 0;
        display: block;
        width: 18px;
        height: 18px;
        background: url(~@/assets/index_img/icon_share.png) center top no-repeat;
        background-size: 100% auto;
        z-index: 2;

        &.share-tpl4 {
            width: 12px;
            height: 12px;
            background: url(~@/assets/index_normal/share.png) center top no-repeat;
            background-size: contain;
        }
    }

    .item-add {
        background: #e6f0f4 url(~@/assets/index_img/icon_add.png) no-repeat center;
        background-size: 30%;
        width: 186.75px;
        height: 244px;
    }

    .item-close {
        position: absolute;
        width: 37.5px;
        height: 37.5px;
        border-radius: 50%;
        right: 0;
        top: 0;
        background: rgb(237, 38, 104) url(~@/assets/index_img/icon_close_w.png) no-repeat center center;
        background-size: 50%;
    }
}

.shixiao {
    -webkit-filter: grayscale(100%);
    -moz-filter: grayscale(100%);
    -ms-filter: grayscale(100%);
    -o-filter: grayscale(100%);
    filter: grayscale(100%);
    filter: gray;

    .item-shixiao-icon {
        width: 40px;
        height: 25px;
        background: url(~@/assets/index_img/shixiao.png) no-repeat;
        background-size: 100%;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 10px;
    }
}

.goods-tab-no {
    width: 100%;
    background: #fff;
    padding: 37.5px 0 75px;
    text-align: center;

    .tab-no-con {
        img {
            display: block;
            height: 67.5px;
            width: auto;
            margin: 0 auto 7.5px;
        }

        p {
            display: inline-block;
            line-height: 22.5px;
            font-size: 13.5px;
            color: #d6dce4;
        }
    }
}

.shzq_couponMain {
    .shzqTabTilte {
        width: 100%;
        display: flex;
        padding-left: 7.5px;

        li {
            flex: 1;
            line-height: 37.5px;
            font-size: 13.5px;
            text-align: center;
            color: #333;
            margin-right: 7.5px;

            .van-icon {
                transform: rotate(90deg);
                -webkit-transform: rotate(90deg);
                -moz-transform: rotate(90deg);
            }

            .iconplaytop {
                color: #333;
            }
        }

        .active {
            color: #fd5100;

            .iconplaytop_blu1::before {
                color: #fd5100;
            }

            .iconplaytop_blu2::after {
                color: #fd5100;
            }
        }

        .mobile_zone2.active,
        .mobile_zone3.active {
            border: 2px solid #f8f8f8;
            background: #f8f8f8;
            border-radius: 7.5px 7.5px 0 0;
        }

        .active.brand_xz {
            height: 31.5px;
        }

        .brand_xz {
            clear: both;
            border: 1px solid #ffa77f;
            border-radius: 18.75px;
            line-height: 30px;
            height: 28.5px;
            margin: 3.75px 7.5px 3.75px 0;
            padding: 0 7.5px;
            white-space: nowrap;
            color: #fd5100;

            span {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 60px;
                display: block;
                float: left;
            }
        }

        .active.active_vc,
        .active.active_gb {
            background: none;
            border-radius: 18.75px;
            border: 1px solid #fff;
        }
    }

    .van-dropdown-brand {
        display: none;
    }

    .van-dropdown-item {
        position: absolute;
    }

    .van-dropdown-item__content {
        z-index: 1;
        padding: 15px 3.75px;
        border-radius: 0 0 7.5px 7.5px;
        text-align: center;
        background: #f8f8f8;
        max-height: 92%;
        clear: both;

        .sjzq_brand {
            clear: both;
            padding-left: 17.25px;
            max-height: 116.25px;
            overflow: auto;
            margin-bottom: 3.75px;

            span {
                font-size: 12px;
                line-height: 30px;
                height: 30px;
                display: block;
                float: left;
                border: 1px solid #aaaaaa;
                border-radius: 18.75px;
                text-align: center;
                min-width: 105px;
                margin: 0 7.5px 11.25px 0;
                margin-top: 0 !important;
            }

            .spanxz {
                background: #fff;
                border-color: #ffa77f;
                color: #fd5100;
            }
        }

        .sjzq_section {
            clear: both;
            padding-left: 3.75px;

            input {
                width: 40%;
                border: none;
                background: #eeeeee;
                border-radius: 18.75px;
                padding: 7.5px 15px;
                margin-bottom: 15px;
                font-size: 12.750000000000002px;
                text-align: center;
            }

            span {
                padding: 0 11.25px;
                line-height: 26.25px;
                height: 26.25px;
                float: none;
                display: inline-block;
                border-radius: 7.5px;
                border: 1px solid #f8f8f8;
            }
        }

        .van-button {
            clear: both;
            width: 40%;
            margin: 0 7.5px;
            height: 37.5px;
            line-height: 37.5px;
            color: #aaaaaa;
            background: #fff;
            border: 1px solid #aaaaaa;
        }

        .van-button-wc {
            clear: both;
            border: 1px solid #f8f8f8;
            height: 39.15px;
            line-height: 39px;
            color: #fff;
            background-image: linear-gradient(to right, #fac522, #ff9524);
        }
    }
}

.shzq_couponMain_fixed {
    position: fixed;
    top: 43.5px;
    width: 100%;
    background: #fff;
    z-index: 1000;

    .van-dropdown-item {
        position: fixed;
    }
}

.single_4_container {
    margin-top: 15px;
}

.text-line-through {
    font-weight: normal;
    text-decoration-line: line-through;
    font-size: 12px;
    color: #ccc;
}
</style>
