<template>
  <Popup v-model="membergiftData.membergifts" class="membergifts">
    <div class="title">
      <img src="~@/assets/index_img/quan_title.png" alt="" />
    </div>
    <div class="quanbox">
      <div
        v-for="(item, key) in membergiftData.quanData"
        :key="key"
        class="quan"
        :couponCode="item.couponCode"
        :checkCode="item.checkCode"
      >
        <div class="quanprice">
          {{ item.price }}
        </div>
        <div class="quanname">
          {{ item.couponName }}
        </div>
        <span class="quantype" :class="'type' + item.checkCode"></span>
      </div>
    </div>
    <div class="bgwhite">
      <a
        href="javascript:void(0);"
        :class="membergiftData.btgrey == true ? 'disable' : ''"
        @click="couponLink()"
      >{{ quanbuttonMap[membergiftData.quanbuttonType] }}</a>
    </div>
    <a
      href="javascript:void(0);"
      class="quanclose"
      @click="CouponClose()"
    ><img
      src="~@/assets/index_img/quan_bt_close.png"
      alt=""
    /></a>
  </Popup>
</template>

<script setup>
import VipApi from "@/api/vip"
import {getCurrentInstance, onMounted, reactive,ref} from "vue"
import { mapGetters } from "vuex"
import { Toast, Popup } from "vant"
import EventBus from "@/api/eventbus"
const quanbuttonMap = {
  0: "一键领取",
  1: "去使用",
  2: "一键领取"
}
const getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null
let pageInfo = {}
if(proxy){
  pageInfo = proxy.$store.getters.pageInfo
}
const membergiftData = reactive({
  btgrey: false,
  membergifts: false,
  quanbuttonMap,
  quanbuttonType: 0,
  couponCode: [],
  activityId:'1123123',
  quanData: [],
  quanlink:""
})
onMounted(()=>{
  EventBus.$on("shareparam", shareparam)
})
function shareparam(link) {
  membergiftData.quanlink = link
}
//入会礼礼包查询
function CouponCheck(shopId) {
  VipApi.CouponCheck({
    shopId: shopId
  }).then(res => {
    if (res.code == 0) {
      if(res.data){
        membergiftData.quanData = res.data.couponCheck
        membergiftData.activityId = res.data.activityId
        let couponCode = [],
          couponCodeEnabled = [],
          couponCodeDisabled = []
        membergiftData.quanData.forEach(item => {
          if (item.checkCode == 0) {
            //checkCode，0可领取，1已领取，2已结束，3已抢光
            couponCodeEnabled.push(item.couponCode)
          } else if (item.checkCode > 1) {
            couponCodeDisabled.push(item.couponCode)
          }
          couponCode.push(item.couponCode)
        })
        if (couponCodeEnabled.length > 0) {
          membergiftData.quanbuttonType = 0
          //一键领取
          membergiftData.membergifts = true
        } else if (couponCodeDisabled.length == 2 || couponCode.length == 1 && couponCodeDisabled.length == 1) {
          membergiftData.quanbuttonType = 2
          membergiftData.btgrey = true
          //一键领取置灰
        } else {
          membergiftData.quanbuttonType = 1
          //去使用
        }
        membergiftData.couponCode = couponCodeEnabled
      }else{
        membergiftData.membergifts = false
      }
    } else {
      Toast(res.message)
    }
  })
}
//展示入会礼礼包
function ShowCouponPop(shopId) {
  VipApi.CouponCheck({
    shopId: shopId
  }).then(res => {
    if (res.code == 0) {
      if(res.data){
        membergiftData.quanData = res.data.couponCheck
        membergiftData.activityId = res.data.activityId
        let couponCode = [],
          couponCodeEnabled = [],
          couponCodeDisabled = []
        membergiftData.quanData.forEach(item => {
          if (item.checkCode == 0) {
            //checkCode，0可领取，1已领取，2已结束，3已抢光
            couponCodeEnabled.push(item.couponCode)
          } else if (item.checkCode > 1) {
            couponCodeDisabled.push(item.couponCode)
          }
          couponCode.push(item.couponCode)
        })
        if (couponCodeEnabled.length > 0) {
          membergiftData.quanbuttonType = 0
          //一键领取
        } else if (couponCodeDisabled.length == 2 || couponCode.length == 1 && couponCodeDisabled.length == 1) {
          membergiftData.quanbuttonType = 2
          membergiftData.btgrey = true
          //一键领取置灰
        } else {
          membergiftData.quanbuttonType = 1
          //去使用
        }
        membergiftData.couponCode = couponCodeEnabled
        membergiftData.membergifts = true
      }else{
        membergiftData.membergifts = false
      }
    } else {
      Toast(res.message)
    }
  })
}
function CouponClose() {
  membergiftData.membergifts = false
}
//入会礼礼包领取
function CouponReceive() {
  VipApi.CouponReceive({
    couponCode: membergiftData.couponCode,
    activityId: membergiftData.activityId
  }).then(res => {
    if (res.code == 0) {
      Toast({
        message: "领取成功，即将跳转到领取卡券页面",
        onClose: () => {
          window.location.href =
            "https://touch.10086.cn/i/mobile/mycoupons.html?shareParam=" +
            membergiftData.quanlink
        }
      })
    } else {
      Toast(res.message)
    }
  })
}
function couponLink() {
  if (membergiftData.quanbuttonType == 1) {
    //去使用
    window.location.href =
      "https://touch.10086.cn/i/mobile/mycoupons.html?shareParam=" +
      membergiftData.quanlink
  }
  if (membergiftData.quanbuttonType == 0) {
    //一键领取
    CouponReceive()
  }
}
</script>

<style lang="scss" scoped>
.membergifts {
  width: 315px;
  background: linear-gradient(139deg, #ff6b85 5%, #ff7ee4 98%);
  border-radius: 8px;
  overflow-y: inherit;
  padding-bottom: 86px;

  .title {
    height: 67px;
    line-height: 67px;
    text-align: center;
    img {
      height: 20px;
    }
  }
  .quanclose {
    position: absolute;
    bottom: -54px;
    left: 140px;
    right: 140px;
    img {
      width: 35px;
      height: 54px;
    }
  }
  .quanbox {
    max-height: 214px;
    overflow: hidden;
    clear: both;
    .quan {
      width: 272px;
      height: 86px;
      background: url(~@/assets/index_img/quan_bg.png) 0 0 no-repeat;
      background-size: 100% 100%;
      margin: 0 auto 21px;
      line-height: 86px;
      text-align: center;
      color: #cf4765;
      position: relative;
      .quanprice {
        width: 94px;
        float: left;
        font-weight: 600;
        font-size: 28px;
        overflow: hidden; /*超过部分不显示*/
        text-overflow: ellipsis; /*超过部分用点点表示*/
        white-space: nowrap; /*不换行*/
      }
      .quanname {
        width: 177px;
        float: right;
        padding: 0 10px;
        font-size: 17px;
        overflow: hidden; /*超过部分不显示*/
        text-overflow: ellipsis; /*超过部分用点点表示*/
        white-space: nowrap; /*不换行*/
      }
      .quantype {
        display: block;
        width: 44px;
        height: 34px;
        position: absolute;
        top: 0px;
        right: 5px;
        &.type1 {
          background: url(~@/assets/index_img/quan_got.png) 0 0 no-repeat;
          background-size: 100% 100%;
        }
        &.type2 {
          background: url(~@/assets/index_img/quan_end.png) 0 0 no-repeat;
          background-size: 100% 100%;
        }
        &.type3 {
          background: url(~@/assets/index_img/quan_empty.png) 0 0 no-repeat;
          background-size: 100% 100%;
        }
      }
    }
  }
  .bgwhite {
    width: 315px;
    height: 86px;
    background: #ffffff;
    border-radius: 0px 0px 8px 8px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    a {
      display: block;
      width: 266px;
      height: 46px;
      line-height: 46px;
      background: linear-gradient(90deg, #ffb72e, #ff4848 100%);
      border-radius: 26px;
      box-shadow: 0px 2px 11px 0px rgba(255, 0, 0, 0.28);
      font-size: 16px;
      color: #ffffff;
      font-weight: 400;
      margin: 19px auto 0;
      &.disable {
        background-image: linear-gradient(90deg, #e6e6e6 0%, #ababab 100%);
        box-shadow: 0 2px 11px 0 rgba(161, 161, 161, 0.28);
      }
    }
  }
}
</style>
