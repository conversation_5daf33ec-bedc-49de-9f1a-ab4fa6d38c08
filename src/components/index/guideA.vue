<template>
  <div v-if="props.items && props.items.length" class="guideA">
    <div v-for="(item, key) in props.items" :key="key" class="item">
      <a href="javascript:void(0)" @click="insertCodeGoods(key,item.adLink)">
        <img :src="item.imgSrc" />
        {{ item.title }}
      </a>
    </div>
  </div>
</template>

<script setup>
import insertCode from "@/utils/insertCode"
import {reactive,defineProps} from "vue"
const props = defineProps({
  items: {
    type: Array,
    default: () =>{
      return []
    }
  },
  preview: {
    type: String,
    default: null
  },
  floorId:{
    type:String,
    default: null
  }
})
function insertCodeGoods(key,url){
  if(props.preview){
    return
  }
  let dcs_id = key
  insertCode(props.floorId+"_guide_"+dcs_id,url)
}
</script>

<style lang="scss" scoped>

  .guideA {
    background: #fff;
    width: 375px;
    margin: 0 auto;
    display: flex;
    // flex-direction: row;
    min-height: 90px;
    flex-flow:row wrap;
    padding-top: 18px;
    .item {
      width:90px;
      padding-bottom: 18px;
      font-size: 13px;
      text-align: center;
      img {
        width: 35px;
        height: 35px;
        margin: 0 auto 5px auto;
        display: block;
      }
    }
  }
</style>