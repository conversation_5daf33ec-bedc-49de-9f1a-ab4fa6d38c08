<template>
  <div class="goodsSelector">
    <van-popup
      v-model="data.showGoodsDialog"
      round
      position="bottom"
      closeable
      close-icon="arrow-left"
      close-icon-position="top-left"
      :style="{ height: '65%' }"
    >
      <p class="goods-dialog-title">
        商品选择
      </p>
      <template v-if="!props.source && props.yiyeShop">
        <van-dropdown-menu>
          <van-dropdown-item v-model="data.allShopType" :options="data.option" />
        </van-dropdown-menu>
      </template>
      <template v-if="props.source == 1">
        <template v-if="props.tplId == 2 || props.tplId == 4">
          <div class="goodsTypeValue">
            {{ goodsTypeValue }}
          </div>
        </template>
        <template v-else>
          <van-dropdown-menu>
            <van-dropdown-item v-model="data.shopManageForm.goodsType" :options="data.option" />
          </van-dropdown-menu>
        </template>
      </template>
      <div class="list-title">
        <p class="radio"></p>
        <p class="goods-img">
          商品图片
        </p>
        <p class="goods-name">
          商品名称
        </p>
        <p class="goods-name">
          展示名称
        </p>
        <p class="goods-price">
          商品价格
        </p>
      </div>
      <van-list
        v-model="data.loading"
        :finished="data.finished"
        :finished-text="data.showNoYyData ? '' : '没有更多商品啦……'"
        class="vant-clearfix"
        :class="{ noYyData: data.showNoYyData }"
        @load="onLoad"
      >
        <van-radio-group
          v-if="props.selecttype && props.selecttype == 'radio'"
          v-model="data.checkboxValue"
          class="radia-group"
        >
          <van-cell v-for="(item, index) in data.list" :key="index">
            <div class="list-item">
              <p class="radio">
                <van-radio :name="item" @click="changeCheckbox(item)"></van-radio>
              </p>
              <p class="goods-img">
                <img :src="item.imgUrl" />
              </p>
              <div class="goods-name overEli">
                <span>{{ item.goodsName }}</span>
              </div>
              <div class="goods-name overEli">
                <span>{{ item.cmTitle || "--" }}</span>
              </div>
              <PriceCom
                v-if="item.priceFrom"
                :price-from="item.priceFrom"
                :shop-price="item.shopPrice"
                :price-section="item.priceSection"
                :show-symbol="false"
                price-class="goods-price"
              />
              <p v-else class="goods-price">
                {{ (item.minPrice / 100).toFixed(2) }}
              </p>
            </div>
          </van-cell>
          <div v-if="data.showNoYyData" class="noYyDataTitle">
            搜索不到商品，请先登录阿波罗平台添加商品
          </div>
        </van-radio-group>
        <van-checkbox-group v-else v-model="data.checkboxValue" class="radia-group">
          <van-cell v-for="(item, index) in data.list" :key="index">
            <div class="list-item">
              <p class="radio">
                <van-checkbox :name="item" @click="changeCheckbox(item)"></van-checkbox>
              </p>
              <p class="goods-img">
                <img :src="item.imgUrl" />
              </p>
              <div class="goods-name overEli">
                <span>{{ item.goodsName }}</span>
              </div>
              <div class="goods-name overEli">
                <span>{{ item.cmTitle || "--" }}</span>
              </div>
              <PriceCom
                v-if="item.priceFrom"
                :price-from="item.priceFrom"
                :shop-price="item.shopPrice"
                :price-section="item.priceSection"
                :show-symbol="false"
                price-class="goods-price"
              />
              <p v-else class="goods-price">
                {{ (item.minPrice / 100).toFixed(2) }}
              </p>
            </div>
          </van-cell>
          <div v-if="data.showNoYyData" class="noYyDataTitle">
            搜索不到商品，请先登录阿波罗平台添加商品
          </div>
        </van-checkbox-group>
        <div v-if="checkedId || data.checkedIdradio" class="btn-area">
          <div class="btn-cancle" @click="cancle">
            取消
          </div>
          <div class="btn-confirm" @click="confirm1">
            确认
          </div>
        </div>
      </van-list>
    </van-popup>
  </div>
</template>
<script setup>
import shopAPI from "@/api/shop"
import { getUrl, getImgUrl } from "@/utils/utils"
import Vue, { reactive, ref, computed, defineProps, toRefs, watch, onMounted, defineExpose } from "vue"
import {
  Popup,
  DropdownMenu,
  DropdownItem,
  List,
  Cell,
  CheckboxGroup,
  Checkbox,
  Toast,
  RadioGroup,
  Radio,
} from "vant"
import PriceCom from "@/components/index/price.vue"
Vue.use(Popup)
  .use(DropdownMenu)
  .use(DropdownItem)
  .use(List)
  .use(Cell)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Radio)
  .use(RadioGroup)
const data = reactive({
  showGoodsDialog: false,
  option: [
    { text: "全部商品", value: null },
    { text: "流量专区", value: 1 },
    { text: "套餐专区", value: 2 },
    { text: "终端专区", value: 3 },
    { text: "号卡专区", value: 4 },
    { text: "宽带专区", value: 5 },
    { text: "配件专区", value: 6 },
  ],
  allShopType: null,
  list: [],
  loading: false,
  finished: false,
  checkedIdradio: false,
  checkboxValue: [],
  shopManageForm: {
    goodsName: null,
    goodsIdInUse: null,
    goodsId: null,
    source: "",
    pageNum: 1,
    pageSize: 10,
    goodsType: null,
  },
  currentGoods: null,
  showNoYyData: false,
})
const props = defineProps({
  selecttype: {
    type: String,
    default: (params) => {
      return params
    },
  },
  confirm: {
    type: Function,
    default: (fn) => {
      return fn
    },
  },
  source: {
    type: [Number, String],
    default: null,
  },
  goodsType: {
    type: [Number, String],
    default: null,
  },
  useSortType: {
    type: Boolean,
    default: false,
  },
  goodsUsedId: {
    type: Array,
    default: () => {
      return []
    },
  },
  tplId: {
    type: String,
    default: null,
  },
  yiyeShop: {
    type: String,
    default: null,
  },
  title: {
    type: String,
    default: "",
  },
  reqsource: {
    type: Number,
    default: 1,
  },
})
const checkedId = computed(() => {
  return data.checkboxValue && data.checkboxValue.length > 0
})
const goodsTypeValue = computed(() => {
  const { title } = toRefs(props)
  return title.value
})
onMounted(() => {
  let { yiyeShop } = toRefs(props)
  if (yiyeShop && yiyeShop.value == "yiyeShop") {
    data.option.push({ text: "异业商品", value: "yiye" })
  }
})
watch(() => data.showGoodsDialog, (val) => {
  if (val) {
    const { source } = toRefs(props)
    data.shopManageForm.source = source.value
    changeValue()
  } else {
    cancle()
  }
})
watch(() => data.allShopType, (val, oldval) => {
  if (val !== oldval) {
    data.list = []
    data.shopManageForm.pageNum = 1
    data.finished = false
    data.shopManageForm.source = ""
    data.shopManageForm.goodsType = null
    if (val) {
      if (val == "yiye") {
        data.shopManageForm.source = 2
        data.shopManageForm.goodsType = null
      } else {
        data.shopManageForm.source = 1
        data.shopManageForm.goodsType = val
      }
    }
    onLoad()
  }
})
// watch([() => showGoodsDialog, () => data.allShopType], (newVal, oldVal) => {
//   console.log(newVal, oldVal)
// })
const openDialog = () => {
  data.allShopType = null
  data.showGoodsDialog = true
}
const changeValue = () => {
  data.list = []
  data.shopManageForm.pageNum = 1
  data.finished = false
  if (props.source == 1 || (!props.source && props.yiyeShop)) {
    data.shopManageForm.goodsType = props.goodsType
  }
  onLoad()
}
const onLoad = async() => {
  data.loading = true
  data.shopManageForm.goodsIdInUse = props.goodsUsedId
  let res = null
  if (props.useSortType) {
    res = await shopAPI
      .goodsSortSekector(data.shopManageForm, props.reqsource)
      .then((r) => {
        return r
      })
  } else {
    res = await shopAPI
      .goodsSekector(data.shopManageForm, props.reqsource)
      .then((r) => {
        return r
      })
  }
  if (res.code) {
    Toast(res.message)
  } else {
    if (data.shopManageForm.pageNum == 1) {
      data.list = []
    }
    if (!(res.data && res.data.goods)) {
      data.loading = false
      data.finished = true
      return false
    }
    res.data.goods.forEach((value, item) => {
      value.goodsLink =
                res.data.source == 1 ? getUrl(value.goodsLink) : value.goodsLink
      value.imgUrl =
                res.data.source == 1 ? getImgUrl(value.picture) : value.picture
      value.isdisabled = false
      value.goodsSource = res.data.source || 1
      //1、o2oGoodsStatus 2 下线 3上线 运营位下线
      //2、goodsStatus 51是上线 商品下架
      // if(res.source==1&&(value.o2oGoodsStatus==2||value.goodsStatus!=51)){
      //   value.isdisabled = true
      // }
      data.list.push(value)
    })
    data.loading = false
    if (
      (data.list && data.list.length == 0 && props.source == 2) ||
            (data.list &&
                data.list.length == 0 &&
                data.shopManageForm.source == 2)
    ) {
      data.showNoYyData = true
      data.finished = true
      return
    }
    if (res.data.totalPages > data.shopManageForm.pageNum) {
      data.shopManageForm.pageNum++
    } else {
      data.finished = true
    }
  }
}
const changeCheckbox = (item) => {
  if (props.selecttype && props.selecttype == "radio") {
    data.checkedIdradio = true
  }
  data.currentGoods = data.checkboxValue
}
const cancle = () => {
  data.checkboxValue = []
}
const confirm1 = () => {
  data.showGoodsDialog = false
  props.confirm(data.currentGoods)
}
// 主动暴露childMethod方法
defineExpose({
  data,
  openDialog
})
</script>
<style lang="scss" scoped>
.goods-dialog-title {
    font-size: 18px;
    height: 50px;
    line-height: 50px;
    text-align: center;
}

.vant-clearfix {
    height: 300px;
    overflow: auto;
}

.list-title,
.list-item {
    display: flex;
    text-align: center;

    .goods-name {
        flex: 1;
    }

    .goods-img {
        width: 80px;
    }
}

.list-title {
    font-size: 14px;
    color: #fff;
    background: #64bbff;
    height: 40px;
    line-height: 40px;

    .radio {
        width: 60px;
    }

    .goods-price {
        width: 80px;
    }
}

.overEli {
    height: 60px;
    display: flex;
    max-width: 170px;

    span {
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        align-self: center;
        line-height: 19px;
        text-overflow: ellipsis;
        display: -webkit-box;
        text-align: center;
        width: 100%;
        white-space: normal;
        word-wrap: break-word;
        word-break: break-all;
    }
}

.list-item {
    .radio {
        width: 40px;
        padding-top: 20px;
    }

    .goods-price {
        width: 62px;
        display: inline-flex;
        flex-flow: wrap;
        line-height: 1.8;
        align-items: center;

        :deep(span:nth-of-type(1)) {
            margin-right: 5px;
        }
    }

    p {
        font-size: 10px;
        height: 60px;
        line-height: 60px;

        &.goods-img {
            img {
                width: 50px;
                height: 50px;
            }
        }
    }
}

.btn-area {
    // display: none;
    position: fixed;
    z-index: 999;
    bottom: 0;
    width: 100%;
    height: 59.25px;
    line-height: 59.25px;
    text-align: center;
    font-size: 13.999987500000001px;
    background: #fff;

    .btn-cancle {
        float: left;
        width: 49.5%;
        color: #393939;
        background: #d9d9d9;
    }

    .btn-confirm {
        float: right;
        width: 49.5%;
        color: #fff;
        background: #64bbff;
    }
}

.noYyData {
    background: url(~@/assets/index_img/empty.png) center center no-repeat;
    background-size: 28%;
}

.noYyDataTitle {
    font-size: 16px;
    margin-top: 240px;
    text-align: center;
}

.goodsTypeValue {
    box-sizing: border-box;
    max-width: 100%;
    padding: 0 7.875px;
    color: #323233;
    font-size: 15px;
    height: 48px;
    line-height: 48px;
    text-align: center;
}

.goodsSelector :deep(.van-list__placeholder) {
    height: 59.25px;
}
</style>
