<template>
  <div v-if="props.items && props.items.length" class="guideC" :class="user.isMember&&(!props.hasbanner) ? 'nobanner' : ''">
    <div v-for="item in props.items" :key="item.linkId" class="item" :class="item.descId">
      <a href="javascript:void(0)" @click="insertCodeGoods(item)">
        <img :src="item.img" />
        {{ item.title }}
      </a>
    </div>
  </div>
</template>

<script setup>
import { getAllSearchParamsArray, getCookie } from "@/utils/utils"
import {defineProps,inject, getCurrentInstance,computed} from "vue"
import { Toast } from "vant"
import UA from "@/utils/ua"
import lbsApi from "@/api/lbs"
import insertCode from "@/utils/insertCode"

let getSon = inject("getSon")
const props = defineProps({
  items: {
    type: Array,
    default: () =>{
      return []
    }
  },
  preview: {
    type: String,
    default: null
  },
  quanlink: {
    type: String,
    default: null,
  },
  hasbanner:{
    type: Boolean,
    default:false
  }
})
let guideItme = null
const getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null

const user = computed(()=>{
  return (proxy && proxy.$store.getters && proxy.$store.getters.user) ? proxy.$store.getters.user : null
})
const pageInfo = computed(()=>{
  if(proxy){
    return proxy.$store.getters.pageInfo
  }else{
    return null
  }
})
function insertCodeGoods(item) {
  if (props.preview) {
    return
  }
  guideItme = item
  if(!item.showStatus){
    insertCode('yd_index_'+pageInfo.value.shopId+"_icon_"+item.descId)
    Toast(item.desc)
    return false
  }else if(user.value.userInfo==null){
    //强登
    openloginSelf(commonInsert)
  }else if(item.descId=='pdqh'){
    yuyue()
  }else{
    commonInsert()
  }
}
async function commonInsert(){
  if(guideItme.descId==='yyzq'){
    const isWXMapp = await UA.isWeChatMiniApp()
    if (isWXMapp) {
      guideItme.link=guideItme.link+(guideItme.link.includes('?')?'&':'?')+'WT.ac_id=SHOP_APPLET_TRANSACT'
    }else{
      let allParams = getAllSearchParamsArray(location.href)
      let ac_id = allParams["WT.ac_id"] || getCookie("ac_id")
      if (ac_id!='SHOP_SHARE') {
        guideItme.link=guideItme.link+(guideItme.link.includes('?')?'&':'?')+'WT.ac_id=SHOP_DIRECT_TRANSACTION'
      }else{
        guideItme.link=guideItme.link+(guideItme.link.includes('?')?'&':'?')+'WT.ac_id=SHOP_SHARE'
      }
    }
  }
  insertCode('yd_index_'+pageInfo.value.pageInfo.shopId+"_icon_"+guideItme.descId,guideItme.link)
}
function yuyue() {
  lbsApi
    .getUrlReq({
      pageType: 2,
      shopId: pageInfo.value.pageInfo.shopId,
    })
    .then((res) => {
      if (res.code == 0) {
        insertCode(
          "yd_index_" + pageInfo.value.pageInfo.shopId + "_icon_zxqh",
          res.data.url
        )
      } else {
        Toast(res.message)
      }
    })
}
function openloginSelf(callback) {
  return getSon.openlogin(callback)
}
</script>

<style lang="scss" scoped>

  .guideC {
    background: #fff;
    width:100%;
    padding-left: 10px;
    padding-right: 10px;
    margin: 0 auto;
    display: flex;
    min-height: 115px;
    flex-flow:row wrap;
    padding-top: 35px;
    &.nobanner{
      padding-top:52px;
    }
    .item {
      width:25%;
      padding-bottom: 25px;
      font-size: 13px;
      text-align: center;
      img {
        width: 42px;
        height: 42px;
        border-radius:0%;
        margin: 0 auto 10px auto;
        display: block;
      }
    }
  }
</style>
