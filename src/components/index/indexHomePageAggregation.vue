<template>
  <main>
    <!-- <referees v-if="list && list.ad && list.ad.length" :tel="telkey" /> -->
    <ul>
      <li
        v-for="(item, index) in list.ad"
        :key="index"
        @click="handleDetail(item)"
      >
        <div v-if="item.goodsList.length" class="lop">
          <div class="headertop">
            <div class="pongetitle">
              {{ item.ydShowTitle || "默认标题" }}
            </div>
            <div class="desc">
              <span>{{ item.ydShowSubtitle || "默认副标题--" }}</span>
            </div>
            <div class="more">
              <span>查看更多</span>
              <img src="~@/assets/index_img/rightj1active.png" alt="" />
            </div>
          </div>

          <div class="contentli">
            <div
              v-for="(item2, index2) in item.goodsList"
              :key="index2"
              class="con"
            >
              <div class="img">
                <img mode="aspectFill" :src="ablImgReplace(item2.goodsPictureUrl)" alt="" />
                <!-- <img mode="aspectFill" src="//i0.hdslb.com/bfs/archive/a4b8151219a138bf8d7084811b164da8e69defa8.jpg@672w_378h_1c" alt=""> -->
                <span>￥{{ item2.activityPrice?item2.activityPrice/1000:'' }}</span>
              </div>
            </div>
          </div>
        </div>
      </li>
    </ul>

  </main>
</template>

<script setup>
import insertCode from "@/utils/insertCode.js"
import {defineProps,watch,ref,} from 'vue'
const props = defineProps({
  dataList:{
    type:Object,
    default:() => {}
  },
  floorId:{
    type:String,
    default:() => ""
  }
})
const list = ref([])
// const telkey = ref(null)
watch(()=>props.dataList,()=>{
  if(!(props.dataList && props.dataList.ad)){
    return
  }const filterDataList = JSON.parse(JSON.stringify(props.dataList))
  let activityUrl = new URL(filterDataList.ad[0].activityUrl)
  let params = getQueryParam(activityUrl.href)
  // telkey.value = desDeclassified(params.key)

  filterDataList.ad.forEach(item => {
    if(item.goodsList.length < 4 && item.goodsList.length >= 1){
      let yuan = item.goodsList[0]
      let len = 4 - item.goodsList.length
      for(let i = 0; i < len; i++){
        item.goodsList.push(yuan)
      }
    }
  })
  list.value = filterDataList
},{immediate:true})
function ablImgReplace(url){
  if(url){
    return url.replace("qdzx.js.cmcc:19940/b2bimage","b2b.cmccsa.cn")
  }else{ return url
  }
}
function getQueryParam(url) {
  url = url == null ? window.location.href : url
  let search = url.substring(url.lastIndexOf('?') + 1)
  let query = {}
  let reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    let name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    query[name] = val
    return rs
  })
  return query
}

function handleDetail(item) {
  const url = item.activityUrl
  // insertCode(this.floorId+"_juheye_"+item.activityId,url)
  insertCode(`yd_index_${item.activityId}_juheye`,url)
}
</script>

<style lang="scss" scoped>
$mo2: 2;
main {
  padding: 0 10px / $mo2;
}
ul {
  li {
    .lop{
       padding: 20px / $mo2 30px / $mo2;
    border: 2px solid #eee;
    border-radius: 16px / $mo2;
    margin: 20px / $mo2 0;
    padding-bottom: 10px /$mo2;
    }
    .headertop {
      margin-bottom: 20px/ $mo2;
      display: flex;
      .pongetitle {
        font-size: 30px/ $mo2;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: flex;
        align-items: center;
      }
      .desc {
        color: #fff;
        margin-left: 20px/ $mo2;
        margin-right: 20px/ $mo2;

        flex: 1;
        display: flex;
        min-width: 0;
        span {
          display: block;
          overflow: hidden;
          padding: 5px/ $mo2 14px/ $mo2;
          background: #ff6664;
          border-radius: 20px/ $mo2;
          font-size: 24px/ $mo2;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .more {
        margin-left: auto;
        display: flex;
        align-items: center;
        font-size: 24px/ $mo2;
        color: #9e9e9e;
        span {
          margin-right: 10px/ $mo2;
        }
        img {
          width: 10px/ $mo2;
          height: 22px/ $mo2;
          margin-top: 2px/ $mo2;
        }
      }
    }
    .contentli {
      display: flex;
      align-items: center;
      .con {
        margin-right: 55px/ $mo2;
        &:last-child {
          margin-right: 0;
        }
        width: 128px/ $mo2;
        .img {
          width: 128px/ $mo2;
          img {
            width: 100%;
            height: 128px/ $mo2;
          }
          span {
            font-size: 24px/ $mo2;
            width: 100%;
            display: block;
            color: #ed2668;
            padding: 10px 0;
            text-align: center;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
          }
        }
      }
    }
  }
}
</style>
