<template>
  <div v-if="props.broadbandlink&&props.broadbandStatus==1" class="mt7 broadband">
    <h1 class="goods-tt">
      宽带预约
    </h1>
    <a href="javascript:void(0)" @click="insertCodeGoods('waistbanner_kdyy',props.broadbandlink)">
      <img class="pic" src="@/assets/index_img/pic_broadband_booking.png">
    </a>
  </div>
</template>
<script setup>
import {defineProps} from "vue"
import insertCode from "@/utils/insertCode"
const props = defineProps({
  broadbandlink: {
    type: String,
    default: ''
  },
  broadbandStatus: {
    type: [String,Number],
    default: ''
  },
  preview: {
    type: String,
    default: null
  },
  configure:{
    type:String,
    default: null
  },
  shopId:{
    type: [String,Number],
    default: ''
  }
})

function insertCodeGoods(key,url){
  let dcs_id = key
  if(props.preview){
    return
  }
  insertCode('yd_index_'+props.shopId+"_"+dcs_id,url)
}
</script>

<style lang="scss" scoped>
.goods-tt{
  height:45px;
  line-height:45px;
  font-size:16px;
  color:#333333;
  padding-left:39px;
  margin-bottom:1px;
  background:url(~@/assets/index_img/icon_06.png) 15px 12px no-repeat #fff;
  background-size:20px 20px;
  }
.pic{
    width:375px;
    display: block;
}
</style>


