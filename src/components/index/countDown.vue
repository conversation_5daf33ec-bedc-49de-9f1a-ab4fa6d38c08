<template>
  <span class="countDown">
    {{ time }}
  </span>
</template>

<script setup>
import {reactive,ref,defineProps} from "vue"
const time = ref(null)
const props = defineProps({
  countdowntim: {
    type: String,
    default: null
  }
})
function countTime() {
  //获取当前时间
  let date = new Date()
  let now = date.getTime()
  //设置截止时间
  let endDate = new Date("2022-08-24 23:23:23")
  let end = endDate.getTime()
  //时间差
  let leftTime = end - now
  // console.log(leftTime)
  //定义变量 d,h,m,s保存倒计时的时间
  let d,h,m,s
  if (leftTime >= 0) {
    d = Math.floor(leftTime / 1000 / 60 / 60 / 24)
    h = Math.floor(leftTime / 1000 / 60 / 60 % 24)
    m = Math.floor(leftTime / 1000 / 60 % 60)
    s = Math.floor(leftTime / 1000 % 60)
  }
  let sij=d+" "+ h+":"+m+":"+s
  time.value=sij
}
</script>