server {
  if ($request_method !~ ^(GET|HEAD|POST)$ ) {
    return 444;
  }
  listen  80  default;
  server_name  localhost;
  access_log off;
  location / {
    return 403;
  }
}
server {
  listen       80;
  listen  [::]:80;
  server_name  touch.10086.cn grey.touch.10086.cn m.staff.ydsc.liuliangjia.cn;

  if ($request_method !~ ^(GET|HEAD|POST)$ ) {
    return 444;
  }
  if ($time_iso8601 ~ "^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})") {
    set $curtime $1$2$3$4;
  }
  access_log  /home/<USER>/logs/nginx/nginx.access.$curtime.log aspire;
  gzip on;
  gzip_min_length 1k;
  gzip_buffers 4 16k;
  gzip_http_version 1.1;
  gzip_comp_level 2;
  gzip_types text/plain application/x-javascript application/javascript text/css application/xml;
  gzip_vary on;
  etag off;
  root   /var/www/;
  if ( $request_uri ~ (\./|%2[eE]%2[fF]|%00) ) {
    rewrite ^.*$ /yundian/404.html? permanent ;
  }

  location / {
    index  index.html index.htm;
    try_files $uri $uri/ /yundian/index.html;
  }
  location ~ \.html$ {
    try_files $uri $uri/ /yundian/index.html;
    expires -1;
  }
  set $checkext '0';
  if ( $uri ~ \.(png|jpg|gif|js|css|html|ico|svg|woff|woff2|ttf|eot)$ ) {
    set $checkext '1';
  }
  if ( $uri ~ ^[^\.]*$ ) {
    set $checkext '1';
  }
  if ( $checkext = '0' ) {
    rewrite . https://shop.10086.cn;
  }



  #error_page  404              /404.html;
  error_page 404 403 =200 /yundian/index.html; #新增404定义
  # redirect server error pages to the static page /50x.html
  #
  error_page   500 502 503 504  /50x.html;
  location = /50x.html {
    root   /usr/share/nginx/html;
  }
}
