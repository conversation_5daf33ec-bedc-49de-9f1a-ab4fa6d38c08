user  root;
worker_processes  4;

error_log  /home/<USER>/logs/nginx/nginx.error.log warn;
pid        /var/run/nginx.pid;


events {
    worker_connections  65535;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    absolute_redirect  off;
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';
    log_format  aspire  '"$http_x_forwarded_for"\t"$remote_addr"\t"$request_time"\t'
                        '"$time_local"\t"$request"\t"$status"\t"$body_bytes_sent"\t'
                        '"$http_referer"\t"$http_user_agent"';
    access_log  /home/<USER>/logs/nginx/nginx.access.log aspire buffer=32k;
    server_tokens off;
    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    #gzip  on;
    client_body_timeout 20s;
    client_header_timeout 1s;
    include /etc/nginx/conf.d/*.conf;
}
